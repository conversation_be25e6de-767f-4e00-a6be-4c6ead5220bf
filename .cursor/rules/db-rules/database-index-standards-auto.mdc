---
description: 适用于任何与SQL相关的操作
globs: 
alwaysApply: false
---

# 数据库索引设计规范

## 上下文

- 适用于所有数据库表的索引设计
- 确保数据库查询性能
- 避免不必要的索引导致的性能损失

## 关键规则

- 每个表必须有主键索引，通常为 `id` 字段，使用 `PRIMARY KEY (id)`
- 频繁作为查询条件的字段应建立索引，包括但不限于：
  - 外键字段，如 `xxxId`、`xxx_id`
  - 状态字段，如 `status`、`deleteStatus`
  - 时间字段，如 `createTime`、`updateTime`
  - 编码字段，如 `orderNo`、`code`
- 索引命名应遵循以下规则：
  - 单字段索引可直接用字段名称，如 `KEY `status` (`status`)`
  - 前缀索引添加 `idx_` 前缀，如 `KEY `idx_name` (`name`(32))`
  - 组合索引使用 `idx_field1_field2` 格式，如 `KEY `idx_shop_status` (`shopId`, `status`)`
- 选择性高的字段（如唯一编号）适合作为索引
- 避免在低选择性字段上建立单列索引（如性别只有男/女两种值）
- 字符串类型应考虑使用前缀索引以减少索引大小，如 `KEY `idx_name` (`name`(32))`
- 组合索引遵循最左前缀原则，将使用频率高的字段放在左侧
- 避免索引过多，单表索引数量通常控制在5个以内
- 定期检查和优化不使用的索引
- 大数据表的 `TEXT` 或 `BLOB` 类型不应直接建立索引，应该使用前缀索引或单独的字段

## 示例

<example>
```sql
CREATE TABLE IF NOT EXISTS `qianniao_order_1` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `orderNo` varchar(32) NOT NULL COMMENT '订单编号',
    `customerId` int(11) NOT NULL COMMENT '客户ID',
    `orderStatus` tinyint(1) NOT NULL DEFAULT '1' COMMENT '订单状态',
    `orderAmount` decimal(10,2) NOT NULL COMMENT '订单金额',
    `shopId` int(11) NOT NULL COMMENT '店铺ID',
    `createTime` datetime NOT NULL COMMENT '创建时间',
    `updateTime` datetime NOT NULL COMMENT '更新时间',
    `deleteStatus` tinyint(1) NOT NULL DEFAULT '5' COMMENT '删除状态：4-已删除，5-正常',
    PRIMARY KEY (`id`),
    UNIQUE KEY `orderNo` (`orderNo`),
    KEY `idx_customer` (`customerId`),
    KEY `idx_status` (`orderStatus`),
    KEY `idx_shop_time` (`shopId`, `createTime`),
    KEY `idx_delete_status` (`deleteStatus`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '订单表';
```
</example>

<example type="invalid">
```sql
-- 索引过多的例子
CREATE TABLE IF NOT EXISTS `qianniao_product_1` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name` varchar(100) NOT NULL COMMENT '商品名称',
    `code` varchar(32) NOT NULL COMMENT '商品编码',
    `price` decimal(10,2) NOT NULL COMMENT '商品价格',
    `categoryId` int(11) NOT NULL COMMENT '分类ID',
    `brandId` int(11) NOT NULL COMMENT '品牌ID',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
    `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存',
    `createTime` datetime NOT NULL COMMENT '创建时间',
    `updateTime` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `name` (`name`), -- 全字段索引浪费空间
    KEY `code` (`code`),
    KEY `price` (`price`), -- 价格字段不适合单独索引
    KEY `categoryId` (`categoryId`),
    KEY `brandId` (`brandId`),
    KEY `status` (`status`),
    KEY `stock` (`stock`), -- 库存不适合单独索引
    KEY `createTime` (`createTime`),
    KEY `updateTime` (`updateTime`) -- 更新时间索引可能不常用
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;
```
</example> 