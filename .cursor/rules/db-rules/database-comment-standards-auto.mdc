---
description: 适用于任何与SQL相关的操作
globs: 
alwaysApply: false
---

# 数据库注释规范

## 上下文

- 适用于所有数据库表和字段的注释
- 提高数据库文档的可读性和可维护性
- 方便开发人员理解数据库结构和业务逻辑

## 关键规则

- 每个表必须有表注释，说明表的用途，使用 `COMMENT = '表说明'`
- 每个字段必须有字段注释，使用 `COMMENT '字段说明'`
- 字段注释应清晰说明字段用途，避免使用简单重复字段名的注释
- 对于状态、类型等枚举字段，使用int类型， 注释中应说明每个值的含义，如 `COMMENT '状态：4-禁用，5-启用'`
- 注释应使用中文，便于中国开发者理解
- 关键业务字段的注释应包含业务规则说明
- 冗余字段应在注释中说明，如 `COMMENT '用户名（冗余自用户表）'`
- 时间戳字段的注释应说明其用途，如 `COMMENT '创建时间'`
- 金额相关字段应在注释中说明单位，如 `COMMENT '销售价格（元）'`
- 当字段含义发生变化时，必须更新注释以保持一致性
- 当字段被设计为json格式时，必须使用json类型

## 示例

<example>
```sql
CREATE TABLE IF NOT EXISTS `qianniao_user_1` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(32) NOT NULL COMMENT '用户名，登录用',
    `realName` varchar(32) DEFAULT NULL COMMENT '真实姓名',
    `mobile` varchar(11) NOT NULL COMMENT '手机号码，唯一标识',
    `password` varchar(64) NOT NULL COMMENT '密码，MD5加密',
    `gender` tinyint(1) DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
    `userType` tinyint(1) NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-管理员',
    `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额（元）',
    `lastLoginTime` datetime DEFAULT NULL COMMENT '最后登录时间',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `createTime` datetime NOT NULL COMMENT '创建时间',
    `updateTime` datetime NOT NULL COMMENT '更新时间',
    `deleteStatus` tinyint(1) NOT NULL DEFAULT '5' COMMENT '删除状态：4-已删除，5-正常',
    PRIMARY KEY (`id`),
    UNIQUE KEY `mobile` (`mobile`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '用户信息表，存储系统用户基本信息';
```
</example>

<example type="invalid">
```sql
CREATE TABLE IF NOT EXISTS `qianniao_product_1` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID', -- 注释过于简单
    `name` varchar(100) NOT NULL COMMENT '名称', -- 注释过于简单，未说明是什么的名称
    `price` decimal(10,2) NOT NULL COMMENT '价格', -- 未说明单位
    `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '类型', -- 未说明具体类型值的含义
    `createTime` datetime NOT NULL, -- 缺少注释
    `updateTime` datetime NOT NULL, -- 缺少注释
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4; -- 缺少表注释
```
</example>