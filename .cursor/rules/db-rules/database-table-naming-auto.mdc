---
description: 适用于任何与SQL相关的操作
globs: 
alwaysApply: false
---

# 数据库表命名规范

## 上下文

- 适用于所有数据库表的创建和修改操作
- 确保表名遵循一致的命名规则
- 提高代码可读性和可维护性

## 关键规则

- 表名必须以项目前缀开头，如 `qianniao_` 等
- 表名应使用全小写字母和下划线，或遵循驼峰命名法（取决于项目约定）
- 表名应包含模块名称，如 `qianniao_car_sale_vehicle_1` 中的 `car_sale`
- 表名应明确表示其包含的数据类型，如 `vehicle`、`sales`、`revenue` 等
- 表名中的数字后缀（如 `_1`）表示数据库分表序号
- 相关表应使用相似的前缀，如 `qianniao_car_sale_` 表示车辆销售模块的所有表
- 备份表应以原表名加上日期或版本号，如 `qianniao_users_backup_20230101`
- 表名应避免使用保留字或通用术语作为整个表名
- 表名长度应控制在合理范围内，一般不超过64个字符

## 示例

<example>
```sql
-- 商品分类表
CREATE TABLE IF NOT EXISTS `qianniao_goods_category_1` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    -- 其他字段
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '商品分类表';

-- 销售订单表
CREATE TABLE IF NOT EXISTS `qianniao_sale_order_1` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    -- 其他字段
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '销售订单表';
```
</example>

<example type="invalid">
```sql
-- 表名不规范的例子
CREATE TABLE IF NOT EXISTS `Products` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    -- 其他字段
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 命名混乱的例子
CREATE TABLE IF NOT EXISTS `qianniao_UserData` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    -- 其他字段
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;
```
</example> 