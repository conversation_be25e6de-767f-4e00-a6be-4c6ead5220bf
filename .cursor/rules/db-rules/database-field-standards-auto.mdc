---
description: 适用于任何与SQL相关的操作
globs: 
alwaysApply: false
---

# 数据库字段命名和规范标准

## 上下文

- 适用于所有数据库相关操作，包括表的创建、修改和查询
- 数据库使用 MySQL，主要为 UTF8MB4 编码
- 这些规则确保所有表结构遵循一致的命名约定和结构标准

## 关键规则

- 主键字段必须命名为 `id`，类型为 `int(11)` 或 `int(10) unsigned`，并设置为 `AUTO_INCREMENT`
- 每个表必须包含 `createTime` 或 `create_time` 字段，类型为 `datetime` 或 `int(10)`（存储Unix时间戳）
- 每个表必须包含 `updateTime` 或 `update_time` 字段，类型为 `datetime` 或 `int(10)`（存储Unix时间戳）
- 表名和字段命名应采用一致的风格：要么全部使用下划线命名法（如 `create_time`），要么全部使用驼峰命名法（如 `createTime`）
- 每个字段必须包含 `COMMENT` 以说明其用途
- 常用逻辑删除字段为 `deleteStatus` 或 `delete_status`，值为 `4-已删除，5-正常` 或 `0-正常，1-已删除`
- 非必填字段应设置默认值，如 `DEFAULT NULL` 或特定的默认值
- 所有表必须设置字符集为 `utf8mb4`，如 `ENGINE = InnoDB DEFAULT CHARSET = utf8mb4`
- 所有表必须包含表注释，如 `COMMENT = '表的用途描述'`
- 对于频繁查询的字段应创建索引，使用 `KEY` 或 `INDEX` 关键字

## 示例

<example>
```sql
CREATE TABLE IF NOT EXISTS `qianniao_example_table` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name` varchar(64) NOT NULL COMMENT '名称',
    `description` text COMMENT '描述',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `createTime` datetime NOT NULL COMMENT '创建时间',
    `updateTime` datetime NOT NULL COMMENT '更新时间',
    `deleteStatus` tinyint(1) NOT NULL DEFAULT '5' COMMENT '删除状态：4-已删除，5-正常',
    PRIMARY KEY (`id`),
    KEY `name` (`name`),
    KEY `status` (`status`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '示例表';
```
</example>

<example type="invalid">
```sql
CREATE TABLE IF NOT EXISTS `example_table` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `Name` varchar(64) NOT NULL,
    `Description` text,
    `Status` tinyint(1) NOT NULL DEFAULT '1',
    PRIMARY KEY (`ID`)
) ENGINE = InnoDB;
```
</example> 