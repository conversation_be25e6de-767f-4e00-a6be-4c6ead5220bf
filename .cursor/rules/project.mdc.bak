---
description: 
globs: 
alwaysApply: true
---
CONTEXT: Claude 3.7 in Cursor IDE. Prevent unauthorized changes.
META: ALWAYS begin with [MODE: X]. Default: [MODE: FAST]. Only change on command.
MODES:
:one: RESEARCH [do res]

Purpose: Understand code without modifying
Allow: Reading, questions
Forbid: Suggestions, changes
Output: app/(pages)/contacts/contacts.doc.md (Overview, Files, Data Flow, Backend, Refs)

:two: INNOVATE [do inn]

Purpose: Brainstorm solutions
Allow: Ideas, pros/cons, feedback
Forbid: Planning, code, implementation

:three: PLAN [do pla]

Purpose: Detailed plan (no creative decisions later)
Allow: Paths, functions, technical details
Forbid: Code
Output: app/(pages)/contacts/contacts.task.md checklist (1. [Action] 2. [Action]…)

:four: EXECUTE [do exe]

Purpose: Implement EXACTLY per plan
Allow: Only planned steps
Forbid: Any deviation
If deviation needed → return to do pla
Guidelines: _prefix for all internal dirs, ~300-400 LOC/file

:five: REVIEW [do rev]

Purpose: Compare implementation with plan
Format: :warning: DEVIATION: [description]
Verdict: :check_box_with_check: MATCHES or :cross_mark: DEVIATES

:six: FAST [do fas]

Purpose: Minimal changes only
Forbid: Logic changes, optimizations, refactoring
If scope grows → return to do pla

TRANSITIONS: do res, do inn, do pla, do exe, do rev, do fas, + (next mode)
GUIDELINES: