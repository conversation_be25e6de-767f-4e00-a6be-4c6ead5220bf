---
description: 
globs: merchant/**/*.vue
alwaysApply: false
---

# Merchant Vue组件使用规则

## 上下文

- 适用于merchant项目中的Vue组件开发和使用
- 确保uni-app项目组件使用的一致性和可维护性
- 提供项目中常用组件的使用方法指南

## 关键规则

- 统一使用`uView`UI框架的组件，如`u-icon`、`u-navbar`等
- 图片上传统一使用`QiniuUpload`组件，支持多图上传和删除
- 日期选择器相关组件使用`DatePicker`、`DateFilter`和`DateRangePicker`
- 自定义图标使用`custom-icon`前缀配合图标名，如`<text class="custom-icon custom-icon-xinzeng"></text>`
- 导航跳转使用混入的`goPage`方法，不直接使用uni-app原生导航方法
- 数据展示卡片使用`StatCard`和`ChartCard`组件
- 列表筛选使用`FilterBar`组件统一管理筛选条件
- 路由检查使用全局`$accessCheck`和`$Access`进行权限验证
- 公用方法调用使用`$utils`对象
- 高精度数学计算使用`$NP`对象
- 全局状态管理使用Vuex，通过`$store`访问

## 示例

<example>
<!-- 日期筛选组件 -->
<DateFilter
  title="选择日期"
  type="range"
  :default-option="0"
  @change="handleDateChange"
/>

<!-- 数据统计卡片 -->
<StatCard
  title="销售统计"
  :stats="salesStats"
  layout="grid"
  @action-click="viewSalesDetail"
/>

<!-- 七牛云上传组件 -->
<QiniuUpload
  :images="imageList"
  :count="9"
  @uploadSuccess="handleUploadSuccess"
  @handleRemove="handleImageRemove"
/>

<!-- 权限验证 -->
<view v-if="$accessCheck($Access.overview)">
  <!-- 受权限保护的内容 -->
</view>
</example>

<example type="invalid">
<!-- 错误：直接使用uni-app原生导航方法 -->
<view @click="uni.navigateTo({url: '/pages/index/index'})">跳转</view>

<!-- 错误：未使用项目统一的上传组件 -->
<view @click="uni.chooseImage()">选择图片</view>

<!-- 错误：未使用权限验证 -->
<view>
  <!-- 未经权限验证的敏感内容 -->
</view>
</example> 