---
description: 
globs: admin-front/src/**/*.vue
alwaysApply: false
---

# Admin-Front Vue组件使用规则

## 上下文

- 适用于admin-front项目中的Vue组件开发和使用
- 确保组件使用的一致性和可维护性
- 提供项目中常用组件的使用方法指南

## 关键规则

- 所有选择类组件需提供`v-model`或`:value/@change`双向绑定
- 分页组件统一使用`FooterPage`组件
- 表单提交前必须进行数据验证，使用`this.$refs.form.validate()`
- 弹窗类组件需处理关闭事件，避免内存泄漏
- 选择器组件必须支持`clearable`属性以允许清空选择
- 图片上传组件统一使用`UploadQiniu`或`QiniuMultipleUp`组件
- 表格组件需配置合理的列宽和`show-overflow-tooltip`属性
- 列表类组件需要实现加载状态`loading`属性
- 区域选择使用`RegionSelect`或`AreaSelect`组件
- 组件接收的回调函数应遵循`@xxChange`的命名方式
- 组件传参类型必须在`props`中明确定义，提供默认值

## 示例

<example>
<!-- 分页组件使用 -->
<FooterPage 
  :totalPage.sync="total" 
  :currentPage.sync="currentPage" 
  @pageChange="handlePageChange" 
  @sizeChange="handleSizeChange"
/>

<!-- 通用选择组件使用 -->
<SelectCustomer 
  v-model="customerId"
  placeholder="请选择客户" 
  :clearable="true"
  @change="handleCustomerChange"
/>

<!-- 图片上传组件使用 -->
<UploadQiniu
  :limit="1"
  :fileList="imageList"
  @uploadSuccess="handleUploadSuccess"
  @handleRemove="handleRemoveImage"
/>
</example>

<example type="invalid">
<!-- 错误：未提供必要的属性和事件处理 -->
<FooterPage />

<!-- 错误：未使用v-model或提供change事件 -->
<SelectCustomer :value="customerId" />

<!-- 错误：直接使用el-upload而非项目统一的上传组件 -->
<el-upload action="/upload" :file-list="files"></el-upload>
</example> 