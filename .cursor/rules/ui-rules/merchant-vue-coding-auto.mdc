---
description: 
globs: merchant/**/*.vue
alwaysApply: false
---

# Merchant Vue文件编码规则

## 上下文

- 适用于merchant项目中的所有Vue文件
- 确保uni-app项目代码结构和风格一致性
- 提高代码可维护性和可读性

## 关键规则

- Vue文件必须按照`<template>`, `<script>`, `<style>`顺序组织
- uni-app项目使用`view`、`text`等uni标签替代HTML标签
- 组件名称使用PascalCase（大驼峰）命名，如`StatCard`、`DatePicker`
- 页面文件放在`pages`目录下，组件文件放在`components`目录下
- 样式使用`scss`预处理器，引入`@/uni.scss`中定义的变量
- `props`必须定义类型和默认值，必要时提供验证函数
- 需要在多个页面共享的逻辑使用`mixin`实现
- 全局状态使用Vuex管理，页面内状态使用`data`
- 复杂页面视图结构应拆分为多个子组件
- API请求使用`$u.api`进行调用
- 使用组件时属性超过3个应换行并垂直对齐
- 页面和组件应包含合适的注释，尤其是复杂方法的说明
- 对时间、金额等格式化操作使用`$utils`中的方法

## 示例

<example>
<template>
  <view class="date-filter">
    <view class="filter-header" v-if="title">
      <text class="filter-title">{{ title }}</text>
    </view>
    
    <view class="filter-content">
      <!-- 日期选择组件 -->
      <date-picker
        :type="pickerType"
        :initial-date="date"
        @change="onDateChange"
      ></date-picker>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DateFilter',
  props: {
    title: {
      type: String,
      default: '日期筛选'
    },
    pickerType: {
      type: String,
      default: 'month'
    }
  },
  data() {
    return {
      date: new Date()
    }
  },
  methods: {
    onDateChange(e) {
      this.$emit('change', e.date)
    }
  }
}
</script>

<style lang="scss" scoped>
.date-filter {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  
  .filter-header {
    margin-bottom: 15px;
    
    .filter-title {
      font-size: 16px;
      font-weight: 500;
      color: $uni-text-color;
    }
  }
}
</style>
</example>

<example type="invalid">
<template>
<div>
  <div class="header">{{ title }}</div>
  <select v-model="selected" @change="handleChange">
    <option v-for="item in options" :value="item.value">
      {{ item.label }}
    </option>
  </select>
</div>
</template>
<script>
export default {
  data() { return { selected: '', options: [] } },
  methods: { handleChange() { this.$emit('change') } }
}
</script>
<style>
.header { color: blue; font-size: 16px; }
</style>
</example> 