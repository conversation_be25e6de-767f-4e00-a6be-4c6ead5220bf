---
description: 适用于admin-front项目中的所有Vue文件，包括新创建的vue文件
globs: 
alwaysApply: false
---

# Vue文件编码规则 - Admin Front项目

## 上下文

- 适用于admin-front项目中的所有Vue文件
- 用于保持Vue文件代码风格一致性及提高代码质量
- 确保所有新的Vue文件遵循项目既有风格和最佳实践

## 关键规则

- Vue文件必须按照`<template>`, `<script>`, `<style>`顺序组织
- 组件名称必须使用PascalCase（大驼峰）命名方式，如`SelectCustomer`
- Props必须定义类型和默认值（除非确实没有默认状态）
- 复杂组件应提供使用示例注释在顶部
- 组件方法和属性按照功能分组排列（data, computed, methods等）
- CSS应使用`scoped`属性和`lang="scss"`，遵循项目样式约定
- 表格、分页等常用UI模式应使用项目统一组件（如FooterPage）
- 元素属性超过3个时应换行并垂直对齐
- 使用`v-for`必须提供`key`属性
- 组件props应明确声明传入数据的类型和约束条件
- 表单类组件应包含输入验证和清空功能
- 异步操作需显示加载状态指示
- 路由配置文件 admin-front/src/router/index.js
- 菜单配置文件 admin-front/src/vab/components/GalleryBar/*menus.js
- 权限文件 admin-front/src/access/node.js

## 目录结构
- admin-front
-- src
--- access：权限管理
--- api：接口
--- assets：静态资源
--- component：组件
--- config：配置文件
--- extra：扩展
--- icons：图标
--- layouts：布局
--- mixin：混入
--- router：路由
--- store：状态管理
--- utils：工具类
--- vab: vab组件
--- views：页面
--- viewsMerch: 商家端
--- vxeTable: vxe-table组件
--- App.vue：入口文件
--- main.js：入口文件
--- package.json：包管理文件
--- README.md：项目说明文件
--- vue.config.js：webpack配置文件
-- public：公共资源
-- dist：打包后的文件
-- .gitignore：git忽略文件
-- .eslintrc.js：eslint配置文件

## 示例

<example>
<!--客户选择组件-->
<template>
  <div class="select-customer">
    <el-select
      :value="value"
      :placeholder="placeholder"
      :clearable="clearable"
      filterable
      remote
      :loading="loading"
      @change="selChange"
    >
      <el-option
        v-for="(item, index) in customerList"
        :key="index"
        :label="item.name"
        :value="item.id"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: "SelectCustomer",
  props: {
    value: {
      type: [Number, String],
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择客户",
    },
    clearable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      loading: false,
      customerList: [],
    };
  },
  methods: {
    selChange(val) {
      this.$emit("change", val);
    },
  },
};
</script>

<style lang="scss" scoped>
.select-customer {
  display: inline-block;
}
</style>
</example>

<example type="invalid">
<template>
  <div>
    <el-select v-model="selected" filterable>
      <el-option v-for="item in list" :label="item.name" :value="item.id"></el-option>
    </el-select>
  </div>
</template>
<script>
export default {
  data() { return { selected: "", list: [] } },
  methods: { change() { this.$emit('select', this.selected) } }
}
</script>
<style>
div { margin: 10px }
</style>
</example> 