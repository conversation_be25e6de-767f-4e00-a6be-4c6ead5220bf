---
description: 
globs: qianniao-admin/**/Model/**/*.php
alwaysApply: false
---

# 千鸟商城 Model 层编码规范

## 上下文

- 适用于 qianniao-admin 项目的 Model 层代码
- 所有的 Model 类都应该遵循以下规范
- 目的是实现业务逻辑层与数据访问层的分离，增强代码可维护性

## 关键规则

- 所有 Model 类必须放在 `JinDouYun\Model` 命名空间下，根据业务模块划分子命名空间
- Model 类命名规则为 `M` + 实体名，如 `MStaff`, `MOrder`, `MRoutePlanning`
- Model 类文件命名规则为 `M{实体名}.Class.php`，如 `MStaff.Class.php`, `MRoutePlanning.Class.php`
- Model 类通常应继承 `MBaseModel` 基类
- 所有成员变量应该有类型注释，并使用 `protected` 或 `private` 可见性
- Dao 对象和引用的其他模型对象应采用 `obj` 为前缀的命名方式，如 `$this->objDStaff`，`$this->objMCustomer`
- 构造函数参数优先顺序应为 `$enterpriseId`、`$userCenterId/userId`，之后是其他可选参数
- 在构造函数中应先调用父类构造函数，再初始化所有 Dao 对象和需要用到的模型对象
- 对于需要分表的Dao，应在构造函数中设置表名，如 `$this->objDRoutePlanning->setTable('qianniao_route_planning_'.$onlineEnterpriseId)`
- 所有方法的返回值应该是 `ResultWrapper` 类型，不允许直接返回原始数据
- 使用 `ResultWrapper::success($data)` 返回成功数据，使用 `ResultWrapper::fail($message, $errorCode)` 返回错误信息
- 业务方法应使用 try-catch 结构进行异常处理，在catch中返回 `ResultWrapper::fail()` 并附带详细错误信息
- 所有数据访问操作通过 Dao 层进行，不允许在 Model 层直接操作数据库
- 对数据库操作的错误处理，应返回 Dao 层的错误信息，如 `ResultWrapper::fail($this->objDStaff->error(), ErrorCode::$dberror)`
- 所有方法应该有PHPDoc注释，说明方法功能、参数和返回值
- 方法命名使用驼峰命名法，应遵循 `getXXX`, `addXXX`, `updateXXX`, `deleteXXX` 等统一风格
- 复杂的数据处理逻辑应提取为私有方法，保持主要方法清晰简洁
- 对于需要格式化的数据，应单独提供 `format` 相关方法进行处理
- 参数验证应在方法开始处进行，确保参数的有效性

## 示例

<example>
<?php

namespace JinDouYun\Model\CarSale;

use JinDouYun\Dao\CarSale\DRoutePlanning;
use JinDouYun\Dao\Customer\DCustomer;
use JinDouYun\Dao\Customer\DCustomerVisitsLog;
use JinDouYun\Model\Customer\MCustomer;
use JinDouYun\Model\MBaseModel;
use Mall\Framework\Core\ResultWrapper;
use Mall\Framework\Core\ErrorCode;
use Mall\Framework\Core\StatusCode;

class MRoutePlanning extends MBaseModel
{
    /**
     * 当前用户ID
     * @var int
     */
    protected $onlineUserId;

    /**
     * 当前企业ID
     * @var int
     */
    protected $onlineEnterpriseId;
    
    /**
     * 路线规划数据访问对象
     * @var DRoutePlanning
     */
    protected $objDRoutePlanning;
    
    /**
     * 客户数据访问对象
     * @var DCustomer
     */
    protected $objDCustomer;

    /**
     * 客户模型
     * @var MCustomer
     */
    protected $objMCustomer;

    /**
     * 构造函数，初始化表前缀
     *
     * @param int $onlineEnterpriseId 当前企业ID
     * @param int $onlineUserId 当前用户ID
     * @param int|null $loginUserCenterId 登录用户中心ID
     * @param bool $isFront 是否前台
     */
    public function __construct($onlineEnterpriseId, $onlineUserId, $loginUserCenterId = null, $isFront = false)
    {
        parent::__construct($onlineEnterpriseId, $onlineUserId);
        $this->onlineUserId = $onlineUserId;
        $this->onlineEnterpriseId = $onlineEnterpriseId;
        
        // 初始化数据访问对象
        $this->objDRoutePlanning = new DRoutePlanning();
        $this->objDRoutePlanning->setTable('qianniao_route_planning_'.$onlineEnterpriseId);
        
        $this->objDCustomer = new DCustomer();
        $this->objDCustomer->setTable('qianniao_customer_'.$onlineEnterpriseId);
        
        $this->objMCustomer = new MCustomer($onlineEnterpriseId, $onlineUserId);
    }

    /**
     * 获取路线规划客户列表
     *
     * @param int $enterpriseId 企业ID
     * @param string $date 日期
     * @return ResultWrapper 客户列表
     */
    public function getCustomersForRoutePlanning($enterpriseId, $date = ''): ResultWrapper
    {
        try {
            if (empty($date)) {
                $date = date('Y-m-d');
            }

            // 获取客户列表
            $customerResult = $this->objMCustomer->getAllCustomer([
                'limit' => 100,
                'offset' => 0,
                'hasLocation' => 1, // 仅获取有位置信息的客户
            ]);
            
            if (!$customerResult->isSuccess()) {
                return ResultWrapper::fail($customerResult->getData(), $customerResult->getErrorCode());
            }
            
            $customers = $customerResult->getData()['data'];
            if (empty($customers)) {
                return ResultWrapper::success([]);
            }

            // 获取今日已拜访的客户
            $visitedCustomerIds = $this->getVisitedCustomerIdsByDate($enterpriseId, $this->onlineUserId, $date);

            // 构建客户数据
            $result = $this->buildCustomerData($customers, $visitedCustomerIds);
            
            return ResultWrapper::success($result);
        } catch (\Exception $e) {
            return ResultWrapper::fail('获取路线规划客户列表失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }
    
    /**
     * 记录客户拜访
     *
     * @param int $enterpriseId 企业ID
     * @param int $userId 用户ID
     * @param int $customerId 客户ID
     * @param string $date 日期
     * @param array $params 附加参数
     * @return ResultWrapper 拜访记录ID
     */
    public function recordCustomerVisit($enterpriseId, $userId, $customerId, $date = '', $params = []): ResultWrapper
    {
        try {
            if (empty($date)) {
                $date = date('Y-m-d');
            }

            // 记录客户拜访日志
            $data = [
                'enterpriseId' => $enterpriseId,
                'userId' => $userId,
                'customerId' => $customerId,
                'visitTime' => date('Y-m-d H:i:s'),
                'visitType' => isset($params['visitType']) ? $params['visitType'] : 1,
                'createTime' => time()
            ];

            $visitId = $this->objDCustomerVisitLog->insert($data);

            if ($visitId === false) {
                return ResultWrapper::fail('记录客户拜访失败：' . $this->objDCustomerVisitLog->error(), ErrorCode::$dberror);
            }

            return ResultWrapper::success($visitId);
        } catch (\Exception $e) {
            return ResultWrapper::fail('记录客户拜访失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }
}
</example>

<example type="invalid">
<?php

namespace JinDouYun\Model;

// 错误：命名空间不规范，应按模块划分子命名空间
class User
{
    // 错误：类名应以 M 开头
    // 错误：没有继承 MBaseModel
    
    // 错误：数据访问对象没有使用obj前缀命名
    private $dUser;
    
    // 错误：构造函数参数顺序不规范
    public function __construct($userId = '', $enterpriseId = '')
    {
        // 错误：没有调用父类构造函数
        // 错误：没有设置表名
        $this->dUser = new DUser();
    }
    
    // 错误：方法没有注释
    public function getUserInfo($userId)
    {
        // 错误：没有参数验证
        
        // 错误：没有使用try-catch处理异常
        $userData = $this->dUser->getUserById($userId);
        
        // 错误：没有错误处理
        
        // 错误：直接返回数据，没有使用ResultWrapper包装
        return $userData;
    }
    
    // 错误：直接在Model层操作数据库
    public function updateUserStatus($userId, $status)
    {
        $db = new PDO('mysql:host=localhost;dbname=test', 'root', '');
        $stmt = $db->prepare("UPDATE user SET status = ? WHERE id = ?");
        return $stmt->execute([$status, $userId]);
    }
}
</example>