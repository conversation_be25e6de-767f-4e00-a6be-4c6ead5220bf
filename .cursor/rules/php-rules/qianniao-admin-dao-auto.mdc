---
description: 
globs: qianniao-admin/**/Dao/**/*.php
alwaysApply: false
---

# 千鸟商城 Dao 层编码规范

## 上下文

- 适用于 qianniao-admin 项目的 Dao 层代码
- 所有的 Dao 类都应该遵循以下规范
- 目的是实现数据访问的规范化，统一数据库操作接口

## 关键规则

- 所有 Dao 类必须放在 `JinDouYun\Dao` 命名空间下，按业务模块划分子命名空间
- Dao 类命名必须以 D 开头，后跟实体名称，如 `DUser`
- Dao 类必须继承 `BaseDao` 基类
- 构造函数中必须定义以下属性：
  - `$this->_table` - 指定操作的表名
  - `$this->_primary` - 指定主键字段名，通常为 'id'
  - `$this->_fields` - 定义表的所有字段数组，包含字段注释
  - `$this->_readonly` - 定义只读字段数组，通常包含 'id'
  - `$this->_create_autofill` - 定义创建时自动填充的字段，如 'createTime' => time()
  - `$this->_update_autofill` - 定义更新时自动填充的字段，如 'updateTime' => time()
- 构造函数必须接收 `$serviceDB` 参数，并在最后调用父类构造函数 `parent::__construct($serviceDB)`
- 增删改查操作必须使用 BaseDao 提供的方法（insert, update, delete, select）
- 复杂查询可通过 `query` 方法执行原生 SQL，但需要添加注释说明查询目的
- 使用 `getTableName` 方法计算分表表名
- 分表操作使用 `setTable` 方法切换表
- 使用事务时必须配对使用 `beginTransaction`, `commit` 和 `rollBack`
- 批量插入应使用 `insert` 方法的 `$multiple` 参数
- 自定义查询方法应遵循 `getXXXByYYY` 的命名规则，如 `getUserById`
- 查询结果应直接返回，不进行业务逻辑处理
- 所有字段名应与数据库字段保持一致，通常使用驼峰命名法
- 所有查询方法应提供完整的 PHPDoc 注释，包括参数和返回值说明
- 自定义方法中，如需切换表操作，须先使用 `$this->_table` 指定表名

## 示例

<example>
<?php

namespace JinDouYun\Dao\User;

use JinDouYun\Dao\BaseDao;

class DUser extends BaseDao
{
    public function __construct($serviceDB = 'default')
    {
        $this->_table = 'user';
        $this->_primary = 'id';
        $this->_fields = [
            "id", // int(10) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
            "name", // varchar(50) NOT NULL COMMENT '用户名称',
            "mobile", // char(11) DEFAULT NULL COMMENT '手机号',
            "password", // varchar(255) DEFAULT NULL COMMENT '密码',
            "status", // tinyint(3) DEFAULT '5' COMMENT '状态 5:正常 4:禁用',
            "deleteStatus", // tinyint(3) DEFAULT '5' COMMENT '删除状态 4:删除 5:正常',
            "createTime", // int(10) DEFAULT NULL COMMENT '创建时间',
            "updateTime", // int(10) DEFAULT NULL COMMENT '更新时间',
            "extend", // json DEFAULT NULL COMMENT '拓展字段',
        ];
        $this->_readonly = ['id'];
        $this->_create_autofill = [
            'createTime' => time()
        ];
        $this->_update_autofill = [
            'updateTime' => time()
        ];
        
        parent::__construct($serviceDB);
    }
    
    /**
     * 根据用户ID获取用户信息
     * 
     * @param int $userId 用户ID
     * @return array 用户信息
     */
    public function getUserById($userId)
    {
        $result = $this->select('*', ['id' => $userId], 1);
        return $result ? $result[0] : [];
    }
    
    /**
     * 根据多个用户ID获取用户列表
     * 
     * @param array $userIds 用户ID数组
     * @return array 用户列表
     */
    public function getUsersByIds($userIds)
    {
        if (empty($userIds)) {
            return [];
        }
        
        return $this->select('*', ['id' => ['IN', $userIds]]);
    }
    
    /**
     * 添加用户
     * 
     * @param array $userData 用户数据
     * @return int|bool 成功返回用户ID，失败返回false
     */
    public function addUser($userData)
    {
        return $this->insert($userData);
    }
    
    /**
     * 更新用户信息
     * 
     * @param int $userId 用户ID
     * @param array $userData 用户数据
     * @return int 影响的行数
     */
    public function updateUser($userId, $userData)
    {
        return $this->update($userData, ['id' => $userId]);
    }
    
    /**
     * 根据复杂条件查询用户列表
     * 
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array 用户列表
     */
    public function getUsersByCondition($condition, $page = 1, $pageSize = 20)
    {
        $where = [];
        
        if (!empty($condition['name'])) {
            $where['name'] = ['LIKE', '%' . $condition['name'] . '%'];
        }
        
        if (isset($condition['status'])) {
            $where['status'] = $condition['status'];
        }
        
        $offset = ($page - 1) * $pageSize;
        $limit = [$offset, $pageSize];
        
        return $this->select('*', $where, $limit, 'id DESC');
    }
    
    /**
     * 执行自定义复杂SQL查询
     * 
     * @param array $params 查询参数
     * @return array 查询结果
     */
    public function getCustomStatistics($params)
    {
        $startTime = isset($params['startTime']) ? $params['startTime'] : 0;
        $endTime = isset($params['endTime']) ? $params['endTime'] : time();
        
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as normal,
                    SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as disabled
                FROM {$this->_table}
                WHERE deleteStatus = 5
                    AND createTime BETWEEN {$startTime} AND {$endTime}";
        
        $result = $this->query($sql);
        return $result ? $result[0] : [];
    }
}
</example>

<example type="invalid">
<?php

namespace JinDouYun\Dao;

// 错误：命名空间不规范，应按模块划分子命名空间
class User extends BaseDao
{
    // 错误：类名应以 D 开头
    
    // 错误：缺少 _fields、_readonly 等定义
    public function __construct()
    {
        // 错误：未指定表名
        // 错误：未调用父类构造函数
    }
    
    // 错误：方法名不符合 getXXXByYYY 规范
    public function fetchUserData($userId)
    {
        // 错误：直接拼接 SQL，有注入风险
        $sql = "SELECT * FROM user WHERE id = " . $userId;
        return $this->query($sql);
    }
    
    // 错误：方法缺少注释说明
    public function add($data)
    {
        // 错误：缺少必要的字段过滤
        return $this->insert($data);
    }
    
    // 错误：在查询方法中包含业务逻辑处理
    public function getUserInfo($userId)
    {
        $user = $this->select('*', ['id' => $userId], 1);
        if ($user) {
            $user[0]['createTimeFormat'] = date('Y-m-d H:i:s', $user[0]['createTime']);
            // 错误：业务逻辑处理应放在 Model 层
            $user[0]['isAdmin'] = $this->checkUserIsAdmin($user[0]['id']);
        }
        return $user ? $user[0] : [];
    }
    
    // 错误：DAO 层不应包含业务逻辑方法
    private function checkUserIsAdmin($userId)
    {
        return $userId == 1;
    }
}
</example>