---
description: 新创建的php文件；qianniao-admin 项目代码规范，包含 controller、model 、 dao 层
globs:
alwaysApply: false
---
# 千鸟商城代码规范

## 上下文

- 适用于 qianniao-admin 项目的所有代码
- 所有类都应该遵循以下规范
- 目的是维持代码风格一致性，提高开发效率和可维护性
- 创建或更新的所有php代码文件放在目录 @qianniao-admin/gxs.hui1688.cn/
- 如果文件能根据其作用放在现有目录下就不要新建文件夹
- php文件路径需要符合现有的结构，不需要遵循命名空间JinDouYun
- 在创建新文件时必须查询是否已存在相关的文件，保持项目文件干净整洁

## 目录结构
- qianniao-admin
-- gxs.hui1688.cn
--- Cache: 缓存
--- Common: 公共文件
--- Config: 配置文件
--- Controller: 控制器
--- Dao: 数据访问层
--- Model: 业务服务层
--- Util: 服务层
-- sql: sql文件

## Controller 层规范

- 所有 Controller 类必须放在 `JinDouYun\Controller` 命名空间下，按业务模块划分子命名空间
- Controller 类命名应使用业务实体名称，通常不需要加Controller、API等后缀，如 `Staff`, `Order`
- Controller 类文件命名规则为 `{实体名}.Class.php`，如 `Staff.Class.php`
- Controller 类必须继承 `BaseController` 基类
- 在构造函数中必须先调用父类构造函数传入参数 (`$isCheckAcl`, `$isMustLogin`)，再初始化模型对象
- 模型对象应采用 `obj` 为前缀的命名方式，如 `$this->objMStaff = new MStaff(...)`
- 接口参数优先使用 `$this->request->getRawJson()` 获取请求体JSON数据，对于URL参数使用 `$this->request->param('request_id')`
- 参数验证应提取为公共方法，如 `commonFieldFilter()`，过滤和校验所有输入参数
- 参数校验失败应使用 `$this->sendOutput('错误消息', ErrorCode::$paramError)` 中断处理
- 业务处理应调用对应的 Model 层方法，处理 Model 返回的 ResultWrapper 对象
- 业务处理结果应使用 `$this->sendOutput($result->getData())` 或 `$this->sendOutput($result->getData(), $result->getErrorCode())` 返回
- 对于分页数据，应使用规范的分页参数格式: `pageIndex`, `pageSize`, `pageTotal`
- 不应在 Controller 层直接操作数据库，必须通过 Model 层进行
- 方法命名应采用 `getXXX`, `addXXX`, `updateXXX`, `deleteXXX` 等统一风格

## Model 层规范

- 所有 Model 类必须放在 `JinDouYun\Model` 命名空间下，按业务模块划分子命名空间
- Model 类命名规则为 `M` + 实体名，如 `MStaff`, `MOrder`
- Model 类文件命名规则为 `M{实体名}.Class.php`，如 `MStaff.Class.php`
- Model 类通常继承 `MBaseModel` 基类
- Dao 对象和引用的其他模型对象应采用 `obj` 为前缀的命名方式，如 `$this->objDStaff`，`$this->objMCustomer`
- 在构造函数中应初始化所有 Dao 对象和需要用到的模型对象
- 构造函数参数优先顺序应为 `$enterpriseId`、`$userCenterId/userId`，之后是其他可选参数
- 应显式初始化类成员变量，并标注类型
- 所有数据访问操作通过 Dao 层进行，不允许在 Model 层直接操作数据库
- 所有方法的返回值应该是 `ResultWrapper` 类型，不允许直接返回原始数据
- 使用 `ResultWrapper::success()` 返回成功数据，使用 `ResultWrapper::fail()` 返回错误信息
- 对数据库操作的错误处理，应返回 Dao 层的错误信息，如 `ResultWrapper::fail($this->objDStaff->error(), ErrorCode::$dberror)`
- 所有方法应该有注释，说明方法功能、参数和返回值
- 方法命名应与 Controller 层保持一致，使用 `getXXX`, `addXXX`, `updateXXX`, `deleteXXX` 等统一风格
- 复杂的数据处理逻辑应提取为私有方法，保持主要方法清晰简洁
- 异常处理使用 try-catch 结构，在 catch 中应返回 `ResultWrapper::fail()` 并附带详细错误信息

## Dao 层规范

- 所有 Dao 类必须放在 `JinDouYun\Dao` 命名空间下，按业务模块划分子命名空间
- Dao 类命名规则为 `D` + 实体名，如 `DStaff`, `DOrder`
- Dao 类文件命名规则为 `D{实体名}.Class.php`，如 `DStaff.Class.php`
- Dao 类不应负责具体的数据库访问操作，不应包含业务逻辑
- 所有表名应当用常量或成员变量定义，不允许硬编码
- 对于需要分表的操作，应提供 `setTable` 方法来设置实际表名
- 应提供基本的 CRUD 操作方法: `get`, `select`, `insert`, `update`, `delete` 等
- 方法返回值应当统一，查询操作通常返回查询结果或 false，更新操作通常返回影响行数或 false
- 应该记录并提供获取最后错误信息的方法，如 `error()`
- 复杂查询应当使用命名参数预处理语句，避免 SQL 注入风险

## Logger日志类使用规范

- Logger 类位于 `JinDouYun\Controller\Common` 命名空间下
- 主要用于记录系统错误、SQL错误和重要的业务流程日志
- 使用静态方法 `Logger::logs()` 进行日志记录，无需实例化
- 日志方法参数说明：
  - `$errno`: 错误级别，使用 PHP 内置错误常量如 `E_USER_ERROR`, `E_USER_WARNING`, `E_USER_NOTICE`
  - `$errmsg`: 错误描述信息，简明扼要地说明错误内容
  - `$filename`: 错误发生的文件，通常使用 `__CLASS__` 魔术常量
  - `$linenum`: 错误发生的行号，通常使用 `__LINE__` 魔术常量
  - `$vars`: 错误相关的变量或详细信息，可以是字符串、数组或对象
  - `$custom`: 可选参数，使用非容器内PHP时传true
- 日志记录应根据严重程度选择合适的错误级别：
  - `E_USER_ERROR`: 用于记录关键错误，如SQL查询失败、数据库操作失败、关键业务流程失败等
  - `E_USER_WARNING`: 用于记录警告信息，如业务流程中的非致命问题
  - `E_USER_NOTICE`: 用于记录普通通知信息，如重要业务流程的执行过程
- 日志内容规范：
  - 错误信息应简明扼要，明确指出错误类型
  - 对于SQL错误，应记录完整的错误信息，通常使用Dao对象的error()方法获取
  - 对于业务流程，应记录关键参数和处理结果
- 使用示例：
  ```php
  // 记录SQL错误
  Logger::logs(E_USER_ERROR, 'sql错误', __CLASS__, __LINE__, $this->objDOrder->error());

  // 记录业务流程错误
  Logger::logs(E_USER_ERROR, '编辑订单，库存解锁失败', __CLASS__, __LINE__, $unlockResult->getData());

  // 记录业务流程通知
  Logger::logs(E_USER_NOTICE, '计算订单总成本', __CLASS__, __LINE__, ['orderId' => $orderData['id'], 'totalCost' => $totalCost]);
  ```
- 日志文件会保存在`/www/logs/SysError/`目录下，按日期命名为`mm-dd.md`格式

## SqlHelper 使用规范

SqlHelper 是千鸟商城项目中的核心数据库操作类，位于 `Mall\Framework\Core` 命名空间下。所有 Dao 层类都应该基于此类进行数据库操作，遵循以下规范：

### 基本原则

- SqlHelper 是一个抽象类，不能直接实例化，需要通过继承使用
- 所有 Dao 类应继承 SqlHelper 类，并在构造函数中调用父类构造函数
- 不要使用占位符方式拼接 SQL 语句，应使用 SqlHelper 提供的方法
- 查询条件应使用数组形式传递，避免直接拼接 SQL 字符串
- 所有数据库操作应捕获异常并记录错误信息

### 表和字段定义

- 在 Dao 类中必须定义 `$_table` 属性指定表名
- 在 Dao 类中必须定义 `$_fields` 数组列出所有表字段
- 在 Dao 类中应定义 `$_primary` 属性指定主键字段
- 可选定义 `$_readonly` 数组指定只读字段
- 可选定义 `$_create_autofill` 和 `$_update_autofill` 数组指定自动填充字段

### 查询方法使用

- 使用 `select()` 方法进行基础查询，参数顺序为：`where`, `fields`, `order`, `limit`, `offset`, `data`, `multiple`
- 使用 `get()` 方法获取单条记录，参数顺序为：`where`, `fields`, `order`
- 使用 `get_by()` 方法根据字段值获取单条记录，参数顺序为：`field`, `value`, `fields`, `order`
- 使用 `gets_by()` 方法根据字段值获取多条记录，参数顺序为：`field`, `value`, `fields`, `order`, `limit`, `offset`
- 使用 `page()` 方法进行分页查询，参数顺序为：`where`, `fields`, `order`, `page`, `size`, `data`
- 使用 `count()` 方法获取记录总数，参数为：`where`, `data`
- 使用 `get_field()` 方法获取单个字段值，参数为：`field`, `where`, `data`
- 使用 `gets_field()` 方法获取字段值集合，参数为：`field`, `where`, `data`

### 查询条件构建

- 查询条件可以使用字符串形式，如：`"status=1 AND type='vip'"`
- 查询条件推荐使用数组形式，如：`['status' => 1, 'type' => 'vip']`
- 复杂条件可使用三元素数组，如：`[['create_time', 'between', ['2023-01-01', '2023-12-31']]]`
- 支持的操作符包括：`=`, `!=`, `>=`, `<=`, `like`, `in`, `not in`, `<`, `>`, `between`
- 使用链式调用构建查询条件：`$this->where($where)->order($order)->limit($limit)->select()`

### 数据操作方法

- 使用 `insert()` 方法插入数据，参数为：`data`, `multiple`
- 使用 `replace()` 方法替换插入数据，参数为：`data`, `multiple`
- 使用 `update()` 方法更新数据，参数为：`data`, `where`, `limit`, `order`
- 使用 `delete()` 方法删除数据，参数为：`where`, `limit`, `order`, `data`
- 使用 `delete_by()` 方法根据字段值删除数据，参数为：`field`, `value`, `limit`, `order`
- 使用 `set_field()` 方法更新单个字段值，参数为：`field`, `value`, `where`
- 使用 `set_inc()` 方法递增字段值，参数为：`field`, `where`, `step`, `data`
- 使用 `set_dec()` 方法递减字段值，参数为：`field`, `where`, `step`, `data`

### 事务处理

- 使用 `beginTransaction()` 方法开启事务
- 使用 `commit()` 方法提交事务
- 使用 `rollBack()` 方法回滚事务
- 事务操作应使用 try-catch 结构，在 catch 中回滚事务

### 错误处理

- 所有数据库操作方法在失败时返回 `false`
- 使用 `error()` 方法获取最后的错误信息
- 在 Model 层应捕获 Dao 层的错误并记录日志

### 使用示例

```php
// 在 Dao 类中定义表和字段
protected $_table = 'order';
protected $_primary = 'id';
protected $_fields = ['id', 'order_no', 'customer_id', 'amount', 'status', 'create_time', 'update_time'];
protected $_create_autofill = ['create_time' => 'CURRENT_TIMESTAMP'];
protected $_update_autofill = ['update_time' => 'CURRENT_TIMESTAMP'];

// 基本查询
$orders = $this->select(['status' => 1], '*', 'create_time DESC', 10, 0);

// 链式查询
$orders = $this->where(['status' => 1])
              ->order('create_time DESC')
              ->limit(10)
              ->select();

// 复杂条件查询
$orders = $this->select([
    ['create_time', 'between', ['2023-01-01', '2023-12-31']],
    ['status', 'in', [1, 2, 3]],
    'customer_id' => 100
]);

// 插入数据
$data = [
    'order_no' => 'ORD' . date('YmdHis'),
    'customer_id' => 100,
    'amount' => 199.99,
    'status' => 1
];
$orderId = $this->insert($data);

// 更新数据
$result = $this->update(
    ['status' => 2, 'update_time' => date('Y-m-d H:i:s')],
    ['id' => $orderId]
);

// 事务处理
try {
    $this->beginTransaction();
    $orderId = $this->insert($orderData);
    $this->insert($orderItemsData, true); // 批量插入
    $this->commit();
    return $orderId;
} catch (\Exception $e) {
    $this->rollBack();
    $this->error = $e->getMessage();
    return false;
}
```

## 示例代码

### Controller 层示例

```php
namespace JinDouYun\Controller\Department;

use Mall\Framework\Core\ErrorCode;
use JinDouYun\Controller\BaseController;
use JinDouYun\Model\Department\MStaff;

class Staff extends BaseController
{
    /**
     * 职工模型
     * @var MStaff
     */
    protected $objMStaff;

    /**
     * 构造函数
     *
     * @param bool $isCheckAcl 是否检查权限
     * @param bool $isMustLogin 是否必须登录
     */
    public function __construct($isCheckAcl = true, $isMustLogin = true)
    {
        parent::__construct($isCheckAcl, $isMustLogin);
        $this->objMStaff = new MStaff($this->onlineEnterpriseId, $this->onlineUserId);
    }

    /**
     * 获取参数
     *
     * @return array
     */
    public function commonFieldFilter()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            $this->sendOutput('参数为空', ErrorCode::$paramError);
        }

        $returnData = [];
        if (isset($params['staffName']) && !empty($params['staffName'])) {
            $returnData['staffName'] = $params['staffName'];
        }
        if (isset($params['mobile']) && !empty($params['mobile'])) {
            $returnData['mobile'] = $params['mobile'];
        }

        return $returnData;
    }

    /**
     * 职工列表
     */
    public function getAllStaff()
    {
        $params = $this->request->getRawJson();

        $pageParams = pageToOffset($params['page'] ?: 1, $params['pageSize'] ?: 10);
        $selectParams['limit'] = $pageParams['limit'];
        $selectParams['offset'] = $pageParams['offset'];

        $result = $this->objMStaff->getAllStaff($selectParams);

        if ($result->isSuccess()) {
            $returnData = $result->getData();
            $pageData = [
                'pageIndex' => $params['page'],
                'pageSize' => $params['pageSize'],
                'pageTotal' => $returnData['total'],
            ];
            $this->sendOutput($returnData['data'], 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }
}
```

### Model 层示例

```php
namespace JinDouYun\Model\CarSale;

use JinDouYun\Dao\CarSale\DRoutePlanning;
use JinDouYun\Dao\Customer\DCustomer;
use JinDouYun\Model\Customer\MCustomer;
use JinDouYun\Model\MBaseModel;
use Mall\Framework\Core\ResultWrapper;
use Mall\Framework\Core\ErrorCode;
use JinDouYun\Controller\Common\Logger;

class MRoutePlanning extends MBaseModel
{
    /**
     * 当前用户ID
     * @var int
     */
    protected $onlineUserId;

    /**
     * 当前企业ID
     * @var int
     */
    protected $onlineEnterpriseId;

    /**
     * 路线规划数据访问对象
     * @var DRoutePlanning
     */
    protected $objDRoutePlanning;

    /**
     * 客户模型
     * @var MCustomer
     */
    protected $objMCustomer;

    /**
     * 客户数据访问对象
     * @var DCustomer
     */
    protected $objDCustomer;

    /**
     * 构造函数，初始化表前缀
     *
     * @param int $onlineEnterpriseId 当前企业ID
     * @param int $onlineUserId 当前用户ID
     * @param int|null $loginUserCenterId 登录用户中心ID
     * @param bool $isFront 是否前台
     */
    public function __construct($onlineEnterpriseId, $onlineUserId, $loginUserCenterId = null, $isFront = false)
    {
        parent::__construct($onlineEnterpriseId, $onlineUserId);
        $this->onlineUserId = $onlineUserId;
        $this->onlineEnterpriseId = $onlineEnterpriseId;

        // 初始化数据访问对象
        $this->objDRoutePlanning = new DRoutePlanning();
        $this->objDRoutePlanning->setTable('qianniao_route_planning_'.$onlineEnterpriseId);

        $this->objDCustomer = new DCustomer();
        $this->objDCustomer->setTable('qianniao_customer_'.$onlineEnterpriseId);

        $this->objMCustomer = new MCustomer($onlineEnterpriseId, $onlineUserId);
    }

    /**
     * 获取路线规划客户列表
     *
     * @param int $enterpriseId 企业ID
     * @param string $date 日期
     * @return ResultWrapper 客户列表
     */
    public function getCustomersForRoutePlanning($enterpriseId, $date = ''): ResultWrapper
    {
        try {
            if (empty($date)) {
                $date = date('Y-m-d');
            }

            // 获取客户列表
            $customerResult = $this->objMCustomer->getAllCustomer([
                'limit' => 100,
                'offset' => 0,
                'hasLocation' => 1, // 仅获取有位置信息的客户
            ]);

            if (!$customerResult->isSuccess()) {
                Logger::logs(E_USER_ERROR, '获取客户列表失败', __CLASS__, __LINE__, $customerResult->getData());
                return ResultWrapper::fail($customerResult->getData(), $customerResult->getErrorCode());
            }

            $customers = $customerResult->getData()['data'];
            if (empty($customers)) {
                return ResultWrapper::success([]);
            }

            // 构建返回数据...

            return ResultWrapper::success($result);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '获取路线规划客户列表异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('获取路线规划客户列表失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }
}
```

### Dao 层示例

```php
namespace JinDouYun\Dao\CarSale;

use Mall\Framework\Factory;

class DRoutePlanning
{
    /**
     * @var \PDO
     */
    private $db;

    /**
     * 表名
     * @var string
     */
    private $table;

    /**
     * 错误信息
     * @var string
     */
    private $error;

    /**
     * 构造函数
     *
     * @param string $dbConfig 数据库配置
     */
    public function __construct($dbConfig = 'default')
    {
        $this->db = Factory::db($dbConfig);
    }

    /**
     * 设置表名
     *
     * @param string $table 表名
     */
    public function setTable($table)
    {
        $this->table = $table;
    }

    /**
     * 获取单条记录
     *
     * @param array|int $where 查询条件或ID
     * @param string $fields 查询字段
     * @return array|false 查询结果或false
     */
    public function get($where, $fields = '*')
    {
        try {
            // 实现获取单条记录的逻辑
            return $result;
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取错误信息
     *
     * @return string 错误信息
     */
    public function error()
    {
        return $this->error;
    }
}
```

### 无效代码示例

<example type="invalid">
<?php

// 错误：没有使用命名空间
class User
{
    private $db;

    // 错误：直接连接数据库
    public function __construct()
    {
        $this->db = new PDO('mysql:host=localhost;dbname=test', 'root', '');
    }

    // 错误：未进行参数验证
    // 错误：Controller 直接操作数据库
    public function getUserInfo()
    {
        $userId = $_GET['id'];
        $sql = "SELECT * FROM user WHERE id = " . $userId;
        $data = $this->db->query($sql)->fetch();
        echo json_encode($data);
    }
}
</example>
