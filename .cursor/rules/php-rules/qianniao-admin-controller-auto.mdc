---
description:
globs: qianniao-admin/**/Controller/**/*.php
alwaysApply: false
---

# 千鸟商城 Controller 层编码规范

## 上下文

- 适用于 qianniao-admin 项目的 Controller 层代码
- 所有的 Controller 类都应该遵循以下规范
- 目的是维持代码风格一致性，提高开发效率和可维护性

## 关键规则

- 所有 Controller 类必须放在 `JinDouYun\Controller` 命名空间下，按业务模块划分子命名空间
- Controller 类命名应使用业务实体名称，通常不需要加Controller后缀，如 `Staff`, `Order`
- Controller 类文件命名规则为 `{实体名}.Class.php`，如 `Staff.Class.php`
- Controller 类必须继承 `BaseController` 基类
- 在构造函数中必须先调用父类构造函数传入参数 (`$isCheckAcl`, `$isMustLogin`)，再初始化模型对象
- 模型对象应采用 `obj` 为前缀的命名方式，如 `$this->objMStaff = new MStaff(...)`
- 接口参数优先使用 `$this->request->getRawJson()` 获取请求体JSON数据，对于URL参数使用 `$this->request->param('request_id')`
- 参数验证应提取为公共方法，如 `commonFieldFilter()`，过滤和校验所有输入参数
- 参数校验失败应使用 `$this->sendOutput('错误消息', ErrorCode::$paramError)` 中断处理
- 业务处理应调用对应的 Model 层方法，处理 Model 返回的 ResultWrapper 对象
- 业务处理结果应使用 `$this->sendOutput($result->getData())` 或 `$this->sendOutput($result->getData(), $result->getErrorCode())` 返回
- 对于分页数据，应使用规范的分页参数格式: `pageIndex`, `pageSize`, `pageTotal`
- 不应在 Controller 层直接操作数据库，必须通过 Model 层进行
- 方法命名应采用 `getXXX`, `addXXX`, `updateXXX`, `deleteXXX` 等统一风格

## SqlHelper 使用规范

- 在 Controller 层不应直接使用 SqlHelper 类，应通过 Model 层间接使用
- 所有数据库操作必须通过 Model 层的方法进行，Model 层再调用 Dao 层的 SqlHelper 方法
- 禁止在 Controller 层直接拼接 SQL 语句或直接调用数据库查询方法

### SqlHelper 主要方法说明

1. **查询方法**:
   - `select($where, $fields, $order, $limit, $offset, $data, $multiple)`: 查询多条记录
   - `get($where, $fields, $order)`: 查询单条记录
   - `get_by($field, $value, $fields, $order)`: 根据字段值查询单条记录
   - `gets_by($field, $value, $fields, $order, $limit, $offset)`: 根据字段值查询多条记录
   - `get_field($field, $where, $data)`: 获取单个字段值
   - `gets_field($field, $where, $data)`: 获取字段集合
   - `count($where, $data)`: 获取记录总数
   - `page($where, $fields, $order, $page, $size, $data)`: 分页查询

2. **更新方法**:
   - `update($data, $where, $limit, $order)`: 更新记录
   - `set_field($field, $value, $where)`: 更新单个字段值
   - `set_inc($field, $where, $step, $data)`: 递增字段值
   - `set_dec($field, $where, $step, $data)`: 递减字段值

3. **插入方法**:
   - `insert($data, $multiple)`: 插入记录
   - `replace($data, $multiple)`: 替换方式插入记录

4. **删除方法**:
   - `delete($where, $limit, $order, $data)`: 删除记录
   - `delete_by($field, $value, $limit, $order)`: 根据字段值删除记录

5. **事务方法**:
   - `beginTransaction($foreign_key_checks)`: 开启事务
   - `commit()`: 提交事务
   - `rollBack()`: 回滚事务

### SqlHelper 查询条件规范

1. **字符串条件**:
   - 直接使用字符串形式的 SQL 条件，如 `"status=1 AND type='vip'"`
   - 注意：在 Dao 层不应使用占位符方式，应直接拼接 SQL

2. **数组条件**:
   - 使用关联数组，键为字段名，值为查询值：`['status' => 1, 'type' => 'vip']`
   - 数组形式会自动转换为 `AND` 连接的条件

3. **复杂条件**:
   - 使用三元素数组表示复杂条件：`[['status', '=', 1], ['create_time', 'between', [$startTime, $endTime]]]`
   - 支持的操作符：`=`, `!=`, `>=`, `<=`, `like`, `in`, `not in`, `<`, `>`, `between`

4. **主键查询**:
   - 直接传入主键值：`$dao->get(1)` 等同于 `$dao->get('id=1')`
   - 多个主键值：`$dao->select([1, 2, 3])` 等同于 `$dao->select('id IN(1,2,3)')`

### SqlHelper 使用注意事项

1. **返回值处理**:
   - 查询方法返回 `false` 表示查询出错，应检查 `$dao->error()` 获取错误信息
   - 查询方法返回空数组 `[]` 表示没有符合条件的记录
   - 插入/更新/删除方法返回受影响的行数，`0` 表示没有记录被修改，`false` 表示操作出错

2. **链式调用**:
   - 支持链式调用设置查询条件：`$dao->field('id,name')->where('status=1')->order('id DESC')->limit(10)->select()`
   - 链式方法包括：`field()`, `where()`, `order()`, `limit()`, `offset()`, `having()`, `group()`, `distinct()`, `data()`

3. **SQL 注入防护**:
   - 所有用户输入必须经过过滤和验证
   - 在 Controller 和 Model 层应使用参数绑定方式防止 SQL 注入
   - 在 Dao 层应确保所有变量都经过适当转义

## Controller 层示例

<example>
<?php

namespace JinDouYun\Controller\Department;

use Mall\Framework\Core\ErrorCode;
use JinDouYun\Controller\BaseController;
use JinDouYun\Model\Department\MStaff;

class Staff extends BaseController
{
    /**
     * 职工模型
     * @var MStaff
     */
    protected $objMStaff;

    /**
     * 构造函数
     *
     * @param bool $isCheckAcl 是否检查权限
     * @param bool $isMustLogin 是否必须登录
     */
    public function __construct($isCheckAcl = true, $isMustLogin = true)
    {
        parent::__construct($isCheckAcl, $isMustLogin);
        $this->objMStaff = new MStaff($this->onlineEnterpriseId, $this->onlineUserId);
    }

    /**
     * 获取参数
     *
     * @return array
     */
    public function commonFieldFilter()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            $this->sendOutput('参数为空', ErrorCode::$paramError);
        }

        $returnData = [];
        if (isset($params['staffName']) && !empty($params['staffName'])) {
            $returnData['staffName'] = $params['staffName'];
        }
        if (isset($params['mobile']) && !empty($params['mobile'])) {
            $returnData['mobile'] = $params['mobile'];
        }
        // 其他参数校验...

        return $returnData;
    }

    /**
     * 职工添加
     */
    public function addStaff()
    {
        $addStaffData = $this->commonFieldFilter();
        $addStaffData['shopId'] = $this->shopId;

        $result = $this->objMStaff->addStaff($addStaffData);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 职工列表
     */
    public function getAllStaff()
    {
        $params = $this->request->getRawJson();

        $pageParams = pageToOffset($params['page'] ?: 1, $params['pageSize'] ?: 10);
        $selectParams['limit'] = $pageParams['limit'];
        $selectParams['offset'] = $pageParams['offset'];

        // 其他参数处理...

        $result = $this->objMStaff->getAllStaff($selectParams);

        if ($result->isSuccess()) {
            $returnData = $result->getData();
            $pageData = [
                'pageIndex' => $params['page'],
                'pageSize' => $params['pageSize'],
                'pageTotal' => $returnData['total'],
            ];
            $this->sendOutput($returnData['data'], 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), ErrorCode::$dberror);
        }
    }
}
</example>

## SqlHelper 在 Model 和 Dao 层的使用示例

<example>
<?php

namespace JinDouYun\Model\Department;

use Mall\Framework\Core\ResultWrapper;
use JinDouYun\Dao\Department\DStaff;

class MStaff
{
    /**
     * 职工数据访问对象
     * @var DStaff
     */
    protected $objDStaff;

    /**
     * 企业ID
     * @var int
     */
    protected $enterpriseId;

    /**
     * 用户ID
     * @var int
     */
    protected $userId;

    /**
     * 构造函数
     *
     * @param int $enterpriseId 企业ID
     * @param int $userId 用户ID
     */
    public function __construct($enterpriseId, $userId)
    {
        $this->enterpriseId = $enterpriseId;
        $this->userId = $userId;
        $this->objDStaff = new DStaff();
    }

    /**
     * 添加职工
     *
     * @param array $data 职工数据
     * @return ResultWrapper
     */
    public function addStaff($data)
    {
        // 数据验证和处理
        if (empty($data['staffName'])) {
            return new ResultWrapper(null, 1001, '职工姓名不能为空');
        }

        // 添加创建信息
        $data['createTime'] = time();
        $data['createUser'] = $this->userId;
        $data['enterpriseId'] = $this->enterpriseId;

        // 调用 Dao 层方法
        $staffId = $this->objDStaff->addStaff($data);

        if ($staffId === false) {
            return new ResultWrapper(null, 1002, '添加职工失败: ' . $this->objDStaff->error());
        }

        return new ResultWrapper(['staffId' => $staffId]);
    }

    /**
     * 获取职工列表
     *
     * @param array $params 查询参数
     * @return ResultWrapper
     */
    public function getAllStaff($params)
    {
        // 构建查询条件
        $where = [
            'enterpriseId' => $this->enterpriseId,
            'isDeleted' => 0
        ];

        // 添加搜索条件
        if (!empty($params['keyword'])) {
            $where[] = ['staffName', 'like', '%' . $params['keyword'] . '%'];
        }

        // 获取总数
        $total = $this->objDStaff->getStaffCount($where);

        // 获取列表数据
        $list = $this->objDStaff->getStaffList(
            $where,
            'id, staffName, mobile, position, createTime',
            'id DESC',
            $params['limit'],
            $params['offset']
        );

        if ($list === false) {
            return new ResultWrapper(null, 1003, '获取职工列表失败: ' . $this->objDStaff->error());
        }

        // 处理返回数据
        foreach ($list as &$item) {
            // 格式化时间等操作
            $item['createTime'] = (int)$item['createTime'];
        }

        return new ResultWrapper([
            'data' => $list,
            'total' => $total
        ]);
    }
}
</example>

<example>
<?php

namespace JinDouYun\Dao\Department;

use Mall\Framework\Core\SqlHelper;

class DStaff extends SqlHelper
{
    /**
     * 数据表名
     * @var string
     */
    protected $_table = 'staff';

    /**
     * 主键
     * @var string
     */
    protected $_primary = 'id';

    /**
     * 表字段
     * @var array
     */
    protected $_fields = [
        'id', 'staffName', 'mobile', 'position', 'enterpriseId',
        'createTime', 'createUser', 'updateTime', 'updateUser', 'isDeleted'
    ];

    /**
     * 自动填充字段
     * @var array
     */
    protected $_create_autofill = [
        'isDeleted' => 0
    ];

    /**
     * 自动更新字段
     * @var array
     */
    protected $_update_autofill = [
        'updateTime' => 'time()'
    ];

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct('mall');
    }

    /**
     * 添加职工
     *
     * @param array $data 职工数据
     * @return int|bool
     */
    public function addStaff($data)
    {
        return $this->insert($data);
    }

    /**
     * 获取职工数量
     *
     * @param array|string $where 查询条件
     * @return int|bool
     */
    public function getStaffCount($where)
    {
        return $this->count($where);
    }

    /**
     * 获取职工列表
     *
     * @param array|string $where 查询条件
     * @param string|array $fields 查询字段
     * @param string $order 排序
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array|bool
     */
    public function getStaffList($where, $fields = '*', $order = null, $limit = null, $offset = null)
    {
        return $this->select($where, $fields, $order, $limit, $offset);
    }

    /**
     * 更新职工信息
     *
     * @param array $data 更新数据
     * @param array|string $where 更新条件
     * @return int|bool
     */
    public function updateStaff($data, $where)
    {
        return $this->update($data, $where);
    }

    /**
     * 删除职工（逻辑删除）
     *
     * @param int|array $staffId 职工ID
     * @param int $userId 操作用户ID
     * @return int|bool
     */
    public function deleteStaff($staffId, $userId)
    {
        $data = [
            'isDeleted' => 1,
            'updateTime' => time(),
            'updateUser' => $userId
        ];

        return $this->update($data, $staffId);
    }
}
</example>

<example type="invalid">
<?php

namespace JinDouYun\Controller;

// 错误：命名空间不规范，应放在适当的子命名空间中
// 错误：命名应简洁，通常不使用Controller后缀
class UserController
{
    // 错误：没有继承 BaseController

    public function __construct()
    {
        // 错误：没有调用父类构造函数
        // 错误：没有初始化Model对象
    }

    public function getUserInfo()
    {
        // 错误：直接使用 $_GET 获取参数，未通过规范方法
        $userId = $_GET['userId'];

        // 错误：未进行参数验证

        // 错误：直接操作数据库，未通过Model层
        $db = Factory::db();
        $userData = $db->select("SELECT * FROM user WHERE id = ?", [$userId]);

        // 错误：直接输出 JSON，未使用统一的响应格式
        echo json_encode($userData);
        exit;
    }

    // 错误：方法命名不规范
    public function userList()
    {
        // 错误：未处理分页参数

        // 错误：返回结果不符合规范格式
        echo json_encode(['users' => $users]);
    }
}
</example>

## SqlHelper 错误使用示例

<example type="invalid">
<?php

namespace JinDouYun\Dao\Product;

use Mall\Framework\Core\SqlHelper;

class DProduct extends SqlHelper
{
    protected $_table = 'product';
    protected $_primary = 'id';
    protected $_fields = ['id', 'name', 'price', 'stock', 'status'];

    public function __construct()
    {
        parent::__construct('mall');
    }

    /**
     * 错误：使用占位符方式拼接SQL
     * 正确做法：在Dao层应直接拼接SQL，不使用占位符
     */
    public function getProductsByCategory($categoryId)
    {
        // 错误：使用占位符
        return $this->select("category_id = ?", '*', 'id DESC', null, null, [$categoryId]);

        // 正确：直接拼接SQL
        // return $this->select("category_id = " . $categoryId, '*', 'id DESC');
    }

    /**
     * 错误：未定义表字段就使用
     * 正确做法：所有使用的字段必须在 $_fields 中定义
     */
    public function searchProducts($keyword)
    {
        // 错误：使用了未在 $_fields 中定义的字段 'category_id'
        $where = [
            'status' => 1,
            'category_id' => 5  // 错误：未定义的字段
        ];

        // 错误：使用了未在 $_fields 中定义的操作符
        $where[] = ['name', 'contains', $keyword];  // 错误：不支持的操作符

        return $this->select($where);
    }

    /**
     * 错误：不处理返回值和错误
     * 正确做法：检查返回值，处理错误情况
     */
    public function updateProductStock($productId, $stock)
    {
        // 错误：不检查返回值
        $this->update(['stock' => $stock], $productId);

        // 正确做法：
        // $result = $this->update(['stock' => $stock], $productId);
        // if ($result === false) {
        //     // 处理错误
        //     return false;
        // }
        // return $result;
    }

    /**
     * 错误：直接执行原生SQL
     * 正确做法：使用SqlHelper提供的方法
     */
    public function getProductStats()
    {
        // 错误：直接执行原生SQL
        $sql = "SELECT category_id, COUNT(*) as count, AVG(price) as avg_price FROM product GROUP BY category_id";
        return $this->query($sql);

        // 正确做法：使用SqlHelper方法
        // return $this->field('category_id, COUNT(*) as count, AVG(price) as avg_price')
        //             ->group('category_id')
        //             ->select();
    }
}
</example>