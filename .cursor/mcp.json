{"mcpServers": {"playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest", "--vision"]}, "mysql-mcp-server": {"command": "npx", "args": ["-y", "@f4ww4z/mcp-mysql-server"], "env": {"MYSQL_HOST": "localhost", "MYSQL_USER": "root", "MYSQL_PASSWORD": "123456", "MYSQL_DATABASE": "gxs_byshun_com"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/code/work/shenghui"]}}}