<template>
  <div :class="classObj" class="vue-admin-beautiful-wrapper" @click="wrapperClick">
    <!-- 横向布局 -->
    <div
      v-if="'horizontal' === layout"
      :class="{
        fixed: header === 'fixed',
        'no-tags-bar': !showTagsBar,
      }"
      class="layout-container-horizontal"
    >
      <div :class="header === 'fixed' ? 'fixed-header' : ''" class="layout-header">
        <top-bar></top-bar>
        <div v-if="showTagsBar" :class="{ 'tag-bar-horizontal': showTagsBar }">
          <div class="vab-main">
            <tags-bar></tags-bar>
          </div>
        </div>
      </div>
      <div class="vab-main main-padding">
        <app-main></app-main>
      </div>
    </div>
    <!-- 纵向布局 -->
    <div
      v-else-if="'vertical' === layout"
      :class="{
        fixed: header === 'fixed',
        'no-tags-bar': !showTagsBar,
      }"
      class="layout-container-vertical"
    >
      <div v-if="device === 'mobile' && !collapse" class="mask" @click="handleFoldSideBar"></div>
      <side-bar></side-bar>
      <div :class="collapse ? 'is-collapse-main' : ''" class="vab-main">
        <div :class="header === 'fixed' ? 'fixed-header' : ''" class="layout-header">
          <nav-bar></nav-bar>
          <tags-bar v-if="showTagsBar" />
        </div>
        <app-main></app-main>
      </div>
    </div>
    <!--画廊布局 -->
    <div
      v-else-if="'gallery' === layout"
      :class="{
        fixed: header === 'fixed',
        'no-tags-bar': !showTagsBar,
      }"
      class="layout-container-gallery"
    >
      <gallery-bar></gallery-bar>
      <div :class="collapse ? 'is-collapse-main' : ''" class="vab-main">
        <!--      <div class="vab-main is-collapse-main">-->
        <div :class="header === 'fixed' ? 'fixed-header' : ''" class="layout-header">
          <nav-bar></nav-bar>
          <tags-bar v-if="showTagsBar" />
        </div>
        <app-main></app-main>
      </div>
    </div>
    <!--综合布局 -->
    <div
      v-else-if="'comprehensive' === layout"
      :class="{
        fixed: header === 'fixed',
        'no-tags-bar': !showTagsBar,
      }"
      class="layout-container-comprehensive"
    >
      <comprehensive-bar></comprehensive-bar>
      <div :class="collapse ? 'is-collapse-main' : ''" class="vab-main">
        <div :class="header === 'fixed' ? 'fixed-header' : ''" class="layout-header">
          <nav-bar layout="comprehensive"></nav-bar>
          <tags-bar v-if="showTagsBar" />
        </div>
        <app-main></app-main>
      </div>
    </div>
    <!--常规布局 -->
    <div
      v-else-if="'common' === layout"
      :class="{
        fixed: header === 'fixed',
        'no-tags-bar': !showTagsBar,
      }"
      class="layout-container-common"
    >
      <div :class="header === 'fixed' ? 'fixed-header' : ''" class="layout-header">
        <top-bar layout="common"></top-bar>
        <div v-if="showTagsBar">
          <side-bar layout="common"></side-bar>
          <div :class="collapse ? 'is-collapse-main' : ''" class="vab-main">
            <tags-bar layout="common"></tags-bar>
          </div>
        </div>
      </div>
      <div :class="collapse ? 'is-collapse-main' : ''" class="vab-main main-padding">
        <app-main></app-main>
      </div>
    </div>
    <el-backtop></el-backtop>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import { tokenName } from "@/config/settings";
import { getBasicSetup } from "@/api/System";

export default {
  name: "Layout",
  data() {
    return {
      oldLayout: "",
      width: 0,
      playAudio: true,
    };
  },
  computed: {
    ...mapGetters({
      layout: "settings/layout",
      showTagsBar: "settings/showTagsBar",
      collapse: "settings/collapse",
      header: "settings/header",
      device: "settings/device",
    }),
    classObj() {
      return {
        mobile: this.device === "mobile",
      };
    },
  },
  beforeMount() {
    window.addEventListener("resize", this.handleLayouts);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleLayouts);
  },
  mounted() {
    this.oldLayout = this.layout;
    this.handleLayouts();

    this.$nextTick(() => {
      window.addEventListener(
        "storage",
        (e) => {
          if (e.key === tokenName || e.key === null) window.location.reload();
          if (e.key === tokenName && e.value === null) window.location.reload();
        },
        false
      );
    });
    // 获取详情设置详情
    this.getBasicSetup();
    // 如果有出库单权限或者有新订单权限加语音提示
    if (this.systemType === 1) {
      if (this.$accessCheck(this.$Access.newOrderList) || this.$accessCheck(this.$Access.InventoryOut)) {
        const message = "听不到订单提示语音，请进行以下操作";
        const title =
          "1，请检查电脑音响是否开启，音量是否设置为静音；<br>2，请检查【设置-语音配置】是否开启订单提醒！<p style='color: #E6A23C'>确认以上操作后，下一笔新订单即可听到语音播报</p>";
        this.$baseNotify(title, message, "warning", "bottom-right", 5000, true);
      }
    }
  },
  methods: {
    ...mapActions({
      handleOpenSideBar: "settings/openSideBar",
      handleFoldSideBar: "settings/foldSideBar",
      handleChangeLayout: "settings/changeLayout",
      handleToggleDevice: "settings/toggleDevice",
      changePrintTag: "MUser/changePrintTag",
      changeCostPriceTimes: "MUser/changeCostPriceTimes",
      changeSalePriceTimes: "MUser/changeSalePriceTimes",
      changeMemberPriceTimes: "MUser/changeMemberPriceTimes",
      changeEnableLocationManagement: "MUser/changeEnableLocationManagement",
      changeBaseSetting: "MUser/changeBaseSetting",
    }),
    handleLayouts() {
      const width = document.body.getBoundingClientRect().width;
      if (this.width !== width) {
        const isMobile = width - 1 < 992;
        this.handleChangeLayout(isMobile ? "vertical" : this.oldLayout);
        this.handleToggleDevice(isMobile ? "mobile" : "desktop");
        this.width = width;
      }
    },
    wrapperClick() {
      let audio = document.getElementById("orderTip");
      if (audio !== null && this.playAudio) {
        audio.pause();
        this.playAudio = false;
      }
    },
    //  获取设置详情
    async getBasicSetup() {
      const { data } = await getBasicSetup();
      if (data.basicData) {
        this.changePrintTag(data.basicData.printTag || 4);
        this.changeSalePriceTimes(data.basicData.salePriceTimes || 1.2);
        this.changeCostPriceTimes(data.basicData.costPriceTimes || 1.06);
        this.changeMemberPriceTimes(data.basicData.memberPriceTimes || 1.0);
        this.changeEnableLocationManagement(data.basicData.enableLocationManagement || 4);
        this.changeBaseSetting(data.basicData);
      } else {
        this.changePrintTag(4);
        this.changeCostPriceTimes(1.06);
        this.changeSalePriceTimes(1.2);
        this.changeMemberPriceTimes(1.0);
        this.changeEnableLocationManagement(4);
        this.changeBaseSetting({});
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@mixin fix-header {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: $base-z-index - 2;
  width: 100%;
  overflow: hidden;
  transition: $base-transition;
}

.vue-admin-beautiful-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  color: #2c3e50;

  .layout-header {
    box-shadow: $base-box-shadow;
  }

  .layout-container-horizontal,
  .layout-container-common {
    position: relative;

    &.fixed {
      padding-top: calc(#{$base-top-bar-height} + #{$base-tags-bar-height});
    }

    &.fixed.no-tags-bar {
      padding-top: $base-top-bar-height;
    }

    ::v-deep {
      .vab-main {
        width: 88%;
        margin: auto;
      }

      .fixed-header {
        @include fix-header;
      }

      .tag-bar-horizontal {
        background: $base-color-white;
        box-shadow: $base-box-shadow;
      }

      .nav-bar-container {
        .fold-unfold {
          display: none;
        }
      }

      .main-padding {
        .app-main-container {
          margin-top: $base-padding;
          margin-bottom: $base-padding;
          background: $base-color-white;
        }
      }
    }
  }

  .layout-container-common {
    ::v-deep {
      .top-bar-container {
        .vab-main {
          width: 100%;
          margin: auto $base-padding;
        }
      }
    }
  }

  .layout-container-horizontal {
    ::v-deep {
      .tags-bar-container {
        padding-right: 0;
        padding-left: 0;
      }
    }
  }

  .layout-container-vertical,
  .layout-container-comprehensive,
  .layout-container-gallery,
  .layout-container-common {
    position: relative;

    .mask {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: $base-z-index - 1;
      width: 100%;
      height: 100vh;
      overflow: hidden;
      background: #000;
      opacity: 0.5;
    }

    &.fixed {
      padding-top: calc(#{$base-nav-bar-height} + #{$base-tags-bar-height});
    }

    &.fixed.no-tags-bar {
      padding-top: $base-nav-bar-height;
    }

    .vab-main {
      position: relative;
      width: auto;
      min-height: 100%;
      margin-left: $base-left-menu-width;
      background: #f6f8f9;
      transition: $base-transition;

      ::v-deep {
        .fixed-header {
          @include fix-header;

          left: $base-left-menu-width;
          width: $base-right-content-width;
        }

        .nav-bar-container {
          position: relative;
          box-sizing: border-box;
        }

        .tags-bar-container {
          box-sizing: border-box;
        }

        .app-main-container {
          /*width: calc(100% - #{$base-padding} - #{$base-padding});*/
          width: calc(100% - #{$base-padding} - 14px);
          /*margin: $base-padding auto;*/
          margin: 16px auto;
          /*background: $base-color-white;*/
          border-radius: $base-border-radius;
        }
      }

      &.is-collapse-main {
        margin-left: $base-left-menu-width-min;

        ::v-deep {
          .fixed-header {
            left: $base-left-menu-width-min;
            width: calc(100% - 65px);
          }
        }
      }
    }
  }

  /* 手机端开始 */
  &.mobile {
    ::v-deep {
      .el-pager,
      .el-pagination__jump {
        display: none;
      }

      .layout-container-vertical {
        .el-scrollbar.side-bar-container.is-collapse {
          width: 0;
        }

        .vab-main {
          width: 100%;
          margin-left: 0;
        }
      }

      .vab-main {
        .fixed-header {
          left: 0 !important;
          width: 100% !important;
        }
      }
    }
  }

  /* 手机端结束 */
}
</style>
