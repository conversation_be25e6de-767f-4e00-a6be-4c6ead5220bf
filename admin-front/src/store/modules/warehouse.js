import { getAllWarehouse } from "@/api/Stock";

// 仓库管理模块
const state = {
  warehouseList: [], // 仓库列表
  loading: false, // 加载状态
};

const mutations = {
  SET_WAREHOUSE_LIST(state, list) {
    state.warehouseList = list;
  },
  SET_LOADING(state, status) {
    state.loading = status;
  },
};

const actions = {
  // 获取仓库列表
  async getWarehouseList({ commit, state }) {
    if (state.loading) return;

    try {
      commit("SET_LOADING", true);
      const { data } = await getAllWarehouse({
        page: 1,
        pageSize: 999,
        enableStatus: 5,
      });
      commit("SET_WAREHOUSE_LIST", data || []);
    } catch (error) {
      console.error("获取仓库列表失败:", error);
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
};

const getters = {
  // 获取启用状态的仓库列表
  enabledWarehouses: (state) => {
    return state.warehouseList.filter((warehouse) => warehouse.enableStatus === 5);
  },
  warehouseList: (state) => state.warehouseList,
  // 获取仓库名称映射
  warehouseNameMap: (state) => {
    return state.warehouseList.reduce((acc, warehouse) => {
      acc[warehouse.id] = warehouse.warehouseName;
      return acc;
    }, {});
  },
  // 获取单个仓库名称
  getWarehouseName: (state) => (warehouseId) => {
    const warehouse = state.warehouseList.find((w) => w.id == warehouseId);
    return warehouse ? warehouse.warehouseName : `仓库${warehouseId}`;
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
