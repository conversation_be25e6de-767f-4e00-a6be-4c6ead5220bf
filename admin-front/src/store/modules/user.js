/**

 * @description 登录、获取用户信息、退出登录、清除accessToken逻辑，不建议修改
 */
import Vue from "vue";
import { getUserInfo, login, logout, socialLogin } from "@/api/user";
import {
  getEnToken,
  getAccessToken,
  getSupplierToken,
  removeAccessToken,
  setEnToken,
  setAccessToken,
  setSupplierToken,
} from "@/utils/accessToken";
import { resetRouter } from "@/router";
import { title, tokenName } from "@/config/settings";

const state = {
  accessToken: getAccessToken(),
  enToken: getEnToken(),
  avatar: "",
  supplierToken: getSupplierToken(),
};
const getters = {
  accessToken: (state) => state.accessToken,
  enToken: (state) => state.enToken,
  username: (state) => state.username,
  avatar: (state) => state.avatar,
  supplierToken: (state) => state.supplierToken,
};
const mutations = {
  /**

   * @description 设置accessToken
   * @param {*} state
   * @param {*} accessToken
   */
  setAccessToken(state, accessToken) {
    state.accessToken = accessToken;
    setAccessToken(accessToken);
  },
  /**
   * @description 设置企业Token
   * @param {*} state
   * @param Token
   */
  setEnToken(state, Token) {
    state.enToken = Token;
    setEnToken(Token);
  },
  /**

   * @description 设置用户名
   * @param {*} state
   * @param {*} username
   */
  setUsername(state, username) {
    state.username = username;
  },
  /**

   * @description 设置头像
   * @param {*} state
   * @param {*} avatar
   */
  setAvatar(state, avatar) {
    state.avatar = avatar;
  },
  /**
   * @description 设置供应商Token
   * @param state
   * @param Token
   */
  setSupplierToken(state, Token) {
    state.supplierToken = Token;
    setSupplierToken(Token);
  },
};
const actions = {
  /**

   * @description 登录拦截放行时，设置虚拟角色
   * @param {*} { commit, dispatch }
   */
  setVirtualRoles({ commit, dispatch }) {
    dispatch("acl/setFull", true, { root: true });
    commit("setAvatar", "https://i.gtimg.cn/club/item/face/img/2/15922_100.gif");
    commit("setUsername", "admin(未开启登录拦截)");
  },
  /**

   * @description 登录
   * @param {*} { commit }
   * @param token
   */
  async login({ commit }, token) {
    // const { data } = await login(userInfo);
    // const accessToken = data[tokenName];
    const accessToken = token;
    if (accessToken) {
      commit("setAccessToken", accessToken);
      const hour = new Date().getHours();
      const thisTime =
        hour < 8 ? "早上好" : hour <= 11 ? "上午好" : hour <= 13 ? "中午好" : hour < 18 ? "下午好" : "晚上好";
      Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`);
    } else {
      Vue.prototype.$baseMessage(`登录接口异常，未正确返回${tokenName}...`, "error");
    }
  },
  /**

   * @description 第三方登录
   * @param {*} {}
   * @param {*} tokenData
   */
  async socialLogin({}, tokenData) {
    const { data } = await socialLogin(tokenData);
    const accessToken = data[tokenName];
    if (accessToken) {
      const hour = new Date().getHours();
      const thisTime =
        hour < 8 ? "早上好" : hour <= 11 ? "上午好" : hour <= 13 ? "中午好" : hour < 18 ? "下午好" : "晚上好";
      Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`);
    } else {
      Vue.prototype.$baseMessage(`登录接口异常，未正确返回${tokenName}...`, "error");
    }
  },
  /**

   * @description 获取用户信息接口 这个接口非常非常重要，如果没有明确底层前逻辑禁止修改此方法，错误的修改可能造成整个框架无法正常使用
   * @param {*} { commit, dispatch, state }
   * @returns
   */
  async getUserInfo({ commit, dispatch, state }) {
    // console.log('getUserInfo', commit, dispatch, state)
    // const { data } = await getUserInfo(state.accessToken);
    // if (!data) {
    //   Vue.prototype.$baseMessage("验证失败，请重新登录...", "error");
    //   return false;
    // }
    const data = {
      roles: ["admin"],
      ability: ["READ", "WRITE", "DELETE"],
      username: "admin",
      avatar: "https://i.gtimg.cn/club/item/face/img/8/15918_100.gif",
    };
    let { username, avatar, roles, ability } = data;
    if (username && roles && Array.isArray(roles)) {
      dispatch("acl/setRole", roles, { root: true });
      if (ability && ability.length > 0) dispatch("acl/setAbility", ability, { root: true });
      commit("setUsername", username);
      commit("setAvatar", avatar);
    } else {
      Vue.prototype.$baseMessage("用户信息接口异常", "error");
    }
  },

  /**

   * @description 退出登录
   * @param {*} { dispatch }
   */
  async logout({ dispatch }) {
    // await logout(state.accessToken);
    await dispatch("resetAll");
  },
  /**

   * @description 重置accessToken、roles、ability、router等
   * @param {*} { commit, dispatch }
   */
  async resetAll({ dispatch }) {
    await dispatch("setAccessToken", "");
    await dispatch("setEnToken", "");
    await dispatch("setSupplierToken", "");
    await dispatch("acl/setFull", false, { root: true });
    await dispatch("acl/setRole", [], { root: true });
    await dispatch("acl/setAbility", [], { root: true });
    await resetRouter();
    removeAccessToken();
  },
  /**

   * @description 设置token
   */
  setAccessToken({ commit }, accessToken) {
    commit("setAccessToken", accessToken);
  },
  /**
   * @description 设置企业token
   */
  setEnToken({ commit, dispatch }, Token) {
    commit("setEnToken", Token);
  },
  setSupplierToken({ commit }, Token) {
    commit("setSupplierToken", Token);
  },
};
export default { state, getters, mutations, actions };
