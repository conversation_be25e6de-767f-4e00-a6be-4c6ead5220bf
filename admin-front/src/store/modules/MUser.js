const state = {
  userName: sessionStorage.getItem("userName"),
  //开启保质期设置状态
  shelfLifeSetUp: sessionStorage.getItem("shelfLifeSetUp") || 4,
  // 是否开启库区库位
  enableLocationManagement: sessionStorage.getItem("enableLocationManagement") || 4,
  userCenterId: sessionStorage.getItem("userCenterId"),
  nowExpireTime: sessionStorage.getItem("nowExpireTime"),
  enterpriseSalt:
    sessionStorage.getItem("enterpriseSalt") && sessionStorage.getItem("enterpriseSalt") !== "undefined"
      ? JSON.parse(sessionStorage.getItem("enterpriseSalt") || "[]")
      : [],
  enterprise: JSON.parse(sessionStorage.getItem("enterprise") || "{}"),
  enterpriseScope: sessionStorage.getItem("enterpriseScope") || 4,
  cashierShop: JSON.parse(sessionStorage.getItem("cashierShop") || "{}"),
  audioSet: JSON.parse(sessionStorage.getItem("audioSet") || "[]"),
  salePriceTimes: 1.2, //商品市场价 n倍销售价
  costPriceTimes: 1.06, //商品销售价 n倍成本价
  memberPriceTimes: 1.0, //商品会员价 n倍成本价
  printTag: 4,
  enterpriseLength: 1, // 当前账号企业数量
  // 区分后台类型，1-总后台，2-多门店，3-多商户
  systemType: sessionStorage.getItem("systemType") || 1,
  // 多商户商户数据
  MerchantData: JSON.parse(sessionStorage.getItem("MerchantData") || "[]"),
  storeData: JSON.parse(sessionStorage.getItem("storeData") || "{}"),
  // 基础系统设置
  baseSetting: JSON.parse(sessionStorage.getItem("baseSetting") || "{}"),
};
const getters = {
  baseSetting: (state) => state.baseSetting,
  userName: (state) => state.userName,
  shelfLifeSetUp: (state) => state.shelfLifeSetUp,
  enableLocationManagement: (state) => state.enableLocationManagement,
  userCenterId: (state) => state.userCenterId,
  nowExpireTime: (state) => state.nowExpireTime,
  enterpriseSalt: (state) => state.enterpriseSalt,
  enterprise: (state) => state.enterprise,
  enterpriseScope: (state) => state.enterpriseScope,
  cashierShop: (state) => state.cashierShop,
  audioSet: (state) => state.audioSet,
  printTag: (state) => state.printTag,
  salePriceTimes: (state) => state.salePriceTimes,
  costPriceTimes: (state) => state.costPriceTimes,
  memberPriceTimes: (state) => state.memberPriceTimes,
  // 当前账号企业数量
  enterpriseLength: (state) => state.enterpriseLength,
  // 区分后台类型，1-总后台，2-多门店，3-多商户
  systemType: (state) => parseInt(state.systemType),
  // 多商户商户数据
  MerchantData: (state) => state.MerchantData,
  storeData: (state) => state.storeData,
};

const mutations = {
  // 基础系统设置
  commit_baseSetting(state, baseSetting) {
    state.MerchantData = baseSetting;
    sessionStorage.setItem("baseSetting", JSON.stringify(baseSetting));
  },
  // 多商户商户数据
  commit_MerchantData(state, MerchantData) {
    state.MerchantData = MerchantData;
    sessionStorage.setItem("MerchantData", JSON.stringify(MerchantData));
  },
  // 当前账号企业数量
  commit_enterpriseLength(state, enterpriseLength) {
    state.cashierShop = enterpriseLength;
  },
  // 收银台
  commit_cashierShop(state, cashierShop) {
    state.cashierShop = cashierShop;
    sessionStorage.setItem("cashierShop", JSON.stringify(cashierShop));
  },
  commit_enterpriseScope(state, scope) {
    state.enterpriseScope = scope;
    sessionStorage.setItem("enterpriseScope", scope);
  },
  commit_enterprise(state, enterprise) {
    state.enterprise = enterprise;
    sessionStorage.setItem("enterprise", JSON.stringify(enterprise));
  },
  commit_enterpriseSalt(state, enterpriseSalt) {
    state.enterpriseSalt = enterpriseSalt;
    if (enterpriseSalt) {
      sessionStorage.setItem("enterpriseSalt", JSON.stringify(enterpriseSalt));
    }
  },
  commit_userName(state, userName) {
    // userName
    state.userName = userName;
    sessionStorage.setItem("userName", userName);
  },
  commit_userCenterId(state, id) {
    state.userCenterId = id;
    sessionStorage.setItem("userCenterId", id);
  },
  commit_nowExpireTime(state, time) {
    state.nowExpireTime = time;
    sessionStorage.setItem("nowExpireTime", time);
  },
  //开启保质期设置状态
  commit_shelfLifeSetUp(state, shelfLifeSetUp) {
    state.shelfLifeSetUp = shelfLifeSetUp;
    sessionStorage.setItem("shelfLifeSetUp", shelfLifeSetUp);
  },
  commit_enableLocationManagement(state, enableLocationManagement) {
    state.enableLocationManagement = enableLocationManagement;
    sessionStorage.setItem("enableLocationManagement", enableLocationManagement);
  },
  commit_audioSet(state, audioSet) {
    state.audioSet = audioSet;
    sessionStorage.setItem("audioSet", JSON.stringify(audioSet));
  },
  // 订单打印标示
  commit_printTag(state, printTag) {
    state.printTag = printTag;
  },
  // 商品销售价 n倍成本价
  commit_costPriceTimes(state, costPriceTimes) {
    state.costPriceTimes = costPriceTimes;
  },
  //商品市场价 n倍销售价
  commit_salePriceTimes(state, salePriceTimes) {
    state.salePriceTimes = salePriceTimes;
  },
  //商品会员价 n倍销售价
  commit_memberPriceTimes(state, memberPriceTimes) {
    state.memberPriceTimes = memberPriceTimes;
  },
  // 门店数据
  commit_storeData(state, storeData) {
    // enterpriseSalt
    state.storeData = storeData;
    sessionStorage.setItem("storeData", JSON.stringify(storeData));
  },
  // 区分后台类型
  commit_systemType(state, systemType) {
    // enterpriseSalt
    state.systemType = systemType;
    sessionStorage.setItem("systemType", systemType);
  },
};

const actions = {
  // 基础系统设置
  changeBaseSetting({ commit }, data) {
    commit("commit_baseSetting", data);
  },
  // 当前账号所有的多商户商户数据
  changeMerchantData({ commit }, data) {
    commit("commit_MerchantData", data);
  },
  changeCashierShop({ commit }, data) {
    commit("commit_cashierShop", data);
  },
  changeEnterpriseScope({ commit }, data) {
    commit("commit_enterpriseScope", data);
  },
  changeEnterprise({ commit }, data) {
    commit("commit_enterprise", data);
  },
  changeEnterpriseSalt({ commit }, data) {
    commit("commit_enterpriseSalt", data);
  },
  changeUserName({ commit }, data) {
    commit("commit_userName", data);
  },
  changeUserCenterId({ commit }, data) {
    commit("commit_userCenterId", data);
  },
  changeExpireTime({ commit }, data) {
    commit("commit_nowExpireTime", data);
  },
  //开启保质期设置状态
  changeShelfLifeSetUp({ commit }, data) {
    commit("commit_shelfLifeSetUp", data);
  },
  changeEnableLocationManagement({ commit }, data) {
    commit("commit_enableLocationManagement", data);
  },
  changeAudioSet({ commit }, data) {
    commit("commit_audioSet", data);
  },
  changePrintTag({ commit }, data) {
    commit("commit_printTag", data);
  },
  changeCostPriceTimes({ commit }, data) {
    commit("commit_costPriceTimes", data);
  },
  changeSalePriceTimes({ commit }, data) {
    commit("commit_salePriceTimes", data);
  },
  changeMemberPriceTimes({ commit }, data) {
    commit("commit_memberPriceTimes", data);
  },
  changeEnterpriseLength({ commit }, data) {
    commit("commit_enterpriseLength", data);
  },
  changeSystemType({ commit }, data) {
    commit("commit_systemType", data);
  },
  changeStoreData({ commit }, data) {
    commit("commit_storeData", data);
  },
};

export default {
  state,
  getters,
  mutations,
  actions,
};
