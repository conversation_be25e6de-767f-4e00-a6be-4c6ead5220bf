const state = {
  // admin: !!JSON.parse(sessionStorage.getItem("isSuper")),
  // role: sessionStorage.getItem("nodes")
  //   ? JSON.parse(sessionStorage.getItem("nodes"))
  //   : [],
  admin: false,
  role: false,
  ability: [],
};
const getters = {
  admin: (state) => state.admin,
  role: (state) => state.role,
  ability: (state) => state.ability,
};
const mutations = {
  setFull(state, admin) {
    state.admin = admin;
    // sessionStorage.setItem("isSuper", admin);
  },
  setRole(state, role) {
    state.role = role;
    // sessionStorage.setItem("nodes", JSON.stringify(role));
  },
  setAbility(state, ability) {
    state.ability = ability;
  },
};
const actions = {
  setFull({ commit }, admin) {
    commit("setFull", admin);
  },
  setRole({ commit }, role) {
    commit("setRole", role);
  },
  setAbility({ commit }, ability) {
    commit("setAbility", ability);
  },
};
export default { state, getters, mutations, actions };
