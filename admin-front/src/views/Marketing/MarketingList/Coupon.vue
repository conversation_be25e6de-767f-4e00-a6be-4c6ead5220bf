<template>
  <ContainerQuery>
    <div slot="tip" class="page-tip-div" style="margin-top: 0">
      <i class="el-icon-info"></i>
      温馨提示： 1、优惠券新增审核后，无法删除，请慎重审核！
      2、优惠券可进行上下架操作，新增的审核后默认上架状态，下架后已领取的可以继续使用，未领取的就无法领取了，在商城也不显示！
    </div>
    <div v-if="$accessCheck($Access.CouponListAdd)" slot="left" class="clearfix">
      <el-button type="primary" size="small" @click="$router.push('/Marketing/MarketingList/AddCoupon')">
        新增优惠券
      </el-button>
    </div>
    <div v-if="$accessCheck($Access.CouponListSearch)" slot="more">
      <el-form :inline="true" size="small" style="margin-bottom: 0">
        <el-form-item>
          <el-input
            v-model="search_form.name"
            placeholder="优惠券名称"
            clearable
            style="width: 220px"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <SelectShop
            v-model="search_form.useShop"
            width="150"
            placeholder="选择商铺"
            @clear="clearShop"
            @change="selShop"
          />
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_form.status"
            clearable
            style="width: 150px"
            placeholder="优惠券状态"
            @clear="delSeach('status')"
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in Coupon_status"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_form.applyRange"
            clearable
            style="width: 150px"
            placeholder="商品范围"
            @clear="delSeach('applyRange')"
            @change="pageChange(1)"
          >
            <el-option v-for="item in applyList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_form.grantType"
            clearable
            style="width: 150px"
            placeholder="发放方式"
            @clear="delSeach('grantType')"
            @change="pageChange(1)"
          >
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="search_form.time"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="LocationFrom"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="coupon_list">
      <el-table-column prop="name" label="名称" min-width="180" fixed="left"></el-table-column>
      <el-table-column prop="startTime" label="领取" min-width="260">
        <template slot-scope="scope">
          <p class="order-info-p">
            <span class="order-info-label">有效期:</span>
            <span v-if="scope.row.couponType === 20">领取后30天内有效</span>
            <span v-else>
              {{ $_common.formatDate(scope.row.startTime, "yyyy/MM/dd") }} -
              {{ $_common.formatDate(scope.row.endTime, "yyyy/MM/dd") }}
            </span>
          </p>
          <p class="order-info-p">
            <span class="order-info-label">每人领取:</span>
            {{ scope.row.allowNum }}张
          </p>
        </template>
      </el-table-column>
      <el-table-column prop="grantStartTime" label="发放" min-width="260">
        <template slot-scope="scope">
          <p class="order-info-p">
            <span class="order-info-label">发放日期:</span>
            <span v-if="scope.row.couponType === 20">不限时间</span>
            <span v-else>
              {{ $_common.formatDate(scope.row.grantStartTime, "yyyy/MM/dd") }}
              - {{ $_common.formatDate(scope.row.grantEndTime, "yyyy/MM/dd") }}
            </span>
          </p>
          <p class="order-info-p">
            <span class="order-info-label">商品范围:</span>
            {{
              scope.row.applyRange === 10
                ? "全部商品"
                : scope.row.applyRange === 20
                ? "指定分类"
                : scope.row.applyRange === 30
                ? "指定品牌"
                : scope.row.applyRange === 40
                ? "指定商品"
                : "其他"
            }}
          </p>
          <p class="order-info-p">
            <span class="order-info-label">发放方式:</span>
            {{
              scope.row.grantType === 10
                ? "主动领取"
                : scope.row.grantType === 20
                ? "定向发放"
                : scope.row.grantType === 30
                ? "注册领取"
                : scope.row.grantType === 40
                ? "在线支付赠送"
                : scope.row.grantType === 50
                ? "开卡赠送"
                : ""
            }}
          </p>
        </template>
      </el-table-column>
      <el-table-column v-if="faceValueFlag" prop="reducePrice" label="面值" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.reducePrice) }}
        </template>
      </el-table-column>
      <el-table-column v-if="mutualExclusionFlag" prop="isMutex" label="是否互斥" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.isMutex === 4" class="info-status">否</span>
          <span v-else class="warning-status">是</span>
        </template>
      </el-table-column>
      <el-table-column v-if="thresholdFlag" prop="minPrice" label="使用门槛" min-width="100">
        <template slot-scope="scope">
          {{ Number(scope.row.minPrice) === 0 ? "无门槛" : $_common.formattedNumber(scope.row.minPrice) }}
        </template>
      </el-table-column>
      <el-table-column v-if="quantityFlag" prop="totalNum" label="数量" min-width="160">
        <template slot-scope="scope">
          <p class="order-info-p">
            <span class="order-info-label">合计发放：</span>
            {{ scope.row.totalNum || "0" }}张
          </p>
          <p class="order-info-p">
            <span class="order-info-label">已发放：</span>
            {{ scope.row.receiveNum }}张
          </p>
          <p class="order-info-p">
            <span class="order-info-label">剩余：</span>
            {{ scope.row.couponType === 20 ? "0" : scope.row.usableNum }}张
          </p>
        </template>
      </el-table-column>
      <el-table-column v-if="stateFlag" align="left" label="审核状态" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 1" class="warning-status"> 待审核 </span>
          <span v-else class="success-status">已审核</span>
        </template>
      </el-table-column>
      <el-table-column v-if="stateFlag" align="left" label="启用状态" min-width="120">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="updateEnableStatus($event, scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="220">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.CouponListUpdateAuditStatus)"
            :disabled="scope.row.auditStatus === 2"
            type="text"
            @click="updateAuditStatusMarket(scope.row)"
          >
            审核
          </el-button>
          <el-button
            v-if="$accessCheck($Access.CouponListEdit)"
            :disabled="scope.row.auditStatus === 2 && scope.row.enableStatus === 5"
            type="text"
            @click="$router.push(`/Marketing/MarketingList/EditCoupon/${scope.row.id}`)"
          >
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.CouponListDel)"
            :disabled="scope.row.auditStatus === 2 && scope.row.enableStatus === 5"
            type="text"
            @click="delData(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import SelectShop from "@/component/goods/SelectShop.vue";
import { getAll, searchMarket, CouponUpdateEnableStatus, updateAuditStatusMarket, delMarket } from "@/api/Market";

export default {
  name: "Coupon",
  components: {
    SelectShop,
  },
  data() {
    return {
      coupon_list: [],
      pageSize: 10,
      page: 1,
      total: 0,
      useShopName: "",
      typeList: [
        { value: 10, label: "主动领取" },
        { value: 20, label: "定向发放" },
        { value: 30, label: "邀约注册" },
        { value: 40, label: "注册既享" },
      ],
      Coupon_status: [
        {
          label: "已审核",
          value: 2,
        },
        {
          label: "待审核",
          value: 1,
        },
      ],
      applyList: [
        {
          label: "全部商品",
          value: 10,
        },
        {
          label: "指定分类",
          value: 20,
        },
        {
          label: "指定品牌",
          value: 30,
        },
        {
          label: "指定商品",
          value: 40,
        },
      ],
      search_form: {
        name: "",
        shopName: "", // 店铺名称
        useShop: "", // 店铺名称
        applyRange: "",
        grantType: "",
        start: "", // 时间
        end: "",
        status: "",
      },
      topData: {},
      checkList: ["面值", "是否互斥", "使用门槛", "数量", "审核状态", "启用状态"],
      columns: [
        {
          label: "面值",
        },
        {
          label: "是否互斥",
        },
        {
          label: "使用门槛",
        },
        {
          label: "数量",
        },
        {
          label: "审核状态",
        },
        {
          label: "启用状态",
        },
      ],
      faceValueFlag: true,
      mutualExclusionFlag: true,
      thresholdFlag: true,
      quantityFlag: true,
      stateFlag: true,
      grantFlag: true,
    };
  },
  created() {
    this.getData();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    delSeach(val) {
      if (val === "grantType") {
        this.search_form.grantType = "";
        this.pageChange(1);
      } else if (val === "applyRange") {
        this.search_form.applyRange = "";
        this.pageChange(1);
      } else {
        this.search_form.status = "";
        this.pageChange(1);
      }
    },
    //  获取列表
    async getAll() {
      const { data, pageTotal } = await getAll({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.coupon_list = data;
      this.total = pageTotal;
    },
    //  搜索
    async searchMarket() {
      const { data, pageTotal } = await searchMarket({
        page: this.page,
        pageSize: this.pageSize,
        auditStatus: this.search_form.status,
        grantType: this.search_form.grantType,
        applyRange: this.search_form.applyRange,
        useShop: this.search_form.useShop,
        name: this.search_form.name,
        start: this.search_form.start,
        end: this.search_form.end,
      });

      this.coupon_list = data;
      this.total = pageTotal;
    },
    getData() {
      const isKey = this.$_common.isSerch(this.search_form);
      if (isKey) {
        this.searchMarket();
      } else {
        this.getAll();
      }
    },
    //  审核
    async updateEnableStatus(val, row) {
      try {
        const data = await CouponUpdateEnableStatus({
          id: row.id,
          enableStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        this.getData();
      }
    },
    //  审核
    async updateAuditStatusMarket(row) {
      this.$confirm("确定审核此优惠券吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateAuditStatusMarket({
          id: row.id,
          auditStatus: 2,
        });

        this.getData();
        this.$message({
          type: "success",
          message: "审核成功!",
        });
      });
    },
    //  删除
    async delData(id) {
      this.$confirm("确定删除此优惠券吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delMarket(id);

        this.getData();
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
    clearShop() {
      this.search_form.useShop = "";
      this.pageChange(1);
    },
    selShop(val, row) {
      this.pageChange(1);
    },
    //  时间
    LocationFrom(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    pageChange(page) {
      this.page = page;
      this.getData();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    change() {
      this.faceValueFlag = this.checkList.some((item) => item === "面值");
      this.mutualExclusionFlag = this.checkList.some((item) => item === "是否互斥");
      this.thresholdFlag = this.checkList.some((item) => item === "使用门槛");
      this.quantityFlag = this.checkList.some((item) => item === "数量");
      this.stateFlag = this.checkList.some((item) => item === "审核状态");
      this.grantFlag = this.checkList.some((item) => item === "启用状态");
    },
  },
};
</script>

<style scoped lang="scss">
.num-ul {
  padding-bottom: 10px;
  padding-left: 10px;
}
.num-ul > li {
  padding-right: 10px;
}
.order-info-p {
  height: auto;
  padding-bottom: 0;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
