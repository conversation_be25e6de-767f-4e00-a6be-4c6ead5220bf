<template>
  <Container>
    <div slot="left">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="search_form.keyword"
            placeholder="优惠券名称"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="search_form.time"
            type="daterange"
            range-separator="-"
            start-placeholder="发放开始日期"
            end-placeholder="发放结束日期"
            @change="LocationFrom"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_form.status"
            clearable
            style="width: 150px"
            placeholder="优惠券状态"
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in Coupon_list"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <SelectCustomer v-model="customer_name" @clear="customerClear" @change="customerSel" />
        </el-form-item>
      </el-form>
    </div>
    <ul class="clearfix num-ul">
      <li class="float_left">
        <span class="de_label">发放张数:</span>
        <span class="de_val">
          <i style="color: #f40">{{ total }}张</i>
        </span>
      </li>
      <li class="float_left">
        <span class="de_label">使用张数:</span>
        <span class="de_val">
          <i style="color: #f40">{{ useTotal }}张</i>
        </span>
      </li>
      <li class="float_left">
        <span class="de_label">发放金额:</span>
        <span class="de_val">
          <i style="color: #f40"> {{ $_common.formattedNumber(reducePrice) }}元 </i>
        </span>
      </li>
      <li class="float_left">
        <span class="de_label">使用金额:</span>
        <span class="de_val">
          <i style="color: #f40"> {{ $_common.formattedNumber(useReducePrice) }}元 </i>
        </span>
      </li>
    </ul>
    <el-table :data="tableData" :span-method="objectSpanMethod">
      <el-table-column prop="name" label="名称" min-width="200" fixed="left"></el-table-column>
      <el-table-column prop="customerName" label="领取客户" min-width="100" show-overflow-tooltip></el-table-column>
      <el-table-column v-if="validTimeFlag" prop="startTime" label="有效日期" min-width="180">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.startTime, "yyyy-MM-dd") }}至{{
            $_common.formatDate(scope.row.endTime, "yyyy-MM-dd")
          }}
        </template>
      </el-table-column>
      <el-table-column v-if="faceValueFlag" prop="reducePrice" label="面值" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.reducePrice) }}
        </template>
      </el-table-column>
      <el-table-column v-if="thresholdFlag" prop="minPrice" label="使用门槛" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.minPrice) }}
        </template>
      </el-table-column>
      <el-table-column v-if="issueDateFlag" prop="createTime" label="发放日期" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime, "yyyy-MM-dd") }}
        </template>
      </el-table-column>
      <el-table-column v-if="dateOfServiceFlag" prop="useTime" label="使用日期" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.useTime === 0 ? "暂无" : $_common.formatDate(scope.row.useTime, "yyyy-MM-dd") }}
        </template>
      </el-table-column>
      <el-table-column v-if="stateFlag" prop="isUse" label="状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.isUse === 4" class="success-status"> 已使用 </span>
          <span v-else class="danger-status">未使用</span>
        </template>
      </el-table-column>
      <el-table-column v-if="disbursementFlag" prop="source" label="发放方式" min-width="100">
        <template slot-scope="scope">
          {{
            scope.row.source === 10
              ? "主动领取"
              : scope.row.source === 20
              ? "定向发放 "
              : scope.row.source === 30
              ? "注册领取"
              : scope.row.source === 40
              ? "在线支付赠送"
              : scope.row.source === 50
              ? "开卡赠送"
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column v-if="scopeFlag" prop="useShopName" label="发放范围" min-width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.useShopName.length ? scope.row.useShopName.join("，") : scope.row.useShopName[0] }}
        </template>
      </el-table-column>

      <el-table-column prop="orderNo" min-width="120">
        <template slot="header">
          <span>相关订单</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <!--          {{ scope.row.orderNo || "暂无" }}-->
          <span v-if="scope.row.orderData && scope.row.orderData.length">
            <span v-for="(item, index) in scope.row.orderData" :key="index">
              <span class="click-div" @click="goDetail(item, scope.row)">
                {{ item.no }}
              </span>
              <br />
            </span>
          </span>
          <span v-else>暂无</span>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import SelectCustomer from "@/component/common/SelectCustomer";
import { getUserCoupon } from "@/api/Market";
export default {
  name: "ReleaseRecord",
  components: {
    SelectCustomer,
  },
  data() {
    return {
      spanArr: [],
      pos: 0,
      tableData: [],
      customer_show: false,
      pageSize: 10,
      page: 1,
      total: 0,
      useTotal: 0,
      reducePrice: 0,
      useReducePrice: 0,
      customer_name: "",
      search_form: {
        keyword: "", // 商品名称关键字
        shopName: "", // 店铺名称
        shopId: "", // 店铺名称
        start: "", // 时间
        end: "",
        status: "", // 订单状态
        customerId: "",
      },
      Coupon_list: [
        {
          label: "已使用",
          value: 4,
        },
        {
          label: "未使用",
          value: 5,
        },
      ],
      topData: {},
      checkList: ["有效日期", "面值(元)", "使用门槛", "发放日期", "使用日期", "状态", "发放方式", "发放范围"],
      columns: [
        {
          label: "有效日期",
        },
        {
          label: "面值(元)",
        },
        {
          label: "使用门槛",
        },
        {
          label: "发放日期",
        },
        {
          label: "使用日期",
        },
        {
          label: "状态",
        },
        {
          label: "发放方式",
        },
        {
          label: "发放范围",
        },
      ],
      validTimeFlag: true,
      faceValueFlag: true,
      thresholdFlag: true,
      issueDateFlag: true,
      dateOfServiceFlag: true,
      stateFlag: true,
      disbursementFlag: true,
      scopeFlag: true,
    };
  },
  created() {
    this.getUserCoupon();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getUserCoupon();
  },
  methods: {
    //  列表
    async getUserCoupon() {
      const { data, pageTotal, useTotal, reducePrice, useReducePrice } = await getUserCoupon({
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.search_form.keyword,
        isUse: this.search_form.status,
        customerId: this.search_form.customerId,
        startTime: this.search_form.start,
        endTime: this.search_form.end,
      });
      this.tableData = data;
      this.total = pageTotal;
      this.useTotal = useTotal;
      this.reducePrice = reducePrice;
      this.useReducePrice = useReducePrice;
    },
    // 选择客户
    customerSel(val, list) {
      this.search_form.customerId = list[0].id;
      this.pageChange(1);
    },
    customerClear() {
      this.search_form.customerId = "";
      this.customer_name = "";
      this.pageChange(1);
    },
    //  时间
    LocationFrom(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },

    pageChange(page) {
      this.page = page;
      this.getUserCoupon();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (["采购单号", "供应商"].includes(column.label)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    change() {
      this.validTimeFlag = this.checkList.some((item) => item === "有效日期");
      this.faceValueFlag = this.checkList.some((item) => item === "面值(元)");
      this.thresholdFlag = this.checkList.some((item) => item === "使用门槛");
      this.issueDateFlag = this.checkList.some((item) => item === "发放日期");
      this.dateOfServiceFlag = this.checkList.some((item) => item === "使用日期");
      this.stateFlag = this.checkList.some((item) => item === "状态");
      this.disbursementFlag = this.checkList.some((item) => item === "发放方式");
      this.scopeFlag = this.checkList.some((item) => item === "发放范围");
    },
    goDetail(item, row) {
      this.$router.push({
        path: `/order/manageO/OrderDetails/${row.customerId}/${item.id}`,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.num-ul {
  width: 100%;
  padding: 0 16px;
  line-height: 50px;
  li {
    padding-right: 10px;
  }
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
