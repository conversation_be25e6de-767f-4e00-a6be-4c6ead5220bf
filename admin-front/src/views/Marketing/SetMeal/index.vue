<template>
  <Container>
    <div slot="left">
      <el-input
        v-model="search_form.search"
        placeholder="活动名称"
        size="small"
        clearable
        style="width: 220px; margin-right: 10px"
        @keyup.enter.native="pageChange(1)"
        @clear="pageChange(1)"
      >
        <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
      </el-input>
      <el-button
        v-if="$accessCheck($Access.SetMeal_addGroupGoods)"
        type="primary"
        size="small"
        @click="$router.push('/Marketing/SetMeal/addSetMeal')"
      >
        新增活动
      </el-button>
    </div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="所有活动" name="all"></el-tab-pane>
      <el-tab-pane label="未开始" name="notStarted"></el-tab-pane>
      <el-tab-pane label="进行中" name="conduct"></el-tab-pane>
      <el-tab-pane label="已结束" name="end"></el-tab-pane>
    </el-tabs>
    <el-table :data="setMeal_list">
      <el-table-column prop="title" label="活动名称" show-overflow-tooltip min-width="140"></el-table-column>
      <el-table-column prop="materielNum" label="商品数量" min-width="100"></el-table-column>
      <el-table-column prop="subOrderNum" label="订单数量" min-width="100"></el-table-column>
      <el-table-column prop="subOrderPayMoney" label="订单实付金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.subOrderPayMoney) }}
        </template>
      </el-table-column>
      <el-table-column label="活动有效时间" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.isExpire === 5">
            {{ $_common.formatDate(scope.row.startTime) }}至{{ $_common.formatDate(scope.row.endTime) }}
          </span>
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column prop="enableStatus" label="活动状态" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="enableChange($event, scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column v-if="$accessCheck($Access.SetMeal_editGroupGoods)" prop="address" label="操作" min-width="120">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.SetMeal_editGroupGoods)"
            type="text"
            @click="$router.push(`./editSetMeal/${scope.row.id}`)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { getAllComBinPackage, enableComBinPackage } from "@/api/Market";
export default {
  name: "Index",
  data() {
    return {
      search_form: {
        search: "",
      },
      activeName: "all",
      setMeal_list: [],
      total: 0,
      page: 1,
      pageSize: 10,
      state: 0,
    };
  },
  created() {
    this.getAllComBinPackage();
  },
  methods: {
    handleClick(tab, event) {
      if (this.activeName === "notStarted") {
        this.state = 1;
      } else if (this.activeName === "conduct") {
        this.state = 2;
      } else if (this.activeName === "end") {
        this.state = 3;
      } else {
        this.state = 0;
      }
      this.getAllComBinPackage();
    },
    pageChange(val) {
      this.page = val;
      this.getAllComBinPackage();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
    },
    async getAllComBinPackage() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.search_form.search,
      };
      if (this.state) {
        params.state = this.state;
      }
      const { data, pageTotal } = await getAllComBinPackage(params);
      this.setMeal_list = data;
      this.total = pageTotal;
    },
    async enableChange(val, row) {
      try {
        const data = await enableComBinPackage(row.id, {
          enableStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        this.getAllComBinPackage();
      }
    },
  },
};
</script>

<style scoped></style>
