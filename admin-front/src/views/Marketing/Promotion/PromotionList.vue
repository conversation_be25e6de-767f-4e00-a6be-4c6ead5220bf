<template>
  <ContainerQuery>
    <div slot="left">
      <el-button
        v-if="$accessCheck($Access.ActivityGoodsAdd)"
        type="primary"
        size="small"
        @click="$router.push('/Marketing/Promotion/AddPromotion')"
      >
        新增促销
      </el-button>
    </div>
    <div v-if="$accessCheck($Access.ActivityGoodsSearch)" slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="search_form.title"
            placeholder="活动标题"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <SelectShop
            v-model="search_form.useShop"
            width="150"
            :clearable="true"
            placeholder="选择商铺"
            @clear="clearShop"
            @change="selShop"
          />
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="search_form.time"
            type="daterange"
            range-separator="-"
            start-placeholder="活动开始日期"
            end-placeholder="活动结束日期"
            @change="LocationFrom"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_form.isEnd"
            clearable
            style="width: 150px"
            placeholder="活动状态"
            @clear="delType('isEnd')"
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in ActivityStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_form.enableStatus"
            style="width: 150px"
            clearable
            placeholder="上架/下架"
            @change="pageChange(1)"
          >
            <el-option v-for="item in activeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="activity_list">
      <el-table-column
        prop="title"
        label="活动标题"
        fixed="left"
        show-overflow-tooltip
        min-width="160"
      ></el-table-column>
      <el-table-column v-if="promotionTypeFlag" prop="activityType" label="促销类型" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.activityType === 10 ? "商品特价" : "商品秒杀" }}
        </template>
      </el-table-column>
      <el-table-column v-if="creationTimeFlag" prop="createTime" label="创建时间" min-width="150">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column v-if="commodityFlag" prop="useShopName" label="商铺名称" show-overflow-tooltip min-width="180">
        <template slot-scope="scope">
          {{ scope.row.useShopName.join(",") }}
        </template>
      </el-table-column>
      <el-table-column v-if="clientTypeFlag" prop="customerRange" label="客户类型" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.customerRange === 5 ? "全部客户" : "部分客户" }}
        </template>
      </el-table-column>
      <el-table-column v-if="scopeFlag" prop="categoryName" label="活动商品" min-width="80">
        <template slot-scope="scope">
          <span class="click-div" @click="LookGoods(scope.row.id)">查看</span>
        </template>
      </el-table-column>
      <el-table-column v-if="activityTimeFlag" prop="startTime" label="活动时间" min-width="200">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.startTime, "yyyy-MM-dd") }}至{{
            $_common.formatDate(scope.row.endTime, "yyyy-MM-dd")
          }}
        </template>
      </el-table-column>
      <el-table-column v-if="standUpAndDownFlag" prop="enableStatus" label="活动状态" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.ActivityGoodsUpdateEnableStatus)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="上架"
            inactive-text="下架"
            @change="activityStatus($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 上架 </span>
            <span v-else class="info-status">下架</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="internalCommunicationFlag"
        prop="remark"
        label="内部沟通"
        min-width="100"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column fixed="right" width="240">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.ActivityGoodsUpdateAuditStatus)"
            :disabled="parseInt(scope.row.auditStatus) === 2"
            type="text"
            @click="updateEnableStatus(scope.row.id)"
          >
            审核
          </el-button>
          <el-button
            v-if="$accessCheck($Access.ActivityGoodsEdit)"
            type="text"
            @click="$router.push(`/Marketing/Promotion/editPromotion/${scope.row.id}`)"
          >
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.ActivityGoodsDel)"
            :disabled="parseInt(scope.row.auditStatus) === 2 && parseInt(scope.row.enableStatus) === 5"
            type="text"
            @click="delData(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :close-on-press-escape="false" :close-on-click-modal="false" title="活动商品" :visible.sync="isShow">
      <el-table :data="activityGoods">
        <el-table-column show-overflow-tooltip prop="goodsName" label="商品名称" min-width="140"></el-table-column>
        <el-table-column prop="goodsCode" min-width="140" label="商品编码"></el-table-column>
        <el-table-column align="center" prop="price" label="促销价格">
          <template slot-scope="scope">
            <span style="color: #ff4040">
              {{ $_common.formattedNumber(scope.row.price) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column min-width="140" prop="unitName" label="规格"></el-table-column>
        <el-table-column min-width="100" prop="isMutex" align="center" label="是否互斥">
          <template slot-scope="scope">
            <span v-if="scope.row.isMutex === 4" class="info-status">否</span>
            <span class="danger-status">是</span>
          </template>
        </el-table-column>
        <el-table-column min-width="100" prop="surplusNum" label="活动剩余数"></el-table-column>
        <el-table-column min-width="100" prop="limitNum" label="限购数量"></el-table-column>
      </el-table>
    </el-dialog>

    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import SelectShop from "@/component/goods/SelectShop.vue";
import {
  activityStatus,
  updateActivityStatus,
  getInfoActivity,
  delActivity,
  getAllActivity,
  searchActivity,
} from "@/api/Market";
import { GoodsUpdateEnableStatus } from "@/api/goods";

export default {
  name: "PromotionList",
  components: {
    SelectShop,
  },
  data() {
    return {
      activity_list: [],
      isShow: false,
      pageSize: 10,
      page: 1,
      total: 0,
      activityGoods: [],
      activeList: [
        { value: 4, label: "下架" },
        { value: 5, label: "上架" },
      ],
      ActivityStatus: [
        { value: 4, label: "已结束" },
        { value: 5, label: "未结束" },
      ],
      status_list: [
        {
          label: "商品特价",
          value: 10,
        },
        {
          label: "商品秒杀",
          value: 20,
        },
      ],
      search_form: {
        title: "",
        activityType: "",
        enableStatus: "",
        start: "",
        end: "",
        useShop: "",
        isEnd: "",
      },
      topData: {},
      checkList: ["促销类型", "创建时间", "商铺名称", "客户类型", "商品范围", "活动时间", "上架/下架", "内部沟通"],
      columns: [
        {
          label: "促销类型",
        },
        {
          label: "创建时间",
        },
        {
          label: "商铺名称",
        },
        {
          label: "客户类型",
        },
        {
          label: "商品范围",
        },
        {
          label: "活动时间",
        },
        {
          label: "上架/下架",
        },
        {
          label: "内部沟通",
        },
      ],
      promotionTypeFlag: true,
      creationTimeFlag: true,
      commodityFlag: true,
      clientTypeFlag: true,
      scopeFlag: true,
      activityTimeFlag: true,
      standUpAndDownFlag: true,
      internalCommunicationFlag: true,
    };
  },
  created() {
    this.getData();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    // 是否上下架
    async activityStatus(val, row) {
      try {
        const data = await activityStatus({
          id: row.id,
          enableStatus: val,
          activityType: row.activityType,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        this.getData();
      }
    },
    delType(val) {
      if (val === "isEnd") {
        this.search_form.isEnd = "";
        this.pageChange(1);
      } else {
        this.search_form.activityType = "";
        this.pageChange(1);
      }
    },
    //  审核
    updateEnableStatus(id) {
      this.$confirm("确定审核此商品促销吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateActivityStatus({
          id: id,
          auditStatus: 2,
        });

        this.$message({
          type: "success",
          message: "审核成功!",
        });
        this.getAllActivity();
      });
    },
    async LookGoods(id) {
      this.isShow = true;
      const { data } = await getInfoActivity(id);

      this.activityGoods = data.activityGoods;
    },
    //  删除
    delData(id) {
      this.$confirm("确定删除此商品促销吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delActivity(id);

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.getAllActivity();
      });
    },
    //  获取列表
    async getAllActivity() {
      const { data, pageTotal } = await getAllActivity({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.activity_list = data;
      this.total = pageTotal;
    },
    //  搜索
    async searchActivity() {
      const { data, pageTotal } = await searchActivity({
        page: this.page,
        pageSize: this.pageSize,
        title: this.search_form.title,
        activityType: this.search_form.activityType,
        enableStatus: this.search_form.enableStatus,
        start: this.search_form.start,
        end: this.search_form.end,
        useShop: this.search_form.useShop,
        isEnd: this.search_form.isEnd,
      });

      this.activity_list = data;
      this.total = pageTotal;
    },
    //  判断
    getData() {
      const isKey = this.$_common.isSerch(this.search_form);
      if (isKey) {
        this.searchActivity();
      } else {
        this.getAllActivity();
      }
    },
    clearShop() {
      this.search_form.useShop = "";
      this.pageChange(1);
    },
    selShop(val, row) {
      this.pageChange(1);
    },
    //  时间
    LocationFrom(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    pageChange(page) {
      this.page = page;
      this.getData();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    change() {
      this.promotionTypeFlag = this.checkList.some((item) => item === "促销类型");
      this.creationTimeFlag = this.checkList.some((item) => item === "创建时间");
      this.commodityFlag = this.checkList.some((item) => item === "商铺名称");
      this.clientTypeFlag = this.checkList.some((item) => item === "客户类型");
      this.scopeFlag = this.checkList.some((item) => item === "商品范围");
      this.activityTimeFlag = this.checkList.some((item) => item === "活动时间");
      this.standUpAndDownFlag = this.checkList.some((item) => item === "上架/下架");
      this.internalCommunicationFlag = this.checkList.some((item) => item === "内部沟通");
    },
  },
};
</script>

<style scoped lang="scss">
.num-ul {
  padding-bottom: 10px;
  padding-left: 10px;
}
.num-ul > li {
  padding-right: 10px;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
