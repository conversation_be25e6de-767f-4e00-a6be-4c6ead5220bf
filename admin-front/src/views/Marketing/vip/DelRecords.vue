<template>
  <Container>
    <el-form slot="left" :inline="true" size="small">
      <el-form-item>
        <el-input
          v-model="keyword"
          size="small"
          style="width: 260px"
          clearable
          placeholder="会员卡名称"
          @clear="searchData"
        >
          <el-button slot="append" @click="searchData">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="cardenabled" placeholder="会员状态">
          <el-option v-for="item in cardStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table border :data="card_list" size="small" @row-dblclick="goDetail">
      <el-table-column label="删除客户" min-width="200" prop="id" align="center"></el-table-column>
      <el-table-column label="会员卡名称" min-width="200" prop="name" align="center"></el-table-column>
      <el-table-column prop="createTime" label="删除时间" align="center" min-width="200">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="price" label="价格" align="center" min-width="200">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.price) }}
        </template>
      </el-table-column>
      <el-table-column prop="discount" label="折扣" align="center" min-width="200">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.discount) }}
        </template>
      </el-table-column>
      <el-table-column prop="enableStatus" label="状态" align="center" min-width="200">
        <template slot-scope="scope">
          {{ scope.row.enableStatus === 5 ? "启用" : "禁用" }}
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { getAllVipCard } from "@/api/Market";
export default {
  components: {
    FooterPage,
  },
  data() {
    return {
      edit_brand_id: 0,
      total: 0,
      page: 1,
      pageSize: 10,
      keyword: "",
      card_list: [],
      cardenabled: "",
      cardStatus: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "5",
          label: "启用",
        },
        {
          value: "4",
          label: "停用",
        },
      ],
    };
  },
  created() {
    // this.getAllVipCard()
  },

  methods: {
    async getAllVipCard() {
      const data = await getAllVipCard({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.card_list = data.data;
      this.total = data.pageTotal;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllVipCard();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    searchData() {
      this.pageChange(1);
    },
  },
};
</script>
<style scoped></style>
