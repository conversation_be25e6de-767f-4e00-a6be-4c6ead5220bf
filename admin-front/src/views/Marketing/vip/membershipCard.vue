<template>
  <ContainerQuery>
    <div slot="tip" class="page-tip-div" style="margin-top: 0">
      <i class="el-icon-info"></i>
      温馨提示： 1、会员卡新增后，无法删除，新增时请慎重！ 2、会员卡只有在禁用下才可以进行编辑操作！
    </div>
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <el-input
          v-model="keyword"
          clearable
          placeholder="会员卡名称"
          style="width: 220px"
          @keyup.enter.native="searchData"
          @clear="searchData"
        >
          <el-button slot="append" @click="searchData">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="cardenabled" clearable placeholder="会员状态" style="width: 150px" @change="searchData">
          <el-option v-for="item in cardStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div v-if="$accessCheck($Access.MembershipCardAddVipCard)" slot="left">
      <el-button size="small" type="primary" @click="$router.push(`/Marketing/vip/AddCard`)"> 创建会员卡 </el-button>
    </div>
    <el-table :data="card_list">
      <el-table-column label="名称" min-width="160" prop="name"></el-table-column>
      <el-table-column v-if="creationTimeFlag" prop="createTime" label="创建时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column v-if="validityFlag" label="有效期/月" min-width="160" prop="effectiveDate">
        <template slot-scope="scope">
          {{ scope.row.effectiveMonth === 0 ? "永久有效" : scope.row.effectiveMonth }}
        </template>
      </el-table-column>
      <el-table-column v-if="priceFlag" prop="price" label="价格" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.price) }}
        </template>
      </el-table-column>
      <el-table-column v-if="discountFlag" prop="discount" label="折扣" min-width="100">
        <template slot-scope="scope"> {{ $_common.formatNub(scope.row.discount, 1) }}折 </template>
      </el-table-column>
      <el-table-column v-if="getTheNumberFlag" prop="num" label="领取数量" min-width="100">
        <template slot-scope="scope">
          <div
            class="sku-btn"
            @click="$router.push(`/Marketing/vip/CollectionRecords?vipId=${scope.row.id}&&vipName=${scope.row.name}`)"
          >
            {{ scope.row.num }}张
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="remainingQuantityFlag" prop="totalNum" label="剩余数量" min-width="100">
        <template slot-scope="scope">
          {{ $NP.minus(scope.row.totalNum, scope.row.num) }}
        </template>
      </el-table-column>
      <el-table-column v-if="stateFlag" prop="enableStatus" label="状态" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.MembershipCardUpdateVipCardStatus)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="updateCard($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column min-width="160" align="center" fixed="right">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.MembershipCardEditVipCard)"
            :disabled="scope.row.enableStatus === 5"
            type="text"
            @click="$router.push(`/Marketing/vip/EditCard/${scope.row.id}`)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { getAllVipCard, updateVipCardStatus, delVipCard } from "@/api/Market";
export default {
  name: "MembershipCard",
  components: {
    FooterPage,
  },
  data() {
    return {
      edit_brand_id: 0,
      total: 0,
      page: 1,
      pageSize: 10,
      keyword: "",
      card_list: [],
      cardenabled: "",
      cardStatus: [
        {
          value: "5",
          label: "启用",
        },
        {
          value: "4",
          label: "停用",
        },
      ],
      checkList: ["创建时间", "有效期/月", "价格", "折扣/折", "领取数量", "剩余数量", "状态"],
      columns: [
        {
          label: "创建时间",
        },
        {
          label: "有效期/月",
        },
        {
          label: "价格",
        },
        {
          label: "折扣/折",
        },
        {
          label: "领取数量",
        },
        {
          label: "剩余数量",
        },
        {
          label: "状态",
        },
      ],
      creationTimeFlag: true,
      validityFlag: true,
      priceFlag: true,
      discountFlag: true,
      getTheNumberFlag: true,
      remainingQuantityFlag: true,
      stateFlag: true,
    };
  },
  created() {
    this.getAllVipCard();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllVipCard();
  },
  methods: {
    async getAllVipCard() {
      const data = await getAllVipCard({
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        enableStatus: this.cardenabled,
      });

      this.card_list = data.data;
      this.total = data.pageTotal;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllVipCard();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    searchData() {
      this.pageChange(1);
    },
    async updateCard(val, row) {
      try {
        const data = await updateVipCardStatus({
          id: row.id,
          enableStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        await this.getAllVipCard();
      }
    },
    deleteCard(id) {
      this.$confirm("会员卡删除请谨慎，一旦删除购买领取的用户将不再显示和使用该会员卡", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delVipCard(id);

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        await this.getAllVipCard();
      });
    },
    change() {
      this.creationTimeFlag = this.checkList.some((item) => item === "创建时间");
      this.validityFlag = this.checkList.some((item) => item === "有效期/月");
      this.priceFlag = this.checkList.some((item) => item === "价格");
      this.discountFlag = this.checkList.some((item) => item === "折扣/折");
      this.getTheNumberFlag = this.checkList.some((item) => item === "领取数量");
      this.remainingQuantityFlag = this.checkList.some((item) => item === "剩余数量");
      this.stateFlag = this.checkList.some((item) => item === "状态");
    },
  },
};
</script>
<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
.sku-btn {
  width: 71px;
  height: 22px;
  line-height: 22px;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #cad0d7;
  text-align: center;
  font-size: 12px;
  cursor: pointer;
}
</style>
