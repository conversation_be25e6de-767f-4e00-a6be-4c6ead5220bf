<template>
  <Container>
    <el-form slot="left" :inline="true" size="small">
      <el-form-item>
        <el-input
          v-model="keyword"
          style="width: 220px"
          clearable
          placeholder="会员卡名称"
          @keyup.enter.native="searchData"
          @clear="searchData"
        >
          <el-button slot="append" @click="searchData">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="cardenabled" style="width: 150px" placeholder="会员状态" clearable @change="searchData">
          <el-option v-for="item in cardStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table :data="card_list">
      <el-table-column label="领取客户" min-width="160" prop="customerName"></el-table-column>
      <el-table-column label="会员卡名称" min-width="160" prop="name"></el-table-column>
      <el-table-column label="是否失效" min-width="100" prop="enableStatus">
        <template slot-scope="scope">
          <span v-if="scope.row.enableStatus === 5" class="success-status"> 未失效 </span>
          <span v-else class="danger-status">已失效</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="领取时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="expireTime" label="失效时间" min-width="160">
        <template slot-scope="scope">
          {{ scope.row.effectiveDate === 5 ? "永久有效" : $_common.formatDate(scope.row.expireTime) }}
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { receiveRecord } from "@/api/Market";
export default {
  name: "MembershipCard",
  components: {
    FooterPage,
  },
  data() {
    return {
      edit_brand_id: 0,
      total: 0,
      page: 1,
      pageSize: 10,
      keyword: "",
      card_list: [],
      cardenabled: "",
      cardStatus: [
        {
          value: "5",
          label: "未过期",
        },
        {
          value: "4",
          label: "已过期",
        },
      ],
    };
  },
  created() {
    this.receiveRecord();
  },
  activated() {
    if (this.$_isInit()) return;
    this.receiveRecord();
  },
  methods: {
    async receiveRecord() {
      const data = await receiveRecord({
        page: this.page,
        pageSize: this.pageSize,
        outDate: this.cardenabled,
        keyword: this.keyword,
      });

      this.card_list = data.data;
      this.total = data.pageTotal;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.receiveRecord();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    searchData() {
      this.pageChange(1);
    },
  },
};
</script>
<style scoped></style>
