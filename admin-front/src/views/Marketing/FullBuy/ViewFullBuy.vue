<template>
  <ContainerTit>
    <div slot="headr">
      <el-button @click="$router.go(-1)">返回</el-button>
      <el-button
        v-if="$accessCheck($Access.FullBuy_auditFullBuy) && [0, 1, 4].includes(form.auditStatus)"
        type="primary"
        @click="handleAudit"
        >审核</el-button
      >
    </div>

    <el-form ref="form" :model="form" label-width="120px">
      <div class="detail-tab-item">
        <div class="detail-tab-title">活动信息</div>
        <div class="detail-tab-main">
          <el-form-item label="活动名称">
            <span>{{ form.name }}</span>
          </el-form-item>

          <el-form-item label="活动时间">
            <span>{{ $_common.formatDate(form.time[0]) }} 至 {{ $_common.formatDate(form.time[1]) }}</span>
          </el-form-item>

          <el-form-item label="使用范围">
            <el-tag v-for="shopName in form.useShopName" :key="shopName" style="margin-right: 10px">
              {{ shopName }}
            </el-tag>
          </el-form-item>

          <el-form-item label="商品范围">
            <span>{{ getApplyRangeText(form.applyRange) }}</span>
            <template v-if="form.applyRange === 20">
              <div class="selected-tags">
                <el-tag v-for="item in form.categoryList" :key="item.id">
                  {{ item.title }}
                </el-tag>
              </div>
            </template>
            <template v-if="form.applyRange === 30">
              <div class="selected-tags">
                <el-tag v-for="item in form.brandList" :key="item.id">
                  {{ item.title }}
                </el-tag>
              </div>
            </template>
          </el-form-item>

          <el-form-item v-if="form.applyRange === 40" label="参与商品">
            <el-table :data="form.scopeGoods" style="width: 100%" border>
              <el-table-column prop="title" label="商品名称" width="180">
                <template slot-scope="scope">
                  <div class="goods-title">
                    {{ scope.row.title }}
                  </div>
                  <div class="goods-no">
                    {{ scope.row.code }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="价格" width="250">
                <template slot-scope="scope">
                  <WarehousePriceEditor
                    v-model="scope.row.salePrice"
                    :editable="false"
                    :ladder-price="scope.row.ladderPrice"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="inventory" label="库存" width="100"></el-table-column>
            </el-table>
          </el-form-item>

          <el-form-item label="满额阶梯">
            <div v-for="(level, index) in form.fullAmountLevels" :key="index" class="level-item">
              <div class="level-header">
                <span class="level-title">满{{ level.amount }}元可换购以下商品</span>
              </div>
              <div v-if="level.goods.length > 0" class="goods-list">
                <el-table :data="level.goods" border>
                  <el-table-column prop="title" label="商品名称"></el-table-column>
                  <el-table-column prop="code" label="商品编码"></el-table-column>
                  <el-table-column prop="specValueName" label="规格">
                    <template slot-scope="scope">
                      <span>{{ scope.row.unitName }};</span>
                      <span v-for="(itemS, indexS) in scope.row.specGroup" :key="indexS">
                        {{ itemS.specValueName }};
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="price" label="原价">
                    <template slot-scope="scope">
                      <WarehousePriceEditor
                        v-model="scope.row.salePrice"
                        :editable="false"
                        :ladder-price="scope.row.ladderPrice"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="换购价">
                    <template slot-scope="scope">
                      <span>¥{{ scope.row.exchangePrice }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="换购数量限制">
                    <template slot-scope="scope">
                      <span>{{ scope.row.limitQuantity }}件</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="inventory" label="库存"></el-table-column>
                </el-table>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="适用人群">
            <span>{{ getUserRangeText(form.userRange) }}</span>
            <template v-if="form.userRange === 2">
              <div class="selected-tags">
                <el-tag v-for="item in form.customerTypes" :key="item.id">
                  {{ item.name }}
                </el-tag>
              </div>
            </template>
            <template v-if="form.userRange === 3">
              <div class="selected-tags">
                <el-tag v-for="item in form.customerTags" :key="item.id">
                  {{ item.name }}
                </el-tag>
              </div>
            </template>
          </el-form-item>

          <el-form-item label="优惠券支持">
            <span>{{ form.enableCoupon ? "支持使用优惠券" : "不支持使用优惠券" }}</span>
          </el-form-item>

          <el-form-item label="活动状态">
            <el-tag :type="form.status === 5 ? 'success' : 'info'">
              {{ form.status === 5 ? "开启" : "关闭" }}
            </el-tag>
          </el-form-item>

          <el-form-item label="审核状态">
            <el-tag :type="$_common.getAuditStatusType(form.auditStatus)">
              {{ $_common.getAuditStatusText(form.auditStatus) }}
            </el-tag>
          </el-form-item>
        </div>
      </div>

      <div class="detail-tab-item">
        <div class="detail-tab-title">活动说明</div>
        <div class="detail-tab-main">
          <el-form-item label="活动说明">
            <div class="description-content">{{ form.description || "暂无说明" }}</div>
          </el-form-item>
        </div>
      </div>
    </el-form>

    <!-- 审核弹窗 -->
    <el-dialog
      title="审核"
      :visible.sync="auditDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核结果" prop="auditStatus">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio :label="2">通过</el-radio>
            <el-radio :label="3">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" :rows="4" placeholder="请输入审核意见"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="auditLoading" @click="submitAudit">确 定</el-button>
      </div>
    </el-dialog>
  </ContainerTit>
</template>

<script>
import { auditFullBuy, getFullBuyInfo } from "@/api/Market";
import WarehousePriceEditor from "@/component/goods/WarehousePriceEditor";

export default {
  name: "ViewFullBuy",
  components: {
    WarehousePriceEditor,
  },
  /**
   * 组件数据
   * @property {string} fullBuyId - 活动ID，从路由参数获取
   * @property {Object} form - 活动表单数据
   * @property {boolean} auditDialogVisible - 审核弹窗显示状态
   * @property {boolean} auditLoading - 审核提交加载状态
   * @property {Object} auditForm - 审核表单数据
   * @property {Object} auditRules - 审核表单验证规则
   */
  data() {
    // 自定义验证规则：审核不通过时必须填写审核意见
    const validateAuditRemark = (rule, value, callback) => {
      if (this.auditForm.auditStatus === 3 && (!value || value.trim() === "")) {
        callback(new Error("审核不通过时必须填写审核意见"));
      } else {
        callback();
      }
    };

    return {
      fullBuyId: this.$route.params.id,
      form: {
        name: "",
        time: [],
        applyRange: 10,
        scopeGoods: [],
        fullAmountLevels: [],
        description: "",
        categoryList: [],
        brandList: [],
        useShop: [],
        userRange: 1,
        customerTypes: [],
        customerTags: [],
        enableCoupon: false,
        status: 4,
        auditStatus: 1,
      },
      auditDialogVisible: false,
      auditLoading: false,
      auditForm: {
        auditStatus: 2,
        auditRemark: "",
      },
      auditRules: {
        auditStatus: [{ required: true, message: "请选择审核结果", trigger: "change" }],
        auditRemark: [
          { validator: validateAuditRemark, trigger: "blur" },
          { min: 2, max: 200, message: "长度在 2 到 200 个字符", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getInfo();
  },
  methods: {
    /**
     * 获取活动详情信息
     * @description 获取活动基本信息
     * @async
     */
    async getInfo() {
      try {
        const { data } = await getFullBuyInfo(this.fullBuyId);
        this.form = {
          ...this.form,
          ...data,
          time: [data.startTime * 1000, data.endTime * 1000],
        };
      } catch (error) {
        this.$message.error("获取活动信息失败");
      }
    },
    /**
     * 打开审核弹窗
     * @description 初始化审核表单并显示弹窗
     */
    handleAudit() {
      this.auditDialogVisible = true;
      this.auditForm = {
        auditStatus: 2,
        auditRemark: "",
      };
      // 重置表单验证
      if (this.$refs.auditForm) {
        this.$refs.auditForm.clearValidate();
      }
    },
    /**
     * 提交审核
     * @description 验证表单并提交审核结果
     * @async
     */
    submitAudit() {
      this.$refs.auditForm.validate(async (valid) => {
        if (valid) {
          try {
            this.auditLoading = true;
            await auditFullBuy(this.fullBuyId, {
              id: this.fullBuyId,
              auditStatus: this.auditForm.auditStatus,
              auditRemark: this.auditForm.auditRemark,
            });

            this.$message.success("审核成功");
            this.auditDialogVisible = false;
            this.$router.go(-1);
          } catch (error) {
            console.error("审核失败:", error);
            this.$message.error(error.message || "审核失败");
          } finally {
            this.auditLoading = false;
          }
        }
      });
    },
    /**
     * 格式化时间戳
     * @param {number} time - Unix时间戳（秒）
     * @returns {string} 格式化后的时间字符串
     */
    formatTime(time) {
      return this.$_common.formatDate(time * 1000);
    },
    // 获取商品范围文本
    getApplyRangeText(range) {
      const rangeMap = {
        10: "全部商品",
        20: "指定分类",
        30: "指定品牌",
        40: "指定商品",
      };
      return rangeMap[range] || "-";
    },
    // 获取适用人群文本
    getUserRangeText(range) {
      const rangeMap = {
        1: "全部客户",
        2: "指定客户类型",
        3: "指定客户标签",
      };
      return rangeMap[range] || "-";
    },
  },
};
</script>

<style lang="scss" scoped>
.detail-tab-item {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 4px;

  .detail-tab-title {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    font-size: 16px;
    font-weight: 500;
  }

  .detail-tab-main {
    padding: 20px;
  }
}

.level-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  .level-header {
    margin-bottom: 15px;

    .level-title {
      font-weight: 500;
      color: #303133;
    }
  }
}

.selected-tags {
  margin-top: 10px;

  .el-tag {
    margin-right: 10px;
    margin-bottom: 10px;
  }
}

.goods-title {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.goods-no {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.description-content {
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  min-height: 60px;
  line-height: 1.5;
}
</style>
