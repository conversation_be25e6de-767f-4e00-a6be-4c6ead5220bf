<template>
  <ContainerTit>
    <div slot="headr">
      <el-button v-if="!fullBuyId" @click="delPauseSave(1)">清除暂存</el-button>
      <el-button v-if="!fullBuyId" :loading="loading" @click="addPauseSave">暂存</el-button>
      <el-button type="primary" :loading="loading" @click="submitForm">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <div class="detail-tab-item">
        <div class="detail-tab-title">活动信息</div>
        <div class="detail-tab-main">
          <el-form-item label="活动名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入活动名称" style="width: 400px"></el-input>
          </el-form-item>

          <el-form-item label="活动时间" prop="time">
            <el-date-picker
              v-model="form.time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="适用店铺" prop="shopId">
            <SelectShop v-model="form.shopId" :enable="true" />
            <el-button size="mini" type="text" @click="$router.push('/SystemSettings/liansuoguanli/AddShop')">
              【新建商铺】
            </el-button>
          </el-form-item>
          <el-form-item label="商品范围" prop="applyRange">
            <el-radio-group v-model="form.applyRange" :disabled="!form.shopId">
              <el-radio :label="10">全部商品</el-radio>
              <el-radio :label="20">指定分类</el-radio>
              <el-radio :label="30">指定品牌</el-radio>
              <el-radio :label="40">指定商品</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="form.applyRange === 40" label="参与商品">
            <div class="clearfix">
              <span class="float_left" style="color: #999"> 选择参与满额计算的商品 </span>
              <el-button type="primary" size="small" class="float_left" @click="goods_show = true">
                选择商品
              </el-button>
            </div>
            <el-table :data="form.scopeGoods" style="width: 100%; margin-top: 10px" border>
              <el-table-column prop="title" label="商品名称" width="180">
                <template slot-scope="scope">
                  <div class="goods-title">
                    {{ scope.row.title }}
                  </div>
                  <div class="goods-no">
                    {{ scope.row.code }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="unitName" label="规格" width="200">
                <template slot-scope="scope">
                  <span>{{ scope.row.unitName }}</span>
                  <span v-for="(itemS, indexS) in scope.row.specGroup" :key="indexS">
                    {{ itemS.specValueName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="inventory" label="库存" width="100"></el-table-column>
              <el-table-column prop="price" label="价格" width="180">
                <template slot-scope="scope">
                  <WarehousePriceEditor
                    v-model="scope.row.salePrice"
                    :editable="false"
                    :ladder-price="scope.row.ladderPrice"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" @click="editScopeGoods(scope.row, scope.$index)">编辑</el-button>
                  <el-button type="text" @click="removeScopeGoods(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>

          <el-form-item v-if="form.applyRange === 20" label="参与分类">
            <div class="level-item">
              <div class="selected-tags">
                <el-tag v-for="item in form.categoryList" :key="item.id" closable @close="removeCategory(item)">
                  {{ item.title }}
                </el-tag>
                <el-tag class="new-tag" type="primary" @click="categoryDialogVisible = true">
                  <i class="el-icon-plus"></i> 添加分类
                </el-tag>
              </div>
            </div>
          </el-form-item>

          <el-form-item v-if="form.applyRange === 30" label="参与品牌">
            <div class="level-item">
              <div class="selected-tags">
                <el-tag v-for="item in form.brandList" :key="item.id" closable @close="removeBrand(item)">
                  {{ item.title }}
                </el-tag>
                <el-tag class="new-tag" type="primary" @click="brandDialogVisible = true">
                  <i class="el-icon-plus"></i> 添加品牌
                </el-tag>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="满额阶梯" prop="fullAmountLevels">
            <div v-for="(level, index) in form.fullAmountLevels" :key="index" class="level-item">
              <el-input-number
                v-model="level.amount"
                controls-position="right"
                :min="getMinAmount(index)"
                :precision="2"
                :step="10"
                placeholder="满额金额"
                @change="validateLevelAmount(index)"
              ></el-input-number>
              <el-button :disabled="!form.shopId" type="text" size="mini" @click="selectLevelGoods(index)">
                选择换购商品({{ level.goods.length }}/20)
              </el-button>
              <el-button type="text" @click="removeLevel(index)">删除阶梯</el-button>

              <div v-if="level.goods.length > 0" class="goods-list">
                <el-table :data="level.goods" border>
                  <el-table-column prop="title" label="商品名称"></el-table-column>
                  <el-table-column prop="code" label="商品编码"></el-table-column>
                  <el-table-column prop="specValueName" label="规格">
                    <template slot-scope="scope">
                      <span>{{ scope.row.unitName }};</span>
                      <span v-for="(itemS, indexS) in scope.row.specGroup" :key="indexS">
                        {{ itemS.specValueName }};
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="price" label="原价">
                    <template slot-scope="scope">
                      <WarehousePriceEditor
                        v-model="scope.row.salePrice"
                        :editable="false"
                        :ladder-price="scope.row.ladderPrice"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="换购价" width="200">
                    <template slot-scope="scope">
                      <el-input-number
                        v-model="scope.row.exchangePrice"
                        controls-position="right"
                        :min="0"
                        :max="scope.row.price"
                        :precision="2"
                        :step="1"
                      ></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="换购数量限制" width="200">
                    <template slot-scope="scope">
                      <el-input-number
                        v-model="scope.row.limitQuantity"
                        controls-position="right"
                        :min="1"
                        :max="scope.row.inventory"
                        :step="1"
                      ></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                      <el-button type="text" @click="removeLevelGoods(index, scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <el-button type="primary" plain @click="addLevel">添加阶梯</el-button>
          </el-form-item>

          <el-form-item label="适用人群" prop="userRange">
            <el-radio-group v-model="form.userRange">
              <el-radio :label="1">全部客户</el-radio>
              <el-radio :label="2">指定客户类型</el-radio>
              <el-radio :label="3">指定客户标签</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="form.userRange === 2" label="客户类型">
            <div class="level-item">
              <div class="selected-tags">
                <el-tag v-for="item in form.customerTypes" :key="item.id" closable @close="removeCustomerType(item)">
                  {{ item.name }}
                </el-tag>
                <el-tag class="new-tag" type="primary" @click="openCustomerTypeDialog">
                  <i class="el-icon-plus"></i> 添加客户类型
                </el-tag>
              </div>
            </div>
          </el-form-item>

          <el-form-item v-if="form.userRange === 3" label="客户标签">
            <div class="level-item">
              <div class="selected-tags">
                <el-tag v-for="item in form.customerTags" :key="item.id" closable @close="removeCustomerTag(item)">
                  {{ item.name }}
                </el-tag>
                <el-tag class="new-tag" type="primary" @click="customerTagDialogVisible = true">
                  <i class="el-icon-plus"></i> 添加客户标签
                </el-tag>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="优惠券支持" prop="enableCoupon">
            <el-switch v-model="form.enableCoupon" active-text="支持使用优惠券" inactive-text="不支持使用优惠券">
            </el-switch>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <div class="detail-tab-title">活动说明</div>
        <div class="detail-tab-main">
          <el-form-item label="活动说明" prop="description">
            <el-input v-model="form.description" type="textarea" rows="4" placeholder="请输入活动说明"></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <!-- 商品选择弹窗 -->
    <SaleGoodsSel
      v-if="goods_show"
      :base-goods="false"
      :is-show="goods_show"
      :shop-id="form.shopId"
      @cancel="goods_show = false"
      @confirm="selScopeGoods"
    ></SaleGoodsSel>

    <!-- 换购商品选择弹窗 -->
    <SaleGoodsSel
      v-if="levelGoodsShow"
      :base-goods="false"
      :is-show="levelGoodsShow"
      :shop-id="form.shopId"
      @cancel="levelGoodsShow = false"
      @confirm="handleLevelGoodsConfirm"
    ></SaleGoodsSel>

    <!-- 商品规格编辑弹窗 -->
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="编辑商品规格"
      :visible.sync="goods_istrue"
      width="46%"
    >
      <el-table :data="scopeGoodsSpecs" style="width: 100%" border :span-method="objectSpanMethod">
        <el-table-column prop="title" label="商品" min-width="140">
          <template slot-scope="scope">
            <div class="goods-title">
              {{ scope.row.title }}
            </div>
            <div class="goods-no">
              {{ scope.row.code }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="unitName" label="规格" min-width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.unitName }},</span>
            <span>{{ scope.row.specValueName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="salePrice" label="商城价" min-width="100"></el-table-column>
        <el-table-column label="参与活动" min-width="100">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.activity_istrue"></el-switch>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="goods_istrue = false">取 消</el-button>
        <el-button type="primary" @click="confirmScopeGoods">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 分类选择弹窗 -->
    <CategorySelModel
      v-if="categoryDialogVisible"
      :is-check="false"
      :only-enable="true"
      :dialog-visible="categoryDialogVisible"
      @close="categoryDialogVisible = false"
      @confirm="handleCategoryConfirm"
    />

    <!-- 品牌选择弹窗 -->
    <BrandSelModel
      v-if="brandDialogVisible"
      :is-check="false"
      :dialog-visible="brandDialogVisible"
      @close="brandDialogVisible = false"
      @confirm="handleBrandConfirm"
    />

    <!-- 添加客户类型选择弹窗 -->
    <CustomerTypeSelModel
      v-if="customerTypeDialogVisible"
      :is-show="customerTypeDialogVisible"
      :is-check="false"
      :dialog-visible="customerTypeDialogVisible"
      @close="customerTypeDialogVisible = false"
      @confirm="handleCustomerTypeConfirm"
    />

    <!-- 修改客户标签选择弹窗 -->
    <TagLibModel
      v-if="customerTagDialogVisible"
      :is-show="customerTagDialogVisible"
      :is-check="false"
      :is-radio="false"
      @cancel="customerTagDialogVisible = false"
      @confirm="handleCustomerTagConfirm"
    />
  </ContainerTit>
</template>

<script>
import { addPauseSave, delPauseSave, getPauseSave } from "@/api/common";
import { getGoodsInfo } from "@/api/goods";
import { editFullBuy, getFullBuyInfo, addFullBuy as marketAddFullBuy } from "@/api/Market";
import TagLibModel from "@/component/common/TagLibModel.vue";
import CustomerTypeSelModel from "@/component/customer/CustomerTypeSelModel";
import BrandSelModel from "@/component/goods/BrandSelModel";
import CategorySelModel from "@/component/goods/CategorySelModel";
import SaleGoodsSel from "@/component/goods/SaleGoodsSel.vue";
import SelectShop from "@/component/goods/SelectShop.vue";
import WarehousePriceEditor from "@/component/goods/WarehousePriceEditor.vue";

export default {
  name: "AddFullBuy",
  components: {
    SaleGoodsSel,
    CategorySelModel,
    BrandSelModel,
    SelectShop,
    CustomerTypeSelModel,
    TagLibModel,
    WarehousePriceEditor,
  },
  data() {
    return {
      fullBuyId: "",
      loading: false,
      pageName: "",
      form: {
        name: "",
        time: [],
        applyRange: 10, // 默认为全部商品
        scopeGoods: [], // 参与满额计算的商品列表
        fullAmountLevels: [
          {
            // 满额阶梯
            amount: 0,
            goods: [],
          },
        ],
        description: "",
        categoryList: [], // 选中的分类列表
        brandList: [], // 选中的品牌列表
        shopId: 0, // 添加适用店铺字段
        userRange: 1, // 默认全部客户
        customerTypes: [], // 客户类型列表
        customerTags: [], // 客户标签列表
        enableCoupon: false, // 是否支持优惠券
      },
      rules: {
        name: [{ required: true, message: "请输入活动名称", trigger: "blur" }],
        time: [{ required: true, message: "请选择活动时间", trigger: "change" }],
        applyRange: [{ required: true, message: "请选择商品范围", trigger: "change" }],
        fullAmountLevels: [
          {
            type: "array",
            required: true,
            message: "请至少配置一个满额阶梯",
            trigger: "change",
          },
          {
            validator: this.validateLevels,
            trigger: "change",
          },
        ],
        // description: [{ required: true, message: "请输入活动说明", trigger: "blur" }],
        shopId: [
          {
            required: true,
            type: "number",
            message: "请选择店铺",
            trigger: "change",
          },
        ],
        userRange: [{ required: true, message: "请选择适用人群", trigger: "change" }],
        customerTypes: [
          {
            type: "array",
            required: true,
            message: "请选择客户类型",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (this.form.userRange === 2 && (!value || value.length === 0)) {
                callback(new Error("请至少选择一个客户类型"));
              } else {
                callback();
              }
            },
          },
        ],
        customerTags: [
          {
            type: "array",
            required: true,
            message: "请选择客户标签",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (this.form.userRange === 3 && (!value || value.length === 0)) {
                callback(new Error("请至少选择一个客户标签"));
              } else {
                callback();
              }
            },
          },
        ],
      },
      goods_show: false,
      goods_istrue: false,
      scopeGoodsSpecs: [],
      spanArr: [],
      currentGoodsIndex: -1,
      shopId: "",
      shopName: "",
      categoryDialogVisible: false,
      brandDialogVisible: false,
      levelIndex: 0,
      currentLevelIndex: -1,
      levelGoodsShow: false,
      customerTypeDialogVisible: false,
      customerTagDialogVisible: false,
    };
  },
  created() {
    this.pageName = this.$route.name;
    if (this.$route.params.id) {
      this.fullBuyId = this.$route.params.id;
      this.getInfo();
    } else {
      this.getPauseSave();
    }
  },
  methods: {
    // 获取活动详情
    async getInfo() {
      try {
        const { data } = await getFullBuyInfo(this.fullBuyId);
        // 处理时间格式
        this.form.time = [data.startTime * 1000, data.endTime * 1000];

        this.form = {
          ...this.form,
          name: data.name,
          applyRange: data.applyRange,
          description: data.description,
          shopId: data.shopId,
          categoryList: data.categoryList || [],
          brandList: data.brandList || [],
          scopeGoods: data.scopeGoods || [],
          fullAmountLevels: data.fullAmountLevels || [
            {
              amount: 0,
              goods: [],
            },
          ],
          userRange: data.userRange || 1,
          customerTypes: data.customerTypes || [],
          customerTags: data.customerTags || [],
          enableCoupon: data.enableCoupon || false,
        };
      } catch (error) {
        console.error("获取活动详情失败:", error);
        this.$message.error("获取活动详情失败");
      }
    },

    // 获取暂存
    async getPauseSave() {
      try {
        const { data } = await getPauseSave({
          key: this.pageName,
        });

        if (!data || !data.form || Object.keys(data.form).length === 0) return;

        // 使用暂存的表单数据
        this.form = {
          ...this.form, // 保留默认值
          ...data.form, // 使用暂存的数据覆盖
        };

        console.log("恢复暂存数据:", this.form);
      } catch (error) {
        console.error("获取暂存信息失败:", error);
        this.$message.error("获取暂存信息失败");
      }
    },

    // 暂存方法
    async addPauseSave() {
      try {
        this.loading = true;

        // 数据格式验证
        if (!this.form.time || !Array.isArray(this.form.time) || this.form.time.length !== 2) {
          throw new Error("活动时间格式不正确");
        }

        // 调用暂存API
        await addPauseSave({
          key: this.pageName,
          data: {
            form: this.form, // 将表单数据嵌套在 form 字段中
            metadata: {
              createdAt: Date.now(),
              version: "1.2",
              source: "AddFullBuy",
              dataSize: JSON.stringify(this.form).length,
              context: {
                userAgent: navigator.userAgent,
                screenWidth: window.screen.width,
                screenHeight: window.screen.height,
              },
            },
          },
        });

        this.$message({
          type: "success",
          message: "暂存成功!",
        });
        this.$closeCurrentGoEdit("/Marketing/FullBuy/FullBuyList");
      } catch (error) {
        console.error("暂存失败:", error);
        this.$message.error(`暂存失败：${error.message}`);
      } finally {
        this.loading = false;
      }
    },

    // 清除暂存
    async delPauseSave(type) {
      try {
        await delPauseSave({
          key: this.pageName,
        });

        if (type) {
          this.$message({
            type: "success",
            message: "清除暂存成功",
          });
          this.$closeCurrentGoEdit("/marketing/fullbuy/AddFullBuy");
        }
      } catch (error) {
        console.error("清除暂存失败:", error);
        this.$message.error("清除暂存失败");
      }
    },

    // 获取当前阶梯的最小金额（基于前一个阶梯的金额）
    getMinAmount(index) {
      if (index === 0) return 0;
      return this.form.fullAmountLevels[index - 1].amount + 0.01;
    },

    // 验证阶梯金额
    validateLevelAmount(index) {
      const currentAmount = this.form.fullAmountLevels[index].amount;

      // 验证与前一个阶梯的金额
      if (index > 0) {
        const prevAmount = this.form.fullAmountLevels[index - 1].amount;
        if (currentAmount <= prevAmount) {
          this.$message.warning(`第${index + 1}阶梯金额必须大于第${index}阶梯金额`);
          this.form.fullAmountLevels[index].amount = prevAmount + 0.01;
        }
      }

      // 验证与后一个阶梯的金额
      if (index < this.form.fullAmountLevels.length - 1) {
        const nextAmount = this.form.fullAmountLevels[index + 1].amount;
        if (currentAmount >= nextAmount) {
          this.$message.warning(`第${index + 1}阶梯金额必须小于第${index + 2}阶梯金额`);
          this.form.fullAmountLevels[index].amount = nextAmount - 0.01;
        }
      }
    },

    // 验证整个阶梯配置
    validateLevels(rule, value, callback) {
      if (!value || value.length === 0) {
        callback(new Error("请至少配置一个满额阶梯"));
        return;
      }

      // 验证每个阶梯是否配置了换购商品
      const invalidLevel = value.findIndex((level) => !level.goods || level.goods.length === 0);
      if (invalidLevel !== -1) {
        callback(new Error(`请为第${invalidLevel + 1}个阶梯配置换购商品`));
        return;
      }

      // 验证阶梯金额是否递增
      for (let i = 1; i < value.length; i++) {
        if (value[i].amount <= value[i - 1].amount) {
          callback(new Error("满额阶梯金额必须递增"));
          return;
        }
      }

      callback();
    },

    // 选择换购商品（弹窗）
    selectLevelGoods(levelIndex) {
      const currentGoods = this.form.fullAmountLevels[levelIndex].goods;
      if (currentGoods.length >= 20) {
        this.$message.warning("换购商品数量已达到上限（20个）");
        return;
      }
      this.currentLevelIndex = levelIndex;
      this.levelGoodsShow = true;
    },

    // 处理换购商品选择确认
    handleLevelGoodsConfirm(selectedGoods) {
      if (this.currentLevelIndex === -1) return;

      const currentLevel = this.form.fullAmountLevels[this.currentLevelIndex];
      const currentCount = currentLevel.goods.length;
      const selectedCount = selectedGoods.length;

      // 检查是否超出20个商品的限制
      if (currentCount + selectedCount > 20) {
        this.$message.warning(`当前已有${currentCount}个商品，最多还可添加${20 - currentCount}个商品`);
        return;
      }

      // 处理选中的商品
      currentLevel.goods.push(...selectedGoods);

      this.levelGoodsShow = false;
      this.currentLevelIndex = -1;
    },

    // 添加满额阶梯
    addLevel() {
      this.form.fullAmountLevels.push({
        amount: 0,
        goods: [],
      });
    },

    // 删除满额阶梯
    removeLevel(index) {
      this.form.fullAmountLevels.splice(index, 1);
    },

    // 选择参与满额计算的商品
    selScopeGoods(val) {
      this.shopId = val[0].shopId;
      this.shopName = val[0].shopName;
      const goodsArr = val.map((item) => ({
        ...item,
        goodsId: item.id,
        joinSku: [],
        price: item.salePrice,
        inventory: item.inventory,
        title: item.title,
        code: item.code,
      }));

      this.form.scopeGoods = this.$_common.unique(this.form.scopeGoods.concat(goodsArr), ["skuId"]);
      this.goods_show = false;
    },

    // 删除参与满额计算的商品
    removeScopeGoods(index) {
      this.form.scopeGoods.splice(index, 1);
    },

    // 删除某个阶梯的换购商品
    removeLevelGoods(levelIndex, goodsIndex) {
      this.form.fullAmountLevels[levelIndex].goods.splice(goodsIndex, 1);
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          // 验证商品库存
          const invalidStock = this.form.fullAmountLevels.some((level) =>
            level.goods.some((goods) => goods.limitQuantity > goods.inventory)
          );

          if (invalidStock) {
            this.$message.error("换购数量不能大于商品库存");
            return;
          }

          try {
            this.loading = true;
            // 转换时间为时间戳
            const startTime = new Date(this.form.time[0]).getTime();
            const endTime = new Date(this.form.time[1]).getTime();

            const params = {
              name: this.form.name,
              startTime, // 使用时间戳
              endTime, // 使用时间戳
              applyRange: this.form.applyRange,
              categoryList: this.form.applyRange === 20 ? this.form.categoryList : [],
              brandList: this.form.applyRange === 30 ? this.form.brandList : [],
              scopeGoods: this.form.applyRange === 40 ? this.form.scopeGoods : [],
              fullAmountLevels: this.form.fullAmountLevels,
              description: this.form.description,
              shopId: this.form.shopId,
              userRange: this.form.userRange,
              customerTypes: this.form.userRange === 2 ? this.form.customerTypes : [],
              customerTags: this.form.userRange === 3 ? this.form.customerTags : [],
              enableCoupon: this.form.enableCoupon,
            };

            // 添加时间戳验证
            if (isNaN(startTime) || isNaN(endTime)) {
              throw new Error("时间格式转换失败");
            }

            if (this.fullBuyId) {
              await editFullBuy(this.fullBuyId, params);
              this.$message.success("编辑成功");
            } else {
              await marketAddFullBuy(params);
              this.$message.success("添加成功");
            }

            this.delPauseSave(); // 提交成功后清除暂存
            this.$closeCurrentGoEdit("/Marketing/FullBuy/FullBuyList");
          } catch (error) {
            console.error("保存失败:", error);
            this.$message.error(error.message || "保存失败");
          } finally {
            this.loading = false;
          }
        }
      });
    },

    // 取消
    cancel() {
      this.$router.push("/Marketing/FullBuy/FullBuyList");
    },

    // 编辑商品规格
    async editScopeGoods(row, index) {
      this.goods_istrue = true;
      this.currentGoodsIndex = index;
      await this.showGoodsSpecs(row);
    },

    // 获取商品规格信息
    async showGoodsSpecs(val) {
      let target = this.$_common.deepClone(this.scopeGoodsSpecs);
      const { data } = await getGoodsInfo(val.id || val.goodsId);

      if (data.specType === 2) {
        target = data.specMultiple.map((item) => {
          const specValueName = item.specGroup.map((itemS) => itemS.specValueName).join("_");

          let activityIstrue = true;
          if (val.joinSku) {
            activityIstrue = val.joinSku.includes(item.id);
          }

          return {
            ...item,
            specValueName,
            activity_istrue: activityIstrue,
            images: val.images,
            title: val.title,
            code: val.code,
            goodsId: val.id,
            materielId: val.basicGoodsId,
          };
        });
      } else if (data.specType === 1) {
        target = data.specMultiple.map((item) => {
          let activityIstrue = true;
          if (val.joinSku) {
            activityIstrue = val.joinSku.includes(item.id);
          }

          return {
            ...item,
            specValueName: "无",
            activity_istrue: activityIstrue,
            images: val.images,
            title: val.title,
            code: val.code,
            goodsId: val.id,
            materielId: val.basicGoodsId,
          };
        });
      }

      this.scopeGoodsSpecs = target;
      const getSpanArr = this.$_common.getSpanArr(this.scopeGoodsSpecs, "goodsId");
      this.spanArr = getSpanArr.spanArr;
    },

    // 确认商品规格选择
    confirmScopeGoods() {
      this.goods_istrue = false;
      const joinSkuList = this.scopeGoodsSpecs.filter((item) => item.activity_istrue);
      const joinSku = joinSkuList.map((item) => item.id);
      this.form.scopeGoods[this.currentGoodsIndex].joinSku = joinSku;
    },

    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (["商品"].includes(column.label)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },

    // 分类相关方法
    handleCategoryConfirm(categories) {
      this.form.categoryList = this.$_common.unique(this.form.categoryList.concat(categories), ["id"]);
      this.categoryDialogVisible = false;
    },
    removeCategory(category) {
      const index = this.form.categoryList.findIndex((item) => item.id === category.id);
      if (index !== -1) {
        this.form.categoryList.splice(index, 1);
      }
    },

    // 品牌相关方法
    handleBrandConfirm(brands) {
      this.form.brandList = this.$_common.unique(this.form.brandList.concat(brands), ["id"]);
      this.brandDialogVisible = false;
    },
    removeBrand(brand) {
      const index = this.form.brandList.findIndex((item) => item.id === brand.id);
      if (index !== -1) {
        this.form.brandList.splice(index, 1);
      }
    },

    // 客户类型相关方法
    handleCustomerTypeConfirm(types) {
      if (!Array.isArray(types)) {
        types = [types];
      }

      const formattedTypes = types.map((type) => ({
        id: type.id,
        name: type.title || type.name,
      }));

      this.form.customerTypes = this.$_common.unique(this.form.customerTypes.concat(formattedTypes), ["id"]);
      this.customerTypeDialogVisible = false;
    },
    removeCustomerType(type) {
      const index = this.form.customerTypes.findIndex((item) => item.id === type.id);
      if (index !== -1) {
        this.form.customerTypes.splice(index, 1);
      }
    },

    // 客户标签相关方法
    handleCustomerTagConfirm(tags) {
      const formattedTags = tags.map((tag) => ({
        id: tag.id,
        name: tag.title || tag.name,
      }));

      this.form.customerTags = this.$_common.unique(this.form.customerTags.concat(formattedTags), ["id"]);
      this.customerTagDialogVisible = false;
    },
    removeCustomerTag(tag) {
      const index = this.form.customerTags.findIndex((item) => item.id === tag.id);
      if (index !== -1) {
        this.form.customerTags.splice(index, 1);
      }
    },

    // 打开客户类型选择弹窗
    openCustomerTypeDialog() {
      this.customerTypeDialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.goods-list {
  margin-top: 10px;

  .el-table {
    margin-bottom: 10px;
  }
}

.level-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

.float_left {
  float: left;
}

.goods-title {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.goods-no {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.selected-tags {
  .el-tag {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .new-tag {
    cursor: pointer;
    &:hover {
      border-color: #409eff;
      color: #409eff;
    }
  }
}
</style>
