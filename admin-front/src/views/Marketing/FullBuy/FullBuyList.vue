<template>
  <ContainerQuery>
    <div slot="left">
      <el-button v-if="$accessCheck($Access.FullBuy_addFullBuy)" type="primary" @click="handleAdd">添加活动</el-button>
    </div>

    <div slot="more">
      <el-form :inline="true" size="small" style="margin-bottom: 0">
        <el-form-item>
          <el-input v-model="search_form.title" placeholder="活动名称" clearable style="width: 220px">
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <el-table v-loading="loading" :data="list" border style="width: 100%">
      <el-table-column prop="name" label="活动名称" min-width="120">
        <template slot-scope="scope">
          <span class="click-div" @click="handleView(scope.row)">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动时间" width="300">
        <template slot-scope="scope">
          {{ formatTime(scope.row.startTime) }} 至 {{ formatTime(scope.row.endTime) }}
        </template>
      </el-table-column>
      <el-table-column label="满额阶梯" min-width="180">
        <template slot-scope="scope">
          <div v-for="(level, index) in parseFullAmount(scope.row.fullAmount)" :key="index">
            满{{ level.amount }}元可换购{{ level.goods.length }}件商品
          </div>
        </template>
      </el-table-column>
      <el-table-column label="适用店铺" width="150">
        <template slot-scope="scope"> {{ scope.row.shopName }} </template>
      </el-table-column>
      <el-table-column label="审核状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getAuditStatusType(scope.row.auditStatus)">
            {{ scope.row.auditStatusLabel }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="活动状态" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :disabled="!$accessCheck($Access.FullBuy_updateStatusFullBuy)"
            active-color="#13ce66"
            inactive-color="#999"
            :active-value="5"
            :inactive-value="4"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            :disabled="[2, 3].includes(scope.row.auditStatus)"
            type="text"
            size="small"
            @click="handleAudit(scope.row)"
          >
            审核
          </el-button>
          <el-button
            type="text"
            size="small"
            :disabled="[2, 3].includes(scope.row.auditStatus)"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            :disabled="[2, 3].includes(scope.row.auditStatus)"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <FooterPage
      :page-size="pageSize"
      :total="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    />
  </ContainerQuery>
</template>

<script>
import { delFullBuy, getAllFullBuy, updateStatusFullBuy } from "@/api/Market";
import dayjs from "dayjs";

export default {
  name: "FullBuyList",
  data() {
    return {
      search_form: {
        title: "",
      },
      list: [],
      total: 0,
      loading: false,
      page: 1,
      pageSize: 20,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true;
      try {
        const response = await getAllFullBuy({
          page: this.page,
          pageSize: this.pageSize,
          title: this.search_form.title,
        });

        if (response.state) {
          this.list = response.data;
          this.total = response.pageTotal;
        } else {
          this.$message.error(response.message || "获取列表失败");
        }
      } catch (error) {
        console.error("获取列表失败:", error);
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 格式化时间戳
    formatTime(timestamp) {
      if (!timestamp) return "--";
      return dayjs(timestamp * 1000).format("YYYY-MM-DD HH:mm:ss");
    },

    // 解析满额数据
    parseFullAmount(fullAmount) {
      try {
        return Array.isArray(fullAmount) ? fullAmount : [];
      } catch (error) {
        console.error("解析满额数据失败:", error);
        return [];
      }
    },

    // 获取审核状态样式
    getAuditStatusType(status) {
      const types = {
        0: "info", // 待补全
        1: "info", // 待审核
        2: "success", // 审核通过
        3: "danger", // 审核不通过
        4: "warning", // 审核中
      };
      return types[status] || "info";
    },

    // 处理状态变更
    async handleStatusChange(row) {
      try {
        await updateStatusFullBuy(row.id, { status: row.status });
        this.$message.success("状态更新成功");
        await this.getList();
      } catch (error) {
        console.error("更新状态失败:", error);
        this.$message.error("状态更新失败");
        row.status = row.status === 4 ? 5 : 4; // 恢复状态
      }
    },

    // 分页相关方法
    pageChange(page) {
      this.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },

    // 操作相关方法
    handleAdd() {
      this.$router.push("/Marketing/FullBuy/AddFullBuy");
    },
    handleEdit(row) {
      this.$router.push(`/Marketing/FullBuy/EditFullBuy/${row.id}`);
    },
    async handleDelete(row) {
      try {
        await this.$confirm("确认删除该活动?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        await delFullBuy(row.id);
        this.$message.success("删除成功");
        await this.getList();
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除失败:", error);
          this.$message.error("删除失败");
        }
      }
    },
    // 处理审核
    handleAudit(row) {
      this.$router.push(`/Marketing/FullBuy/ViewFullBuy/${row.id}`);
    },
    // 处理查看
    handleView(row) {
      this.$router.push(`/Marketing/FullBuy/ViewFullBuy/${row.id}`);
    },
  },
};
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
}
.delete-btn {
  color: #f56c6c;
}
</style>
