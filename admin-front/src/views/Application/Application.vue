<template>
  <div class="application-view">
    <ul class="model-ul">
      <li v-for="(item, index) in application_list" :key="index" class="detail-tab-item">
        <p class="detail-tab-title">
          {{ item.label }}
        </p>
        <ul class="detail-tab-main">
          <li v-for="(itemC, indexC) in item.children" :key="indexC" class="mm-li">
            <div class="clearfix mm-li-div" @click="inApp(itemC)">
              <div class="clearfix">
                <span
                  :style="{ backgroundColor: itemC.color }"
                  class="iconfont float_left mml-icon"
                  :class="[itemC.icon]"
                ></span>
                <p class="mml-tit float_left">
                  {{ itemC.label }}
                </p>
              </div>

              <p class="mml-des">
                {{ itemC.description }}
              </p>
            </div>
          </li>
        </ul>
      </li>
    </ul>
  </div>
</template>

<script>
import * as nodes from "@/access/node";
import { title } from "@/config/settings.js";

export default {
  name: "Application",
  data() {
    return {
      application_list: [
        {
          label: "应用",
          children: [
            {
              icon: "icon-dayin",
              color: "#43bb1f",
              label: "单据模板",
              access: nodes.TemplateListgetAll,
              description: "单据模板打印",
              path: "/BillTemplate/TemplateList",
            },
            {
              icon: "icon-jine1",
              color: "#fe6b58",
              label: "销售提成",
              access: nodes.saleCommission,

              description: "销售员提成，清晰明了",
              path: "/saleCommission/CommissionRule",
            },
            {
              icon: "icon-shanghu",
              color: "rgb(18,186,123)",
              label: "多商户",
              description: "为商家提供完整的入驻子商户线上经营方案",
              path: "/Merchants/MerchartsList",
            },
            {
              icon: "icon-icon-pin",
              color: "rgb(194, 109, 210)",
              label: "钱货日清对账",
              description: "",
              path: "/MoneyGoodsBill",
              access: nodes.getTodayStatistics,
            },
            {
              icon: "icon-icon-pin",
              color: "rgb(194, 109, 210)",
              label: "车载销售",
              description: "",
              path: "/CarSale",
              access: nodes.getTodayStatistics,
            },
          ],
        },
        {
          label: "营销",
          children: [
            {
              icon: "icon-coupon",
              color: "#E6A23C",
              label: "优惠券",
              access: nodes.CouponList,
              description: "多样优惠券活动，达成快速引流",
              path: "/Marketing/MarketingList/Coupon",
            },
            {
              icon: "icon-miaosha1",
              color: "#F56C6C",
              label: "商品秒杀",
              access: nodes.ActivityGoods,
              description: "商品售出率过低？限时秒杀来帮您",
              path: "/Marketing/Promotion/PromotionList",
            },
            {
              icon: "icon-huiyuanka",
              color: "#E6A23C",
              label: "会员卡",
              access: nodes.MembershipCard,
              description: "打折、赠券、提升用户粘合度",
              path: "/Marketing/vip/membershipCard",
            },
            {
              icon: "icon-icon6",
              color: "#12BA7B",
              label: "组合套餐",
              access: nodes.Application_SetMeal,
              description: "关联商品组合营销",
              path: "/Marketing/SetMeal/index",
            },
            {
              icon: "icon-fenxiao",
              color: "#1890FF",
              label: "分销",
              access: nodes.Distribution,
              description: "裂变分销,智能锁粉快速沉淀客户",
              path: "/Distribution/DOverview",
            },
            {
              icon: "icon-jifen",
              color: "#1890FF",
              label: "积分商城",
              access: nodes.PointsMall,
              description: "专属会员积分兑换系统",
              path: "/PointsMall/GoodsManage",
            },
            {
              icon: "icon-manzeng",
              color: "#fe6b58",
              label: "满赠",
              access: nodes.FullGive_getAllFullGive,
              description: "阶梯满赠，提升客单促消费",
              path: "/Marketing/FullGive/FullGiveList",
            },
            {
              icon: "icon-huangou",
              color: "#409EFF",
              label: "满额换购",
              access: nodes.FullBuy_getAllFullBuy,
              description: "满额换购活动，提升客单价",
              path: "/Marketing/FullBuy/FullBuyList",
            },
          ],
        },
        {
          label: "新零售",
          children: [
            {
              icon: "icon-jiageguanli",
              color: "#fb2c95",
              label: "收银台",
              access: nodes.Cashier,
              description: "连接线上线下，提升店铺经营效率",
              path: "/Cashier/CashierList",
            },
            {
              icon: "icon-shanghu",
              color: "#1890FF",
              label: "多门店",
              access: nodes.Application_Multistore,
              description: "连接线上门店一体化管理，提高各门店经营效率",
              path: "/Multistore/StoreList",
            },
            {
              icon: "icon-tabtubiao-1",
              color: "#1890FF",
              label: "供应商管理端",
              access: nodes.Application_supplierManage,
              description: "供应商管理端",
              path: "/Supplier/supplierSet",
            },
          ],
        },
        // {
        //   label: "分销中心",
        //   children: [
        //     {
        //       icon: "icon-fenxiao",
        //       color: "#1890FF",
        //       label: "分销",
        //       access: nodes.Distribution,
        //       description: "裂变分销,智能锁粉快速沉淀客户",
        //       path: "/Distribution/DOverview",
        //     },
        //     {
        //       icon: "icon-jifen",
        //       color: "#1890FF",
        //       label: "积分商城",
        //       // access: nodes.Distribution,
        //       description: "裂变分销,智能锁粉快速沉淀客户",
        //       path: "/PointsMall/GoodsManage",
        //     },
        //   ],
        // },
        // {
        //   label: "店铺拓展",
        //   children: [
        //     {
        //       icon: "icon-jine1",
        //       color: "#fe6b58",
        //       label: "销售提成",
        //       description: "销售员提成，清晰明了",
        //       path: "/saleCommission/CommissionList",
        //     },
        //   ],
        // },
      ],
      application: [],
    };
  },
  mounted() {},
  methods: {
    inApp(row) {
      if (!this.$accessCheck(row.access)) {
        this.$message.warning("抱歉，您暂无权限");
        return;
      }
      if (row.label === "多门店" && parseInt(this.$store.getters["MUser/enterpriseScope"]) === 4) {
        this.$message.warning(`抱歉您的企业为单店铺类型，无法使用应用【多店铺】,请联系${title}售后开通`);
        return;
      }
      this.$router.push(row.path);
    },
  },
};
</script>

<style scoped>
.application-view {
  /*padding: 16px;*/
  /*color: #333333;*/
  /*background-color: #ffffff;*/
}
.model-li-main {
  display: flex;
  flex-wrap: wrap;
}
.model-li {
  margin-bottom: 30px;
}
.model-tit {
  /*height: 30px;*/
  line-height: 30px;
  font-size: 16px;
  font-weight: 400;
}
.mm-li {
  display: inline-block;
  margin-top: 15px;
  width: calc(25% - 15px);
  height: 100px;
  margin-right: 15px;
  background-color: #f5f7fa;
  /*border: 1px solid #dddddd;*/
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
  line-height: 100px;
}
.mm-li-div {
  text-align: left;
  line-height: 16px;
  width: calc(100% - 30px);
  /*padding: 18px 12px 0;*/
  display: inline-block;
  vertical-align: middle;
}
.mml-icon {
  width: 42px;
  height: 42px;
  background-color: #67c23a;
  line-height: 42px;
  text-align: center;
  color: #fff;
  font-size: 26px;
  border-radius: 8px;
  margin-right: 12px;
}
.mml-tit {
  line-height: 42px;
  font-size: 16px;
}
.mml-des {
  color: #666666;
  margin-top: 10px;
  font-size: 12px;
}
</style>
