<!--
 * @description 员工应付单列表组件
 * <AUTHOR> Assistant
 * @date 2024-07-15
-->
<template>
  <ContainerQuery>
    <!-- 筛选区域 -->
    <div slot="more">
      <el-form :inline="true" :model="searchForm" class="search-form" size="small">
        <el-form-item label="批次号">
          <el-input
            v-model="searchForm.batchNo"
            placeholder="请输入批次号"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="店铺">
          <SelectShop
            v-model="searchForm.shopId"
            placeholder="请选择店铺"
            clearable
            @clear="clearShop"
            @change="pageChange(1)"
          />
        </el-form-item>
        <el-form-item label="业务员">
          <el-input
            v-model="searchForm.salesmanName"
            placeholder="请选择业务员"
            style="width: 210px"
            readonly
            clearable
            @clear="clearSalesman"
          >
            <i slot="suffix" class="el-input__icon el-icon-search" @click="staff_show = true"></i>
          </el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable @change="pageChange(1)">
            <el-option :value="1" label="待审核"></el-option>
            <el-option :value="2" label="已审核"></el-option>
            <el-option :value="3" label="已支付"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结算期间">
          <el-date-picker
            v-model="searchForm.periodRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="pageChange(1)"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"> </el-table-column>
      <el-table-column prop="no" label="批次号" width="180"> </el-table-column>
      <el-table-column prop="shopName" label="店铺" width="120"> </el-table-column>
      <el-table-column prop="staffName" label="业务员" width="100"> </el-table-column>
      <el-table-column prop="periodDesc" label="结算描述" width="150"> </el-table-column>
      <el-table-column prop="amount" label="应付金额" width="120">
        <template slot-scope="scope"> {{ $_common.formattedNumber(scope.row.amount) }} </template>
      </el-table-column>
      <el-table-column prop="paidAmount" label="已付金额" width="120">
        <template slot-scope="scope"> {{ $_common.formattedNumber(scope.row.paidAmount) }} </template>
      </el-table-column>
      <el-table-column prop="statusText" label="状态" width="80">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">{{ scope.row.statusText }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="160">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleViewDetail(scope.row)">查看详情</el-button>
          <el-button v-if="scope.row.status === 0" size="mini" type="success" @click="handleAudit(scope.row)"
            >审核</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="pageChange"
    />

    <!-- 弹窗组件区域 -->
    <!-- 详情弹窗 -->
    <el-dialog title="应付单详情" :visible.sync="detailDialogVisible" width="700px">
      <div v-if="detailData" class="detail-content">
        <div class="detail-header">
          <h3>基本信息</h3>
        </div>
        <el-row :gutter="20" class="detail-row">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">应付单ID：</span>
              <span class="detail-value">{{ detailData.id }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">批次号：</span>
              <span class="detail-value">{{ detailData.batchNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">店铺：</span>
              <span class="detail-value">{{ detailData.shopName }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="detail-row">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">业务员：</span>
              <span class="detail-value">{{ detailData.salesmanName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">结算期间：</span>
              <span class="detail-value">{{ detailData.periodDesc }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">状态：</span>
              <span class="detail-value">
                <el-tag :type="getStatusTagType(detailData.status)">{{ detailData.statusText }}</el-tag>
              </span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="detail-row">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">应付金额：</span>
              <span class="detail-value highlight">{{ $_common.formattedNumber(detailData.amount) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">已付金额：</span>
              <span class="detail-value">{{ $_common.formattedNumber(detailData.paidAmount) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">创建时间：</span>
              <span class="detail-value">{{ $_common.formatDate(detailData.createTime) }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="detail-header">
          <h3>收入明细（共 {{ detailData.revenueList ? detailData.revenueList.length : 0 }} 条）</h3>
        </div>
        <el-table :data="detailData.revenueList || []" border style="width: 100%">
          <el-table-column prop="orderId" label="订单号" width="180"> </el-table-column>
          <el-table-column prop="vehicleName" label="车辆" width="120"> </el-table-column>
          <el-table-column prop="amount" label="金额" width="100">
            <template slot-scope="scope"> {{ $_common.formattedNumber(scope.row.amount) }} </template>
          </el-table-column>
          <el-table-column prop="commissionRate" label="提成比例" width="100">
            <template slot-scope="scope"> {{ scope.row.commissionRate }}% </template>
          </el-table-column>
          <el-table-column prop="commissionAmount" label="提成金额" width="100">
            <template slot-scope="scope"> {{ $_common.formattedNumber(scope.row.commissionAmount) }} </template>
          </el-table-column>
          <el-table-column prop="dealTime" label="成交时间" width="160">
            <template slot-scope="scope">
              {{ $_common.formatDate(scope.row.dealTime) }}
            </template>
          </el-table-column>
        </el-table>

        <div v-if="detailData.auditInfo" class="detail-header">
          <h3>审核信息</h3>
        </div>
        <el-row v-if="detailData.auditInfo" :gutter="20" class="detail-row">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">审核人：</span>
              <span class="detail-value">{{ detailData.auditInfo.auditUserName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">审核时间：</span>
              <span class="detail-value">{{ $_common.formatDate(detailData.auditInfo.auditTime) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">审核备注：</span>
              <span class="detail-value">{{ detailData.auditInfo.auditRemark || "无" }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button v-if="detailData && detailData.status === 0" type="success" @click="handleAudit(detailData)"
          >审核</el-button
        >
      </div>
    </el-dialog>

    <!-- 审核弹窗 -->
    <el-dialog title="审核应付单" :visible.sync="auditDialogVisible" width="500px">
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="3">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" :rows="3" placeholder="请输入审核备注"> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitAudit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 业务员选择弹窗 -->
    <StaffList
      v-if="staff_show"
      :is-show="staff_show"
      :is-check="false"
      :shop-id="searchForm.shopId"
      staff-type="salesman"
      :isserch="true"
      @cancel="staff_show = false"
      @confirm="staffSel"
    />

    <!-- 新增业务员弹窗 -->
    <AddStaff v-if="add_staff" :visible="add_staff" @close="add_staff = false" />
  </ContainerQuery>
</template>

<script>
import { auditStaffPayReceipt, getStaffPayReceiptInfo, getStaffPayReceiptList } from "@/api/CarSale";
import FooterPage from "@/component/common/FooterPage";
// import StaffSelect from "@/component/common/StaffSelect";
import StaffList from "@/component/common/staffListModal";
import SelectShop from "@/component/goods/SelectShop";
import ContainerQuery from "@/component/layout/ContainerQuery";
import AddStaff from "@/component/SystemSettings/AddStaff";

export default {
  name: "PayReceiptList",
  components: {
    ContainerQuery,
    FooterPage,
    SelectShop,
    // StaffSelect,
    StaffList,
    AddStaff,
  },
  data() {
    return {
      loading: false,
      // 搜索表单
      searchForm: {
        shopId: "",
        salesManId: "",
        salesmanName: "",
        batchNo: "",
        status: "",
        periodRange: null,
        periodStart: 0,
        periodEnd: 0,
      },
      // 业务员选择相关
      staff_show: false,
      add_staff: false,
      // 表格数据
      tableData: [],
      page: 1,
      pageSize: 20,
      total: 0,
      // 详情弹窗
      detailDialogVisible: false,
      detailData: null,
      // 审核弹窗
      auditDialogVisible: false,
      auditForm: {
        id: null,
        status: 1,
        auditRemark: "",
      },
      auditRules: {
        status: [{ required: true, message: "请选择审核结果", trigger: "change" }],
        auditRemark: [{ required: false, message: "请输入审核备注", trigger: "blur" }],
      },
    };
  },
  created() {
    this.loadTableData();
  },
  methods: {
    // 加载表格数据
    async loadTableData() {
      this.loading = true;

      try {
        // 处理时间范围
        if (this.searchForm.periodRange && this.searchForm.periodRange.length === 2) {
          this.searchForm.periodStart = Math.floor(this.searchForm.periodRange[0] / 1000);
          this.searchForm.periodEnd = Math.floor(this.searchForm.periodRange[1] / 1000);
        } else {
          this.searchForm.periodStart = 0;
          this.searchForm.periodEnd = 0;
        }

        const params = {
          shopId: this.searchForm.shopId,
          salesManId: this.searchForm.salesManId,
          batchNo: this.searchForm.batchNo,
          status: this.searchForm.status,
          periodStart: this.searchForm.periodStart,
          periodEnd: this.searchForm.periodEnd,
          page: this.page,
          pageSize: this.pageSize,
        };

        const response = await getStaffPayReceiptList(params);

        if (response.state) {
          // 处理表格数据，添加文本显示
          this.tableData = response.data.map((item) => {
            return {
              ...item,
              statusText: this.getStatusText(item.status),
            };
          });
          this.total = response.data.pageTotal || this.tableData.length;
        } else {
          this.$message.error(response.data || "获取应付单列表失败");
        }
      } catch (error) {
        console.error("获取应付单列表失败", error);
        this.$message.error("网络错误，请稍后重试");
      } finally {
        this.loading = false;
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: "待审核",
        1: "已审核",
        2: "已支付",
        3: "已拒绝",
      };
      return statusMap[status] || "未知";
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        0: "warning",
        1: "success",
        2: "primary",
        3: "danger",
      };
      return typeMap[status] || "info";
    },

    // 分页查询
    pageChange(val) {
      if (typeof val === "number") {
        this.page = val;
      }
      this.loadTableData();
    },

    // 重置搜索条件
    resetSearch() {
      this.searchForm = {
        shopId: "",
        salesManId: "",
        batchNo: "",
        status: "",
        periodRange: null,
        periodStart: 0,
        periodEnd: 0,
      };
      this.page = 1;
      this.loadTableData();
    },

    // 查看详情
    handleViewDetail(row) {
      this.loading = true;
      getStaffPayReceiptInfo(row.id)
        .then((response) => {
          if (response.data.state) {
            this.detailData = {
              ...response.data.data,
              statusText: this.getStatusText(response.data.data.status),
            };
            this.detailDialogVisible = true;
          } else {
            this.$message.error(response.data.data || "获取详情失败");
          }
        })
        .catch((error) => {
          console.error("获取详情失败", error);
          this.$message.error("网络错误，请稍后重试");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 审核
    handleAudit(row) {
      this.auditForm = {
        id: row.id,
        status: 5,
        auditRemark: "",
      };
      this.auditDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.auditForm && this.$refs.auditForm.clearValidate();
      });
    },

    // 提交审核
    handleSubmitAudit() {
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          auditStaffPayReceipt({
            id: this.auditForm.id,
            status: this.auditForm.status,
            auditRemark: this.auditForm.auditRemark,
            auditUserId: this.$store.state.user.userId || 0,
            auditUserName: this.$store.state.user.username || "系统管理员",
          })
            .then((response) => {
              if (response.data.state) {
                this.$message.success("审核成功");
                this.auditDialogVisible = false;
                // 刷新详情（如果详情弹窗打开）
                if (this.detailDialogVisible && this.detailData && this.detailData.id === this.auditForm.id) {
                  this.handleViewDetail(this.detailData);
                }
                // 刷新列表
                this.loadTableData();
              } else {
                this.$message.error(response.data.data || "审核失败");
              }
            })
            .catch((error) => {
              console.error("审核失败", error);
              this.$message.error("网络错误，请稍后重试");
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    // 清除店铺选择
    clearShop() {
      this.searchForm.shopId = "";
      this.searchForm.salesManId = "";
      this.searchForm.salesmanName = "";
      this.pageChange(1);
    },

    // 清除业务员选择
    clearSalesman() {
      this.searchForm.salesManId = "";
      this.searchForm.salesmanName = "";
      this.pageChange(1);
    },

    // 选择业务员
    staffSel(row) {
      this.searchForm.salesmanName = row[0].staffName;
      this.searchForm.salesManId = row[0].id;
      this.pageChange(1);
    },
  },
};
</script>

<style lang="scss" scoped>
.pay-receipt-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-area {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.table-area {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.detail-content {
  padding: 10px;
}

.detail-header {
  margin: 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
}

.detail-row {
  margin-bottom: 15px;
}

.detail-item {
  margin-bottom: 10px;

  .detail-label {
    font-weight: bold;
    color: #606266;
  }

  .detail-value {
    margin-left: 5px;
    color: #333;

    &.highlight {
      color: #f56c6c;
      font-weight: bold;
    }
  }
}
</style>
