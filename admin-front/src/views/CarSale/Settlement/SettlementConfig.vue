<!--
 * @description 员工结算配置组件
 * <AUTHOR> Assistant
 * @date 2024-07-15
-->
<template>
  <ContainerQuery>
    <!-- 顶部按钮区 -->
    <div slot="left">
      <el-button type="primary" @click="handleAddConfig">新增配置</el-button>
    </div>

    <!-- 筛选区域 -->
    <div slot="more">
      <el-form :inline="true" :model="searchForm" class="search-form" size="small">
        <el-form-item label="店铺">
          <SelectShop
            v-model="searchForm.shopId"
            placeholder="请选择店铺"
            clearable
            :is-default="true"
            @default="defaultShop"
            @clear="clearShop"
            @change="pageChange(1)"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable @change="pageChange(1)">
            <el-option :value="1" label="启用"></el-option>
            <el-option :value="0" label="停用"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"> </el-table-column>
      <el-table-column prop="shopName" label="店铺名称" min-width="120"> </el-table-column>
      <el-table-column prop="financeType" label="财务类型" width="150"> </el-table-column>
      <el-table-column prop="settlementTypeText" label="结算类型" width="120"> </el-table-column>
      <el-table-column prop="cyclePeriodText" label="结算周期" width="120"> </el-table-column>
      <el-table-column prop="cycleDay" label="结算日" width="80"> </el-table-column>
      <el-table-column prop="isAutoSettleText" label="自动结算" width="100"> </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="5"
            :inactive-value="4"
            @change="handleToggleStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="mini" class="success-btn" @click="handleManualSettle(scope.row)"
            >手动结算</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="pageChange"
    />

    <!-- 配置表单弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form ref="configForm" :model="configForm" :rules="configRules" label-width="120px">
        <el-form-item label="店铺" prop="shopId">
          <SelectShop v-model="configForm.shopId" placeholder="请选择店铺" clearable />
        </el-form-item>
        <el-form-item label="财务类型" prop="financeTypeId">
          <el-select v-model="configForm.financeTypeId" placeholder="请选择财务类型">
            <el-option v-for="item in financeTypeOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结算类型" prop="settlementType">
          <el-radio-group v-model="configForm.settlementType">
            <el-radio :label="1">按时间周期</el-radio>
            <el-radio :label="2">按业务完成情况</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="configForm.settlementType === 1">
          <el-form-item label="周期类型" prop="cyclePeriod">
            <el-select v-model="configForm.cyclePeriod" placeholder="请选择周期类型">
              <el-option :value="1" label="日结"></el-option>
              <el-option :value="2" label="周结"></el-option>
              <el-option :value="3" label="月结"></el-option>
              <el-option :value="4" label="季结"></el-option>
              <el-option :value="5" label="年结"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="结算日" prop="cycleDay">
            <el-input-number v-model="configForm.cycleDay" :min="1" :max="31"></el-input-number>
            <span class="form-tips">月结/季结/年结时生效，指定每月/季/年的第几天进行结算</span>
          </el-form-item>
        </template>
        <template v-if="configForm.settlementType === 2">
          <el-form-item label="业务完成阈值" prop="businessCompleteThreshold">
            <el-input-number v-model="configForm.businessCompleteThreshold" :min="1"></el-input-number>
            <span class="form-tips">例如：达到10单进行结算</span>
          </el-form-item>
        </template>
        <el-form-item label="自动结算" prop="isAutoSettle">
          <el-switch v-model="configForm.isAutoSettle" :active-value="1" :inactive-value="0"></el-switch>
          <span class="form-tips">开启后将根据配置自动执行结算</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitConfig">确定</el-button>
      </div>
    </el-dialog>

    <!-- 手动结算弹窗 -->
    <el-dialog title="手动结算" :visible.sync="settleDialogVisible" width="500px">
      <el-form ref="settleForm" :model="settleForm" :rules="settleRules" label-width="120px">
        <el-form-item label="结算类型" prop="settlementType">
          <el-radio-group v-model="settleForm.settlementType">
            <el-radio :label="1">按时间周期</el-radio>
            <el-radio :label="2">按业务完成情况</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="settleForm.settlementType === 1">
          <el-form-item label="结算周期" prop="periodRange">
            <el-date-picker
              v-model="settleForm.periodRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="timestamp"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="周期描述" prop="periodDesc">
            <el-input v-model="settleForm.periodDesc" placeholder="请输入周期描述"></el-input>
          </el-form-item>
        </template>
        <el-form-item label="业务员" prop="salesManIds">
          <el-input
            v-model="settleForm.salesmanNames"
            placeholder="请选择业务员（不选则结算所有）"
            readonly
            style="width: 100%"
          >
            <el-button slot="append" icon="el-icon-search" @click="showStaffModal"></el-button>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="settleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitSettle">确定</el-button>
      </div>
    </el-dialog>

    <!-- 添加员工选择弹窗 -->
    <staffListModal
      v-if="staffShow"
      :is-show="staffShow"
      :is-check="true"
      @cancel="staffShow = false"
      @confirm="staffSel"
    />
  </ContainerQuery>
</template>

<script>
import { getSettlementConfigList, saveSettlementConfig, updateSettlementConfigStatus } from "@/api/CarSale";
import FooterPage from "@/component/common/FooterPage";
import staffListModal from "@/component/common/staffListModal";
import SelectShop from "@/component/goods/SelectShop";
import ContainerQuery from "@/component/layout/ContainerQuery";
import request from "@/utils/request";

export default {
  name: "SettlementConfig",
  components: {
    ContainerQuery,
    FooterPage,
    SelectShop,
    staffListModal,
  },
  data() {
    return {
      loading: false,
      // 搜索表单
      searchForm: {
        shopId: "",
        status: "",
      },
      // 表格数据
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
      // 配置弹窗
      dialogVisible: false,
      dialogTitle: "新增结算配置",
      configForm: {
        id: null,
        shopId: "",
        financeTypeId: "",
        financeType: "",
        settlementType: 1,
        cyclePeriod: 3,
        cycleDay: 1,
        businessCompleteThreshold: 10,
        isAutoSettle: 1,
      },
      configRules: {
        shopId: [{ required: true, message: "请选择店铺", trigger: "change" }],
        financeTypeId: [{ required: true, message: "请选择财务类型", trigger: "change" }],
        settlementType: [{ required: true, message: "请选择结算类型", trigger: "change" }],
        cyclePeriod: [{ required: true, message: "请选择周期类型", trigger: "change" }],
        cycleDay: [{ required: true, message: "请输入结算日", trigger: "blur" }],
        businessCompleteThreshold: [{ required: true, message: "请输入业务完成阈值", trigger: "blur" }],
      },
      // 手动结算弹窗
      settleDialogVisible: false,
      settleForm: {
        shopId: "",
        settlementType: 1,
        periodRange: [],
        periodStart: 0,
        periodEnd: 0,
        periodDesc: "",
        salesManIds: [],
        salesmanNames: "",
      },
      settleRules: {
        periodRange: [{ required: true, message: "请选择结算周期", trigger: "change" }],
      },
      // 选项数据
      financeTypeOptions: [],
      pickerOptions: {
        // Add any necessary picker options here
      },
      staffShow: false,
    };
  },
  created() {
    this.fetchFinanceTypeOptions();
  },
  methods: {
    // 加载表格数据
    loadTableData() {
      this.loading = true;
      const params = {
        shopId: this.searchForm.shopId,
        status: this.searchForm.status,
        page: this.page,
        pageSize: this.pageSize,
      };

      getSettlementConfigList(params)
        .then((response) => {
          if (response.state) {
            // 处理表格数据，添加文本显示
            this.tableData = response.data.map((item) => {
              return {
                ...item,
                settlementTypeText: item.settlementType === 1 ? "按时间周期" : "按业务完成",
                cyclePeriodText: this.getCyclePeriodText(item.cyclePeriod),
                isAutoSettleText: item.isAutoSettle === 1 ? "是" : "否",
                statusText: item.status === 1 ? "启用" : "停用",
                settleTimeText: item.settleTime ? this.formatDate(item.settleTime * 1000) : "--",
              };
            });
            this.total = response.pageTotal || this.tableData.length;
          } else {
            this.$message.error(response.data || "获取结算配置失败");
          }
        })
        .catch((error) => {
          console.error("获取结算配置失败", error);
          this.$message.error("网络错误，请稍后重试");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取财务类型选项
    fetchFinanceTypeOptions() {
      // 模拟数据，实际项目中应该通过API获取
      this.financeTypeOptions = [{ value: 50, label: "车载销售业务员结算" }];
    },

    // 获取周期类型文本
    getCyclePeriodText(cyclePeriod) {
      const map = {
        1: "日结",
        2: "周结",
        3: "月结",
        4: "季结",
        5: "年结",
      };
      return map[cyclePeriod] || "未知";
    },

    // 格式化日期
    formatDate(timestamp) {
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(
        2,
        "0"
      )} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}:${String(
        date.getSeconds()
      ).padStart(2, "0")}`;
    },

    // 分页查询
    pageChange(val) {
      if (typeof val === "number") {
        this.page = val;
      }
      this.loadTableData();
    },

    // 重置搜索条件
    resetSearch() {
      this.searchForm = {
        shopId: "",
        status: "",
      };
      this.page = 1;
      this.loadTableData();
    },

    // 新增配置
    handleAddConfig() {
      this.dialogTitle = "新增结算配置";
      this.configForm = {
        id: null,
        shopId: "",
        financeTypeId: "",
        financeType: "",
        settlementType: 1,
        cyclePeriod: 3,
        cycleDay: 1,
        businessCompleteThreshold: 10,
        isAutoSettle: 1,
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.configForm && this.$refs.configForm.clearValidate();
      });
    },

    // 编辑配置
    handleEdit(row) {
      this.dialogTitle = "编辑结算配置";
      this.configForm = { ...row };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.configForm && this.$refs.configForm.clearValidate();
      });
    },

    // 切换状态
    handleToggleStatus(row) {
      const status = row.status;
      const statusText = status === 5 ? "启用" : "停用";

      this.$confirm(`确定要${statusText}该结算配置吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updateSettlementConfigStatus(row.id, status)
            .then((response) => {
              if (response.state) {
                this.$message.success(`${statusText}成功`);
                // 状态已在前端直接更新，无需再修改
                row.statusText = status === 5 ? "启用" : "停用";
              } else {
                // 操作失败，恢复原状态
                row.status = status === 5 ? 4 : 5;
                this.$message.error(response.data || `${statusText}失败`);
              }
            })
            .catch((error) => {
              // 操作失败，恢复原状态
              row.status = status === 1 ? 0 : 1;
              console.error(`${statusText}失败`, error);
              this.$message.error("网络错误，请稍后重试");
            });
        })
        .catch(() => {
          // 用户取消操作，恢复原状态
          row.status = status === 1 ? 0 : 1;
        });
    },

    // 手动结算
    handleManualSettle(row) {
      this.settleForm = {
        shopId: row.shopId,
        settlementType: row.settlementType,
        periodRange: [],
        periodStart: 0,
        periodEnd: 0,
        periodDesc: "",
        salesManIds: [],
        salesmanNames: "",
      };
      this.settleDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.settleForm && this.$refs.settleForm.clearValidate();
      });
    },

    // 提交配置
    handleSubmitConfig() {
      this.$refs.configForm.validate((valid) => {
        if (valid) {
          const formData = { ...this.configForm };

          // 根据结算类型设置必要字段
          if (formData.settlementType === 1) {
            delete formData.businessCompleteThreshold;
          } else {
            delete formData.cyclePeriod;
            delete formData.cycleDay;
          }

          // 根据财务类型ID获取财务类型名称
          const financeType = this.financeTypeOptions.find((item) => item.value === formData.financeTypeId);
          if (financeType) {
            formData.financeType = financeType.label;
          }

          this.loading = true;

          saveSettlementConfig(formData)
            .then((response) => {
              if (response.state) {
                this.$message.success("保存成功");
                this.dialogVisible = false;
                this.loadTableData();
              } else {
                this.$message.error(response.data || "保存失败");
              }
            })
            .catch((error) => {
              console.error("保存失败", error);
              this.$message.error("网络错误，请稍后重试");
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    // 提交结算
    handleSubmitSettle() {
      this.$refs.settleForm.validate((valid) => {
        if (valid) {
          const formData = { ...this.settleForm };

          // 处理日期范围
          if (formData.settlementType === 1 && formData.periodRange && formData.periodRange.length === 2) {
            formData.periodStart = Math.floor(formData.periodRange[0] / 1000);
            formData.periodEnd = Math.floor(formData.periodRange[1] / 1000);
          }
          delete formData.periodRange;

          this.loading = true;

          // 根据结算类型调用不同的接口
          const url = formData.configId
            ? "/CarSale/StaffSettlement/settleByConfigId"
            : formData.settlementType === 1
            ? "/CarSale/StaffSettlement/settleByTimePeriod"
            : "/CarSale/StaffSettlement/settleByBusinessComplete";

          request({
            url,
            method: "post",
            data: formData,
          })
            .then((response) => {
              if (response.state) {
                this.$message.success(`结算成功，生成了${response.data.settlementCount || 0}条应付单`);
                this.settleDialogVisible = false;
                this.loadTableData();
              } else {
                this.$message.error(response.data || "结算失败");
              }
            })
            .catch((error) => {
              console.error("结算失败", error);
              this.$message.error("网络错误，请稍后重试");
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    // 清除店铺选择
    clearShop() {
      this.searchForm.shopId = "";
      this.pageChange(1);
    },

    // 默认店铺选择
    defaultShop(val, row) {
      this.searchForm.shopId = val;
      this.pageChange(1);
    },

    // 显示员工选择弹窗
    showStaffModal() {
      this.staffShow = true;
    },

    // 员工选择回调
    staffSel(selectedStaff) {
      if (selectedStaff && selectedStaff.length > 0) {
        this.settleForm.salesManIds = selectedStaff.map((staff) => staff.id);
        this.settleForm.salesmanNames = selectedStaff.map((staff) => staff.staffName).join("、");
      } else {
        this.settleForm.salesManIds = [];
        this.settleForm.salesmanNames = "";
      }
      this.staffShow = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.settlement-config-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-area {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.table-area {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.form-tips {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.success-btn {
  color: #67c23a;
}

.el-button + .el-button {
  margin-left: 8px;
}

.el-button[type="text"] {
  padding: 2px 0;
}
</style>
