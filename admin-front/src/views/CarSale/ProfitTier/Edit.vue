<template>
  <ContainerTit>
    <span slot="pagetit">{{ isEdit ? "编辑分成层级" : "添加分成层级" }}</span>
    <div slot="headr">
      <el-button type="primary" @click="submitForm('tierForm')">保存</el-button>
      <el-button @click="goBack">取消</el-button>
    </div>

    <div class="page-div">
      <el-form ref="tierForm" v-loading="loading" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="层级名称" prop="tierName">
              <el-input v-model="form.tierName" placeholder="请输入层级名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所有权模式" prop="ownershipMode">
              <el-radio-group v-model="form.ownershipMode">
                <el-radio :label="1">公司所有</el-radio>
                <el-radio :label="2">业务员所有</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">分成比例设置（总和必须为100%）</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="公司比例(%)" prop="companyRatio">
              <el-input-number
                v-model="form.companyRatio"
                :min="0"
                :max="100"
                :precision="2"
                style="width: 100%"
                @change="calculateTotal"
              >
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门比例(%)" prop="departmentRatio">
              <el-input-number
                v-model="form.departmentRatio"
                :min="0"
                :max="100"
                :precision="2"
                style="width: 100%"
                @change="calculateTotal"
              >
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务员比例(%)" prop="salesmanRatio">
              <el-input-number
                v-model="form.salesmanRatio"
                :min="0"
                :max="100"
                :precision="2"
                style="width: 100%"
                @change="calculateTotal"
              >
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 比例总计显示 -->
        <el-row>
          <el-col :span="24">
            <div class="ratio-total" :class="{ 'ratio-error': totalRatio !== 100 }">
              分成比例总计：{{ totalRatio }}% {{ totalRatio !== 100 ? "(必须等于100%)" : "" }}
            </div>
          </el-col>
        </el-row>

        <!-- 分配比例可视化 -->
        <el-row>
          <el-col :span="24">
            <div class="distribution-preview">
              <div v-if="form.companyRatio > 0" class="bar-item company" :style="{ width: form.companyRatio + '%' }">
                公司: {{ form.companyRatio }}%
              </div>
              <div
                v-if="form.departmentRatio > 0"
                class="bar-item department"
                :style="{ width: form.departmentRatio + '%' }"
              >
                部门: {{ form.departmentRatio }}%
              </div>
              <div v-if="form.salesmanRatio > 0" class="bar-item salesman" :style="{ width: form.salesmanRatio + '%' }">
                业务员: {{ form.salesmanRatio }}%
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 部门员工分成设置（当部门比例大于0时显示） -->
        <div v-if="form.departmentRatio > 0">
          <el-divider content-position="center">部门员工分成设置</el-divider>

          <el-row>
            <el-col :span="24">
              <el-form-item label="部门员工分成" prop="departmentStaffRatio">
                <div class="staff-ratio-list">
                  <div class="staff-ratio-header">
                    <el-button type="primary" size="small" icon="el-icon-plus" @click="showStaffSelector"
                      >选择员工</el-button
                    >
                    <el-button
                      v-if="staffRatioList.length > 0"
                      type="success"
                      size="small"
                      icon="el-icon-refresh"
                      style="margin-left: 10px"
                      @click="distributeEvenlyRatio"
                      >均分比例</el-button
                    >
                  </div>
                  <div v-for="(item, index) in staffRatioList" :key="index" class="staff-ratio-item">
                    <el-input
                      v-model="item.staffName"
                      placeholder="员工姓名"
                      style="width: 150px; margin-right: 10px"
                      disabled
                    >
                    </el-input>
                    <el-input-number
                      v-model="item.ratio"
                      :min="0"
                      :max="100"
                      :precision="2"
                      placeholder="分成比例(%)"
                      style="width: 150px; margin-right: 10px"
                      @change="updateStaffRatio"
                    >
                    </el-input-number>
                    <el-button
                      type="danger"
                      icon="el-icon-delete"
                      circle
                      size="mini"
                      @click="removeStaffRatio(index)"
                    ></el-button>
                  </div>

                  <div class="staff-ratio-total" :class="{ 'ratio-error': staffRatioTotal !== 100 }">
                    员工分成总计：{{ staffRatioTotal }}% {{ staffRatioTotal !== 100 ? "(必须等于100%)" : "" }}
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="form.remarks" type="textarea" placeholder="请输入备注信息" :rows="3"> </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 员工选择弹窗 -->
    <staff-list-modal
      :is-show="staffSelectorVisible"
      :is-check="true"
      @confirm="handleStaffSelected"
      @cancel="staffSelectorVisible = false"
    />
  </ContainerTit>
</template>

<script>
import { addProfitTier, getProfitTierInfo, updateProfitTier } from "@/api/CarSale";
import { getAllStaff } from "@/api/Department";
import StaffListModal from "@/component/common/staffListModal";
import ContainerTit from "@/component/layout/ContainerTit";

export default {
  name: "ProfitTierEdit",
  components: {
    ContainerTit,
    StaffListModal,
  },
  data() {
    // 验证分配比例总和
    const validateRatioTotal = (rule, value, callback) => {
      const total = this.form.companyRatio + this.form.departmentRatio + this.form.salesmanRatio;
      if (Math.abs(total - 100) > 0.01) {
        callback(new Error("分配比例总和必须为100%"));
      } else {
        callback();
      }
    };

    // 验证部门员工分成比例
    const validateStaffRatio = (rule, value, callback) => {
      if (this.form.departmentRatio <= 0) {
        callback();
        return;
      }

      if (this.staffRatioTotal !== 100) {
        callback(new Error("部门员工分成比例总和必须为100%"));
      } else {
        callback();
      }
    };

    return {
      isEdit: false,
      loading: false,
      tierId: null,
      staffSelectorVisible: false,
      form: {
        tierName: "",
        ownershipMode: 1,
        companyRatio: 70,
        departmentRatio: 0,
        salesmanRatio: 30,
        departmentStaffRatio: {},
        remarks: "",
      },
      staffRatioList: [],
      rules: {
        tierName: [
          { required: true, message: "请输入层级名称", trigger: "blur" },
          { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" },
        ],
        ownershipMode: [{ required: true, message: "请选择所有权模式", trigger: "change" }],
        companyRatio: [
          { required: true, message: "请输入公司比例", trigger: "blur" },
          { validator: validateRatioTotal, trigger: "blur" },
        ],
        departmentRatio: [
          { required: true, message: "请输入部门比例", trigger: "blur" },
          { validator: validateRatioTotal, trigger: "blur" },
        ],
        salesmanRatio: [
          { required: true, message: "请输入业务员比例", trigger: "blur" },
          { validator: validateRatioTotal, trigger: "blur" },
        ],
        departmentStaffRatio: [{ validator: validateStaffRatio, trigger: "blur" }],
      },
    };
  },
  computed: {
    // 计算分成比例总和
    totalRatio() {
      return parseFloat((this.form.companyRatio + this.form.departmentRatio + this.form.salesmanRatio).toFixed(2));
    },
    // 计算员工分成比例总和
    staffRatioTotal() {
      return parseFloat(this.staffRatioList.reduce((total, item) => total + (item.ratio || 0), 0).toFixed(2));
    },
  },
  created() {
    // 检查权限
    if (this.isEdit && !this.$accessCheck(this.$Access.ProfitTier_Edit)) {
      this.$message.error("您没有编辑分成层级的权限");
      this.goBack();
      return;
    }

    if (!this.isEdit && !this.$accessCheck(this.$Access.ProfitTier_Add)) {
      this.$message.error("您没有添加分成层级的权限");
      this.goBack();
      return;
    }

    // 判断是否是编辑模式
    if (this.$route.params.id) {
      this.isEdit = true;
      this.tierId = this.$route.params.id;
      this.getTierDetail();
    }
  },
  methods: {
    // 获取利润分成等级详情
    async getTierDetail() {
      if (!this.tierId) return;

      this.loading = true;
      try {
        const res = await getProfitTierInfo(this.tierId);
        if (res.errorcode === 0) {
          const data = res.data;

          // 设置表单数据
          this.form = {
            tierName: data.tierName || "",
            ownershipMode: data.ownershipMode || 1,
            companyRatio: parseFloat(data.companyRatio) || 0,
            departmentRatio: parseFloat(data.departmentRatio) || 0,
            salesmanRatio: parseFloat(data.salesmanRatio) || 0,
            remarks: data.remarks || "",
          };

          // 处理部门员工分成数据
          if (data.departmentStaffRatio) {
            let staffRatio = data.departmentStaffRatio;
            if (typeof staffRatio === "string") {
              try {
                staffRatio = JSON.parse(staffRatio);
              } catch (e) {
                console.error("解析departmentStaffRatio失败", e);
                staffRatio = {};
              }
            }

            this.form.departmentStaffRatio = staffRatio;

            // 获取员工信息并设置员工列表
            await this.initializeStaffList(staffRatio);
          }
        } else {
          this.$message.error(res.message || "获取利润分成等级详情失败");
          this.goBack();
        }
      } catch (error) {
        console.error("获取利润分成等级详情出错", error);
        this.$message.error("获取利润分成等级详情失败");
        this.goBack();
      } finally {
        this.loading = false;
      }
    },

    // 初始化员工列表，获取员工姓名
    async initializeStaffList(staffRatio) {
      try {
        // 获取所有员工信息
        const response = await getAllStaff({
          page: 1,
          pageSize: 1000, // 设置一个较大的数值以获取所有员工
          deleteStatus: 5, // 使用活跃状态
        });

        if (response && response.data) {
          const staffData = response.data;
          const staffMap = {};

          // 创建员工ID到员工姓名的映射
          staffData.forEach((staff) => {
            staffMap[staff.id] = staff.staffName;
          });

          // 填充员工列表
          const staffList = [];
          for (const [staffId, ratio] of Object.entries(staffRatio)) {
            staffList.push({
              staffId: staffId,
              staffName: staffMap[staffId] || `员工(ID: ${staffId})`, // 使用员工姓名，如果找不到则使用ID
              ratio: ratio,
            });
          }
          this.staffRatioList = staffList;
        }
      } catch (error) {
        console.error("获取员工信息失败", error);
        // 回退到只使用ID
        const staffList = [];
        for (const [staffId, ratio] of Object.entries(staffRatio)) {
          staffList.push({
            staffId: staffId,
            staffName: `员工(ID: ${staffId})`,
            ratio: ratio,
          });
        }
        this.staffRatioList = staffList;
      }
    },

    // 显示员工选择器
    showStaffSelector() {
      this.staffSelectorVisible = true;
    },

    // 处理员工选择结果
    handleStaffSelected(selectedStaff) {
      // 检查已选员工，避免重复添加
      const existingIds = this.staffRatioList.map((item) => item.staffId);

      // 过滤掉已存在的员工
      const newStaff = selectedStaff.filter((staff) => !existingIds.includes(staff.id));

      // 为新选择的员工添加分成比例项
      const staffToAdd = newStaff.map((staff) => ({
        staffId: staff.id,
        staffName: staff.staffName,
        ratio: 0,
      }));

      // 合并到现有员工列表
      this.staffRatioList = [...this.staffRatioList, ...staffToAdd];

      // 更新员工比例数据
      this.updateStaffRatio();

      // 关闭弹窗
      this.staffSelectorVisible = false;
    },

    // 计算总和
    calculateTotal() {
      // 使用computed属性totalRatio自动计算
    },

    // 均分员工比例
    distributeEvenlyRatio() {
      if (this.staffRatioList.length === 0) return;

      const count = this.staffRatioList.length;
      const evenRatio = parseFloat((100 / count).toFixed(2));

      // 分配均等比例给每个员工
      this.staffRatioList.forEach((item, index) => {
        // 最后一个员工取剩余比例，确保总和为100%
        if (index === count - 1) {
          const sumOthers = this.staffRatioList.slice(0, count - 1).reduce((sum, i) => sum + i.ratio, 0);
          item.ratio = parseFloat((100 - sumOthers).toFixed(2));
        } else {
          item.ratio = evenRatio;
        }
      });

      // 更新员工比例数据
      this.updateStaffRatio();

      this.$message.success(`已将比例均分给${count}名员工`);
    },

    // 移除员工分成
    removeStaffRatio(index) {
      this.staffRatioList.splice(index, 1);
      this.updateStaffRatio();
    },

    // 更新员工分成数据
    updateStaffRatio() {
      const staffRatio = {};
      this.staffRatioList.forEach((item) => {
        if (item.staffId) {
          staffRatio[item.staffId] = item.ratio;
        }
      });
      this.form.departmentStaffRatio = staffRatio;
    },

    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (!valid) {
          return false;
        }

        // 验证比例总和
        if (Math.abs(this.totalRatio - 100) > 0.01) {
          this.$message.error("分成比例总和必须为100%");
          return false;
        }

        // 如果有部门分成，验证员工分成
        if (this.form.departmentRatio > 0 && Math.abs(this.staffRatioTotal - 100) > 0.01) {
          this.$message.error("部门员工分成比例总和必须为100%");
          return false;
        }

        this.loading = true;

        try {
          const formData = {
            tierName: this.form.tierName,
            ownershipMode: this.form.ownershipMode,
            companyRatio: this.form.companyRatio,
            departmentRatio: this.form.departmentRatio,
            salesmanRatio: this.form.salesmanRatio,
            remarks: this.form.remarks,
          };

          // 处理部门员工分成
          if (this.form.departmentRatio > 0) {
            formData.departmentStaffRatio = this.form.departmentStaffRatio;
          }

          let res;
          if (this.isEdit) {
            res = await updateProfitTier(this.tierId, formData);
          } else {
            res = await addProfitTier(formData);
          }

          if (res.errorcode === 0) {
            this.$message.success(this.isEdit ? "更新成功" : "添加成功");
            this.goBack();
          } else {
            this.$message.error(res.message || (this.isEdit ? "更新失败" : "添加失败"));
          }
        } catch (error) {
          console.error(this.isEdit ? "更新利润分成等级出错" : "添加利润分成等级出错", error);
          this.$message.error(this.isEdit ? "更新失败" : "添加失败");
        } finally {
          this.loading = false;
        }
      });
    },

    // 返回列表页
    goBack() {
      this.$router.push({ name: "ProfitTierList" });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-div {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  margin-top: 4px;
}

.ratio-total {
  margin: 10px 0;
  padding: 8px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 4px;
  font-weight: bold;
  text-align: center;
}

.ratio-error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.distribution-preview {
  display: flex;
  height: 30px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bar-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  color: white;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.company {
  background-color: #409eff;
}

.department {
  background-color: #67c23a;
}

.salesman {
  background-color: #e6a23c;
}

.staff-ratio-list {
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.staff-ratio-header {
  margin-bottom: 15px;
}

.staff-ratio-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.staff-ratio-total {
  margin-top: 15px;
  padding: 8px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 4px;
  font-weight: bold;
}
</style>
