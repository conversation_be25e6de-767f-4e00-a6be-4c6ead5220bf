<template>
  <ContainerQuery>
    <div slot="left">
      <el-button v-if="$accessCheck($Access.ProfitTier_Add)" type="primary" size="small" @click="handleAddTier"
        >添加分成层级</el-button
      >
    </div>
    <!-- 搜索表单 -->
    <div slot="more">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="层级名称">
          <el-input v-model="searchForm.tierName" placeholder="层级名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="所有权模式">
          <el-select v-model="searchForm.ownershipMode" placeholder="请选择" clearable>
            <el-option label="公司所有" :value="1"></el-option>
            <el-option label="业务员所有" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 层级列表表格 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column type="index" width="50" label="序号"></el-table-column>
      <el-table-column prop="tierName" label="层级名称" min-width="120"></el-table-column>
      <el-table-column label="所有权模式" min-width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.ownershipMode === 1" type="success">公司所有</el-tag>
          <el-tag v-if="scope.row.ownershipMode === 2" type="warning">业务员所有</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="分成比例" min-width="280">
        <template slot-scope="scope">
          <div class="distribution-bar">
            <div
              v-if="scope.row.companyRatio > 0"
              class="bar-item company"
              :style="{ width: scope.row.companyRatio + '%' }"
            >
              公司: {{ scope.row.companyRatio }}%
            </div>
            <div
              v-if="scope.row.departmentRatio > 0"
              class="bar-item department"
              :style="{ width: scope.row.departmentRatio + '%' }"
            >
              部门: {{ scope.row.departmentRatio }}%
            </div>
            <div
              v-if="scope.row.salesmanRatio > 0"
              class="bar-item salesman"
              :style="{ width: scope.row.salesmanRatio + '%' }"
            >
              业务员: {{ scope.row.salesmanRatio }}%
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="remarks" label="备注" min-width="120">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.remarks" :content="scope.row.remarks" placement="top">
            <span>{{
              scope.row.remarks.length > 20 ? scope.row.remarks.substring(0, 20) + "..." : scope.row.remarks
            }}</span>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="140">
        <template slot-scope="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="180">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.ProfitTier_Edit)"
            size="mini"
            type="text"
            @click="handleEditTier(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-if="$accessCheck($Access.ProfitTier_Delete)"
            size="mini"
            type="text"
            @click="handleDeleteTier(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </ContainerQuery>
</template>

<script>
import { deleteProfitTier, getAllProfitTier } from "@/api/CarSale";
import ContainerQuery from "@/component/layout/ContainerQuery";

export default {
  name: "ProfitTierList",
  components: {
    ContainerQuery,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      searchForm: {
        tierName: "",
        ownershipMode: "",
      },
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const params = {
          page: this.page,
          pageSize: this.pageSize,
          tierName: this.searchForm.tierName,
          ownershipMode: this.searchForm.ownershipMode,
        };

        const res = await getAllProfitTier(params);
        if (res.errorcode === 0) {
          this.tableData = res.data || [];
          this.total = res.pageTotal || 0;

          // 格式化数据
          this.tableData.forEach((item) => {
            // 确保比例字段存在且是数值
            ["companyRatio", "salesmanRatio", "departmentRatio"].forEach((field) => {
              if (item[field] === undefined || item[field] === null) {
                item[field] = 0;
              } else {
                item[field] = parseFloat(item[field]);
              }
            });

            // 处理departmentStaffRatio
            if (item.departmentStaffRatio && typeof item.departmentStaffRatio === "string") {
              try {
                item.departmentStaffRatio = JSON.parse(item.departmentStaffRatio);
              } catch (e) {
                console.error("解析departmentStaffRatio失败", e);
                item.departmentStaffRatio = {};
              }
            }
          });
        } else {
          this.$message.error(res.message || "获取分成层级列表失败");
        }
      } catch (error) {
        console.error("获取分成层级列表出错", error);
        this.$message.error("获取分成层级列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.page = 1;
      this.fetchData();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        tierName: "",
        ownershipMode: "",
      };
      this.handleSearch();
    },

    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchData();
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.page = val;
      this.fetchData();
    },

    // 添加分成层级
    handleAddTier() {
      this.$router.push({ name: "ProfitTierAdd" });
    },

    // 编辑分成层级
    handleEditTier(row) {
      this.$router.push({ name: "ProfitTierEdit", params: { id: row.id } });
    },

    // 删除分成层级
    handleDeleteTier(row) {
      this.$confirm("确认删除该分成层级?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const res = await deleteProfitTier(row.id);
            if (res.errorcode === 0) {
              this.$message.success("删除成功");
              this.fetchData();
            } else {
              this.$message.error(res.message || "删除失败");
            }
          } catch (error) {
            console.error("删除分成层级出错", error);
            this.$message.error("删除失败");
          }
        })
        .catch(() => {});
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return "-";
      // 检查时间戳格式
      if (typeof timestamp === "number") {
        // Unix时间戳 (秒)
        return new Date(timestamp * 1000).toLocaleString();
      } else {
        // 已格式化的日期字符串
        return timestamp;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.distribution-bar {
  display: flex;
  width: 100%;
  height: 24px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bar-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  color: white;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.company {
  background-color: #409eff;
}

.department {
  background-color: #67c23a;
}

.salesman {
  background-color: #e6a23c;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
