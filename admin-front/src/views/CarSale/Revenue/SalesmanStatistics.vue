<template>
  <div class="salesman-statistics-container">
    <ContainerQuery>
      <div slot="left">
        <span class="title">销售员业绩统计</span>
        <div style="display: inline-block; margin-left: 20px">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            style="width: 350px"
            @change="handleDateChange"
          >
          </el-date-picker>
          <el-button type="primary" size="small" style="margin-left: 10px" @click="fetchData">查询</el-button>
          <el-button size="small" @click="goBack">返回</el-button>
        </div>
      </div>

      <div v-loading="loading">
        <!-- 业绩总览 -->
        <el-row :gutter="20" class="data-overview">
          <el-col :span="6">
            <div class="data-card total-salesman">
              <div class="data-title">销售员总数</div>
              <div class="data-value">{{ overviewData.totalSalesman }} 人</div>
              <div class="data-desc">全部销售人员</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="data-card has-sales">
              <div class="data-title">有销售业绩</div>
              <div class="data-value">{{ overviewData.activeSalesman }} 人</div>
              <div class="data-desc">活跃率 {{ overviewData.activeRate }}%</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="data-card avg-sales">
              <div class="data-title">人均销量</div>
              <div class="data-value">{{ overviewData.averageSales }} 台</div>
              <div class="data-desc">人均销售额 ¥{{ formatNumber(overviewData.averageAmount) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="data-card avg-profit">
              <div class="data-title">人均创利</div>
              <div class="data-value">¥ {{ formatNumber(overviewData.averageProfit) }}</div>
              <div class="data-desc">人均分成 ¥{{ formatNumber(overviewData.averageShare) }}</div>
            </div>
          </el-col>
        </el-row>

        <!-- 销售员销量排行榜 -->
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="24">
            <el-card shadow="hover">
              <div slot="header">
                <span>销售员业绩排行</span>
                <el-radio-group v-model="rankType" size="mini" style="float: right" @change="switchRankData">
                  <el-radio-button label="count">按销量</el-radio-button>
                  <el-radio-button label="amount">按销售额</el-radio-button>
                  <el-radio-button label="profit">按利润</el-radio-button>
                </el-radio-group>
              </div>
              <div class="chart-container">
                <div id="salesmanRankChart" style="width: 100%; height: 400px"></div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 销售员详细数据表格 -->
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="24">
            <el-card shadow="hover">
              <div slot="header">销售员业绩明细</div>
              <el-table :data="tableData" border style="width: 100%">
                <el-table-column type="index" width="50" label="排名"></el-table-column>
                <el-table-column prop="name" label="销售员" min-width="100"></el-table-column>
                <el-table-column prop="department" label="所属部门" min-width="120"></el-table-column>
                <el-table-column label="销售数量" min-width="100" sortable>
                  <template slot-scope="scope">
                    {{ scope.row.count }} 台
                    <el-progress
                      :percentage="calculatePercentage(scope.row.count, maxValues.count)"
                      :show-text="false"
                      :stroke-width="10"
                      color="#409EFF"
                    >
                    </el-progress>
                  </template>
                </el-table-column>
                <el-table-column label="销售金额" min-width="140" sortable>
                  <template slot-scope="scope">
                    <span class="amount">¥ {{ formatNumber(scope.row.amount) }}</span>
                    <el-progress
                      :percentage="calculatePercentage(scope.row.amount, maxValues.amount)"
                      :show-text="false"
                      :stroke-width="10"
                      color="#67C23A"
                    >
                    </el-progress>
                  </template>
                </el-table-column>
                <el-table-column label="创造利润" min-width="140" sortable>
                  <template slot-scope="scope">
                    <span class="profit">¥ {{ formatNumber(scope.row.profit) }}</span>
                    <el-progress
                      :percentage="calculatePercentage(scope.row.profit, maxValues.profit)"
                      :show-text="false"
                      :stroke-width="10"
                      color="#E6A23C"
                    >
                    </el-progress>
                  </template>
                </el-table-column>
                <el-table-column label="获得分成" min-width="140" sortable>
                  <template slot-scope="scope">
                    <span class="share">¥ {{ formatNumber(scope.row.share) }}</span>
                    <el-progress
                      :percentage="calculatePercentage(scope.row.share, maxValues.share)"
                      :show-text="false"
                      :stroke-width="10"
                      color="#F56C6C"
                    >
                    </el-progress>
                  </template>
                </el-table-column>
                <el-table-column prop="lastSaleDate" label="最近销售" min-width="120"></el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="viewSalesmanDetail(scope.row)">查看明细</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </ContainerQuery>

    <!-- 销售员明细弹窗 -->
    <el-dialog :title="currentSalesman.name + ' 的销售明细'" :visible.sync="detailDialogVisible" width="80%">
      <div v-loading="detailLoading">
        <el-table :data="salesmanDetailData" border style="width: 100%">
          <el-table-column prop="orderId" label="订单编号" min-width="120"></el-table-column>
          <el-table-column label="车辆信息" min-width="200">
            <template slot-scope="scope">
              {{ scope.row.vehicleBrand }} {{ scope.row.vehicleModel }}
              <el-tag size="mini">{{ scope.row.vehicleVin }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="customerName" label="客户" min-width="100"></el-table-column>
          <el-table-column label="销售价格" min-width="120">
            <template slot-scope="scope">
              <span class="price">{{ scope.row.salePrice }} 元</span>
            </template>
          </el-table-column>
          <el-table-column label="净利润" min-width="120">
            <template slot-scope="scope">
              <span class="price">{{ scope.row.netProfit }} 元</span>
            </template>
          </el-table-column>
          <el-table-column label="销售员分成" min-width="120">
            <template slot-scope="scope">
              <span class="price">{{ scope.row.salesmanShare }} 元</span>
            </template>
          </el-table-column>
          <el-table-column prop="saleDate" label="销售日期" min-width="120"></el-table-column>
          <el-table-column label="结算状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.settlementStatus === 0 ? 'danger' : 'success'">
                {{ scope.row.settlementStatus === 0 ? "未结算" : "已结算" }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <div class="dialog-footer" style="margin-top: 20px; text-align: right">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSalesmanDetail, getSalesmanStatistics } from "@/api/CarSale";
import ContainerQuery from "@/component/layout/ContainerQuery";
import * as echarts from "echarts";

export default {
  name: "SalesmanStatistics",
  components: {
    ContainerQuery,
  },
  data() {
    return {
      loading: false,
      detailLoading: false,
      dateRange: [this.getStartOfMonth(), new Date().toISOString().slice(0, 10)],
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 1);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 3);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      rankType: "count",
      salesmanRankChart: null,
      overviewData: {
        totalSalesman: 0,
        activeSalesman: 0,
        activeRate: 0,
        averageSales: 0,
        averageAmount: 0,
        averageProfit: 0,
        averageShare: 0,
      },
      tableData: [],
      maxValues: {
        count: 1,
        amount: 1,
        profit: 1,
        share: 1,
      },
      detailDialogVisible: false,
      currentSalesman: {},
      salesmanDetailData: [],
    };
  },
  mounted() {
    this.fetchData();
    window.addEventListener("resize", this.resizeCharts);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.resizeCharts);
    this.disposeCharts();
  },
  methods: {
    // 获取当月第一天
    getStartOfMonth() {
      const date = new Date();
      date.setDate(1);
      return date.toISOString().slice(0, 10);
    },

    // 格式化数字，添加千位分隔符
    formatNumber(num) {
      if (!num && num !== 0) return "0";
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },

    // 日期范围变化处理
    handleDateChange() {
      // 日期变化时可以自动重新获取数据，也可以等用户点击查询按钮
    },

    // 切换排行榜数据类型
    switchRankData() {
      this.renderRankChart();
    },

    // 计算进度条百分比
    calculatePercentage(value, max) {
      if (!value || !max || max === 0) return 0;
      return (value / max) * 100;
    },

    // 获取统计数据
    async fetchData() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$message.warning("请选择日期范围");
        return;
      }

      this.loading = true;
      try {
        const params = {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1],
        };

        const res = await getSalesmanStatistics(params);
        if (res.code === 0 && res.data) {
          // 处理概览数据
          this.overviewData = res.data.overview || this.getDefaultOverviewData();

          // 处理销售员明细数据
          this.tableData = res.data.salesmanList || [];

          // 计算最大值用于进度条
          this.calculateMaxValues();

          // 渲染图表
          this.$nextTick(() => {
            this.initRankChart();
          });
        } else {
          this.$message.error(res.message || "获取销售员统计数据失败");
          // 使用默认数据渲染图表
          this.useDefaultData();
        }
      } catch (error) {
        console.error("获取销售员统计数据出错", error);
        this.$message.error("获取销售员统计数据失败");
        // 使用默认数据渲染图表
        this.useDefaultData();
      } finally {
        this.loading = false;
      }
    },

    // 计算最大值用于进度条
    calculateMaxValues() {
      if (!this.tableData || this.tableData.length === 0) return;

      this.maxValues = {
        count: Math.max(...this.tableData.map((item) => item.count || 0)),
        amount: Math.max(...this.tableData.map((item) => item.amount || 0)),
        profit: Math.max(...this.tableData.map((item) => item.profit || 0)),
        share: Math.max(...this.tableData.map((item) => item.share || 0)),
      };

      // 防止0除以0的情况
      for (const key in this.maxValues) {
        if (this.maxValues[key] === 0) this.maxValues[key] = 1;
      }
    },

    // 初始化默认数据用于演示
    getDefaultOverviewData() {
      return {
        totalSalesman: 12,
        activeSalesman: 8,
        activeRate: 66.67,
        averageSales: 4,
        averageAmount: 606250,
        averageProfit: 121250,
        averageShare: 24250,
      };
    },

    // 使用默认数据
    useDefaultData() {
      // 默认概览数据
      this.overviewData = this.getDefaultOverviewData();

      // 默认销售员明细数据
      this.tableData = [
        {
          id: 1,
          name: "张三",
          department: "一部",
          count: 8,
          amount: 1280000,
          profit: 256000,
          share: 51200,
          lastSaleDate: "2023-06-20",
        },
        {
          id: 2,
          name: "李四",
          department: "二部",
          count: 7,
          amount: 1050000,
          profit: 210000,
          share: 42000,
          lastSaleDate: "2023-06-18",
        },
        {
          id: 3,
          name: "王五",
          department: "一部",
          count: 6,
          amount: 960000,
          profit: 192000,
          share: 38400,
          lastSaleDate: "2023-06-15",
        },
        {
          id: 4,
          name: "赵六",
          department: "三部",
          count: 5,
          amount: 700000,
          profit: 140000,
          share: 28000,
          lastSaleDate: "2023-06-10",
        },
        {
          id: 5,
          name: "钱七",
          department: "二部",
          count: 4,
          amount: 560000,
          profit: 112000,
          share: 22400,
          lastSaleDate: "2023-06-08",
        },
        {
          id: 6,
          name: "孙八",
          department: "三部",
          count: 2,
          amount: 300000,
          profit: 60000,
          share: 12000,
          lastSaleDate: "2023-05-28",
        },
        { id: 7, name: "周九", department: "一部", count: 0, amount: 0, profit: 0, share: 0, lastSaleDate: "-" },
        { id: 8, name: "吴十", department: "二部", count: 0, amount: 0, profit: 0, share: 0, lastSaleDate: "-" },
      ];

      // 计算最大值用于进度条
      this.calculateMaxValues();

      // 初始化图表
      this.$nextTick(() => {
        this.initRankChart();
      });
    },

    // 初始化排行榜图表
    initRankChart() {
      if (this.salesmanRankChart) {
        this.salesmanRankChart.dispose();
      }

      this.salesmanRankChart = echarts.init(document.getElementById("salesmanRankChart"));
      this.renderRankChart();
    },

    // 渲染排行榜图表
    renderRankChart() {
      if (!this.salesmanRankChart) return;

      // 按照当前类型对数据进行排序
      const sortedData = [...this.tableData]
        .filter((item) => item[this.rankType] > 0) // 只显示有数据的销售员
        .sort((a, b) => b[this.rankType] - a[this.rankType])
        .slice(0, 10); // 取前10名

      const names = sortedData.map((item) => item.name);
      const values = sortedData.map((item) => item[this.rankType]);

      // 定义不同指标的颜色
      const colorMap = {
        count: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: "#83bff6" },
          { offset: 1, color: "#188df0" },
        ]),
        amount: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: "#91d5ff" },
          { offset: 1, color: "#1890ff" },
        ]),
        profit: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: "#b7eb8f" },
          { offset: 1, color: "#52c41a" },
        ]),
      };

      // 定义不同指标的标题和单位
      const titleMap = {
        count: { title: "销售数量排行", unit: "台" },
        amount: { title: "销售金额排行", unit: "元" },
        profit: { title: "创造利润排行", unit: "元" },
      };

      const option = {
        title: {
          text: titleMap[this.rankType].title,
          left: "center",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: (params) => {
            const data = params[0];
            return `${data.name}: ${this.formatNumber(data.value)} ${titleMap[this.rankType].unit}`;
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          axisLabel: {
            formatter: (value) => {
              if (this.rankType === "count") {
                return value;
              } else {
                return value >= 10000 ? (value / 10000).toFixed(1) + "万" : value;
              }
            },
          },
        },
        yAxis: {
          type: "category",
          data: names.reverse(), // 反转数组使排名从高到低显示
          axisLabel: {
            interval: 0,
            margin: 20,
          },
        },
        series: [
          {
            name: titleMap[this.rankType].title,
            type: "bar",
            data: values.reverse(), // 反转数据以匹配名称
            itemStyle: {
              color: colorMap[this.rankType],
            },
            label: {
              show: true,
              position: "right",
              formatter: (params) => {
                if (this.rankType === "count") {
                  return params.value + " " + titleMap[this.rankType].unit;
                } else {
                  return this.formatNumber(params.value) + " " + titleMap[this.rankType].unit;
                }
              },
            },
          },
        ],
      };

      this.salesmanRankChart.setOption(option);
    },

    // 调整图表大小
    resizeCharts() {
      if (this.salesmanRankChart) {
        this.salesmanRankChart.resize();
      }
    },

    // 销毁图表实例
    disposeCharts() {
      if (this.salesmanRankChart) {
        this.salesmanRankChart.dispose();
        this.salesmanRankChart = null;
      }
    },

    // 查看销售员详情
    async viewSalesmanDetail(salesman) {
      this.currentSalesman = salesman;
      this.detailDialogVisible = true;
      this.detailLoading = true;

      try {
        const params = {
          salesManId: salesman.id,
          startDate: this.dateRange[0],
          endDate: this.dateRange[1],
        };

        const res = await getSalesmanDetail(params);
        if (res.code === 0 && res.data) {
          this.salesmanDetailData = res.data.list || [];
        } else {
          this.$message.error(res.message || "获取销售员详情失败");
          this.salesmanDetailData = [];
        }
      } catch (error) {
        console.error("获取销售员详情出错", error);
        this.$message.error("获取销售员详情失败");
        this.salesmanDetailData = [];
      } finally {
        this.detailLoading = false;
      }
    },

    // 返回列表页
    goBack() {
      this.$router.push({ name: "RevenueList" });
    },
  },
};
</script>

<style scoped>
.salesman-statistics-container {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.data-overview {
  margin-bottom: 20px;
}
.data-card {
  padding: 20px;
  border-radius: 8px;
  color: white;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.data-title {
  font-size: 16px;
  margin-bottom: 10px;
}
.data-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}
.data-desc {
  font-size: 12px;
  opacity: 0.8;
}
.total-salesman {
  background: linear-gradient(to right, #1890ff, #36cbcb);
}
.has-sales {
  background: linear-gradient(to right, #52c41a, #87d068);
}
.avg-sales {
  background: linear-gradient(to right, #fa8c16, #ffd666);
}
.avg-profit {
  background: linear-gradient(to right, #722ed1, #b37feb);
}
.amount,
.profit,
.share,
.price {
  color: #f56c6c;
  font-weight: bold;
}
</style>
