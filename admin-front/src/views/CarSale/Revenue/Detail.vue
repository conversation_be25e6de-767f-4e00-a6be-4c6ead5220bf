<template>
  <ContainerTit>
    <div class="btn-top-div">
      <el-button size="small" @click="goBack">返回</el-button>
      <el-button
        v-if="
          revenueData.settlementStatus === 4 &&
          revenueData.auditStatus === 2 &&
          $accessCheck($Access.Revenue_Settlement)
        "
        type="primary"
        size="small"
        @click="handleSettle"
        >结算</el-button
      >
      <el-button
        v-if="[0, 1, 4].includes(revenueData.auditStatus) && $accessCheck($Access.Revenue_Audit)"
        type="primary"
        size="small"
        @click="handleAudit(2)"
        >审核通过</el-button
      >
      <el-button
        v-if="[0, 1, 4].includes(revenueData.auditStatus) && $accessCheck($Access.Revenue_Audit)"
        type="warning"
        size="small"
        @click="handleAudit(3)"
        >审核拒绝</el-button
      >
    </div>
    <div v-loading="loading" class="revenueInfo">
      <el-empty v-if="!revenueData.id && !loading" description="未找到销售记录"></el-empty>
      <template v-else>
        <el-tabs v-model="activeName">
          <el-tab-pane label="销售收入详情" name="base">
            <el-row style="padding-bottom: 13px">
              <el-col :span="24">
                <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">基本信息</p>
              </el-col>
              <el-col class="form" :span="6">
                <span class="form_left">销售单号</span>
                <span class="form_right">
                  <span
                    v-if="revenueData.orderNo && $accessCheck($Access.newOrderLitGetOrderInfoById)"
                    class="click-div"
                    @click="$router.push(`/order/manageO/OrderDetails/1/${revenueData.orderId}`)"
                  >
                    {{ revenueData.orderNo }}
                  </span>
                  <span v-else>{{ revenueData.orderNo || "-" }}</span>
                </span>
              </el-col>
              <el-col class="form" :span="6" style="padding-left: 54px">
                <span class="form_left">销售员</span>
                <span class="form_right">{{ revenueData.salesmanName || "-" }}</span>
              </el-col>
              <el-col class="form" :span="6">
                <span class="form_left">审核状态</span>
                <span class="form_right">
                  <span
                    :class="{
                      'success-status': revenueData.auditStatus === 2,
                      'danger-status': revenueData.auditStatus === 3,
                      'warning-status': [0, 1, 4].includes(revenueData.auditStatus),
                    }"
                  >
                    {{ $_common.getAuditStatusText(revenueData.auditStatus) }}
                  </span>
                </span>
              </el-col>
              <el-col class="form" :span="6">
                <span class="form_left">结算状态</span>
                <span class="form_right">
                  <span :class="revenueData.settlementStatus === 5 ? 'success-status' : 'danger-status'">
                    {{ revenueData.settlementStatus === 5 ? "已结算" : "未结算" }}
                  </span>
                </span>
              </el-col>
              <el-col v-if="revenueData.settlementStatus === 5" class="form" :span="6">
                <span class="form_left">结算日期</span>
                <span class="form_right">
                  {{ revenueData.settlementTime ? $_common.formatDate(revenueData.settlementTime) : "-" }}
                </span>
              </el-col>
              <el-col v-if="revenueData.auditStatus === 2 || revenueData.auditStatus === 3" class="form" :span="6">
                <span class="form_left">审核时间</span>
                <span class="form_right">
                  {{ revenueData.auditTime ? $_common.formatDate(revenueData.auditTime) : "-" }}
                </span>
              </el-col>
              <el-col v-if="revenueData.auditStatus === 2 || revenueData.auditStatus === 3" class="form" :span="6">
                <span class="form_left">审核人</span>
                <span class="form_right">{{ revenueData.auditUserName || "-" }}</span>
              </el-col>
              <el-col v-if="revenueData.auditStatus === 2 || revenueData.auditStatus === 3" class="form" :span="24">
                <span class="form_left">审核备注</span>
                <span class="form_right">{{ revenueData.auditRemark || "无" }}</span>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>

        <!-- 价格和利润信息 -->
        <div class="order_bottom">
          <p class="text">价格和利润信息</p>
          <el-row>
            <el-col class="form" :span="6" style="padding-left: 54px">
              <span class="form_left">订单金额</span>
              <span class="form_right"> {{ $_common.formattedNumber(revenueData.orderAmount) }}</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">总成本</span>
              <span class="form_right"> {{ $_common.formattedNumber(revenueData.costAmount) }}</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">附加费用</span>
              <span class="form_right"> {{ $_common.formattedNumber(revenueData.otherExpenses) }}</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">毛利润</span>
              <span class="form_right"> {{ $_common.formattedNumber(revenueData.grossProfit) }}</span>
            </el-col>
            <el-col class="form" :span="6" style="padding-left: 54px">
              <span class="form_left">净利润</span>
              <span class="form_right"> {{ $_common.formattedNumber(revenueData.netProfit) }}</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">所有权模式</span>
              <span class="form_right">{{ revenueData.ownershipMode === 1 ? "自营车辆" : "寄售车辆" }}</span>
            </el-col>
          </el-row>
        </div>

        <!-- 分成信息 -->
        <div class="order_bottom">
          <p class="text">分成信息</p>
          <el-row>
            <el-col class="form" :span="6" style="padding-left: 54px">
              <span class="form_left">公司分成比例</span>
              <span class="form_right">{{ revenueData.profitTierData?.companyRatio || 0 }}%</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">销售员分成比例</span>
              <span class="form_right">{{ revenueData.profitTierData?.salesmanRatio || 0 }}%</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">部门分成比例</span>
              <span class="form_right">{{ revenueData.profitTierData?.departmentRatio || 0 }}%</span>
            </el-col>
            <el-col class="form" :span="6" style="padding-left: 54px">
              <span class="form_left">公司分成金额</span>
              <span class="form_right"> {{ $_common.formattedNumber(revenueData.companyRevenue) }}</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">销售员分成金额</span>
              <span class="form_right"> {{ $_common.formattedNumber(revenueData.salesmanRevenue) }}</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">部门分成金额</span>
              <span class="form_right"> {{ $_common.formattedNumber(revenueData.departmentRevenue) }}</span>
            </el-col>
          </el-row>
        </div>

        <!-- 备注信息 -->
        <div class="order_bottom">
          <p class="text">备注信息</p>
          <el-row>
            <el-col class="form" :span="24" style="padding-left: 54px">
              <span class="form_left">备注</span>
              <span class="form_right">{{ revenueData.remarks || "无" }}</span>
            </el-col>
          </el-row>
        </div>
      </template>
    </div>

    <!-- 结算确认对话框 -->
    <el-dialog title="确认结算" :visible.sync="settleDialogVisible" width="30%">
      <span>确认将此销售记录标记为已结算状态吗？</span>
      <div class="settle-date-picker">
        <span>结算日期：</span>
        <el-date-picker
          v-model="settlementDate"
          type="date"
          placeholder="选择结算日期"
          value-format="yyyy-MM-dd"
          :picker-options="{ disabledDate: futureDisabled }"
        >
        </el-date-picker>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="settleDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="settleLoading" @click="confirmSettle">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核销售记录" :visible.sync="auditDialogVisible" width="30%">
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
        <el-form-item label="审核状态">
          <el-tag :type="auditForm.auditStatus === 2 ? 'success' : 'danger'" size="medium">
            {{ auditForm.auditStatus === 2 ? "审核通过" : "审核拒绝" }}
          </el-tag>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" :rows="4" placeholder="请输入审核备注"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="auditLoading" @click="confirmAudit">确 定</el-button>
      </span>
    </el-dialog>
  </ContainerTit>
</template>

<script>
import { auditRevenue, getRevenueInfo, updateSettlementStatus } from "@/api/CarSale";
import ContainerTit from "@/component/layout/ContainerTit";

export default {
  name: "RevenueDetail",
  components: {
    ContainerTit,
  },
  data() {
    return {
      loading: false,
      revenueId: null,
      activeName: "base",
      revenueData: {
        id: "",
        revenueId: "",
        saleDate: "",
        salesmanName: "",
        settlementStatus: 4,
        settlementDate: "",
        settlementTime: "",
        settlementOperator: "",
        vehicleId: "",
        vehicleBrand: "",
        vehicleSeries: "",
        vehicleModel: "",
        vehicleVin: "",
        licensePlate: "",
        vehicleColor: "",
        firstRegistration: "",
        customerId: "",
        customerName: "",
        customerPhone: "",
        customerIdNumber: "",
        costAmount: 0,
        salePrice: 0,
        additionalFees: 0,
        totalCost: 0,
        grossProfit: 0,
        netProfit: 0,
        profitRate: 0,
        ownershipMode: 1,
        profitTierName: "",
        companyRatio: 0,
        salesmanRatio: 0,
        partnerRatio: 0,
        companyShare: 0,
        salesmanShare: 0,
        partnerShare: 0,
        auditStatus: 0,
        auditTime: "",
        auditUserId: "",
        auditUserName: "",
        auditRemark: "",
        remarks: "",
      },
      // 结算相关
      settleDialogVisible: false,
      settleLoading: false,
      settlementDate: new Date().toISOString().slice(0, 10),
      // 审核相关
      auditDialogVisible: false,
      auditLoading: false,
      auditForm: {
        auditStatus: 2,
        auditRemark: "",
      },
      auditRules: {
        auditRemark: [
          { required: true, message: "请输入审核备注", trigger: "blur" },
          { min: 2, max: 200, message: "备注长度在 2 到 200 个字符", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    // 检查权限
    if (!this.$accessCheck(this.$Access.Revenue_Detail)) {
      this.$message.error("您没有查看销售收入详情的权限");
      this.goBack();
      return;
    }

    this.revenueId = this.$route.params.id;
    if (this.revenueId) {
      this.fetchRevenueDetail();
    } else {
      this.$message.error("未指定销售记录ID");
    }
  },
  methods: {
    // 获取销售记录详情
    async fetchRevenueDetail() {
      if (!this.revenueId) return;

      this.loading = true;
      try {
        const res = await getRevenueInfo(this.revenueId);
        if (res.errorcode === 0 && res.data) {
          this.revenueData = res.data;
        } else {
          this.$message.error(res.message || "获取销售记录详情失败");
        }
      } catch (error) {
        console.error("获取销售记录详情出错", error);
        this.$message.error("获取销售记录详情失败");
      } finally {
        this.loading = false;
      }
    },

    // 处理结算操作
    handleSettle() {
      if (this.revenueData.settlementStatus === 5) {
        this.$message.warning("该销售记录已结算");
        return;
      }
      this.settleDialogVisible = true;
    },

    // 禁用未来日期
    futureDisabled(time) {
      return time.getTime() > Date.now();
    },

    // 确认结算
    async confirmSettle() {
      if (!this.settlementDate) {
        this.$message.warning("请选择结算日期");
        return;
      }

      this.settleLoading = true;
      try {
        const res = await updateSettlementStatus(
          this.revenueId,
          5, // 设置为已结算
          this.settlementDate
        );

        if (res.code === 0) {
          this.$message.success("销售记录结算成功");
          this.settleDialogVisible = false;
          this.fetchRevenueDetail(); // 刷新数据
        } else {
          this.$message.error(res.message || "销售记录结算失败");
        }
      } catch (error) {
        console.error("销售记录结算出错", error);
        this.$message.error("销售记录结算失败");
      } finally {
        this.settleLoading = false;
      }
    },

    // 处理审核操作
    handleAudit(status) {
      if (![0, 1, 4].includes(this.revenueData.auditStatus)) {
        this.$message.warning("当前状态不允许审核");
        return;
      }

      this.auditForm = {
        auditStatus: status,
        auditRemark: "同意",
      };
      this.auditDialogVisible = true;
    },

    // 确认审核
    confirmAudit() {
      this.$refs.auditForm.validate(async (valid) => {
        if (valid) {
          this.auditLoading = true;
          try {
            const res = await auditRevenue(this.revenueId, {
              auditStatus: this.auditForm.auditStatus,
              auditRemark: this.auditForm.auditRemark,
            });

            if (res.errorcode === 0) {
              this.$message.success("审核操作成功");
              this.auditDialogVisible = false;
              this.fetchRevenueDetail(); // 刷新数据
            } else {
              this.$message.error(res.message || "审核操作失败");
            }
          } catch (error) {
            console.error("审核操作出错", error);
            this.$message.error("审核操作失败");
          } finally {
            this.auditLoading = false;
          }
        }
      });
    },

    // 返回列表页
    goBack() {
      this.$router.push({ name: "RevenueList" });
    },
  },
};
</script>

<style scoped lang="scss">
.btn-top-div {
  position: absolute;
  right: 20px;
  top: 16px;
  z-index: 999;
}

.revenueInfo {
  background-color: #fff;
}

.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}

.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}

.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}

.success-status {
  color: #67c23a;
}

.warning-status {
  color: #e6a23c;
}

.danger-status {
  color: #f56c6c;
}

.info-status {
  color: #909399;
}

.click-div {
  color: #409eff;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}

.settle-date-picker {
  margin-top: 20px;
  display: flex;
  align-items: center;

  span {
    margin-right: 10px;
  }
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}
</style>

<style>
.revenueInfo .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.revenueInfo .is-active {
  font-weight: 700;
  color: #000;
}
.revenueInfo .el-tabs__nav {
  margin-left: 24px;
}
</style>
