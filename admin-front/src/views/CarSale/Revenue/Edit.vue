<template>
  <ContainerTit>
    <span slot="pagetit">{{ isEdit ? "编辑销售收入" : "添加销售收入" }}</span>
    <div slot="headr">
      <el-button type="primary" @click="submitForm('revenueForm')">保存</el-button>
      <el-button @click="goBack">取消</el-button>
    </div>

    <div class="page-div">
      <el-form ref="revenueForm" v-loading="loading" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单编号" prop="orderId">
              <el-input v-model="form.orderId" placeholder="请输入订单编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售日期" prop="saleDate">
              <el-date-picker
                v-model="form.saleDate"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="车辆" prop="vehicleId">
              <el-select
                v-model="form.vehicleId"
                filterable
                remote
                reserve-keyword
                placeholder="请输入品牌/型号/VIN码搜索"
                :remote-method="remoteVehicleSearch"
                :loading="vehicleLoading"
                style="width: 100%"
                @change="handleVehicleChange"
              >
                <el-option
                  v-for="item in vehicleOptions"
                  :key="item.id"
                  :label="item.brand + ' ' + item.model + ' | ' + item.vin"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所有权模式" prop="ownershipMode">
              <el-select v-model="form.ownershipMode" placeholder="请选择所有权模式" style="width: 100%">
                <el-option label="自有车辆" :value="1"></el-option>
                <el-option label="联营车辆" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户" prop="customerId">
              <el-select
                v-model="form.customerId"
                filterable
                remote
                reserve-keyword
                placeholder="请输入客户姓名/手机号搜索"
                :remote-method="remoteCustomerSearch"
                :loading="customerLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in customerOptions"
                  :key="item.id"
                  :label="item.name + ' | ' + item.mobile"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售员" prop="salesManId">
              <el-select v-model="form.salesManId" filterable placeholder="请选择销售员" style="width: 100%">
                <el-option
                  v-for="item in salesmanOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">价格信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="销售价格(元)" prop="salePrice">
              <el-input-number
                v-model="form.salePrice"
                :min="0"
                :step="1000"
                style="width: 100%"
                @change="calculateProfit"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="成本价格(元)" prop="costPrice">
              <el-input-number
                v-model="form.costPrice"
                :min="0"
                :step="1000"
                style="width: 100%"
                @change="calculateProfit"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其他成本(元)" prop="otherCosts">
              <el-input-number
                v-model="form.otherCosts"
                :min="0"
                :step="100"
                style="width: 100%"
                @change="calculateProfit"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="毛利润(元)">
              <el-input :value="grossProfit" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="净利润(元)">
              <el-input :value="netProfit" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">利润分配信息</el-divider>

        <el-row v-if="selectedTier">
          <el-col :span="24">
            <div class="profit-tier-info">
              <h4>匹配的分成层级: {{ selectedTier.tierName }}</h4>
              <div class="distribution-preview">
                <div class="bar-item company" :style="{ width: selectedTier.companyRatio + '%' }">
                  公司: {{ selectedTier.companyRatio }}% ({{ companyShare }} 元)
                </div>
                <div class="bar-item department" :style="{ width: selectedTier.departmentRatio + '%' }">
                  部门: {{ selectedTier.departmentRatio }}% ({{ departmentShare }} 元)
                </div>
                <div class="bar-item salesman" :style="{ width: selectedTier.salesmanRatio + '%' }">
                  销售: {{ selectedTier.salesmanRatio }}% ({{ salesmanShare }} 元)
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="form.remarks" type="textarea" placeholder="请输入备注信息" :rows="3"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </ContainerTit>
</template>

<script>
import { createRevenue, getAllVehicle, getProfitTierByProfit, getRevenueInfo, updateRevenue } from "@/api/CarSale";
import { getAllCustomer } from "@/api/Customer";
import { getAllStaff } from "@/api/Department";
import ContainerTit from "@/component/layout/ContainerTit";

export default {
  name: "RevenueEdit",
  components: {
    ContainerTit,
  },
  data() {
    return {
      loading: false,
      isEdit: false,
      revenueId: null,
      vehicleLoading: false,
      customerLoading: false,
      vehicleOptions: [],
      customerOptions: [],
      salesmanOptions: [],
      selectedVehicle: null,
      selectedTier: null,
      grossProfit: 0,
      netProfit: 0,
      companyShare: 0,
      departmentShare: 0,
      salesmanShare: 0,
      form: {
        orderId: "",
        vehicleId: "",
        customerId: "",
        salesManId: "",
        saleDate: new Date().toISOString().slice(0, 10),
        salePrice: 0,
        costPrice: 0,
        otherCosts: 0,
        ownershipMode: 1,
        remarks: "",
      },
      rules: {
        orderId: [{ required: true, message: "请输入订单编号", trigger: "blur" }],
        vehicleId: [{ required: true, message: "请选择车辆", trigger: "change" }],
        customerId: [{ required: true, message: "请选择客户", trigger: "change" }],
        salesManId: [{ required: true, message: "请选择销售员", trigger: "change" }],
        saleDate: [{ required: true, message: "请选择销售日期", trigger: "change" }],
        salePrice: [{ required: true, message: "请输入销售价格", trigger: "blur" }],
        costPrice: [{ required: true, message: "请输入成本价格", trigger: "blur" }],
        ownershipMode: [{ required: true, message: "请选择所有权模式", trigger: "change" }],
      },
    };
  },
  created() {
    // 检查权限
    if (this.isEdit && !this.$accessCheck(this.$Access.Revenue_Edit)) {
      this.$message.error("您没有编辑销售收入的权限");
      this.goBack();
      return;
    }

    if (!this.isEdit && !this.$accessCheck(this.$Access.Revenue_Add)) {
      this.$message.error("您没有添加销售收入的权限");
      this.goBack();
      return;
    }

    // 获取销售员列表
    this.fetchSalesmanOptions();

    // 判断是否是编辑模式
    if (this.$route.params.id) {
      this.isEdit = true;
      this.revenueId = this.$route.params.id;
      this.fetchRevenueDetail();
    }
  },
  methods: {
    // 获取收入详情
    async fetchRevenueDetail() {
      if (!this.revenueId) return;

      this.loading = true;
      try {
        const res = await getRevenueInfo(this.revenueId);
        if (res.code === 0 && res.data) {
          // 填充表单数据
          this.form = {
            ...this.form,
            ...res.data,
            vehicleId: res.data.vehicleId,
            customerId: res.data.customerId,
            salesManId: res.data.salesManId,
          };

          // 预加载选项
          await this.loadVehicleById(res.data.vehicleId);
          await this.loadCustomerById(res.data.customerId);

          // 计算利润和分配
          this.calculateProfit();
        } else {
          this.$message.error(res.message || "获取收入详情失败");
        }
      } catch (error) {
        console.error("获取收入详情出错", error);
        this.$message.error("获取收入详情失败");
      } finally {
        this.loading = false;
      }
    },

    // 远程搜索车辆
    async remoteVehicleSearch(query) {
      if (query.length < 2) return;

      this.vehicleLoading = true;
      try {
        const params = {
          keyword: query,
          pageIndex: 1,
          pageSize: 20,
        };

        const res = await getAllVehicle(params);
        if (res.code === 0 && res.data) {
          this.vehicleOptions = res.data.list || [];
        }
      } catch (error) {
        console.error("搜索车辆出错", error);
      } finally {
        this.vehicleLoading = false;
      }
    },

    // 加载特定车辆
    async loadVehicleById(vehicleId) {
      if (!vehicleId) return;

      this.vehicleLoading = true;
      try {
        const params = {
          vehicleId: vehicleId,
          pageIndex: 1,
          pageSize: 1,
        };

        const res = await getAllVehicle(params);
        if (res.code === 0 && res.data && res.data.list && res.data.list.length > 0) {
          const vehicle = res.data.list[0];
          this.vehicleOptions = [vehicle];
          this.selectedVehicle = vehicle;
          // 设置成本价格
          if (!this.isEdit) {
            this.form.costPrice = vehicle.purchasePrice || 0;
          }
        }
      } catch (error) {
        console.error("加载车辆出错", error);
      } finally {
        this.vehicleLoading = false;
      }
    },

    // 远程搜索客户
    async remoteCustomerSearch(query) {
      if (query.length < 2) return;

      this.customerLoading = true;
      try {
        const params = {
          keyword: query,
          pageIndex: 1,
          pageSize: 20,
        };

        const res = await getAllCustomer(params);
        if (res.code === 0 && res.data) {
          this.customerOptions = res.data.list || [];
        }
      } catch (error) {
        console.error("搜索客户出错", error);
      } finally {
        this.customerLoading = false;
      }
    },

    // 加载特定客户
    async loadCustomerById(customerId) {
      if (!customerId) return;

      this.customerLoading = true;
      try {
        const params = {
          customerId: customerId,
          pageIndex: 1,
          pageSize: 1,
        };

        const res = await getAllCustomer(params);
        if (res.code === 0 && res.data && res.data.list && res.data.list.length > 0) {
          const customer = res.data.list[0];
          this.customerOptions = [customer];
        }
      } catch (error) {
        console.error("加载客户出错", error);
      } finally {
        this.customerLoading = false;
      }
    },

    // 获取销售员选项
    async fetchSalesmanOptions() {
      try {
        const res = await getAllStaff({});
        if (res.code === 0 && res.data) {
          this.salesmanOptions = res.data.list || [];
        }
      } catch (error) {
        console.error("获取销售员列表出错", error);
      }
    },

    // 选择车辆后处理
    async handleVehicleChange(vehicleId) {
      if (!vehicleId) {
        this.selectedVehicle = null;
        return;
      }

      // 找到选中的车辆
      const vehicle = this.vehicleOptions.find((v) => v.id === vehicleId);
      if (vehicle) {
        this.selectedVehicle = vehicle;
        // 设置成本价格
        if (!this.isEdit) {
          this.form.costPrice = vehicle.purchasePrice || 0;
          this.calculateProfit();
        }
      }
    },

    // 计算利润
    async calculateProfit() {
      // 计算毛利润：销售价格 - 成本价格
      this.grossProfit = this.form.salePrice - this.form.costPrice;

      // 计算净利润：毛利润 - 其他成本
      this.netProfit = this.grossProfit - this.form.otherCosts;

      // 获取匹配的分成层级
      if (this.netProfit > 0) {
        try {
          const res = await getProfitTierByProfit(this.netProfit, this.form.ownershipMode);
          if (res.code === 0 && res.data) {
            this.selectedTier = res.data;
            this.calculateShares();
          } else {
            this.selectedTier = null;
          }
        } catch (error) {
          console.error("获取分成层级出错", error);
          this.selectedTier = null;
        }
      } else {
        this.selectedTier = null;
      }
    },

    // 计算分成金额
    calculateShares() {
      if (!this.selectedTier) return;

      this.companyShare = +((this.netProfit * this.selectedTier.companyRatio) / 100).toFixed(2);
      this.departmentShare = +((this.netProfit * this.selectedTier.departmentRatio) / 100).toFixed(2);
      this.salesmanShare = +((this.netProfit * this.selectedTier.salesmanRatio) / 100).toFixed(2);
    },

    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (this.netProfit <= 0) {
            this.$confirm("当前净利润为负或零，确定要继续吗?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                this.saveRevenue();
              })
              .catch(() => {});
          } else {
            this.saveRevenue();
          }
        } else {
          console.log("表单验证失败");
          return false;
        }
      });
    },

    // 保存收入记录
    async saveRevenue() {
      this.loading = true;
      try {
        // 构建请求数据
        const params = {
          ...this.form,
          grossProfit: this.grossProfit,
          netProfit: this.netProfit,
        };

        let res;
        if (this.isEdit) {
          // 编辑模式
          res = await updateRevenue(this.revenueId, params);
        } else {
          // 添加模式
          res = await createRevenue(params);
        }

        if (res.code === 0) {
          this.$message.success(`${this.isEdit ? "更新" : "添加"}销售收入成功`);
          this.goBack();
        } else {
          this.$message.error(res.message || `${this.isEdit ? "更新" : "添加"}销售收入失败`);
        }
      } catch (error) {
        console.error(`${this.isEdit ? "更新" : "添加"}销售收入出错`, error);
        this.$message.error(`${this.isEdit ? "更新" : "添加"}销售收入失败`);
      } finally {
        this.loading = false;
      }
    },

    // 返回列表页
    goBack() {
      this.$router.push({ name: "RevenueList" });
    },
  },
};
</script>

<style scoped>
.revenue-edit-container {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.profit-tier-info {
  margin: 15px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}
.profit-tier-info h4 {
  margin-top: 0;
  margin-bottom: 15px;
}
.distribution-preview {
  display: flex;
  height: 30px;
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
}
.bar-item {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  padding: 0 5px;
}
.company {
  background-color: #409eff;
}
.department {
  background-color: #67c23a;
}
.salesman {
  background-color: #e6a23c;
}
</style>
