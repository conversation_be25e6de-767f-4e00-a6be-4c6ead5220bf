<template>
  <div class="sales-statistics-container">
    <ContainerQuery>
      <div slot="left">
        <span class="title">销售统计</span>
        <div style="display: inline-block; margin-left: 20px">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            style="width: 350px"
            @change="handleDateChange"
          >
          </el-date-picker>
          <el-button type="primary" size="small" style="margin-left: 10px" @click="fetchData">查询</el-button>
          <el-button size="small" @click="viewSalesmanStats">查看销售员统计</el-button>
          <el-button size="small" @click="goBack">返回</el-button>
        </div>
      </div>

      <div v-loading="loading">
        <!-- 概览数据卡片 -->
        <el-row :gutter="20" class="data-overview">
          <el-col :span="6">
            <div class="data-card total-sales">
              <div class="data-title">总销量</div>
              <div class="data-value">{{ overviewData.totalSales }} 台</div>
              <div class="data-desc">同比 {{ formatGrowth(overviewData.salesGrowth) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="data-card total-amount">
              <div class="data-title">总销售额</div>
              <div class="data-value">¥ {{ formatNumber(overviewData.totalAmount) }}</div>
              <div class="data-desc">同比 {{ formatGrowth(overviewData.amountGrowth) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="data-card avg-price">
              <div class="data-title">平均售价</div>
              <div class="data-value">¥ {{ formatNumber(overviewData.averagePrice) }}</div>
              <div class="data-desc">同比 {{ formatGrowth(overviewData.priceGrowth) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="data-card settlement-rate">
              <div class="data-title">结算率</div>
              <div class="data-value">{{ overviewData.settlementRate }}%</div>
              <div class="data-desc">总利润 ¥{{ formatNumber(overviewData.totalProfit) }}</div>
            </div>
          </el-col>
        </el-row>

        <!-- 销售趋势分析 -->
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="24">
            <el-card shadow="hover">
              <div slot="header">
                <span>销售趋势分析</span>
                <el-radio-group v-model="trendTimeUnit" size="mini" style="float: right" @change="switchTrendData">
                  <el-radio-button label="month">按月</el-radio-button>
                  <el-radio-button label="week">按周</el-radio-button>
                  <el-radio-button label="day">按日</el-radio-button>
                </el-radio-group>
              </div>
              <div class="chart-container">
                <div id="salesTrendChart" style="width: 100%; height: 400px"></div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 品牌销售分布和利润分布 -->
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="12">
            <el-card shadow="hover">
              <div slot="header">品牌销售分布</div>
              <div class="chart-container">
                <div id="brandDistributionChart" style="width: 100%; height: 350px"></div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <div slot="header">品牌利润分布</div>
              <div class="chart-container">
                <div id="profitDistributionChart" style="width: 100%; height: 350px"></div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 车型销售排行榜 -->
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="24">
            <el-card shadow="hover">
              <div slot="header">车型销售排行榜</div>
              <el-table :data="vehicleRankData" border style="width: 100%">
                <el-table-column type="index" width="50" label="排名"></el-table-column>
                <el-table-column prop="brand" label="品牌" min-width="100"></el-table-column>
                <el-table-column prop="series" label="车系" min-width="120"></el-table-column>
                <el-table-column prop="model" label="车型" min-width="150"></el-table-column>
                <el-table-column label="销售数量" min-width="100" sortable>
                  <template slot-scope="scope">
                    {{ scope.row.count }} 台
                    <el-progress
                      :percentage="calculatePercentage(scope.row.count, vehicleMaxValues.count)"
                      :show-text="false"
                      :stroke-width="10"
                      color="#409EFF"
                    >
                    </el-progress>
                  </template>
                </el-table-column>
                <el-table-column label="销售金额" min-width="140" sortable>
                  <template slot-scope="scope">
                    <span class="amount">¥ {{ formatNumber(scope.row.amount) }}</span>
                    <el-progress
                      :percentage="calculatePercentage(scope.row.amount, vehicleMaxValues.amount)"
                      :show-text="false"
                      :stroke-width="10"
                      color="#67C23A"
                    >
                    </el-progress>
                  </template>
                </el-table-column>
                <el-table-column label="平均价格" min-width="140">
                  <template slot-scope="scope">
                    <span class="amount">¥ {{ formatNumber(scope.row.avgPrice) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="平均利润" min-width="140">
                  <template slot-scope="scope">
                    <span class="profit">¥ {{ formatNumber(scope.row.avgProfit) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="利润率" min-width="100">
                  <template slot-scope="scope">
                    <span class="profit-rate">{{ scope.row.profitRate }}%</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </ContainerQuery>
  </div>
</template>

<script>
import { getSalesStatistics } from "@/api/CarSale";
import ContainerQuery from "@/component/layout/ContainerQuery";
import * as echarts from "echarts";

export default {
  name: "SalesStatistics",
  components: {
    ContainerQuery,
  },
  data() {
    return {
      loading: false,
      dateRange: [this.getFirstDayOfYear(), new Date().toISOString().slice(0, 10)],
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 1);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 3);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本年度",
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().getFullYear(), 0, 1);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "上一年度",
            onClick(picker) {
              const start = new Date(new Date().getFullYear() - 1, 0, 1);
              const end = new Date(new Date().getFullYear() - 1, 11, 31);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      trendTimeUnit: "month",
      salesTrendChart: null,
      brandDistributionChart: null,
      profitDistributionChart: null,
      overviewData: {
        totalSales: 0,
        salesGrowth: 0,
        totalAmount: 0,
        amountGrowth: 0,
        averagePrice: 0,
        priceGrowth: 0,
        settlementRate: 0,
        totalProfit: 0,
      },
      vehicleRankData: [],
      vehicleMaxValues: {
        count: 1,
        amount: 1,
      },
      statisticsData: {
        trendData: {
          month: { dates: [], sales: [], amounts: [] },
          week: { dates: [], sales: [], amounts: [] },
          day: { dates: [], sales: [], amounts: [] },
        },
        brandData: [],
        profitData: [],
      },
    };
  },
  mounted() {
    this.fetchData();
    window.addEventListener("resize", this.resizeCharts);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.resizeCharts);
    this.disposeCharts();
  },
  methods: {
    // 获取当年第一天
    getFirstDayOfYear() {
      const date = new Date();
      date.setMonth(0);
      date.setDate(1);
      return date.toISOString().slice(0, 10);
    },

    // 格式化数字，添加千位分隔符
    formatNumber(num) {
      if (!num && num !== 0) return "0";
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },

    // 格式化增长率，添加正负号和百分比
    formatGrowth(value) {
      if (!value && value !== 0) return "0%";
      const prefix = value > 0 ? "+" : "";
      return prefix + value.toFixed(2) + "%";
    },

    // 日期范围变化处理
    handleDateChange() {
      // 日期变化时可以自动重新获取数据，也可以等用户点击查询按钮
    },

    // 切换趋势图时间单位
    switchTrendData() {
      this.renderTrendChart();
    },

    // 计算进度条百分比
    calculatePercentage(value, max) {
      if (!value || !max || max === 0) return 0;
      return (value / max) * 100;
    },

    // 获取统计数据
    async fetchData() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$message.warning("请选择日期范围");
        return;
      }

      this.loading = true;
      try {
        const params = {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1],
        };

        const res = await getSalesStatistics(params);
        if (res.code === 0 && res.data) {
          // 处理概览数据
          this.overviewData = res.data.overview || this.getDefaultOverviewData();

          // 处理趋势数据
          this.statisticsData.trendData = res.data.trendData || this.getDefaultTrendData();

          // 处理品牌分布数据
          this.statisticsData.brandData = res.data.brandData || this.getDefaultBrandData();

          // 处理利润分布数据
          this.statisticsData.profitData = res.data.profitData || this.getDefaultProfitData();

          // 处理车型排行数据
          this.vehicleRankData = res.data.vehicleRank || this.getDefaultVehicleRankData();

          // 计算最大值用于进度条
          this.calculateVehicleMaxValues();

          // 渲染图表
          this.$nextTick(() => {
            this.initCharts();
          });
        } else {
          this.$message.error(res.message || "获取销售统计数据失败");
          // 使用默认数据渲染图表
          this.useDefaultData();
        }
      } catch (error) {
        console.error("获取销售统计数据出错", error);
        this.$message.error("获取销售统计数据失败");
        // 使用默认数据渲染图表
        this.useDefaultData();
      } finally {
        this.loading = false;
      }
    },

    // 计算车型排行最大值用于进度条
    calculateVehicleMaxValues() {
      if (!this.vehicleRankData || this.vehicleRankData.length === 0) return;

      this.vehicleMaxValues = {
        count: Math.max(...this.vehicleRankData.map((item) => item.count || 0)),
        amount: Math.max(...this.vehicleRankData.map((item) => item.amount || 0)),
      };

      // 防止0除以0的情况
      for (const key in this.vehicleMaxValues) {
        if (this.vehicleMaxValues[key] === 0) this.vehicleMaxValues[key] = 1;
      }
    },

    // 初始化默认数据用于演示
    getDefaultOverviewData() {
      return {
        totalSales: 128,
        salesGrowth: 15.2,
        totalAmount: 19250000,
        amountGrowth: 18.5,
        averagePrice: 150390,
        priceGrowth: 3.2,
        settlementRate: 87.5,
        totalProfit: 3850000,
      };
    },

    // 获取默认趋势数据
    getDefaultTrendData() {
      return {
        month: {
          dates: ["1月", "2月", "3月", "4月", "5月", "6月"],
          sales: [15, 18, 22, 25, 26, 22],
          amounts: [2250000, 2700000, 3300000, 3750000, 3900000, 3350000],
        },
        week: {
          dates: ["第1周", "第2周", "第3周", "第4周", "第5周", "第6周", "第7周", "第8周"],
          sales: [3, 4, 5, 6, 4, 7, 8, 5],
          amounts: [450000, 600000, 750000, 900000, 600000, 1050000, 1200000, 750000],
        },
        day: {
          dates: ["6.1", "6.2", "6.3", "6.4", "6.5", "6.6", "6.7", "6.8", "6.9", "6.10"],
          sales: [1, 0, 2, 1, 3, 2, 1, 0, 2, 1],
          amounts: [150000, 0, 300000, 150000, 450000, 300000, 150000, 0, 300000, 150000],
        },
      };
    },

    // 获取默认品牌分布数据
    getDefaultBrandData() {
      return [
        { name: "大众", value: 35 },
        { name: "丰田", value: 25 },
        { name: "本田", value: 18 },
        { name: "奔驰", value: 12 },
        { name: "宝马", value: 8 },
        { name: "其他", value: 2 },
      ];
    },

    // 获取默认利润分布数据
    getDefaultProfitData() {
      return [
        { name: "大众", value: 950000 },
        { name: "丰田", value: 775000 },
        { name: "本田", value: 680000 },
        { name: "奔驰", value: 880000 },
        { name: "宝马", value: 525000 },
        { name: "其他", value: 40000 },
      ];
    },

    // 获取默认车型排行数据
    getDefaultVehicleRankData() {
      return [
        {
          brand: "大众",
          series: "帕萨特",
          model: "帕萨特 2.0T 旗舰版",
          count: 20,
          amount: 4000000,
          avgPrice: 200000,
          avgProfit: 40000,
          profitRate: 20,
        },
        {
          brand: "丰田",
          series: "凯美瑞",
          model: "凯美瑞 2.5L 豪华版",
          count: 18,
          amount: 3960000,
          avgPrice: 220000,
          avgProfit: 44000,
          profitRate: 20,
        },
        {
          brand: "本田",
          series: "雅阁",
          model: "雅阁 1.5T 锐酷版",
          count: 15,
          amount: 3000000,
          avgPrice: 200000,
          avgProfit: 38000,
          profitRate: 19,
        },
        {
          brand: "大众",
          series: "途观L",
          model: "途观L 380TSI 四驱旗舰版",
          count: 12,
          amount: 3240000,
          avgPrice: 270000,
          avgProfit: 54000,
          profitRate: 20,
        },
        {
          brand: "奔驰",
          series: "C级",
          model: "奔驰C级 C 260 L 运动版",
          count: 10,
          amount: 3500000,
          avgPrice: 350000,
          avgProfit: 87500,
          profitRate: 25,
        },
        {
          brand: "丰田",
          series: "汉兰达",
          model: "汉兰达 2.0T 四驱豪华版",
          count: 8,
          amount: 2560000,
          avgPrice: 320000,
          avgProfit: 64000,
          profitRate: 20,
        },
        {
          brand: "本田",
          series: "CR-V",
          model: "CR-V 锐·混动 2.0L 四驱",
          count: 7,
          amount: 1750000,
          avgPrice: 250000,
          avgProfit: 47500,
          profitRate: 19,
        },
        {
          brand: "宝马",
          series: "3系",
          model: "宝马3系 325Li M运动套装",
          count: 6,
          amount: 2400000,
          avgPrice: 400000,
          avgProfit: 100000,
          profitRate: 25,
        },
      ];
    },

    // 使用默认数据
    useDefaultData() {
      // 默认概览数据
      this.overviewData = this.getDefaultOverviewData();

      // 默认趋势数据
      this.statisticsData.trendData = this.getDefaultTrendData();

      // 默认品牌分布数据
      this.statisticsData.brandData = this.getDefaultBrandData();

      // 默认利润分布数据
      this.statisticsData.profitData = this.getDefaultProfitData();

      // 默认车型排行数据
      this.vehicleRankData = this.getDefaultVehicleRankData();

      // 计算最大值用于进度条
      this.calculateVehicleMaxValues();

      // 初始化图表
      this.$nextTick(() => {
        this.initCharts();
      });
    },

    // 初始化所有图表
    initCharts() {
      this.initTrendChart();
      this.initBrandDistributionChart();
      this.initProfitDistributionChart();
    },

    // 初始化销售趋势图表
    initTrendChart() {
      if (this.salesTrendChart) {
        this.salesTrendChart.dispose();
      }

      this.salesTrendChart = echarts.init(document.getElementById("salesTrendChart"));
      this.renderTrendChart();
    },

    // 渲染销售趋势图表
    renderTrendChart() {
      if (!this.salesTrendChart) return;

      const data = this.statisticsData.trendData[this.trendTimeUnit];

      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: ["销售数量", "销售金额"],
          top: 10,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: data.dates,
        },
        yAxis: [
          {
            type: "value",
            name: "销售数量",
            position: "left",
            axisLabel: {
              formatter: "{value} 台",
            },
          },
          {
            type: "value",
            name: "销售金额",
            position: "right",
            axisLabel: {
              formatter: (value) => {
                return (value / 10000).toFixed(1) + "万";
              },
            },
          },
        ],
        series: [
          {
            name: "销售数量",
            type: "bar",
            data: data.sales,
            itemStyle: {
              color: "#409EFF",
            },
          },
          {
            name: "销售金额",
            type: "line",
            yAxisIndex: 1,
            data: data.amounts,
            itemStyle: {
              color: "#67C23A",
            },
            lineStyle: {
              width: 3,
              shadowColor: "rgba(0,0,0,0.3)",
              shadowBlur: 10,
              shadowOffsetY: 8,
            },
            symbol: "circle",
            symbolSize: 8,
          },
        ],
      };

      this.salesTrendChart.setOption(option);
    },

    // 初始化品牌销售分布图表
    initBrandDistributionChart() {
      if (this.brandDistributionChart) {
        this.brandDistributionChart.dispose();
      }

      this.brandDistributionChart = echarts.init(document.getElementById("brandDistributionChart"));

      const option = {
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
        },
        legend: {
          orient: "vertical",
          right: 10,
          top: "center",
          data: this.statisticsData.brandData.map((item) => item.name),
        },
        series: [
          {
            name: "品牌销售",
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "18",
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: this.statisticsData.brandData,
          },
        ],
      };

      this.brandDistributionChart.setOption(option);
    },

    // 初始化利润分布图表
    initProfitDistributionChart() {
      if (this.profitDistributionChart) {
        this.profitDistributionChart.dispose();
      }

      this.profitDistributionChart = echarts.init(document.getElementById("profitDistributionChart"));

      const option = {
        tooltip: {
          trigger: "item",
          formatter: (params) => {
            return `${params.seriesName} <br/>${params.name}: ¥${this.formatNumber(params.value)} (${params.percent}%)`;
          },
        },
        legend: {
          orient: "vertical",
          right: 10,
          top: "center",
          data: this.statisticsData.profitData.map((item) => item.name),
        },
        series: [
          {
            name: "品牌利润",
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "18",
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: this.statisticsData.profitData,
          },
        ],
      };

      this.profitDistributionChart.setOption(option);
    },

    // 调整图表大小
    resizeCharts() {
      if (this.salesTrendChart) {
        this.salesTrendChart.resize();
      }
      if (this.brandDistributionChart) {
        this.brandDistributionChart.resize();
      }
      if (this.profitDistributionChart) {
        this.profitDistributionChart.resize();
      }
    },

    // 销毁图表实例
    disposeCharts() {
      if (this.salesTrendChart) {
        this.salesTrendChart.dispose();
        this.salesTrendChart = null;
      }
      if (this.brandDistributionChart) {
        this.brandDistributionChart.dispose();
        this.brandDistributionChart = null;
      }
      if (this.profitDistributionChart) {
        this.profitDistributionChart.dispose();
        this.profitDistributionChart = null;
      }
    },

    // 查看销售员统计
    viewSalesmanStats() {
      this.$router.push({
        name: "SalesmanStatistics",
        query: {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1],
        },
      });
    },

    // 返回列表页
    goBack() {
      this.$router.push({ name: "RevenueList" });
    },
  },
};
</script>

<style scoped>
.sales-statistics-container {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.data-overview {
  margin-bottom: 20px;
}
.data-card {
  padding: 20px;
  border-radius: 8px;
  color: white;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.data-title {
  font-size: 16px;
  margin-bottom: 10px;
}
.data-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}
.data-desc {
  font-size: 12px;
  opacity: 0.8;
}
.total-sales {
  background: linear-gradient(to right, #1890ff, #36cbcb);
}
.total-amount {
  background: linear-gradient(to right, #52c41a, #87d068);
}
.avg-price {
  background: linear-gradient(to right, #fa8c16, #ffd666);
}
.settlement-rate {
  background: linear-gradient(to right, #f56c6c, #fc9153);
}
.chart-container {
  position: relative;
}
.amount,
.profit {
  color: #f56c6c;
  font-weight: bold;
}
.profit-rate {
  color: #e6a23c;
  font-weight: bold;
}
</style>
