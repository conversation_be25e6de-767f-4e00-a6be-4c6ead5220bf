<template>
  <ContainerQuery>
    <div slot="left">
      <el-button
        v-if="$accessCheck($Access.Application_RevenueStatistics)"
        type="success"
        size="small"
        @click="handleViewStatistics"
        >销售统计</el-button
      >
      <el-button
        v-if="$accessCheck($Access.Application_RevenueStatistics)"
        type="info"
        size="small"
        @click="handleViewSalesmanStats"
        >销售员业绩</el-button
      >
      <el-button
        v-if="$accessCheck($Access.Revenue_Audit) && selectedRows.length > 0"
        type="primary"
        size="small"
        @click="handleBatchAudit(2)"
        >批量审核通过</el-button
      >
      <el-button
        v-if="$accessCheck($Access.Revenue_Audit) && selectedRows.length > 0"
        type="danger"
        size="small"
        @click="handleBatchAudit(3)"
        >批量审核拒绝</el-button
      >
    </div>

    <!-- 搜索表单 -->
    <div slot="more">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="车辆信息">
          <SelectVehicle v-model="searchForm.keyword" @change="vehicleChange" />
        </el-form-item>
        <el-form-item label="业务员">
          <el-input v-model="searchForm.salesmanName" placeholder="请选择业务员" readonly>
            <el-button slot="append" icon="el-icon-search" @click="showStaffModal"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="客户">
          <SelectCustomer v-model="searchForm.customerName" @change="customerChange" />
        </el-form-item>
        <el-form-item label="结算状态">
          <el-select v-model="searchForm.settlementStatus" placeholder="请选择" clearable>
            <el-option label="未结算" :value="4"></el-option>
            <el-option label="已结算" :value="5"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="searchForm.auditStatus" placeholder="请选择" clearable>
            <el-option label="待补全" :value="0"></el-option>
            <el-option label="待审核" :value="1"></el-option>
            <el-option label="审核通过" :value="2"></el-option>
            <el-option label="审核拒绝" :value="3"></el-option>
            <el-option label="审核中" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="销售日期">
          <el-date-picker
            v-model="searchForm.saleDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 250px"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 收入列表表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column type="index" width="50" label="序号"></el-table-column>
      <el-table-column prop="no" label="收入编号" min-width="120">
        <template slot-scope="scope">
          <span v-if="$accessCheck($Access.Revenue_Detail)" class="click-div" @click="handleDetail(scope.row)">
            {{ scope.row.no }}
          </span>
          <span v-else>{{ scope.row.no }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车辆信息" min-width="120">
        <template slot-scope="scope">
          <el-tag size="mini">{{ scope.row.vehicleInfo?.licensePlate }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="客户" min-width="100"></el-table-column>
      <el-table-column prop="salesmanName" label="销售员" min-width="100"></el-table-column>
      <el-table-column label="销售价格" min-width="120" sortable>
        <template slot-scope="scope">
          <span class="price">{{ scope.row.orderAmount }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="毛利润" min-width="120" sortable>
        <template slot-scope="scope">
          <span class="price">{{ scope.row.grossProfit }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="净利润" min-width="120" sortable>
        <template slot-scope="scope">
          <span class="price">{{ scope.row.netProfit }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="$_common.getAuditStatusType(scope.row.auditStatus)">
            {{ $_common.getAuditStatusText(scope.row.auditStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="结算状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.settlementStatus === 4 ? 'danger' : 'success'">
            {{ scope.row.settlementStatus === 4 ? "未结算" : "已结算" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="settlementTime" label="结算日期" min-width="140">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.settlementTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="140">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="240">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.Revenue_Detail)"
            size="mini"
            type="text"
            @click="handleDetail(scope.row)"
            >详情</el-button
          >
          <el-button
            v-if="
              scope.row.settlementStatus === 4 &&
              [0, 1].includes(scope.row.auditStatus) &&
              $accessCheck($Access.Revenue_Edit)
            "
            size="mini"
            type="text"
            @click="handleEditRevenue(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-if="
              scope.row.settlementStatus === 4 &&
              scope.row.auditStatus === 2 &&
              $accessCheck($Access.Revenue_Settlement)
            "
            size="mini"
            type="text"
            @click="handleUpdateSettlement(scope.row, 5)"
            >标记已结算</el-button
          >
          <el-button
            v-if="scope.row.settlementStatus === 5 && $accessCheck($Access.Revenue_Settlement)"
            size="mini"
            type="text"
            @click="handleUpdateSettlement(scope.row, 4)"
            >取消结算</el-button
          >
          <el-button
            v-if="[0, 1, 4].includes(scope.row.auditStatus) && $accessCheck($Access.Revenue_Audit)"
            size="mini"
            type="text"
            @click="handleAudit(scope.row)"
            >审核</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>

    <!-- 结算日期对话框 -->
    <el-dialog title="设置结算日期" :visible.sync="settlementDialogVisible" width="400px">
      <el-form :model="settlementForm" label-width="100px">
        <el-form-item label="结算日期">
          <el-date-picker
            v-model="settlementForm.settlementDate"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="settlementDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSettlement">确认</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核销售记录" :visible.sync="auditDialogVisible" width="500px">
      <el-form ref="auditForm" :model="auditForm" label-width="100px" :rules="auditRules">
        <el-form-item label="审核状态" prop="auditStatus">
          <el-radio-group v-model="auditForm.auditStatus" @change="handleAuditStatusChange">
            <el-radio :label="2">审核通过</el-radio>
            <el-radio :label="3">审核拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" placeholder="请输入审核备注" :rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAudit">确认</el-button>
      </div>
    </el-dialog>

    <!-- 批量审核对话框 -->
    <el-dialog title="批量审核销售记录" :visible.sync="batchAuditDialogVisible" width="500px">
      <p>已选择 {{ selectedRows.length }} 条记录进行批量审核</p>
      <el-form ref="batchAuditForm" :model="batchAuditForm" label-width="100px" :rules="auditRules">
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input
            v-model="batchAuditForm.auditRemark"
            type="textarea"
            placeholder="请输入审核备注"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchAuditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchAudit">确认</el-button>
      </div>
    </el-dialog>

    <!-- 员工选择弹窗 -->
    <staffListModal
      v-if="staffShow"
      :is-show="staffShow"
      :is-check="false"
      @cancel="staffShow = false"
      @confirm="staffSel"
    />
  </ContainerQuery>
</template>

<script>
import { auditRevenue, batchAuditRevenue, getAllRevenue, updateSettlementStatus } from "@/api/CarSale";
import SelectCustomer from "@/component/common/SelectCustomer";
import SelectVehicle from "@/component/common/SelectVehicle";
import staffListModal from "@/component/common/staffListModal";
import ContainerQuery from "@/component/layout/ContainerQuery";

export default {
  name: "RevenueList",
  components: {
    ContainerQuery,
    SelectVehicle,
    SelectCustomer,
    staffListModal,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      selectedRows: [], // 多选行数据
      total: 0,
      pageIndex: 1,
      pageSize: 10,
      searchForm: {
        keyword: "",
        salesmanName: "",
        customerName: "",
        settlementStatus: "",
        auditStatus: "",
        saleDateRange: [],
        vehicleId: "", // 存储选中车辆ID
        salesManId: "", // 存储选中销售员ID
        customerId: "", // 存储选中客户ID
      },
      settlementDialogVisible: false,
      settlementForm: {
        revenueId: null,
        settlementStatus: 1,
        settlementDate: new Date().toISOString().slice(0, 10),
      },
      auditDialogVisible: false,
      auditForm: {
        revenueId: null,
        auditStatus: 2,
        auditRemark: "",
      },
      batchAuditDialogVisible: false,
      batchAuditForm: {
        revenueIds: [],
        auditStatus: 2,
        auditRemark: "",
      },
      auditRules: {
        auditStatus: [{ required: true, message: "请选择审核状态", trigger: "change" }],
        auditRemark: [
          { required: true, message: "请输入审核备注", trigger: "blur" },
          { min: 2, max: 200, message: "备注长度在 2 到 200 个字符", trigger: "blur" },
        ],
      },
      staffShow: false, // 员工选择弹窗显示状态
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 新增方法：处理车辆选择变更
    vehicleChange(value, selectedOptions) {
      if (selectedOptions && selectedOptions.length > 0) {
        this.searchForm.vehicleId = selectedOptions[0].id;
      } else {
        this.searchForm.vehicleId = "";
      }
    },

    // 新增方法：处理客户选择变更
    customerChange(value, selectedOptions) {
      if (selectedOptions && selectedOptions.length > 0) {
        this.searchForm.customerId = selectedOptions[0].id;
      } else {
        this.searchForm.customerId = "";
      }
    },

    async fetchData() {
      this.loading = true;
      try {
        const params = {
          page: this.pageIndex,
          pageSize: this.pageSize,
          keyword: this.searchForm.keyword,
          salesmanId: this.searchForm.salesManId,
          customerName: this.searchForm.customerName,
          settlementStatus: this.searchForm.settlementStatus,
          auditStatus: this.searchForm.auditStatus,
          vehicleId: this.searchForm.vehicleId,
          salesManId: this.searchForm.salesManId,
          customerId: this.searchForm.customerId,
        };

        // 处理日期范围
        if (this.searchForm.saleDateRange && this.searchForm.saleDateRange.length === 2) {
          params.saleDateStart = this.searchForm.saleDateRange[0];
          params.saleDateEnd = this.searchForm.saleDateRange[1];
        }

        const res = await getAllRevenue(params);
        if (res.errorcode === 0) {
          // 使用新的数据结构
          this.tableData = res.data || [];
          // 从 pageData 中获取分页信息
          if (res.pageData) {
            this.total = res.pageData.pageTotal || 0;
            // 同步页码，防止后端分页逻辑变化
            this.pageIndex = res.pageData.pageIndex || this.pageIndex;
            this.pageSize = res.pageData.pageSize || this.pageSize;
          } else {
            // 兼容直接在响应根对象的分页数据
            this.total = res.pageTotal || 0;
            this.pageIndex = res.pageIndex || this.pageIndex;
            this.pageSize = res.pageSize || this.pageSize;
          }
        } else {
          this.$message.error(res.message || "获取销售收入列表失败");
        }
      } catch (error) {
        console.error("获取销售收入列表出错", error);
        this.$message.error("获取销售收入列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.pageIndex = 1;
      this.fetchData();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: "",
        salesmanName: "",
        customerName: "",
        settlementStatus: "",
        auditStatus: "",
        saleDateRange: [],
        vehicleId: "",
        salesManId: "",
        customerId: "",
      };
      this.handleSearch();
    },

    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchData();
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.pageIndex = val;
      this.fetchData();
    },

    // 多选变化事件
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 编辑销售收入
    handleEditRevenue(row) {
      this.$router.push({ name: "RevenueEdit", params: { id: row.id } });
    },

    // 查看销售收入详情
    handleDetail(row) {
      this.$router.push({ name: "RevenueDetail", params: { id: row.id } });
    },

    // 更新结算状态
    handleUpdateSettlement(row, status) {
      if (status === 1) {
        // 标记为已结算，需要填写结算日期
        this.settlementForm.revenueId = row.id;
        this.settlementForm.settlementStatus = status;
        this.settlementDialogVisible = true;
      } else {
        // 取消结算，直接更新
        this.updateSettlementStatus(row.id, status);
      }
    },

    // 确认结算
    confirmSettlement() {
      if (!this.settlementForm.settlementDate) {
        this.$message.warning("请选择结算日期");
        return;
      }

      this.updateSettlementStatus(
        this.settlementForm.revenueId,
        this.settlementForm.settlementStatus,
        this.settlementForm.settlementDate
      );
      this.settlementDialogVisible = false;
    },

    // 更新结算状态API调用
    async updateSettlementStatus(revenueId, status, settlementDate) {
      const statusText = status === 0 ? "取消结算" : "标记已结算";

      try {
        const res = await updateSettlementStatus(revenueId, status, settlementDate);
        if (res.errorcode === 0) {
          this.$message.success(`${statusText}成功`);
          this.fetchData();
        } else {
          this.$message.error(res.data || `${statusText}失败`);
        }
      } catch (error) {
        console.error(`${statusText}出错`, error);
        this.$message.error(`${statusText}失败`);
      }
    },

    // 查看销售统计
    handleViewStatistics() {
      this.$router.push({ name: "RevenueStatistics" });
    },

    // 查看销售员业绩
    handleViewSalesmanStats() {
      this.$router.push({ name: "SalesmanStatistics" });
    },

    // 打开审核对话框
    handleAudit(row) {
      this.auditForm = {
        revenueId: row.id,
        auditStatus: 2,
        auditRemark: "同意",
      };
      this.auditDialogVisible = true;
    },

    // 确认审核
    confirmAudit() {
      this.$refs.auditForm.validate(async (valid) => {
        if (valid) {
          try {
            const res = await auditRevenue(this.auditForm.revenueId, {
              auditStatus: this.auditForm.auditStatus,
              auditRemark: this.auditForm.auditRemark,
            });
            if (res.errorcode === 0) {
              this.$message.success("审核操作成功");
              this.auditDialogVisible = false;
              this.fetchData();
            } else {
              this.$message.error(res.data || "审核操作失败");
            }
          } catch (error) {
            console.error("审核出错", error);
            this.$message.error("审核操作失败");
          }
        }
      });
    },

    // 打开批量审核对话框
    handleBatchAudit(status) {
      // 过滤出可以审核的记录
      const validRows = this.selectedRows.filter((row) => [0, 1, 4].includes(row.auditStatus));
      if (validRows.length === 0) {
        this.$message.warning("选中的记录中没有可审核的记录");
        return;
      }

      this.batchAuditForm = {
        revenueIds: validRows.map((row) => row.id),
        auditStatus: status,
        auditRemark: "同意",
      };
      this.batchAuditDialogVisible = true;
    },

    // 确认批量审核
    confirmBatchAudit() {
      this.$refs.batchAuditForm.validate(async (valid) => {
        if (valid) {
          try {
            const res = await batchAuditRevenue({
              revenueIds: this.batchAuditForm.revenueIds,
              auditStatus: this.batchAuditForm.auditStatus,
              auditRemark: this.batchAuditForm.auditRemark,
            });
            if (res.errorcode === 0) {
              this.$message.success(`成功审核 ${res.data || this.batchAuditForm.revenueIds.length} 条记录`);
              this.batchAuditDialogVisible = false;
              this.fetchData();
            } else {
              this.$message.error(res.data || "批量审核操作失败");
            }
          } catch (error) {
            console.error("批量审核出错", error);
            this.$message.error("批量审核操作失败");
          }
        }
      });
    },

    // 处理审核状态变化
    handleAuditStatusChange(value) {
      this.auditForm.auditRemark = value === 2 ? "同意" : "拒绝";
    },

    // 显示员工选择弹窗
    showStaffModal() {
      this.staffShow = true;
    },

    // 员工选择回调
    staffSel(staff) {
      if (staff && staff.length > 0) {
        const selectedStaff = staff[0];
        this.searchForm.salesmanName = selectedStaff.staffName;
        this.searchForm.salesManId = selectedStaff.id;
        this.handleSearch();
      }
    },
  },
};
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.price {
  color: #f56c6c;
  font-weight: bold;
}
</style>
