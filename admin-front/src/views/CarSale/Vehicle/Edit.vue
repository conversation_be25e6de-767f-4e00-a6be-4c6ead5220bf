<template>
  <ContainerTit>
    <span slot="pagetit">{{ isEdit ? "编辑车辆信息" : "添加车辆信息" }}</span>
    <div slot="headr">
      <el-button type="primary" @click="submitForm('vehicleForm')">保存</el-button>
      <el-button @click="goBack">取消</el-button>
    </div>
    <div class="page-div">
      <el-form ref="vehicleForm" v-loading="loading" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input v-model="form.brand" placeholder="请输入车辆品牌"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入车型"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="颜色" prop="color">
              <el-input v-model="form.color" placeholder="请输入颜色"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年份" prop="year">
              <el-select v-model="form.year" filterable allow-create placeholder="请选择或输入年份" style="width: 100%">
                <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属仓库" prop="warehouseId">
              <el-select
                v-model="form.warehouseId"
                filterable
                placeholder="请选择所属仓库"
                style="width: 100%"
                @change="handleWarehouseChange"
              >
                <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.warehouseName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车牌号" prop="licensePlate">
              <el-input v-model="form.licensePlate" placeholder="请输入车牌号" @input="handleLicensePlateInput">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="销售状态" prop="saleStatus">
              <el-select v-model="form.saleStatus" placeholder="请选择销售状态" style="width: 100%">
                <el-option label="营业" :value="5"></el-option>
                <el-option label="休息" :value="4"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所有权模式" prop="ownershipMode">
              <el-select v-model="form.ownershipMode" placeholder="请选择所有权模式" style="width: 100%">
                <el-option label="公司" :value="1"></el-option>
                <el-option label="业务员" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="利润分成等级" prop="profitTierId">
              <el-select v-model="form.profitTierId" filterable placeholder="请选择利润分成等级" style="width: 100%">
                <el-option v-for="item in profitTierOptions" :key="item.id" :label="item.tierName" :value="item.id">
                  <span>{{ item.tierName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ item.minProfit }} - {{ item.maxProfit === -1 ? "无上限" : item.maxProfit }} 元
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所有者" prop="ownerId">
              <el-input v-model="staffName" placeholder="请选择所有者" readonly style="width: 100%">
                <el-button slot="append" icon="el-icon-search" @click="showStaffModal"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="车辆图片" prop="imageUrls">
              <div class="upload-tip">（建议上传尺寸750px*750px，格式：jpg,jpeg,png; 大小：建议小于1M。）</div>
              <UploadQiniu
                :limit="10"
                :file-list="img_list"
                @uploadSuccess="uploadSuccess"
                @imgSortChange="imgSortChange"
                @handleRemove="uploadRemove"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="form.remarks" type="textarea" placeholder="请输入备注信息" :rows="3"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 员工选择弹窗 -->
    <staffListModal
      v-if="staffShow"
      :is-show="staffShow"
      :is-check="false"
      @cancel="staffShow = false"
      @confirm="staffSel"
    />
  </ContainerTit>
</template>

<script>
import { addVehicle, checkLicensePlateExists, getAllProfitTier, getVehicleDetail, updateVehicle } from "@/api/CarSale";
import { getStaffInfo } from "@/api/Department";
import { getAllWarehouse } from "@/api/Stock";
import staffListModal from "@/component/common/staffListModal";
import UploadQiniu from "@/component/common/UploadQiniu";
import ContainerTit from "@/component/layout/ContainerTit";

export default {
  name: "VehicleEdit",
  components: {
    ContainerTit,
    UploadQiniu,
    staffListModal,
  },
  data() {
    // 车牌号正则表达式验证
    const licensePlatePattern =
      /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5}$/;

    // 自定义车牌号验证规则
    const validateLicensePlate = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入车牌号"));
        return;
      }

      // 格式验证
      if (!licensePlatePattern.test(value)) {
        callback(new Error("请输入正确格式的车牌号，例如：京A12345"));
        return;
      }

      // 防抖，避免频繁请求
      if (this.licensePlateValidateTimeout) {
        clearTimeout(this.licensePlateValidateTimeout);
      }

      this.licensePlateValidateTimeout = setTimeout(async () => {
        try {
          // 唯一性验证
          const vehicleId = this.isEdit ? this.vehicleId : 0;
          const result = await checkLicensePlateExists(value, vehicleId);

          if (result.errorcode !== 0) {
            callback(new Error(result.message || "车牌已录入"));
          } else {
            // 验证通过，更新缓存
            this.licensePlateValid = true;
            callback();
          }
        } catch (error) {
          console.error("车牌号验证异常", error);
          callback(new Error("车牌号验证失败，请重试"));
        }
      }, 500); // 500ms防抖
    };

    // 所有者验证规则
    const validateOwner = (rule, value, callback) => {
      // 当所有权为业务员时，所有者必填
      if (this.form.ownershipMode === 2 && !value) {
        callback(new Error("请选择所有者"));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      isEdit: false,
      vehicleId: null,
      img_list: [], // 图片列表
      yearOptions: this.generateYearOptions(), // 年份选项列表
      licensePlateValidateTimeout: null, // 防抖定时器
      licensePlateValid: false, // 车牌号验证结果缓存
      warehouseOptions: [], // 仓库选项列表
      profitTierOptions: [], // 利润分成等级选项列表
      staffName: "", // 所有者名称
      staffShow: false, // 员工选择弹窗显示状态
      form: {
        brand: "",
        model: "",
        color: "",
        year: "",
        licensePlate: "",
        ownershipMode: 1,
        ownerId: 0,
        warehouseId: "",
        warehouseName: "",
        saleStatus: 4,
        imageUrls: [],
        remarks: "",
        profitTierId: "",
      },
      rules: {
        brand: [{ required: true, message: "请输入品牌", trigger: "blur" }],
        model: [{ required: true, message: "请输入型号", trigger: "blur" }],
        saleStatus: [{ required: true, message: "请选择销售状态", trigger: "change" }],
        ownershipMode: [{ required: true, message: "请选择所有权模式", trigger: "change" }],
        warehouseId: [{ required: true, message: "请选择所属仓库", trigger: "change" }],
        licensePlate: [
          { required: true, message: "请输入车牌号", trigger: "blur" },
          { validator: validateLicensePlate, trigger: "blur" },
        ],
        ownerId: [{ validator: validateOwner, trigger: "change" }],
      },
    };
  },
  created() {
    // 检查权限
    if (this.isEdit && !this.$accessCheck(this.$Access.Vehicle_Edit)) {
      this.$message.error("您没有编辑车辆的权限");
      this.goBack();
      return;
    }

    if (!this.isEdit && !this.$accessCheck(this.$Access.Vehicle_Add)) {
      this.$message.error("您没有添加车辆的权限");
      this.goBack();
      return;
    }

    this.fetchWarehouseList();
    this.fetchProfitTierList();

    if (this.$route.params.id) {
      this.isEdit = true;
      this.vehicleId = this.$route.params.id;
      this.fetchVehicleDetail();
    } else {
      // 默认设置为公司所有
      this.form.ownershipMode = 1;
      // 默认设置为休息状态
      this.form.saleStatus = 4;
    }
  },
  methods: {
    // 获取仓库列表
    async fetchWarehouseList() {
      try {
        const params = {
          page: 1,
          pageSize: 999,
          enableStatus: 5,
        };
        const res = await getAllWarehouse(params);
        if (res && res.errorcode === 0 && res.data) {
          this.warehouseOptions = res.data || [];
        } else {
          this.$message.warning(res?.message || "获取仓库列表失败");
        }
      } catch (error) {
        console.error("获取仓库列表出错", error);
        this.$message.error("获取仓库列表失败");
      }
    },

    // 仓库选择变更
    handleWarehouseChange(warehouseId) {
      const warehouse = this.warehouseOptions.find((item) => item.id === warehouseId);
      if (warehouse) {
        this.form.warehouseName = warehouse.warehouseName;
      }
    },

    // 生成年份选项，最近20年
    generateYearOptions() {
      const currentYear = new Date().getFullYear();
      const years = [];
      for (let i = 0; i < 20; i++) {
        years.push(currentYear - i);
      }
      return years;
    },

    // 获取车辆详情
    async fetchVehicleDetail() {
      if (!this.vehicleId) return;

      this.loading = true;
      try {
        const res = await getVehicleDetail(this.vehicleId);
        if (res.errorcode === 0 && res.data) {
          // 填充表单数据
          this.form = {
            ...this.form,
            ...res.data,
          };

          // 如果有所有者ID，获取所有者信息
          if (this.form.ownerId) {
            try {
              const staffRes = await getStaffInfo(this.form.ownerId);
              if (staffRes.errorcode === 0 && staffRes.data) {
                this.staffName = staffRes.data.staffName || "";
              }
            } catch (error) {
              console.error("获取所有者信息出错", error);
            }
          }

          // 处理图片数据
          if (this.form.imageUrls && this.form.imageUrls.length > 0) {
            // 转换为图片上传组件需要的格式
            this.img_list = this.form.imageUrls.map((url, index) => ({
              uid: index,
              name: `image-${index}.jpg`,
              content: url,
              url: url,
            }));
          }
        } else {
          this.$message.error(res.message || "获取车辆详情失败");
        }
      } catch (error) {
        console.error("获取车辆详情出错", error);
        this.$message.error("获取车辆详情失败");
      } finally {
        this.loading = false;
      }
    },

    // 图片上传成功
    uploadSuccess(uploadPicUrl, fileParam, file, fileList) {
      this.img_list = fileList;
      this.form.imageUrls = fileList.map((item) => item.content);
    },

    // 图片顺序变更
    imgSortChange(fileList) {
      this.img_list = fileList;
      this.form.imageUrls = fileList.map((item) => item.content);
    },

    // 移除图片
    uploadRemove(file, fileList) {
      this.img_list = fileList;
      this.form.imageUrls = fileList.map((item) => item.content);
    },

    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          // 确保车牌号验证通过
          if (!this.licensePlateValid && this.form.licensePlate) {
            try {
              // 再次验证车牌号唯一性
              const vehicleId = this.isEdit ? this.vehicleId : 0;
              const result = await checkLicensePlateExists(this.form.licensePlate, vehicleId);
              if (result.code !== 0) {
                this.$message.error(result.message || "车牌已录入");
                return false;
              }
            } catch (error) {
              console.error("车牌号验证异常", error);
              this.$message.error("车牌号验证失败，请重试");
              return false;
            }
          }

          this.loading = true;
          try {
            // 准备提交数据
            const submitData = { ...this.form };

            let res;
            if (this.isEdit) {
              // 编辑模式
              res = await updateVehicle(this.vehicleId, submitData);
            } else {
              // 添加模式
              res = await addVehicle(submitData);
            }

            if (res.errorcode === 0) {
              this.$message.success(`${this.isEdit ? "更新" : "添加"}车辆信息成功`);
              this.goBack();
            } else {
              this.$message.error(res.message || `${this.isEdit ? "更新" : "添加"}车辆信息失败`);
            }
          } catch (error) {
            console.error(`${this.isEdit ? "更新" : "添加"}车辆信息出错`, error);
            this.$message.error(`${this.isEdit ? "更新" : "添加"}车辆信息失败`);
          } finally {
            this.loading = false;
          }
        } else {
          console.log("表单验证失败");
          return false;
        }
      });
    },

    // 返回列表页
    goBack() {
      this.$router.push({ name: "VehicleList" });
    },

    // 处理车牌号输入，自动转换为大写
    handleLicensePlateInput(value) {
      // 如果输入的内容包含小写字母，转换为大写
      if (value !== value.toUpperCase()) {
        this.form.licensePlate = value.toUpperCase();
        // 重置验证状态，因为内容已经变更
        this.licensePlateValid = false;
      }
    },

    // 获取利润分成等级列表
    async fetchProfitTierList() {
      try {
        const res = await getAllProfitTier();
        if (res && res.errorcode === 0 && res.data) {
          this.profitTierOptions = res.data || [];
        } else {
          this.$message.warning(res?.message || "获取利润分成等级列表失败");
        }
      } catch (error) {
        console.error("获取利润分成等级列表出错", error);
        this.$message.error("获取利润分成等级列表失败");
      }
    },

    // 显示员工选择弹窗
    showStaffModal() {
      this.staffShow = true;
    },

    // 员工选择回调
    staffSel(val) {
      if (val && val.length > 0) {
        const staff = val[0];
        this.staffName = staff.staffName;
        this.form.ownerId = staff.id;
      }
      this.staffShow = false;
    },
  },
};
</script>

<style scoped>
.page-div {
  padding: 10px 20px;
}
.upload-tip {
  color: #909399;
  font-size: 12px;
  margin-bottom: 10px;
}
</style>
