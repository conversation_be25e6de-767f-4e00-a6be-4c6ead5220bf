<template>
  <ContainerQuery>
    <div slot="left">
      <el-button v-if="$accessCheck($Access.Vehicle_Add)" type="primary" size="small" @click="handleAddVehicle"
        >添加车辆</el-button
      >
    </div>

    <!-- 搜索表单 -->
    <div slot="more">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" size="small">
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            clearable
            style="width: 220px"
            placeholder="请输入品牌/型号/车牌号"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchForm.saleStatus"
            clearable
            style="width: 100px"
            placeholder="车辆状态"
            @change="handleSearch"
          >
            <el-option label="营业" :value="5"></el-option>
            <el-option label="休息" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchForm.warehouseId"
            clearable
            style="width: 220px"
            placeholder="所属仓库"
            @change="handleSearch"
          >
            <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.warehouseName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchForm.ownershipMode"
            clearable
            style="width: 100px"
            placeholder="所有权模式"
            @change="handleOwnershipModeChange"
          >
            <el-option label="公司" :value="1"></el-option>
            <el-option label="业务员" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="searchForm.ownershipMode === 2">
          <el-input v-model="staffName" placeholder="选择员工" readonly clearable style="width: 220px">
            <el-button slot="append" icon="el-icon-search" @click="showStaffModal"></el-button>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <!-- 车辆列表表格 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column type="index" width="50" label="序号"></el-table-column>
      <el-table-column prop="brand" label="品牌" min-width="100"></el-table-column>
      <el-table-column prop="model" label="型号" min-width="120"></el-table-column>
      <el-table-column prop="year" label="年份" width="80"></el-table-column>
      <el-table-column prop="color" label="颜色" width="80"></el-table-column>
      <el-table-column prop="warehouseName" label="所属仓库" min-width="120"></el-table-column>
      <el-table-column label="所有者" min-width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.ownershipMode === 1" type="info" size="mini">公司</el-tag>
          <template v-else-if="scope.row.ownershipMode === 2">
            <el-tag v-if="scope.row.ownerName" type="success" size="mini">
              {{ scope.row.ownerName }}
            </el-tag>
            <el-tag v-else-if="scope.row.ownerId" type="warning" size="mini">未知员工</el-tag>
            <el-tag v-else type="danger" size="mini">无</el-tag>
          </template>
          <span v-else class="no-data">-</span>
        </template>
      </el-table-column>
      <el-table-column label="利润分成等级" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.profitTierName">{{ scope.row.profitTierName }}</span>
          <span v-else class="no-data">未设置</span>
        </template>
      </el-table-column>
      <el-table-column label="营业状态" width="100" prop="saleStatus">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.saleStatus"
            :active-value="5"
            :inactive-value="4"
            :disabled="!$accessCheck($Access.Vehicle_Status)"
            @change="(val) => handleStatusChange(scope.row, val)"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="200">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.Vehicle_Edit)"
            size="mini"
            type="text"
            @click="handleEditVehicle(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-if="$accessCheck($Access.Vehicle_Delete)"
            size="mini"
            type="text"
            @click="handleDeleteVehicle(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>

    <!-- 员工选择弹窗 -->
    <staffListModal
      v-if="staffShow"
      :is-show="staffShow"
      :is-check="false"
      @cancel="staffShow = false"
      @confirm="staffSel"
    />
  </ContainerQuery>
</template>

<script>
import { deleteVehicle, getAllProfitTier, getAllVehicle, updateVehicleStatus } from "@/api/CarSale";
import { getAllWarehouse } from "@/api/Stock";
import staffListModal from "@/component/common/staffListModal";
import ContainerQuery from "@/component/layout/ContainerQuery";

export default {
  name: "VehicleList",
  components: {
    ContainerQuery,
    staffListModal,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      pageIndex: 1,
      pageSize: 10,
      warehouseOptions: [], // 仓库选项列表
      profitTierOptions: [], // 利润分成等级选项列表
      staffName: "", // 所有者名称
      staffShow: false, // 员工选择弹窗显示状态
      searchForm: {
        keyword: "",
        status: "",
        warehouseId: "",
        profitTierId: "",
        ownershipMode: "", // 所有权模式
        ownerId: "", // 所有者ID
      },
    };
  },
  created() {
    this.fetchData();
    this.fetchWarehouseList();
    this.fetchProfitTierList();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        // 处理搜索参数
        const params = {
          page: this.pageIndex,
          pageSize: this.pageSize,
        };

        params.keyword = this.searchForm.keyword;

        // 添加其他搜索条件
        if (this.searchForm.warehouseId) {
          params.warehouseId = this.searchForm.warehouseId;
        }

        if (this.searchForm.saleStatus) {
          params.saleStatus = this.searchForm.saleStatus;
        }

        if (this.searchForm.ownershipMode) {
          params.ownershipMode = this.searchForm.ownershipMode;
          if (this.searchForm.ownershipMode === 2 && this.searchForm.ownerId) {
            params.ownerId = this.searchForm.ownerId;
          }
        }

        const res = await getAllVehicle(params);
        if (res.errorcode === 0) {
          this.tableData = this.processFetchedData(res.data || []);
          this.total = res.data_total || 0;
        } else {
          this.$message.error(res.message || "获取车辆列表失败");
        }
      } catch (error) {
        console.error("获取车辆列表出错", error);
        this.$message.error("获取车辆列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 获取仓库列表
    async fetchWarehouseList() {
      try {
        const params = {
          page: 1,
          pageSize: 1000,
          enableStatus: 5,
        };
        const res = await getAllWarehouse(params);
        if (res && res.errorcode === 0 && res.data) {
          this.warehouseOptions = res.data || [];
        }
      } catch (error) {
        console.error("获取仓库列表出错", error);
      }
    },

    // 获取利润分成等级列表
    async fetchProfitTierList() {
      try {
        const res = await getAllProfitTier();
        if (res && res.errorcode === 0 && res.data) {
          this.profitTierOptions = res.data || [];
        }
      } catch (error) {
        console.error("获取利润分成等级列表出错", error);
      }
    },

    // 搜索
    handleSearch() {
      this.pageIndex = 1;
      this.fetchData();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: "",
        status: "",
        warehouseId: "",
        profitTierId: "",
        ownershipMode: "",
        ownerId: "",
      };
      this.staffName = "";
      this.handleSearch();
    },

    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchData();
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.pageIndex = val;
      this.fetchData();
    },

    // 添加车辆
    handleAddVehicle() {
      this.$router.push({ name: "VehicleAdd" });
    },

    // 编辑车辆
    handleEditVehicle(row) {
      this.$router.push({ name: "VehicleEdit", params: { id: row.id } });
    },

    // 删除车辆
    handleDeleteVehicle(row) {
      this.$confirm("确认删除该车辆信息?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const res = await deleteVehicle(row.id);
            if (res.errorcode === 0) {
              this.$message.success("删除成功");
              this.fetchData();
            } else {
              this.$message.error(res.message || "删除失败");
            }
          } catch (error) {
            console.error("删除车辆出错", error);
            this.$message.error("删除失败");
          }
        })
        .catch(() => {});
    },

    // 更新车辆状态
    async handleStatusChange(row, status) {
      try {
        const res = await updateVehicleStatus(row.id, { saleStatus: status });
        if (res.errorcode === 0) {
          this.$message.success("更新状态成功");
          this.fetchData();
        } else {
          this.$message.error(res.message || "更新状态失败");
        }
      } catch (error) {
        console.error("更新车辆状态出错", error);
        this.$message.error("更新状态失败");
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        4: "休息",
        5: "营业",
      };
      return statusMap[status] || "未知";
    },

    // 处理车辆数据，添加利润分成等级名称
    processFetchedData(data) {
      return data.map((item) => {
        // 如果有利润分成等级ID，找到对应的名称
        if (item.profitTierId && this.profitTierOptions.length > 0) {
          const profitTier = this.profitTierOptions.find((tier) => tier.id == item.profitTierId);
          if (profitTier) {
            item.profitTierName = profitTier.tierName;
          }
        }
        return item;
      });
    },

    // 所有权模式变化处理
    handleOwnershipModeChange(val) {
      if (val !== 2) {
        this.searchForm.ownerId = "";
        this.staffName = "";
      }
      this.handleSearch();
    },

    // 显示员工选择弹窗
    showStaffModal() {
      this.staffShow = true;
    },

    // 员工选择回调
    staffSel(val) {
      if (val && val.length > 0) {
        const staff = val[0];
        this.staffName = staff.staffName;
        this.searchForm.ownerId = staff.id;
        this.handleSearch();
      }
      this.staffShow = false;
    },
  },
};
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
