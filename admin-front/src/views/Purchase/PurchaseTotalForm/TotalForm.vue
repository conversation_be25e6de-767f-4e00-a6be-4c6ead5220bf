<template>
  <ContainerQuery>
    <div slot="more">
      <el-form inline size="small" style="margin-bottom: 0">
        <el-form-item>
          <el-input
            v-model="search_form.goodsName"
            placeholder="商品名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="search_form.inStatus" placeholder="入库状态" style="width: 150px" @change="pageChange(1)">
            <el-option
              v-for="item in shenhe_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="search_form.time"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="timestamp"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <GoodsCategory
            v-model="category"
            clearable
            size="small"
            placeholder="商品分类"
            :check-strictly="true"
            width="150"
            @change="categoryChange"
          />
        </el-form-item>
        <!--        <el-form-item label="店铺">-->
        <!--          <SelectShop-->
        <!--            v-model="search_form.shopId"-->
        <!--            :is-default="true"-->
        <!--            :clearable="false"-->
        <!--            @change="selShop"-->
        <!--            @default="shopDefault"-->
        <!--          />-->
        <!--        </el-form-item>-->
        <el-form-item prop="warehouseName">
          <div>
            <el-input v-model="search_form.warehouseName" placeholder="采购仓库" readonly>
              <i slot="suffix" class="el-input__icon el-icon-search" @click="openWarehouse()"></i>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item v-if="type === 2">
          <SelectSupplier v-model="search_form.supplierId" @clear="supplierClear" @change="pageChange(1)" />
        </el-form-item>
        <el-form-item v-if="type === 3">
          <el-input v-model="search_form.buyerName" placeholder="采购人员" style="width: 200px" readonly>
            <i slot="suffix" class="el-input__icon el-icon-search" @click="staff_show = true"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="onReturn" @change="removeChange"> 去除采退 </el-checkbox>
        </el-form-item>
      </el-form>
    </div>
    <div slot="left">
      <el-button size="small" type="primary" @click="getAllData(1)"> 导出 </el-button>
    </div>
    <el-table size="mini" :data="tableData" show-summary :summary-method="getSummaries">
      <el-table-column prop="id" label="ID" fixed="left" width="50"></el-table-column>
      <el-table-column
        v-if="form_name === '商品类别'"
        prop="categoryName"
        label="商品类别"
        min-width="100"
      ></el-table-column>
      <el-table-column prop="warehouseName" label="仓库" min-width="100"></el-table-column>
      <el-table-column
        v-if="form_name === '供应商'"
        prop="supplierName"
        label="供应商"
        min-width="120"
      ></el-table-column>
      <el-table-column
        v-if="form_name === '采购人员'"
        prop="buyerName"
        label="采购人员"
        min-width="120"
      ></el-table-column>
      <el-table-column prop="materielName" show-overflow-tooltip label="商品" min-width="160"></el-table-column>
      <el-table-column prop="materielCode" label="商品编码" min-width="140"></el-table-column>
      <el-table-column prop="unitName" label="规格" min-width="80">
        <template slot-scope="scope"> {{ scope.row.unitName }};{{ scope.row.skuName }} </template>
      </el-table-column>
      <!-- <el-table-column
        prop="shopName"
        label="商铺"
        min-width="160"
        show-overflow-tooltip
      ></el-table-column>-->
      <el-table-column prop="allNum" label="数量" header- min-width="100">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.allNum, 2) }}
          <div v-if="scope.row.isEq === 5">其他单位:{{ $_common.formatNub(scope.row.otherNum) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="inStatus" label="入库状态" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.inStatus === 4 ? "未入库" : "已入库" }}
        </template>
      </el-table-column>
      <el-table-column prop="costPrice" label="成本" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.costPrice, 2) }}
        </template>
      </el-table-column>
      <el-table-column prop="total" label="总成本" header- min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.total, 2) }}
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <WarehouseModel
      v-if="warehouse_show"
      :is-check="false"
      :is-show="warehouse_show"
      @confirm="selWarehouse"
      @cancel="warehouse_show = false"
    />
    <StaffList
      v-if="staff_show"
      :is-show="staff_show"
      :is-check="false"
      :isserch="true"
      @cancel="staff_show = false"
      @confirm="staffSel"
    />
  </ContainerQuery>
</template>

<script>
import GoodsCategory from "@/component/common/GoodsCategory.vue";
import FooterPage from "@/component/common/FooterPage";
import SelectShop from "@/component/goods/SelectShop.vue";
import { getAllPurchaseByFields, exportgetAllPurchaseByFields } from "@/api/Purchase";
import WarehouseModel from "@/component/common/WarehouseModel.vue";
import SelectSupplier from "@/component/common/SelectSupplier.vue";
import StaffList from "@/component/common/staffListModal";

import { exportgetAllSupplierBalanceDetail, getAllSupplierBalanceDetail } from "@/api/Finance";
export default {
  name: "GoodsForm",
  components: {
    GoodsCategory,
    FooterPage,
    // SelectShop,
    WarehouseModel,
    SelectSupplier,
    StaffList,
  },
  data() {
    return {
      //  审核状态
      shenhe_options: [
        { value: 0, label: "全部状态" },
        { value: 5, label: "已入库" },
        { value: 4, label: "未入库" },
      ],
      category: [],
      pageSize: 10,
      page: 1,
      total: 0,
      tableData: [],
      onReturn: true,
      search_form: {
        inStatus: "",
        goodsName: "",
        categoryId: "",
        shopId: "",
        start: "",
        end: "",
        onReturn: 1,
        warehouseName: "",
        warehouseId: "",
        supplierId: "",
        buyerId: "",
        buyerName: "",
      },
      type: 1,
      form_name: "商品类别",
      warehouse_show: false,
      staff_show: false,
    };
  },
  created() {
    switch (this.$route.path) {
      case "/Purchase/PurchaseTotalForm/GoodsForm":
        this.form_name = "商品类别";
        this.type = 1;
        break;
      case "/Purchase/PurchaseTotalForm/SupplierForm":
        this.form_name = "供应商";
        this.type = 2;
        break;
      case "/Purchase/PurchaseTotalForm/StaffForm":
        this.form_name = "采购人员";
        this.type = 3;
        break;
    }
    this.pageChange(1);
  },
  methods: {
    openWarehouse() {
      this.warehouse_show = true;
    },
    selWarehouse(row) {
      this.search_form.warehouseName = row[0].warehouseName;
      this.search_form.warehouseId = row[0].id;
      this.pageChange(1);
    },
    shopDefault(val) {
      this.search_form.shopId = val;
      this.pageChange(1);
    },
    //  选择商铺
    selShop(row) {
      this.pageChange(1);
    },
    //  去除caitui
    removeChange(val) {
      this.search_form.onReturn = val ? 1 : 0;
      this.pageChange(1);
    },
    //  获取汇总表(商品)
    async getAllData(exports) {
      let params = {
        inStatus: this.search_form.inStatus, // 状态
        goodsName: this.search_form.goodsName,
        categoryId: this.search_form.categoryId,
        warehouseId: this.search_form.warehouseId,
        start: this.search_form.start,
        end: this.search_form.end,
        type: this.type,
        onReturn: this.search_form.onReturn,
        page: this.page,
        pageSize: this.pageSize,
        supplierId: this.search_form.supplierId,
        buyerId: this.search_form.buyerId,
      };
      if (exports) {
        params.export = 1;
        const target = await exportgetAllPurchaseByFields({
          ...params,
        });
      } else {
        const { data, pageTotal } = await getAllPurchaseByFields({
          ...params,
        });
        this.tableData = data;
        this.total = pageTotal;
      }
    },
    //  时间搜索
    timeChange(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    //  分类搜索
    categoryChange(val) {
      if (val && val.length) {
        this.category = val;
        this.search_form.categoryId = val[val.length - 1];
      } else {
        this.search_form.categoryId = "";
        this.category = "";
      }
      this.pageChange(1);
    },
    // 合计
    getSummaries(param) {
      return this.$_common.getSummaries(param, ["单价", "销售收入", "成本", "总成本", "毛利", "总毛利"]);
    },
    handleClose() {
      this.dialogVisible = false;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    supplierClear() {
      this.search_form.supplierId = "";
      this.pageChange(1);
    },
    staffSel(row) {
      this.search_form.buyerName = row[0].staffName;
      this.search_form.buyerId = row[0].id;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
