<template>
  <ContainerTit class="CostPriceAdd">
    <div style="position: relative">
      <div class="btn-top-div">
        <!--<el-button>清除暂存</el-button>-->
        <!--<el-button>暂存</el-button>-->
        <el-button
          v-if="$accessCheck($Access.CostPriceAddCostPrice) || $accessCheck($Access.CostPriceEditCostPrice)"
          type="primary"
          @click="saveCostPrice"
          >保存
        </el-button>
      </div>
    </div>
    <el-form ref="form" :model="formData" :inline="true" size="small">
      <el-tabs v-model="activeName" class="main-tabs">
        <el-tab-pane :label="title" name="one">
          <el-row style="padding-bottom: 13px">
            <el-col :span="24">
              <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">商品详情</p>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="选择商品：">
                <el-input
                  v-model="formData.basicGoodsName"
                  :disabled="!!basicGoodsId"
                  readonly
                  placeholder="请选择商品"
                >
                  <i slot="suffix" class="el-input__icon el-icon-search" @click="openGoodsModel"></i>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="调价人员：">
                {{ formData.operatorName }}
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="调价时间：">
                {{ $_common.formatDate(formData.createTime) }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane v-if="basicGoodsId" label="价格趋势" name="two"></el-tab-pane>
      </el-tabs>
      <div v-if="activeName === 'one'" class="order_bottom">
        <div class="title">
          <div class="text">价格管理</div>
          <div style="align-content: center">
            <el-button type="primary" size="small" :disabled="!formData.basicGoodsId" @click="supplier_show = true"
              >新增供应商
            </el-button>
          </div>
        </div>

        <el-tabs v-model="activeSupplier" tab-position="left" :closable="true" @tab-remove="handleRemoveSupplier">
          <el-tab-pane
            v-for="(item, index) in formData.data"
            :key="item.id"
            :label="item.supplierName"
            :name="item.supplierName"
          >
            <!--无属性成本价-->
            <div v-if="false"></div>
            <!--多属性成本价-->
            <div v-if="true">
              <el-tabs v-model="activeUnitName" type="card">
                <el-tab-pane v-for="(unit, indexU) in item.unitData" :key="unit.unitId" :name="unit.unitName">
                  <span v-if="unit.isMaster === 5" slot="label">{{ unit.unitName }}</span>
                  <span v-else slot="label">{{
                    `${unit.unitName}/${$_common.formatNub(unit.conversion)}${masterUnitName}`
                  }}</span>
                  <el-table :data="unit.costPriceData">
                    <el-table-column label="属性" prop="skuName"></el-table-column>
                    <el-table-column label="是否多仓库">
                      <template slot-scope="scope">
                        <el-switch
                          v-model="scope.row.isMultiWarehouse"
                          active-color="#36B365"
                          inactive-color="#ff4949"
                          :active-value="5"
                          :inactive-value="4"
                          @change="handleMultiWarehouseChange(index, indexU, scope.$index)"
                        ></el-switch>
                      </template>
                    </el-table-column>
                    <el-table-column label="成本价/元">
                      <template slot-scope="scope">
                        <template v-if="scope.row.isMultiWarehouse === 4">
                          <el-input-number
                            v-model="scope.row.costPrice"
                            size="mini"
                            :controls="false"
                            :min="0"
                            style="width: 100%"
                          ></el-input-number>
                        </template>
                        <template v-else>
                          <div v-for="(itemW, indexW) in scope.row.costPrice" :key="indexW">
                            <div>{{ allWarehouseObj[itemW.warehouseId] }}</div>
                            <el-input-number
                              v-model="itemW.price"
                              size="mini"
                              :controls="false"
                              :min="0"
                              style="width: 100%"
                            ></el-input-number>
                          </div>
                        </template>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-form>
    <SupplierListModal
      v-if="supplier_show"
      :is-show="supplier_show"
      :modal="true"
      @confirm="addSupplier"
      @cancel="supplier_show = false"
    />
    <!--    商品列表-->
    <GoodsWarehouse
      v-if="show_goods"
      :modal="!formData.basicGoodsId"
      goods-warehouse
      :is-check="false"
      :is-show="show_goods"
      :enable="true"
      @cancel="show_goods = false"
      @confirm="selGoods"
    />
  </ContainerTit>
</template>

<script>
import SupplierListModal from "@/component/common/SupplierListModal.vue";
import GoodsWarehouse from "@/component/goods/GoodsWarehouse.vue";
import { getGoodsBasicInfoById } from "@/api/goods";
import { getAllWarehouse } from "@/api/Stock";
import { CostPriceSupplierSave, CostPriceSupplierGetByBasicGoodsId, CostPriceSupplierIsExistGoods } from "@/api/Price";

export default {
  name: "CostPriceForm",
  components: { GoodsWarehouse, SupplierListModal },
  data() {
    return {
      basicGoodsId: null,
      formData: {
        basicGoodsId: "",
        basicGoodsCode: "",
        basicGoodsName: "",
        operatorName: "",
        createTime: new Date().getTime(),
        data: [],
      },
      activeName: "one",
      activeSupplier: "",
      activeUnitName: "",
      supplier_show: false,
      show_goods: false,
      base_goods: {},
      masterUnitName: "",
      allWarehouseObj: {},
      isEdit: false,
    };
  },
  computed: {
    title() {
      return this.basicGoodsId ? "编辑成本价" : "新增成本价";
    },
    userCenterId() {
      return this.$store.getters["MUser/userCenterId"];
    },
  },
  created() {
    this.getAllWarehouse();
    if (this.$route.params.id) {
      this.basicGoodsId = this.$route.params.id;
      this.getCostPriceSupplierDetails();
      this.selGoods(null, this.basicGoodsId);
    } else {
      this.formData.operatorName = this.userName;
      this.formData.operatorId = this.userCenterId;
    }
  },
  methods: {
    // 获取成本价供应商详情
    async getCostPriceSupplierDetails() {
      const { data } = await CostPriceSupplierGetByBasicGoodsId(this.basicGoodsId);
      this.formData = data;
      this.formData.createTime = data.createTime * 1000;
      this.activeSupplier = this.formData.data[0].supplierName;
      this.activeUnitName = this.formData.data[0].unitData[0].unitName;
      this.isEdit = true;
    },
    async saveCostPrice() {
      // 校验成本价
      for (const i in this.formData.data) {
        for (const j in this.formData.data[i].unitData) {
          for (const k in this.formData.data[i].unitData[j].costPriceData) {
            const costPrice = this.formData.data[i].unitData[j].costPriceData[k];
            if (costPrice.isMultiWarehouse === 4) {
              if (costPrice.costPrice <= 0) {
                this.$message({
                  message: "请设置成本价",
                  type: "error",
                });
                return;
              }
            } else {
              for (const l in costPrice.costPrice) {
                if (costPrice.costPrice[l].price <= 0) {
                  this.$message({
                    message: "请设置成本价",
                    type: "error",
                  });
                  return;
                }
              }
            }
          }
        }
      }

      // 保存成本价
      const formData = {
        ...this.formData,
        createTime: this.formData.createTime / 1000,
      };
      await CostPriceSupplierSave(formData);
      this.$message({
        type: "success",
        message: "保存成功",
      });
      this.$closeCurrentGoEdit("/Purchase/CostPrice/CostPriceList");
    },
    addSupplier(list) {
      const supplierIds = this.formData.data.map((it) => it.supplierId);
      list.forEach((item) => {
        if (!supplierIds.includes(item.id)) {
          this.activeSupplier = item.title;
          this.activeUnitName = this.base_goods.unitData[0].unitName;
          const unitData = [];
          this.base_goods.unitData.forEach((unit) => {
            unitData.push({
              unitId: unit.unitId,
              unitName: unit.unitName,
              isMaster: unit.isMaster,
              conversion: unit.conversion,
              costPriceData: this.base_goods.specMultiple
                .filter((spec) => spec.unitId === unit.unitId)
                .map((spec) => {
                  return {
                    specImage: spec.specImage,
                    skuId: spec.id,
                    skuName: spec.specGroup[0].specValueName,
                    isMultiWarehouse: 4,
                    costPrice: 0,
                  };
                }),
            });
          });
          const data = {
            supplierId: item.id,
            supplierName: item.title,
            unitData: unitData,
          };
          this.formData.data.push(data);
        }
      });
    },
    openGoodsModel() {
      this.show_goods = true;
    },

    async selGoods(row, basicGoodsId) {
      const id = basicGoodsId || row[0].id;
      // 判断是否已存在商品成本价
      console.log(!this.basicGoodsId);
      if (!this.basicGoodsId) {
        const isExist = await CostPriceSupplierIsExistGoods(id);
        if (isExist.data) {
          const confirm = await this.$confirm("该商品已存在成本价，去编辑?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          });
          if (confirm === "confirm") {
            this.$closeCurrentGoEdit(`/Purchase/CostPrice/CostPriceEdit/${id}`);
            return;
          }
        }
      }
      const { data } = await getGoodsBasicInfoById(id, {
        isAddGoods: 5,
      });
      this.base_goods = data;
      this.formData.basicGoodsId = data.id;
      this.formData.basicGoodsCode = data.code;
      this.formData.basicGoodsName = data.title;
      this.base_goods.unitData.forEach((unit) => {
        if (unit.isMaster === 5) {
          this.masterUnitName = unit.unitName;
        }
      });
    },

    handleMultiWarehouseChange(index, indexU, indexS) {
      const formData = this.$_common.deepClone(this.formData);
      const costPriceData = formData.data[index].unitData[indexU].costPriceData[indexS];
      if (costPriceData.isMultiWarehouse === 5) {
        const costPrice = [];
        for (const warehouseId in this.allWarehouseObj) {
          costPrice.push({
            warehouseId: parseInt(warehouseId),
            price: 0,
          });
        }
        costPriceData.costPrice = costPrice;
        this.formData = formData;
      }
    },

    handleRemoveSupplier(tab) {
      const index = this.formData.data.findIndex((item) => item.supplierName === tab.name);
      this.formData.data.splice(index, 1);
    },

    getAllWarehouse() {
      getAllWarehouse({
        page: 1,
        pageSize: 999,
        enableStatus: 5,
        enablePricing: 5,
      }).then((res) => {
        if (res.errorcode === 0) {
          this.allWarehouseObj = {};
          res.data.forEach((item) => {
            this.allWarehouseObj[item.id] = item.warehouseName;
          });
        } else {
          this.$message({
            message: "获取仓库列表失败",
            type: "error",
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.btn-top-div {
  position: absolute;
  right: 20px;
  top: 15px;
  z-index: 999;
}

.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}

.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;

  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }

  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}

.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;

  .title {
    padding: 0 24px;
    border-bottom: 1px solid #ecf0f7;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;

    .text {
      font-size: 16px;
      font-weight: 600;
      color: #2d405e;
      line-height: 64px;
    }
  }
}

.CostPriceAdd {
  background-color: #fff;
  .main-tabs {
    ::v-deep .el-tabs__item {
      font-size: 16px;
      height: 60px;
      line-height: 60px;
    }
    ::v-deep .is-active {
      font-weight: 700;
      color: #000;
    }
    ::v-deep .el-tabs__nav {
      margin-left: 24px;
    }
  }
  ::v-deep .el-tabs--card {
    .el-tabs__header {
      //border-bottom: none;
    }
  }
}
</style>
