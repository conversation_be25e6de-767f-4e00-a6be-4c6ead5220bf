<template>
  <ContainerQuery>
    <div slot="left">
      <el-button v-if="$accessCheck($Access.CostPriceAddCostPrice)" type="primary" @click="addData"
        >新增成本价</el-button
      >
    </div>
    <div slot="more">
      <el-form :model="searchData" size="small" inline>
        <el-form-item prop="keywork">
          <el-input v-model="searchData.keyword" placeholder="商品名称/商品编码" @keyup.native.enter="search">
            <el-button slot="append" @click="search">
              <i class="el-icon-search"></i>
            </el-button>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData">
      <el-table-column label="商品编码" prop="basicGoodsCode"></el-table-column>
      <el-table-column label="商品名称" prop="basicGoodsName"></el-table-column>
      <el-table-column label="状态" prop="enableStatus">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enableStatus"
            active-color="#36B365"
            inactive-color="#ff4949"
            :active-value="5"
            :inactive-value="4"
            @change="changeStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="$router.push(`/Purchase/CostPrice/CostPriceEdit/${scope.row.basicGoodsId}`)"
            >编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import * as $Access from "@/access/node";
import { CostPriceSupplierEnableStatus, CostPriceSupplierGetAll } from "@/api/Price";

export default {
  name: "CostPriceList",
  data() {
    return {
      tableData: [],
      total: 1,
      page: 1,
      pageSize: 10,
      searchData: {
        keyword: "",
      },
    };
  },
  computed: {
    $Access() {
      return $Access;
    },
  },
  created() {
    this.getAllCostPrice();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllCostPrice();
  },
  methods: {
    async getAllCostPrice() {
      const params = {
        keyword: this.searchData.keyword,
        page: this.page,
        pageSize: this.pageSize,
      };
      const { data, pageTotal } = await CostPriceSupplierGetAll(params);
      this.tableData = data;
      this.total = pageTotal;
    },
    async search() {
      this.page = 1;
      await this.getAllCostPrice();
    },
    pageChange(page) {
      this.page = page;
      this.getAllCostPrice();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    addData() {
      this.$router.push("/Purchase/CostPrice/CostPriceAdd");
    },
    async delData(basicGoodsId) {
      this.$confirm("确定要删除该商品的成本价吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await CostPriceSupplierDel(basicGoodsId);
        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.getAllCostPrice();
      });
    },
    async changeStatus(row) {
      const params = {
        basicGoodsId: row.basicGoodsId,
        enableStatus: row.enableStatus,
      };
      await CostPriceSupplierEnableStatus(params);
    },
  },
};
</script>

<style scoped></style>
