<template>
  <ContainerTit class="MerchantPurchaseDetail">
    <div style="position: relative">
      <div v-if="systemType === 1 && form.auditStatus === 1" class="btn-top-div">
        <el-button type="primary" :loading="loading" @click="updatePurchase"> 审核 </el-button>
      </div>
    </div>
    <div>
      <el-tabs v-model="activeName">
        <el-tab-pane label="商户采购单详情" name="one">
          <el-row style="padding-bottom: 13px">
            <el-col :span="24">
              <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">商户采购单信息</p>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">采购单号</span>
              <span class="form_right">{{ form.no }}</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">采购仓库</span>
              <span class="form_right">{{ form.warehouseName }}</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">采购人员</span>
              <span class="form_right">{{ form.buyerName }}</span>
            </el-col>
            <el-col v-if="systemType !== 3" class="form" :span="6" style="padding-left: 68px">
              <span class="form_left">商户</span>
              <span class="form_right">{{ form.merchantName }}</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">制单人员</span>
              <span class="form_right">{{ form.operatorName }}</span>
            </el-col>
            <el-col class="form" :span="6">
              <span class="form_left">制单时间</span>
              <span class="form_right">
                {{ $_common.formatDate(createTime) }}
              </span>
            </el-col>
            <el-col v-if="form.auditStatus === 2" class="form" :span="6">
              <span class="form_left">审核人员</span>
              <span class="form_right">
                {{ form.auditName }}
              </span>
            </el-col>
            <el-col v-if="form.auditStatus === 2" class="form" :span="6">
              <span class="form_left">审核时间</span>
              <span class="form_right">
                {{ $_common.formatDate(form.auditTime) }}
              </span>
            </el-col>
            <el-col class="form" :span="24" style="padding-left: 68px">
              <span class="form_left">备注</span>
              <span class="form_right">
                {{ form.remark || "无" }}
              </span>
            </el-col>
          </el-row>
          <el-alert v-if="err_tip_list.length" title="采购数量换算提示" type="error" show-icon close-text="知道了">
            <ul>
              <li v-for="(item, index) in err_tip_list" :key="index">
                商品【{{ item.goodsName }}】，单位【{{ item.unitName }}】，编码【{{
                  item.goodsCode
                }}】，数量换算有误：{{ item.title }}
              </li>
            </ul>
          </el-alert>
        </el-tab-pane>
      </el-tabs>
      <div class="order_bottom">
        <p class="text">付款单明细</p>
        <el-table :data="goods_list" show-summary size="mini" :summary-method="getSummaries">
          <el-table-column label="#" width="60" type="index"></el-table-column>
          <el-table-column label="商品编码" width="140" prop="goodsCode" show-overflow-tooltip></el-table-column>
          <el-table-column prop="goodsName" label="商品名称" min-width="180">
            <template slot-scope="scope">
              {{ scope.row.goodsName }}
            </template>
          </el-table-column>

          <el-table-column prop="specGropName" label="规格" min-width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.unitName">{{ scope.row.unitName }};</span>
              <span>{{ scope.row.specGropName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="buyerNum" label="采购数量" min-width="100">
            <template slot-scope="scope">
              {{ $_common.formatNub(scope.row.buyerNum) }}
            </template>
          </el-table-column>
          <el-table-column prop="buyerNum" label="其他单位" min-width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.isEq === 5">
                {{ $_common.formatNub(scope.row.otherNum) }}
              </span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="skuValue" label="换算比例数量" min-width="120"></el-table-column>
          <el-table-column prop="skuNum" label="转换数量" min-width="80"></el-table-column>
          <el-table-column prop="buyerUnitPrice" label="采购单价" min-width="100">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.buyerUnitPrice) }}
            </template>
          </el-table-column>
          <el-table-column prop="subtotalPrice" label="小计金额" min-width="100">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.subtotalPrice) }}
            </template>
          </el-table-column>
          <el-table-column prop="storage" label="货架编码" min-width="100"></el-table-column>
          <el-table-column prop="barCode" label="商品条码" min-width="100"></el-table-column>
        </el-table>
        <div class="Enunciate">
          <div class="Enunciate_cont clearfix">
            <div class="float_left">
              <span>其他金额:</span>
              <span>
                {{ $_common.formattedNumber(form.otherAmount) }}
              </span>
              <span style="margin: 0 10px"></span>
              <span>优惠金额:</span>
              <span>
                {{ $_common.formattedNumber(form.couponAmount) }}
              </span>
              <span style="margin: 0 10px"></span>
              <span>采购金额:</span>
              <span>
                {{ $_common.formattedNumber(purchaseAmount) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ContainerTit>
</template>

<script>
import { mapGetters } from "vuex";
import { updatePurchaseStatus, getPurchaseInfoById } from "@/api/Purchase";
const goods = {
  basicGoodsId: "",
  goodsCode: "",
  goodsName: "",
  skuId: "",
  unitName: "",
  categoryId: "",
  categoryName: "",
  buyerNum: 0,
  buyerUnitPrice: 0,
  subtotalPrice: 0,
  couponAmount: 0,
  otherAmount: 0,
};
export default {
  name: "PurchaseOrderAdd",
  data() {
    const validateShop = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择采购仓库"));
      } else {
        callback();
      }
    };
    const validateSupplier = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择商户"));
      } else {
        callback();
      }
    };
    return {
      good_name: true,
      add_merchant: false,
      mearchant_show: false,
      err_tip_list: [], // 错误提示
      now_goods_data: {},
      spec_check: [],
      auditTime: "",
      spec_check_list: [],
      show_goods: false, // 选择商品弹窗
      select_loading: false,
      check_all_spec: false,
      is_spec_indeterminate: false,
      spec_model: false,
      add_staff: false, // 新增员工
      loading: false,
      createTime: new Date().getTime(),
      auditName: "",
      auditId: "",
      auditStatus: "",
      look_path: "",
      goods_options: [],
      del_goods_id: [],
      warehouse_list: [],
      form: {
        code: "",
        shopId: "",
        shopName: "",
        supplierId: "",
        supplierName: "",
        buyerId: "",
        buyerName: "",
        operatorName: "",
        couponAmount: 0,
        otherAmount: 0,
        remark: "",
        goodsData: [],
        warehouseName: "",
        warehouseId: "",
        merchantId: "",
        purchaseType: 5,
      },
      name: "",
      supplier_show: false,
      purchase_list: [], // 采购人员列表

      goods_list: [],
      pusrchase_id: "",
      goods_index: 0,
      warehouse_show: false,
      staff_show: false,
      options: [
        { value: 1, label: "按数量" },
        { value: 2, label: "按金额" },
      ],
      options_value: "",
      contributions: "",
      apportionFlag: false,
      activeName: "one",
    };
  },
  computed: {
    purchaseAmount() {
      let sum = 0;
      if (this.goods_list.length > 1) {
        this.goods_list.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.subtotalPrice));
        });
      } else if (this.goods_list.length === 1) {
        sum = Number(this.goods_list[0].subtotalPrice);
      } else {
        sum = 0;
      }
      return this.$NP.plus(sum, this.$NP.minus(this.form.otherAmount, this.form.couponAmount)) || 0;
    },
    ...mapGetters({
      storeData: "MUser/storeData",
    }),
  },
  created() {
    if (this.$route.params.id) {
      this.pusrchase_id = this.$route.params.id;
      //    调用详情接口
      this.getPurchaseInfoById();
    }
  },
  methods: {
    // 审核状态  updatePurchaseStatus
    async updatePurchase(row) {
      const params = {
        auditStatus: "2",
        auditName: this.userName,
      };
      this.$confirm("确定审核该订单", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updatePurchaseStatus(this.pusrchase_id, params);

        this.$message({
          type: "success",
          message: "操作成功!",
        });
        await this.getPurchaseInfoById();
      });
    },

    //  详情接口
    async getPurchaseInfoById() {
      const { data } = await getPurchaseInfoById(this.pusrchase_id);
      this.form = data;
      // 商品
      this.goods_list = data.details.map((item) => {
        return {
          ...item,
          skuId: item.skuId,
          specGropName: item.skuName,
        };
      });
    },
    // 合计
    getSummaries(param) {
      return this.$_common.getSummaries(param, ["小计金额", "采购数量"]);
    },
  },
};
</script>

<style lang="scss" scoped>
.width240 {
  width: 240px;
}
.other-price-view {
  padding-top: 10px;
  border: 1px solid #ecf0f7;
  border-top: 0;
}
.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.Enunciate {
  width: 100%;
  height: 96px;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  padding: 24px 24px 32px;
  .Enunciate_cont {
    background-color: #fa6400;
    border-radius: 3px;
    padding: 0 24px;
  }
}
.btn-top-div {
  position: absolute;
  right: 20px;
  top: 15px;
  z-index: 999;
}
</style>
<style>
.MerchantPurchaseDetail {
  background-color: #fff;
}
.MerchantPurchaseDetail .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.MerchantPurchaseDetail .is-active {
  font-weight: 700;
  color: #000;
}
.MerchantPurchaseDetail .el-tabs__nav {
  margin-left: 24px;
}
</style>
