<template>
  <div class="print-box">
    <el-row class="tem-row">
      <el-col :span="24">
        <div id="printTest" class="tem-img">
          <div class="clearfix" style="font-size: 12px">
            <div class="float_left">
              <span>打印时间：{{ no_time }}</span>
            </div>
            <div class="float_right">
              <p>制单日期:{{ $_common.formatDate(orderDetail.createTime) }}</p>
            </div>
          </div>
          <p style="font-size: 20px; text-align: center">
            <span>调拨出库单</span>
          </p>
          <div class="infomation">
            <span>单据编号:{{ orderDetail.no }}</span>
            <span style="padding-left: 40px">
              司机:{{ orderDetail.extend ? orderDetail.extend.logistics.driverName : "--" }}
            </span>
            <span style="padding-left: 40px">
              司机电话:{{ orderDetail.extend ? orderDetail.extend.logistics.truckTel : "--" }}
            </span>
            <span style="padding-left: 40px">
              车牌:{{ orderDetail.extend ? orderDetail.extend.logistics.truckName : "--" }}
            </span>
            <span style="padding-left: 40px">
              出库时间:{{ orderDetail.auditTime ? $_common.formatDate(orderDetail.auditTime) : "--" }}
            </span>
            <br />
            <span>调出仓库:{{ orderDetail.outWarehouseName || "--" }}</span>
            <span style="padding-left: 95px"> 调入仓库:{{ orderDetail.inWarehouseName || "--" }} </span>
          </div>
          <div id="second">
            <table border="1" style="width: 100%; margin-top: 10px" class="goods-table">
              <tr>
                <th>序号</th>
                <th>商品编码</th>
                <th>商品名称</th>
                <th>规格</th>
                <th v-if="enableLocationManagement === '5'">库区</th>
                <th v-if="enableLocationManagement === '5'">库位</th>
                <th>应出数量</th>
                <th>出库数量</th>
              </tr>
              <tr v-for="(item, index) in orderDetail.orderDetail" :key="index">
                <td>{{ index + 1 }}</td>
                <td>{{ item.materielCode }}</td>
                <td>{{ item.materielName }}</td>
                <td>{{ item.unitName }};{{ item.skuName }}</td>
                <td v-if="enableLocationManagement === '5'">
                  {{ item.areaName || "--" }}
                </td>
                <td v-if="enableLocationManagement === '5'">
                  {{ item.storageLocationName || "--" }}
                </td>
                <td>
                  {{ $_common.formatNub(item.total) }}
                  <span v-if="!!item.extend && item.extend !== 'null'">
                    （{{ item.extend.u_1_buy }}{{ item.extend.u_1 }}）
                  </span>
                </td>
                <td>{{ $_common.formatNub(item.outNum) }}</td>
              </tr>
              <tr>
                <td colspan="1" style="text-align: center">合计：</td>
                <td>--</td>
                <td>--</td>
                <td>--</td>
                <td v-if="enableLocationManagement === '5'">--</td>
                <td v-if="enableLocationManagement === '5'">--</td>
                <td>{{ totalNum }}</td>
                <td>{{ totalMoney }}</td>
              </tr>
              <tr>
                <td :colspan="9" class="remarks">
                  <pre>备注：{{ orderDetail.remark }}</pre>
                </td>
              </tr>
            </table>
          </div>
          <div class="sign clearfix">
            <span class="float_left">签字确认：</span>
            <span class="float_right">经手人：{{ userName }}</span>
          </div>
        </div>
      </el-col>
      <div style="text-align: center">
        <el-button v-print="'#printTest'" type="primary" @click="PrintNumPrintIncr">
          确认打印（已打印{{ orderDetail.printNum || 0 }}次）
        </el-button>
      </div>
    </el-row>
  </div>
</template>

<script>
import { getSaleOutInfo } from "@/api/Stock";
import { PrintNumPrintIncr } from "@/api/common";
import { mapGetters } from "vuex";
export default {
  name: "StoragePrinting",
  data() {
    return {
      id: "",
      no_time: "",
      orderDetail: { orderDetail: [] },
    };
  },
  computed: {
    ...mapGetters({
      enableLocationManagement: "MUser/enableLocationManagement",
    }),
    totalNum() {
      if (!this.orderDetail.orderDetail.length) {
        return 0;
      } else if (this.orderDetail.orderDetail.length === 1) {
        return this.orderDetail.orderDetail[0].total - 0;
      } else {
        let sum = 0;
        for (let i in this.orderDetail.orderDetail) {
          const item = this.orderDetail.orderDetail[i];
          sum = this.$NP.plus(sum, item.total - 0);
        }
        return sum;
      }
    },
    totalMoney() {
      if (!this.orderDetail.orderDetail.length) {
        return 0;
      } else if (this.orderDetail.orderDetail.length === 1) {
        return this.orderDetail.orderDetail[0].outNum - 0;
      } else {
        let sum = 0;
        for (let i in this.orderDetail.orderDetail) {
          const item = this.orderDetail.orderDetail[i];
          sum = this.$NP.plus(sum, item.outNum - 0);
        }
        return sum;
      }
    },
  },
  created() {
    this.no_time = this.$_common.formatDate(new Date().getTime());
    this.id = this.$route.params.id;
    this.getSaleOutInfo();
  },
  methods: {
    async PrintNumPrintIncr() {
      const data = await PrintNumPrintIncr({
        objectNo: this.orderDetail.no,
        objectType: this.orderDetail.type,
      });
      this.orderDetail.printNum += 1;
      setTimeout(() => {
        window.close();
      }, 1000);
    },
    async getSaleOutInfo() {
      let arr = [];
      const { data } = await getSaleOutInfo(this.id);
      data.outWarehouseData.forEach((item) => {
        arr.push(...item.details);
      });
      this.orderDetail = {
        ...data,
        orderDetail: arr,
      };
      console.log(this.orderDetail);
    },
  },
};
</script>

<style scoped lang="scss">
.tem-row {
  padding: 20px 80px;
  position: relative;
}
.tem-img {
  width: 100%;
  /*padding: 20px 0;*/
}
.infomation {
  margin-bottom: 5px;
  font-size: 14px;
}
.sign {
  margin-top: 10px;
  font-size: 14px;
}
.add_num {
  margin-right: 10px;
}
.goods-table th,
.goods-table td {
  text-align: center;
  line-height: 28px;
  font-size: 14px;
}
.goods-table .remarks {
  line-height: 32px;
  text-align: left;
  padding-left: 5px;
  font-weight: bold;
}
.print-tag {
  position: absolute;
  right: 190px;
  top: 60px;
  img {
    width: 120px;
    opacity: 0.7;
  }
}
</style>
