<template>
  <ContainerTit class="ContainerTit">
    <div class="header">
      <el-steps align-center :active="steps_active">
        <el-step title="供应商导入文件" icon="el-icon-upload"></el-step>
        <el-step title="供应商导入预览" icon="el-icon-s-order"></el-step>
        <el-step title="供应商导入完成" icon="el-icon-s-claim"></el-step>
      </el-steps>
    </div>
    <!--    客户导入预览-->
    <div v-if="steps_active === 0">
      <div class="span">
        <a href="https://upload.qianniao.vip/template/gongyingshang.xlsx" target="_blank">
          <i class="iconfont icon-xiazai-xue"></i>
          <span>下载模板</span>
        </a>
      </div>
      <div class="content">
        <p class="title">
          <i class="required">*</i>
          供应商文件
        </p>
        <div class="input-up">
          <i class="iconfont icon-lianjie"></i>
          <el-upload
            ref="upload"
            action="#"
            accept="xlsx,xls"
            :auto-upload="false"
            :file-list="fileList"
            list-type="text"
            :before-upload="beforeUpload"
            :on-remove="onRemove"
            :on-change="onChange"
          >
            <div slot="trigger" style="width: 360px; padding: 0 10px; text-align: left">
              <span v-if="file_name">{{ file_name }}</span>
              <span v-else style="color: #999999">选取文件</span>
            </div>
          </el-upload>
        </div>
      </div>
    </div>
    <!--    商品导入预览-->
    <div v-else-if="steps_active === 1" class="steps-two">
      <vxe-grid ref="xTable" border="inner" :columns="tableColumn" :data="goods_data"></vxe-grid>
    </div>
    <div v-else class="steps-three">
      <p class="title">导入情况</p>
      <div class="desc">
        {{ Import_the_information }}
        <!--        <el-button type="text">下载失败数据</el-button>-->
      </div>
    </div>
    <div slot="headr">
      <el-button @click="closePage">
        {{ steps_active === 2 ? "完成" : "取消" }}
      </el-button>
      <el-button v-if="steps_active === 0" type="primary" @click="previewData"> 下一步 </el-button>
      <el-button v-if="steps_active === 1" type="primary" @click="exportsData"> 确定导入 </el-button>
      <el-button v-if="steps_active === 2" type="primary" @click="againExport"> 重新上传 </el-button>
    </div>
  </ContainerTit>
</template>

<script>
import { supplierImport } from "@/api/Purchase";
export default {
  name: "GoodsImport",
  data() {
    return {
      file_name: "",
      file: "",
      fileList: [],
      goods_data: [],
      steps_active: 0,
      tableColumn: [
        { field: "title", title: "供应商名称" },
        { field: "realName", title: "联系人姓名" },
        { field: "mobile", title: "联系电话" },
        { field: "provinceName", title: "省" },
        { field: "cityName", title: "市" },
        { field: "areaName", title: "区" },
        { field: "address", title: "详细地址" },
        { field: "position", title: "联系人职务" },
        { field: "accountName", title: "开户人" },
        { field: "bankName", title: "开户银行" },
        { field: "bankCard", title: "银行账号" },
        { field: "remark", title: "客户备注" },
      ],
      Import_the_information: "",
    };
  },
  methods: {
    lookDetail() {},
    // 取消/完成 按钮
    closePage() {
      if (this.steps_active === 1) {
        this.steps_active = 0;
      } else {
        this.$closeCurrentGoEdit("/Purchase/ManageP/Supplier");
      }
    },

    // 下一步
    previewData() {
      // this.$refs.xTable.importData();
      const fileReader = new FileReader();
      fileReader.onload = (ev) => {
        const data = ev.target.result;
        const workbook = this.$XLSX.read(data, { type: "binary" });
        const csvData = this.$XLSX.utils.sheet_to_csv(workbook.Sheets.Sheet1);
        const tableData = [];
        console.log("csvData", csvData.split("\n"));
        // 解析数据
        csvData.split("\n").forEach((vRow, rindex) => {
          if (vRow && rindex > 0) {
            const vCols = vRow.split(",");
            console.log("vCols", vCols);
            const item = {};
            vCols.forEach((val, cIndex) => {
              const column = this.tableColumn[cIndex];
              if (column && column.field) {
                item[column.field] = val;
              }
            });
            tableData.push(item);
          }
        });
        console.log("tableData::", tableData);
        this.goods_data = tableData;
      };
      fileReader.readAsBinaryString(this.file);

      this.steps_active = 1;
    },
    //确定导入
    async exportsData() {
      this.steps_active = 2;
      const { data } = await supplierImport(this.goods_data);
      this.Import_the_information = data;
    },
    //重新上传
    againExport() {
      this.steps_active = 0;
    },
    //覆盖默认的上传行为，可以自定义上传的实现
    submitUpload(request) {
      console.log("submitUpload", request);
      // this.$refs.upload.submit();
    },
    //上传文件之前的钩子，参数为上传的文件，若返回 false 或者返回 Promise 且被 reject，则停止上传。
    beforeUpload(file) {
      console.log("beforeUpload", file);
    },
    //文件列表移除文件时的钩子
    onRemove(file, fileList) {
      console.log("onRemove", file, fileList);
    },
    onChange(file) {
      console.log("onChange", file);
      this.file_name = file.name;
      this.file = file.raw;
    },
  },
};
</script>
<style scoped lang="scss">
.ContainerTit {
  background-color: #ffffff;
}

.span {
  border-radius: 5px;
  height: 15px;
  padding: 0 20px 20px;
  width: 98%;
  margin: 20px auto;
  .icon-xiazai-xue {
    font-size: 16px;
    margin-right: 4px;
  }
}
.header {
  margin: 40px auto 20px;
  width: 60%;
}
.content {
  width: 98%;
  margin: 20px auto;
  padding: 24px 20px;
  border-radius: 4px 4px 0 0;
  background-color: #f5f7fc;
  .title {
    padding-bottom: 20px;
    .required {
      color: #ff4400;
    }
  }
  .input-up {
    width: 360px;
    text-align: left;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #d5dae0 !important;
    position: relative;
    overflow: hidden;
    .icon-lianjie {
      position: absolute;
      top: 50%;
      right: 20px;
      color: #768696;
      transform: translateY(-50%);
      display: block;
    }
  }
}
.steps-three {
  margin-top: 90px;
  text-align: center;
  .title {
    color: #333;
    font-size: 24px;
  }
  .desc {
    color: #333;
    font-size: 14px;
    margin: 18px 0 60px;
  }
}
</style>
