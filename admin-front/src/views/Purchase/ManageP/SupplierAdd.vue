<!--新增供应商-->
<template>
  <ContainerTit>
    <span v-if="supplierId" slot="pagetit">
      {{ isDetail ? "供应商详情" : "编辑供应商" }}
    </span>
    <div slot="headr">
      <el-button
        v-if="!look_path && !supplierId"
        :loading="loading"
        :disabled="$route.params.id"
        @click="delPauseSave(1)"
      >
        清除暂存
      </el-button>
      <el-button v-if="!look_path && !supplierId" :loading="loading" :disabled="$route.params.id" @click="AddTem">
        暂存
      </el-button>
      <el-button v-if="!look_path" type="primary" :loading="loading" @click="conserveSupplier"> 保存提交 </el-button>
    </div>
    <div style="background-color: #fff; padding: 20px">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-form
            ref="add_from"
            :model="add_from"
            size="small"
            :rules="add_rules"
            label-width="160px"
            :disabled="look_path"
          >
            <div class="detail-tab-main">
              <el-form-item label="供应商编码：" prop="code">
                <el-input v-model="add_from.code" placeholder="系统自动生成" disabled></el-input>
              </el-form-item>
              <el-form-item label="供应商名称：" prop="title">
                <el-input v-model="add_from.title" placeholder="请输入供应商名称"></el-input>
              </el-form-item>
              <el-form-item label="联系人：" prop="realName">
                <el-input v-model="add_from.realName" placeholder="请输入姓名"></el-input>
              </el-form-item>
              <el-form-item label="联系电话：" prop="mobile">
                <el-input v-model="add_from.mobile" placeholder="请输入手机号码"></el-input>
              </el-form-item>
              <el-form-item label="所属区域：" prop="provinceCode">
                <RegionSelect v-model="area" style="width: 100%" size="medium" @change="regionChange" />
              </el-form-item>
              <el-form-item label="详细地址：">
                <el-input v-model="add_from.address" placeholder="请输入详细地址"></el-input>
              </el-form-item>
              <el-form-item label="联系人职务：" prop="position">
                <el-input v-model="add_from.position" placeholder="请输入职务"></el-input>
              </el-form-item>
              <el-form-item label="开户人：" prop="accountName">
                <el-input v-model="add_from.accountName" placeholder="请输入真实姓名"></el-input>
              </el-form-item>
              <el-form-item label="开户银行：" prop="bankName">
                <el-input v-model="add_from.bankName" placeholder="请输入开户银行"></el-input>
              </el-form-item>
              <el-form-item label="银行账号：" prop="bankCard">
                <el-input v-model="add_from.bankCard" placeholder="请输入银行账号"></el-input>
              </el-form-item>
              <el-form-item label="状态：">
                <el-radio-group v-model="add_from.enableStatus" @change="enableStatusChange">
                  <el-radio :label="5">正常</el-radio>
                  <el-radio :label="4">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="角色">
                <RoleSelect v-model="role" :is-show-add="true" @change="roleChange" />
              </el-form-item>
              <el-form-item label="备注：" prop="remark">
                <el-input v-model="add_from.remark" type="textarea" placeholder="请输入备注"></el-input>
              </el-form-item>
            </div>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="代销配置" name="consignment">
          <ConsignmentConfig
            v-model="consignmentConfig"
            :supplier-id="supplier_id"
            @enabledChange="consignmentEnabledChange"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </ContainerTit>
</template>

<script>
import { addSupplier, editSupplier, getSupplierInfoById } from "@/api/Purchase";
import { getConsignmentConfig, updateConsignmentConfig } from "@/api/SupplierConsignment";
import { addPauseSave, delPauseSave, getPauseSave } from "@/api/common";
import RegionSelect from "@/component/common/RegionSelectJSON";
import RoleSelect from "@/component/common/RoleSelect";
import ConsignmentConfig from "@/component/supplier/ConsignmentConfig";

export default {
  name: "SupplierAdd",
  components: {
    RoleSelect,
    RegionSelect,
    ConsignmentConfig,
  },
  props: {
    supplierId: {
      type: [Number, String],
      default: 0,
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validatePhone = (rule, value, callback) => {
      const re = /^1[3456789]\d{9}$/; // 正则表达式
      if (value === "") {
        callback(new Error("请输入手机号"));
      } else if (!re.test(value)) {
        callback(new Error("手机号格式有误，请重新输入!"));
      } else {
        callback();
      }
    };
    return {
      loading: false,
      look_path: "",
      supplier_id: "",
      area_options: [],
      unit_list: [],
      brand_list: [],
      freight_model: [],
      area: [], // 所属区域
      role: [],
      activeTab: "basic", // 当前激活的选项卡
      consignmentConfig: {
        enabled: false,
        settlementConfig: {
          cycleType: "MONTHLY",
          customDays: 30
        },
        depositRequired: false,
        minDepositAmount: 1000
      },
      add_from: {
        title: "",
        provinceCode: "",
        cityCode: "",
        districtCode: "",
        address: "",
        realName: "",
        mobile: "",
        enableStatus: 5,
        sex: 0,
        phone: "",
        position: "",
        email: "",
        remark: "",
        accountName: "",
        bankName: "",
        bankCard: "",
        roleId: null,
        consignmentEnabled: false, // 是否启用代销
      },
      add_rules: {
        title: [{ required: true, message: "请输入供应商名称", trigger: "blur" }],
        provinceCode: [{ required: true, message: "请选择所属区域", trigger: "change" }],
        realName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
        mobile: [
          { required: true, message: "请填写联系人手机号", trigger: "blur" },
          { validator: validatePhone, trigger: "blur" },
        ],
        enableStatus: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
      brandName: "",
    };
  },
  created() {
    const path = this.$route.path;
    // 品牌新建供应商标识
    this.brandName = this.$route.path.includes("brand");
    this.look_path = path.substr(33, 12) === "SupplierLook";
    if (this.isDetail) {
      this.look_path = true;
    }
    if (this.supplierId) {
      this.supplier_id = this.supplierId;
      this.getSupplierInfoById();
    }
    if (this.$route.params.id) {
      this.supplier_id = this.$route.params.id;
      this.getSupplierInfoById();
    } else {
      this.getPauseSave();
    }
  },
  methods: {
    //  性别
    sexChange(val) {
      this.add_from.sex = val;
    },
    enableStatusChange(val) {
      this.add_from.enableStatus = val;
    },
    //  区域
    regionChange(val) {
      this.add_from.provinceCode = val[0];
      this.add_from.cityCode = val[1];
      this.add_from.districtCode = val[2];
    },
    // 角色选择
    roleChange(val) {
      this.add_from.roleId = val.length ? val[val.length - 1] : "";
      console.log(this.add_from);
      this.add_from.rolePidPath = val.join(",");
    },
    //  查看详情
    async getSupplierInfoById() {
      const { data } = await getSupplierInfoById(this.supplier_id);

      this.add_from = data;
      this.role = data.roleId || [];
      this.area = [data.provinceCode, data.cityCode, data.districtCode].map((item) => {
        return parseInt(item);
      });

      // 获取代销配置
      if (this.supplier_id) {
        this.getConsignmentConfig();
      }
    },

    // 获取代销配置
    async getConsignmentConfig() {
      try {
        const { data } = await getConsignmentConfig(this.supplier_id);
        if (data) {
          this.consignmentConfig = data;
          this.add_from.consignmentEnabled = data.enabled;
        }
      } catch (error) {
        console.error("获取代销配置失败", error);
      }
    },

    // 代销启用状态变更
    consignmentEnabledChange(val) {
      this.add_from.consignmentEnabled = val;
    },
    // 获取暂存信息
    async getPauseSave() {
      const { data } = await getPauseSave({
        key: "SupplierAdd",
      });

      if (JSON.stringify(data) === "{}") return;
      this.add_from = data;
      this.area = [data.provinceCode, data.cityCode, data.districtCode].map((item) => {
        return parseInt(item);
      });
    },
    // 点击暂存
    async AddTem() {
      this.loading = true;
      const data = await addPauseSave({
        key: "SupplierAdd",
        data: this.add_from,
      });

      await this.$message({
        type: "success",
        message: "暂存成功",
      });
      await this.$closeCurrentGoEdit("/Purchase/ManageP/Supplier");
    },
    // 清除暂存
    async delPauseSave(type) {
      const data = delPauseSave({
        key: "SupplierAdd",
      });

      if (type) {
        this.$message({
          type: "success",
          message: "清除暂存成功",
        });
        this.$closeCurrentGoEdit("/Purchase/ManageP/SupplierAdd");
      }
    },
    // 新增供应商
    async conserveSupplier() {
      if (!this.add_from.title.trim() || !this.add_from.realName.trim()) {
        this.$message.warning("必填项不能为空");
        return;
      }
      this.$refs.add_from.validate(async (valid) => {
        if (valid) {
          let target = {};

          this.loading = true;
          try {
            if (this.supplier_id) {
              // 编辑供应商基本信息
              await editSupplier(this.supplier_id, this.add_from);

              // 更新代销配置
              if (this.activeTab === "consignment") {
                await updateConsignmentConfig(this.supplier_id, {
                  ...this.consignmentConfig,
                  enabled: this.add_from.consignmentEnabled
                });
              }

              this.loading = false;
              await this.delPauseSave();
              this.$emit("drawer_false");
            } else {
              // 新增供应商
              const result = await addSupplier(this.add_from);

              // 如果启用了代销配置，则保存代销配置
              if (this.add_from.consignmentEnabled && result.data && result.data.id) {
                await updateConsignmentConfig(result.data.id, {
                  ...this.consignmentConfig,
                  enabled: this.add_from.consignmentEnabled
                });
              }

              this.loading = false;
              await this.delPauseSave();
              this.$closeCurrentGoEdit("/Purchase/ManageP/Supplier");
            }

            this.$message({
              type: "success",
              message: "提交成功",
            });
          } catch (error) {
            this.loading = false;
            this.$message.error("保存失败：" + (error.message || "未知错误"));
          }
        }
      });
    },
  },
};
</script>

<style scoped>
.form-card {
  width: auto;
}

.form-card-head {
  font-size: 13px;
  color: #2a2a2a;
  font-weight: 600;
  padding-bottom: 8px;
  padding-left: 10px;
}

.detail-tab-main {
  width: 500px;
}
</style>
