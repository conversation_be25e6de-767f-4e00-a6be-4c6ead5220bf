<template>
  <ContainerQuery>
    <div v-if="$accessCheck($Access.PurchaseDetailSearchAllPurchaseDetails)" slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="search_form.goodsName"
            placeholder="商品名称"
            clearable
            style="width: 220px"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="search_form.time"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="LocationFrom"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <GoodsCategory
            v-model="search_form.categoryId"
            check-strictly
            clearable
            size="small"
            width="150"
            @change="categoryChange"
          />
        </el-form-item>

        <el-form-item>
          <SelectShop
            v-model="search_form.shopId"
            placeholder="选择商铺"
            width="150"
            @clear="clearShop"
            @change="selShop"
          />
        </el-form-item>
        <el-form-item>
          <SelectSupplier
            v-model="search_form.supplier_id"
            style="width: 150px"
            @clear="clearSupplier"
            @change="selUnitSupplier"
          />
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_form.status"
            clearable
            style="width: 150px"
            placeholder="采购订单状态"
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in status_list"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="$accessCheck($Access.PurchaseDetailGetAllPurchaseDetails)" slot="left">
      <el-button size="small" type="primary" @click="searchAllPurchaseDetails(1)"> 导出 </el-button>
    </div>
    <ul v-if="$accessCheck($Access.PurchaseDetailGetAllData)" class="clearfix num-ul">
      <li class="float_left">
        <span class="de_label">供应商数:</span>
        <span class="de_val">
          <i style="color: #f40">{{ topData.supplierNum }}</i>
        </span>
      </li>
      <li class="float_left">
        <span class="de_label">采购订单数:</span>
        <span class="de_val">
          <i style="color: #f40">{{ topData.purchaseNum }}</i>
        </span>
      </li>
      <li class="float_left">
        <span class="de_label">采购金额:</span>
        <span class="de_val">
          <i style="color: #f40">
            {{ $_common.formattedNumber(topData.purchaseAmount) }}
          </i>
        </span>
      </li>
      <li class="float_left">
        <span class="de_label">商品数:</span>
        <span class="de_val">
          <i style="color: #f40">
            {{ $_common.formatNub(topData.goodsNum) }}
          </i>
        </span>
      </li>
    </ul>
    <el-table :data="tableData" :span-method="objectSpanMethod">
      <el-table-column
        prop="no"
        label="采购单号"
        min-width="180"
        fixed="left"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column show-overflow-tooltip prop="goodsName" label="商品" min-width="140"></el-table-column>
      <el-table-column prop="goodsCode" label="商品编码" min-width="140"></el-table-column>
      <el-table-column v-if="unitFlag" prop="unitName" label="规格" min-width="80">
        <template slot-scope="scope"> {{ scope.row.unitName }};{{ scope.row.skuName }} </template>
      </el-table-column>
      <el-table-column v-if="classifyFlag" prop="categoryName" label="分类" min-width="100"></el-table-column>
      <el-table-column v-if="orderSizeFlag" prop="buyerNum" label="采购数量" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.buyerNum) }}
          <div v-if="scope.row.isEq === 5">其他单位:{{ $_common.formatNub(scope.row.otherNum) }}</div>
        </template>
      </el-table-column>
      <el-table-column v-if="purchasePriceFlag" prop="buyerUnitPrice" label="采购单价" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.buyerUnitPrice) }}
        </template>
      </el-table-column>
      <el-table-column v-if="purchaseAmountFlag" prop="subtotalPrice" label="采购金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.subtotalPrice) }}
        </template>
      </el-table-column>
      <el-table-column v-if="InventoryStatusFlag" prop="purchaseInStatus" label="入库状态" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.inStatus === 5" class="success-status"> 已入库 </span>
          <span v-else class="warning-status">未入库</span>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="supplierName" label="供应商" min-width="180">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span class="operation">供应商</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import GoodsCategory from "@/component/common/GoodsCategory";
import SelectSupplier from "@/component/common/SelectSupplier";
import SelectShop from "@/component/goods/SelectShop.vue";
import { mapGetters } from "vuex";
import {
  getAllData,
  getAllPurchaseDetails,
  exportGetAllPurchaseDetails,
  exportSearchAllPurchaseDetails,
  searchAllPurchaseDetails,
} from "@/api/Purchase";
export default {
  name: "PurchaseDetail",
  components: {
    GoodsCategory,
    SelectSupplier,
    SelectShop,
  },
  data() {
    return {
      spanArr: [], // 合并单元格
      pos: 0, // 合并单元格
      tableData: [],
      pageSize: 10,
      page: 1,
      total: 0,
      search_form: {
        goodsName: "", // 商品名称关键字
        shopId: "", // 店铺名称
        supplier_id: "", // 供应商
        start: "", // 时间
        end: "",
        status: "", // 订单状态
        category_id: "",
        merge: "",
        categoryId: [], // 分类
      },
      status_list: [
        {
          label: "待审核",
          value: 1,
        },
        {
          label: "审核成功",
          value: 2,
        },
      ],
      topData: {},
      checkList: ["规格", "分类", "采购数量", "采购单价", "采购金额", "入库状态"],
      columns: [
        {
          label: "规格",
        },
        {
          label: "分类",
        },
        {
          label: "采购数量",
        },
        {
          label: "采购单价",
        },
        {
          label: "采购金额",
        },
        {
          label: "入库状态",
        },
      ],
      unitFlag: true,
      classifyFlag: true,
      orderSizeFlag: true,
      purchasePriceFlag: true,
      purchaseAmountFlag: true,
      InventoryStatusFlag: true,
      merchantId: "", // 多商户商户ID
    };
  },
  computed: {
    ...mapGetters({ storeData: "MUser/storeData" }),
  },
  created() {
    if (this.systemType === 3) {
      this.merchantId = this.storeData.merchantData.id;
    }
    this.getAllPurchaseDetails();
    this.getAllData();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    clearShop() {
      this.search_form.shopId = "";
      this.pageChange(1);
    },
    selShop(val, row) {
      this.pageChange(1);
    },
    categoryChange(val) {
      if (val.length) {
        this.search_form.category_id = val[val.length - 1];
      } else {
        this.search_form.category_id = "";
      }
      this.pageChange(1);
    },
    //  时间
    LocationFrom(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    //  获取上面统计数量
    async getAllData() {
      const { data } = await getAllData();
      this.topData = data;
    },
    clearSupplier() {
      this.search_form.supplier_id = "";
      this.pageChange(1);
    },
    //  选择供应商
    selUnitSupplier(val) {
      this.pageChange(1);
    },
    //  获取列表
    async getAllPurchaseDetails(exports) {
      const params = {
        merchantId: this.merchantId,
        page: this.page,
        pageSize: this.pageSize,
      };
      if (exports) {
        params.export = exports;
        const target = await exportGetAllPurchaseDetails({
          ...params,
        });
      } else {
        const data = await getAllPurchaseDetails({
          ...params,
        });

        this.tableData = data.data;
        this.total = data.pageTotal;
        // 合并单元格
        const getSpanArr = this.$_common.getSpanArr(this.tableData, "no");
        this.spanArr = getSpanArr.spanArr;
        this.pos = getSpanArr.pos;
      }
    },
    //  获取列表 搜索
    async searchAllPurchaseDetails(exports) {
      const params = {
        merchantId: this.merchantId,
        page: this.page,
        pageSize: this.pageSize,
        categoryId: this.search_form.category_id,
        shopId: this.search_form.shopId,
        supplierId: this.search_form.supplier_id,
        auditStatus: this.search_form.status,
        search: this.search_form.goodsName,
        start: this.search_form.start,
        end: this.search_form.end,
        ifMerchant: 4,
      };
      if (exports) {
        params.export = exports;
        const target = await exportSearchAllPurchaseDetails({
          ...params,
        });
      } else {
        const data = await searchAllPurchaseDetails({
          ...params,
        });

        this.tableData = data.data;
        this.total = data.pageTotal;
        // 合并单元格
        const getSpanArr = this.$_common.getSpanArr(this.tableData, "no");
        this.spanArr = getSpanArr.spanArr;
        this.pos = getSpanArr.pos;
      }
    },
    //  判断
    getData(exports) {
      const isKey = this.$_common.isSerch(this.search_form);
      if (isKey) {
        this.searchAllPurchaseDetails(exports);
      } else {
        this.getAllPurchaseDetails(exports);
      }
    },
    pageChange(page) {
      this.page = page;
      this.getData();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (["采购单号", "供应商"].includes(column.label)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    change() {
      this.unitFlag = this.checkList.some((item) => item === "规格");
      this.classifyFlag = this.checkList.some((item) => item === "分类");
      this.orderSizeFlag = this.checkList.some((item) => item === "采购数量");
      this.purchasePriceFlag = this.checkList.some((item) => item === "采购单价");
      this.purchaseAmountFlag = this.checkList.some((item) => item === "采购金额");
      this.InventoryStatusFlag = this.checkList.some((item) => item === "入库状态");
    },
  },
};
</script>

<style scoped lang="scss">
.num-ul {
  line-height: 50px;
  padding: 0 16px;
  li {
    padding-right: 10px;
  }
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
