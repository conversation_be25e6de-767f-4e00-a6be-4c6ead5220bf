<template>
  <ContainerTit class="PurchaseReturnOrderAdd">
    <div style="position: relative">
      <div class="btn-top-div">
        <el-button :loading="loading" :disabled="!!$route.params.id" @click="delPauseSave(1)"> 清除暂存 </el-button>
        <el-button :loading="loading" :disabled="!!$route.params.id" @click="addPauseSave"> 暂存 </el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="look_path === 'PurchaseReturnOrderLook'"
          @click="addPurchaseOrder"
        >
          保存
        </el-button>
      </div>
    </div>
    <div>
      <el-form
        ref="form"
        size="small"
        inline
        :rules="rules"
        :model="form"
        :disabled="look_path === 'PurchaseReturnOrderLook'"
      >
        <el-tabs v-model="activeName">
          <el-tab-pane label="新增采购退货单" name="one" style="position: relative">
            <el-row style="padding-bottom: 13px">
              <el-col :span="24">
                <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">采购退货单信息</p>
              </el-col>
              <el-col v-if="!!pusrchaseOut_id" class="form" :span="6" style="margin-bottom: 0">
                <el-form-item v-if="!!pusrchaseOut_id" label="退货单号:">
                  <el-input placeholder="系统自动生成" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col class="form" :span="6" style="margin-bottom: 0">
                <el-form-item label="关联采购单:" prop="originNo">
                  <el-input v-model="form.originNo" placeholder="关联采购单">
                    <i slot="suffix" class="el-input__icon el-icon-search" @click="purchase_open = true"></i>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col v-if="form.originNo" class="form" :span="6">
                <el-form-item label="退货仓库：">
                  {{ form.warehouseName || "--" }}
                </el-form-item>
              </el-col>
              <el-col v-if="form.originNo" class="form" :span="6">
                <el-form-item :label="ifMerchant === 5 ? '商户:' : '供应商:'">
                  {{ ifMerchant === 5 ? form.merchantName : form.supplierName }}
                </el-form-item>
              </el-col>
              <el-col class="form" :span="6">
                <el-form-item label="制单人员:">
                  {{ form.operatorName }}
                </el-form-item>
              </el-col>
              <el-col class="form" :span="6">
                <el-form-item label="制单时间:">
                  <span>{{ $_common.getNowFormatDate() }}</span>
                </el-form-item>
              </el-col>
              <el-col v-if="form.originNo" class="form" :span="6">
                <el-form-item label="采购人员:">
                  {{ form.buyerName || "--" }}
                </el-form-item>
              </el-col>

              <el-col v-if="auditStatus === 2" class="form" :span="6">
                <el-form-item label="审核人员:">
                  <el-input v-model="auditName" size="mini" placeholder="输入审核人员"></el-input>
                </el-form-item>
              </el-col>
              <el-col v-if="auditStatus === 2" class="form" :span="6">
                <el-form-item label="审核时间:">
                  <span>{{ $_common.formatDate(auditTime) }}</span>
                </el-form-item>
              </el-col>
              <el-col v-if="pusrchaseOut_id" class="form" :span="6">
                <el-form-item prop="auditStatus" label="审核状态:" min-width="180">
                  <span v-if="auditStatus === 2" class="open-span">已审核</span>
                  <span v-if="auditStatus === 1" class="disabled-span"> 未审核 </span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
        <div class="order_bottom">
          <p class="text">付款单明细</p>
          <!--            <div class="clearfix" style="padding-bottom: 10px">-->
          <!--              <el-button-->
          <!--                v-if="pusrchaseOut_id"-->
          <!--                class="float_right"-->
          <!--                type="primary"-->
          <!--                size="mini"-->
          <!--                @click="show_selgoods = true"-->
          <!--              >-->
          <!--                选择商品-->
          <!--              </el-button>-->
          <!--            </div>-->
          <el-table :data="goods_list" size="small" show-summary :summary-method="getSummaries">
            <el-table-column prop="goodsId" label="ID" width="55"></el-table-column>
            <el-table-column prop="goodsCode" label="编码" min-width="140"></el-table-column>
            <el-table-column prop="goodsName" label="商品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="unitName" label="规格" min-width="140">
              <template slot-scope="scope"> {{ scope.row.unitName }}; {{ scope.row.skuName }} </template>
            </el-table-column>
            <el-table-column prop="purchaseNum" label="采购数量" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formatNub(scope.row.purchaseNum) }}
              </template>
            </el-table-column>
            <el-table-column prop="inNum" label="入库数量" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formatNub(scope.row.inNum) }}
              </template>
            </el-table-column>
            <el-table-column prop="inOfNum" label="未入库数量" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formatNub(scope.row.inOfNum) }}
              </template>
            </el-table-column>
            <el-table-column prop="returnNum" label="已退数量" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formatNub(scope.row.returnNum) }}
              </template>
            </el-table-column>
            <el-table-column prop="returnOnNum" label="可退数量" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formatNub(scope.row.returnOnNum) }}
              </template>
            </el-table-column>
            <el-table-column prop="num" label="剩余库存" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formatNub(scope.row.inventoryNum) }}
              </template>
            </el-table-column>
            <el-table-column prop="buyNum" label="退货数量" min-width="100">
              <template slot-scope="scope">
                <el-input-number
                  v-model="scope.row.buyerNum"
                  style="width: 80px"
                  size="mini"
                  type="text"
                  :max="Number(scope.row.inventoryNum)"
                  :min="0"
                  placeholder="退货数量"
                  :controls="false"
                  @change="editNumChange(scope.row, scope.$index)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="otherNum" label="其他单位" min-width="100">
              <template slot-scope="scope">
                <vxe-input
                  v-if="scope.row.isEq === 5"
                  v-model="scope.row.otherNum"
                  style="width: 80px"
                  size="mini"
                  :min="0"
                  placeholder="其他单位"
                  :controls="false"
                  type="integer"
                ></vxe-input>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="buyerUnitPrice" label="退货单价" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.buyerUnitPrice) }}
              </template>
            </el-table-column>
            <el-table-column prop="subtotalPrice" label="小计金额" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.subtotalPrice) }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button type="text" @click="delData(scope.$index, scope.row)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="clearfix other-price-view">
            <div class="float_left">
              <el-form-item label="备注:">
                <el-input v-model="form.remark" style="width: 360px" placeholder="备注"></el-input>
              </el-form-item>
            </div>
            <div class="float_right">
              <el-form-item label="其他金额:">
                <el-input-number
                  v-model="form.otherAmount"
                  style="width: 100px"
                  :controls="false"
                  placeholder="其他金额"
                  :min="0"
                ></el-input-number>
              </el-form-item>
              <!-- <el-form-item label="优惠金额:">
                <el-input
                  style="width: 100px"
                  size="mini"
                  placeholder="优惠金额"
                  v-model="couponAmount"
                ></el-input>
              </el-form-item>-->
              <el-form-item label="退货金额:">
                <el-input v-model="purchaseAmount" style="width: 100px" placeholder="退货金额"></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <!--    选择商品-->
    <GoodsWarehouse
      v-if="show_selgoods"
      :id="form.originId"
      :goods-data="goodsArrData"
      :is-show="show_selgoods"
      :shop="form.warehouseId"
      @cancel="show_selgoods = false"
      @confirm="selMoreGoods"
    ></GoodsWarehouse>
    <!--    采购单-->
    <OrderReturn
      v-if="purchase_open"
      :dialog-visible="purchase_open"
      :enable="true"
      :if-merchant="ifMerchant"
      @close="purchase_open = false"
      @confirm="selpurchase"
    ></OrderReturn>
  </ContainerTit>
</template>

<script>
import GoodsWarehouse from "@/component/goods/GoodsWarehouseData.vue";
import OrderReturn from "@/component/goods/OrderReturn.vue";
import SelectSupplier from "@/component/common/SelectSupplier.vue";
import { getAllStaff } from "@/api/Department";
import { addPauseSave, delPauseSave, getPauseSave } from "@/api/common";
import {
  editPurchaseOut,
  addPurchaseOut,
  getPurchaseOutInfoById,
  getPurchaseAndBatchInfoById,
  getPurchaseInfoById,
} from "@/api/Purchase";
import SelectShop from "@/component/goods/SelectShop.vue";
import { mapGetters } from "vuex";
import { getAllWarehouse } from "@/api/Stock";

export default {
  name: "PurchaseOrderAdd",
  components: {
    GoodsWarehouse,
    // SelectSupplier,
    OrderReturn,
  },
  data() {
    const validateNo = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择关联采购单号"));
      } else {
        callback();
      }
    };
    const validateShop = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择采购仓库"));
      } else {
        callback();
      }
    };
    const validateSupplier = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择供应商"));
      } else {
        callback();
      }
    };
    const validateBuyer = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择采购人员"));
      } else {
        callback();
      }
    };
    return {
      ifMerchant: 4, // 是否商户采购单
      loading: false,
      pusrchaseOut_id: "", // 采购单id
      no: "",
      auditName: "",
      auditTime: "",
      auditId: "",
      auditStatus: "",
      look_path: "",
      form: {
        originId: "",
        originNo: "",
        supplierId: "",
        supplierName: "",
        warehouseId: "",
        warehouseName: "",
        buyerId: "",
        buyerName: "",
        operatorName: "",
        couponAmount: "",
        otherAmount: "",
        remark: "",
        goodsData: [],
        shopId: "",
        shopName: "",
        merchantId: "",
        merchantName: "",
        purchaseStatus: "",
      },
      rules: {
        originNo: [{ required: true, validator: validateNo }],
        warehouseName: [{ required: true, validator: validateShop }],
        supplierName: [{ required: true, validator: validateSupplier }],
        buyerId: [{ required: true, validator: validateBuyer }],
      },
      supplier_show: false,
      show_selgoods: false,
      purchase_open: false,
      purchase_list: [], // 采购人员列表
      purchase_rules: {},
      goods_list: [],
      goodsArrData: [],
      pusrchase_id: "",
      pusrchase_shopId: "",
      deleteArray: [],
      stock_list: [],
      num: "",
      activeName: "one",
    };
  },
  computed: {
    purchaseAmount() {
      let sum = 0;
      if (this.goods_list.length > 1) {
        this.goods_list.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.subtotalPrice));
        });
      } else if (this.goods_list.length === 1) {
        sum = Number(this.goods_list[0].subtotalPrice);
      } else {
        sum = 0;
      }
      return this.$NP.plus(sum, this.form.otherAmount);
    },
    ...mapGetters({
      storeData: "MUser/storeData",
    }),
  },
  created() {
    // this.getAllStaff();
    // this.getAllWarehouse();
    if (this.$route.params.type) {
      this.ifMerchant = Number(this.$route.params.type);
    }
    this.look_path = this.$route.name;
    if (this.$route.query.purchase_id) {
      this.ifMerchant = this.$route.query.ifMerchant ? parseInt(this.$route.query.ifMerchant) : 4;
      // 采购单页面-点击退单→新增采购退货单页面
      this.pusrchase_id = parseInt(this.$route.query.purchase_id);
      this.getPurchaseInfoById2(this.pusrchase_id);
      return;
    }
    if (this.$route.params.id) {
      this.pusrchaseOut_id = this.$route.params.id;
      this.getPurchaseOutInfoById();
    } else {
      this.form.operatorName = this.userName;
      this.getPauseSave();
      if ([2, 3].includes(this.systemType)) {
        this.form.shopId = this.storeData.id; // 店铺
        this.form.shopName = this.storeData.name; // 店铺
      }
    }
  },
  activated() {
    if (this.$_isInit()) return;
    if (this.$route.query.purchase_id) {
      this.ifMerchant = this.$route.query.ifMerchant ? parseInt(this.$route.query.ifMerchant) : 4;
      // 采购单页面-点击退单→新增采购退货单页面
      this.pusrchase_id = parseInt(this.$route.query.purchase_id);
      this.getPurchaseInfoById2(this.pusrchase_id);
    }
  },
  methods: {
    async getAllWarehouse() {
      const { data } = await getAllWarehouse({
        page: 1,
        pageSize: 999,
      });

      this.stock_list = data;
      // if (!this.form.warehouseId) {
      //   this.form.warehouseId = data[0].id;
      // }
    },
    shopDefault(val, row) {
      this.form.shopId = val;
      this.form.shopName = row.name;
    },
    async addPauseSave() {
      const params = {
        ...this.form,
        goodsData: this.$_common.deepClone(this.goods_list).map((item) => {
          delete item.num;
          return item;
        }),
      };
      this.loading = true;
      const data = await addPauseSave({
        key: this.look_path,
        data: params,
      });
      this.loading = false;

      this.$message({
        type: "success",
        message: "暂存成功",
      });
      this.$closeCurrentGoEdit("/Purchase/ManageP/PurchaseReturnOrder");
    },
    // 清除暂存
    async delPauseSave(type) {
      const data = delPauseSave({
        key: this.look_path,
      });

      if (type) {
        this.$message({
          type: "success",
          message: "清除暂存成功",
        });
        this.$closeCurrentGoEdit("/Purchase/ManageP/PurchaseReturnOrderAdd");
      }
    },
    // 获取暂存信息
    async getPauseSave() {
      const { data } = await getPauseSave({
        key: this.look_path,
      });

      if (JSON.stringify(data) === "{}") return;
      this.form.warehouseName = data.warehouseName;
      this.form.warehouseId = data.warehouseId;
      this.form.originId = data.originId;
      this.form.originNo = data.originNo;
      this.form.shopId = data.shopId;
      this.form.shopName = data.shopName;
      this.form.supplierId = data.supplierId;
      this.form.supplierName = data.supplierName;
      this.form.merchantId = data.merchantId;
      this.form.merchantName = data.merchantName;
      this.form.buyerId = data.buyerId;
      this.form.buyerName = data.buyerName;
      this.form.operatorName = data.operatorName;
      // this.purchaseAmount = data.purchaseAmount
      this.form.couponAmount = data.couponAmount;
      this.form.otherAmount = data.otherAmount;
      this.form.remark = data.remark;
      this.auditId = data.auditId;
      this.auditName = data.auditName;
      this.auditStatus = data.auditStatus;
      this.no = data.no;
      this.goods_list = data.goodsData.map((item) => {
        return {
          id: item.id,
          goodsId: item.goodsId,
          goodsCode: item.goodsCode,
          goodsName: item.goodsName,
          unitName: item.unitName,
          skuName: item.skuName,
          buyerNum: item.buyerNum,
          purchaseNum: item.purchaseNum,
          buyerUnitPrice: item.buyerUnitPrice,
          subtotalPrice: this.$NP.times(item.buyerUnitPrice, item.buyerNum),
          couponAmount: item.couponAmount,
          skuId: item.skuId,
          otherAmount: item.otherAmount,
        };
      });
    },
    //  添加采购退货单 addPurchaseOut
    async addPurchaseOrder() {
      if (!this.form.otherAmount) {
        this.form.otherAmount = 0;
      }
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          let target = {};
          if (!this.goods_list.length) {
            this.$message.warning("请选择商品");
            return;
          }

          const isBuyer = this.goods_list.some((item) => item.buyerNum <= 0);
          if (isBuyer) {
            this.$message.warning("退货数量不能为0，请检查");
            return;
          }
          this.form.purchaseStatus = this.ifMerchant;
          const params = {
            ...this.form,
            goodsData: this.$_common.deepClone(this.goods_list).map((item) => {
              delete item.num;
              if (!this.pusrchaseOut_id) {
                delete item.id;
              }
              return item;
            }),
          };

          this.loading = true;
          try {
            if (this.pusrchaseOut_id) {
              params.deleteArray = this.deleteArray;
              if (params.buyerNum >= Number(this.num)) {
                params.buyerNum = Number(this.num);
              }
              target = await editPurchaseOut(this.pusrchaseOut_id, params);
            } else {
              params.deleteArray = this.deleteArray;
              target = await addPurchaseOut(params);
            }
            const data = target;
            this.loading = false;
            this.goods_list = [];
            await this.$message({
              type: "success",
              message: "提交成功",
            });
            await this.delPauseSave();
            await this.$closeCurrentGoEdit("/Purchase/manageP/PurchaseReturnOrder");
          } finally {
            this.loading = false;
          }
        }
      });
    },
    //  获取详情 getPurchaseOutInfoById
    async getPurchaseOutInfoById() {
      const { data } = await getPurchaseOutInfoById(this.pusrchaseOut_id);
      this.form.warehouseName = data.warehouseName;
      this.form.warehouseId = data.warehouseId;
      this.form.originId = data.originId;
      this.form.originNo = data.originNo;
      this.form.shopId = data.shopId;
      this.form.shopName = data.shopName;
      this.form.supplierId = data.supplierId;
      this.form.supplierName = data.supplierName;
      this.form.merchantId = data.merchantId;
      this.form.merchantName = data.merchantName;
      this.form.buyerId = data.buyerId;
      this.form.buyerName = data.buyerName;
      this.form.operatorName = data.operatorName;
      // this.purchaseAmount = data.purchaseAmount
      this.form.couponAmount = data.couponAmount;
      this.form.otherAmount = data.otherAmount;
      this.form.remark = data.remark;
      this.auditId = data.auditId;
      this.auditName = data.auditName;
      this.auditTime = data.auditTime;
      this.auditStatus = data.auditStatus;
      this.no = data.no;
      // this.goods_list = data.details.map((item) => {
      //   return {
      //     ...item,
      //     buyerNum: item.returnOnNum,
      //   };
      // });
      this.goods_list = data.details;
    },
    //  删除
    delData(index, row) {
      if (row.id) {
        this.deleteArray.push(row.id);
      }
      this.goods_list.splice(index, 1);
    },
    // 改变数量
    editNumChange(row, index) {
      if (!row.buyerNum) {
        this.$message.warning("退货数量为空，默认变成1");
      }
      const target = this.$_common.deepClone(this.goods_list);
      target[index].subtotalPrice = this.$NP.times(target[index].buyerNum, target[index].buyerUnitPrice);
      if (target[index].returnOnNum > Number(target[index.num])) {
        target[index].returnOnNum = Number(target[index.num]);
      }
      this.goods_list = target;
    },
    // 选择一个商铺
    selShop(val, row) {
      this.form.shopName = row[0].name;
    },
    //  获取采购单详情
    async getPurchaseInfoById(id, warehouseId) {
      const { data } = await getPurchaseAndBatchInfoById({
        id: id,
        warehouseId: warehouseId,
      });

      this.goods_list = data.map((item) => {
        return {
          isEq: item.isEq,
          otherNum: item.otherNum,
          id: item.id,
          goodsId: item.basicGoodsId,
          goodsCode: item.goodsCode,
          goodsName: item.goodsName,
          unitName: item.unitName,
          skuName: item.skuName,
          skuId: item.skuId,
          purchaseNum: item.buyerNum,
          buyerNum: item.returnOnNum,
          inNum: item.inNum,
          inOfNum: item.inOfNum,
          returnOnNum: item.returnOnNum,
          returnNum: item.returnNum,
          inventoryNum: item.num,
          buyerUnitPrice: item.buyerUnitPrice,
          subtotalPrice: this.$NP.times(item.buyerUnitPrice, item.returnOnNum),
          couponAmount: item.couponAmount,
          otherAmount: item.otherAmount,
        };
      });
      this.goodsArrData = data.map((item) => {
        return {
          ...item,
          materielName: item.goodsName,
          materielCode: item.goodsCode,
        };
      });
    },
    //   获取采购单详情  采购单页面-点击退单→新增采购退货单页面
    async getPurchaseInfoById2() {
      const { data } = await getPurchaseInfoById(this.pusrchase_id);
      this.form.originId = data.id;
      this.form.originNo = data.no;
      this.form.shopId = data.shopId;
      this.form.shopName = data.shopName;
      this.form.buyerId = data.buyerId;
      this.form.buyerName = data.buyerName;
      this.form.remark = data.remark;
      this.form.supplierId = this.ifMerchant === 5 ? 0 : data.supplierId;
      this.form.supplierName = this.ifMerchant === 5 ? "" : data.supplierName;
      this.form.merchantId = this.ifMerchant === 5 ? data.merchantId : 0;
      this.form.merchantName = this.ifMerchant === 5 ? data.merchantName : "";
      this.form.warehouseName = data.warehouseName;
      this.form.warehouseId = data.warehouseId;
      this.form.operatorName = data.operatorName;
      this.form.purchaseStatus = this.ifMerchant;
      await this.getPurchaseInfoById(this.pusrchase_id, data.warehouseId);
    },
    // 选择采购单 selpurchase
    selpurchase(val) {
      const id = val[0].id;
      const warehouseId = val[0].warehouseId;
      this.getPurchaseInfoById(id, warehouseId);
      this.form.originId = val[0].id;
      this.form.originNo = val[0].no;
      this.form.shopId = val[0].shopId;
      this.form.shopName = val[0].shopName;
      this.form.buyerId = val[0].buyerId;
      this.form.buyerName = val[0].buyerName;
      this.form.remark = val[0].remark;
      this.form.supplierId = this.ifMerchant === 5 ? 0 : val[0].supplierId;
      this.form.supplierName = this.ifMerchant === 5 ? "" : val[0].supplierName;
      this.form.merchantId = this.ifMerchant === 5 ? val[0].merchantId : 0;
      this.form.merchantName = this.ifMerchant === 5 ? val[0].merchantName : "";
      this.form.warehouseName = val[0].warehouseName;
      this.form.warehouseId = val[0].warehouseId;
    },
    // 选择单个供应商
    selUnitSupplier(val, list) {
      this.form.supplierName = list[0].title;
    },
    async getAllStaff() {
      const data = await getAllStaff({
        page: 1,
        pageSize: 999,
      });

      this.purchase_list = data.data;
    },
    //  选择商品goodData的数据
    selMoreGoods(val) {
      const row = val.map((item) => {
        return {
          goodsId: item.basicGoodsId,
          goodsCode: item.goodsCode,
          goodsName: item.goodsName,
          unitName: item.unitName,
          skuName: item.skuName,
          skuId: item.skuId,
          purchaseNum: item.num,
          buyerNum: item.num,
          num: item.num,
          buyerUnitPrice: item.buyerUnitPrice,
          subtotalPrice: this.$NP.times(item.buyerUnitPrice, item.num),
          couponAmount: item.couponAmount,
          otherAmount: item.otherAmount,
        };
      });
      if (this.goods_list.length) {
        this.goods_list = this.$_common.unique(this.goods_list.concat(row), ["goodsId", "skuId"]);
      } else {
        this.goods_list = row;
      }
    },
    //  采购人员的id
    purchaseChange(val) {
      const staff = this.purchase_list.find((item) => item.id === val);
      this.form.buyerId = staff.id;
      this.form.buyerName = staff.staffName;
    },
    //  选择仓库的id
    warehouseChange(val) {
      const warehouse = this.stock_list.find((item) => item.id === val);
      this.form.warehouseId = warehouse.id;
      this.form.warehouseName = warehouse.warehouseName;
    },
    // 合计
    getSummaries(param) {
      return this.$_common.getSummaries(param, ["退货数量", "小计金额"]);
    },
    // blurNum(index,row) {
    //   if (row.returnOnNum > row.num) {
    //   }
    //   this.goods_list.returnOnNum = Number(num);
    // },
  },
};
</script>

<style lang="scss" scoped>
.other-price-view {
  padding-top: 10px;
  padding-left: 10px;
  border: 1px solid #ecf0f7;
  border-top: 0;
}
.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.btn-top-div {
  position: absolute;
  right: 20px;
  top: 15px;
  z-index: 999;
}
</style>
<style>
.PurchaseReturnOrderAdd {
  background-color: #fff;
}
.PurchaseReturnOrderAdd .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.PurchaseReturnOrderAdd .is-active {
  font-weight: 700;
  color: #000;
}
.PurchaseReturnOrderAdd .el-tabs__nav {
  margin-left: 24px;
}
</style>
