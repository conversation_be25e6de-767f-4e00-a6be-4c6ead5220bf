<template>
  <!--采购单详情-->
  <ContainerTit class="PurchaseOrderLook">
    <div slot="headr">
      <el-button
        v-if="purchase_detail.auditStatus === 1 && $accessCheck($Access.PurchaseOrderUpdateAuditStatus)"
        type="primary"
        size="small"
        :loading="loading"
        @click="updatePurchase"
      >
        审核通过
      </el-button>
      <el-button
        v-if="purchase_detail.inStatus === 6 && purchase_detail.havaRefund === 4"
        type="primary"
        size="small"
        @click="aginIn"
      >
        再次入库
      </el-button>
      <el-button
        v-if="purchase_detail.inStatus === 6 && purchase_detail.havaRefund === 4"
        type="primary"
        size="small"
        @click="returnMoney"
      >
        一键完结
      </el-button>
    </div>
    <div style="background-color: #ffffff">
      <el-tabs v-model="activeName" @tab-click="switchTabs">
        <el-tab-pane label="采购单详情" name="one"></el-tab-pane>
        <el-tab-pane
          v-if="parseInt(purchase_detail.auditStatus) === 2 && $accessCheck($Access.InventoryInGetInventoryInInfo)"
          label="入库详情"
          name="two"
        ></el-tab-pane>
        <el-tab-pane
          v-if="parseInt(inWarehouse_data.auditStatus) === 2 && $accessCheck($Access.HandleListGetPayInfo)"
          label="应付详情"
          name="three"
        ></el-tab-pane>
      </el-tabs>
    </div>
    <el-row style="padding-bottom: 13px">
      <el-col :span="24">
        <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">采购单详情</p>
      </el-col>
      <el-col class="form" :span="6">
        <span class="form_left">采购单号</span>
        <span class="form_right">{{ purchase_detail.no }}</span>
      </el-col>
      <el-col class="form" :span="6">
        <span class="form_left">制单时间</span>
        <span class="form_right">
          {{ $_common.formatDate(purchase_detail.createTime) }}
        </span>
      </el-col>
      <el-col class="form" :span="6">
        <span class="form_left">制单人员</span>
        <span class="form_right">
          {{ purchase_detail.operatorName }}
        </span>
      </el-col>
      <el-col class="form" :span="6">
        <span class="form_left">采购仓库</span>
        <span class="form_right">
          {{ purchase_detail.warehouseName || "--" }}
        </span>
      </el-col>
      <el-col class="form" :span="6">
        <span class="form_left">采购人员</span>
        <span class="form_right">
          {{ purchase_detail.buyerName }}
        </span>
      </el-col>
      <el-col class="form" :span="6" style="padding-left: 54px">
        <span class="form_left">供应商</span>
        <span class="form_right">
          {{ purchase_detail.supplierName }}
        </span>
      </el-col>
      <el-col class="form" :span="6">
        <span class="form_left">审核状态</span>
        <span class="form_right">
          <span v-if="purchase_detail.auditStatus === 1" class="warning-status"> 未审核 </span>
          <span v-else class="success-status">已审核</span>
        </span>
      </el-col>
      <el-col class="form" :span="6">
        <span class="form_left">审核人员</span>
        <span class="form_right">
          {{ purchase_detail.auditStatus === 1 ? "--" : purchase_detail.auditName }}
        </span>
      </el-col>
      <el-col class="form" :span="6">
        <span class="form_left">审核时间</span>
        <span class="form_right">
          {{ purchase_detail.auditStatus === 1 ? "--" : $_common.formatDate(purchase_detail.auditTime) }}
        </span>
      </el-col>
      <el-col class="form" :span="24">
        <span class="form_left">采购说明</span>
        <span class="form_right">
          {{ purchase_detail.remark || "无" }}
        </span>
      </el-col>
    </el-row>
    <div v-show="activeName === 'one'" class="order_bottom">
      <p class="text">商品清单</p>
      <el-table :data="goods_list" size="mini" show-summary :summary-method="getSummaries">
        <el-table-column prop="goodsId" label="#" width="60" type="index" align="center"></el-table-column>
        <el-table-column prop="goodsName" label="商品名称" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="goodsCode" label="编码" width="140"></el-table-column>
        <el-table-column prop="unitName" label="规格" min-width="120">
          <template slot-scope="scope"> {{ scope.row.unitName }};{{ scope.row.specGropName }} </template>
        </el-table-column>
        <el-table-column prop="buyerNum" label="采购数量" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.buyerNum) }}
          </template>
        </el-table-column>
        <el-table-column prop="otherNum" label="其他单位" min-width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.isEq === 5">
              {{ $_common.formatNub(scope.row.otherNum) }}
            </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="inNum" label="入库数量" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.inNum) }}
          </template>
        </el-table-column>
        <el-table-column prop="inOfNum" label="未入库数量" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.inOfNum) }}
          </template>
        </el-table-column>
        <el-table-column prop="returnOnNum" label="可退数量" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.returnOnNum) }}
          </template>
        </el-table-column>
        <el-table-column prop="returnNum" label="退货数量" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.returnNum) }}
          </template>
        </el-table-column>
        <el-table-column prop="buyerUnitPrice" label="采购单价" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.buyerUnitPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="subtotalPrice" label="小计金额" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.subtotalPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="storage" label="货架编码" min-width="100"></el-table-column>
        <el-table-column prop="barCode" label="商品条码" min-width="100"></el-table-column>
      </el-table>
      <div class="Enunciate">
        <div class="Enunciate_cont clearfix">
          <div class="float_left">
            <span>其他金额:</span>
            <span>
              {{ $_common.formattedNumber(purchase_detail.otherAmount) }}
            </span>
            <span style="margin: 0 10px"></span>
            <span>优惠金额:</span>
            <span>
              {{ $_common.formattedNumber(purchase_detail.couponAmount) }}
            </span>
            <span style="margin: 0 10px"></span>
            <span>采购金额:</span>
            <span>
              {{ $_common.formattedNumber(purchase_detail.purchaseAmount) }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div v-show="activeName === 'two'">
      <el-row style="padding-bottom: 13px">
        <el-col :span="24">
          <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">入库信息</p>
        </el-col>
        <el-col class="form" :span="6">
          <span class="form_left">入库单号</span>
          <span class="form_right">{{ inWarehouse_data.no }}</span>
        </el-col>
        <el-col class="form" :span="6">
          <span class="form_left">制单人员</span>
          <span class="form_right">{{ inWarehouse_data.operatorName }}</span>
        </el-col>
        <el-col class="form" :span="6">
          <span class="form_left">制单时间</span>
          <span class="form_right">
            {{ $_common.formatDate(inWarehouse_data.createTime) }}
          </span>
        </el-col>
        <el-col class="form" :span="6">
          <span class="form_left">审核状态</span>
          <span class="form_right">
            <span v-if="parseInt(inWarehouse_data.auditStatus) === 1" class="warning-status"> 未审核 </span>
            <span v-else class="success-status">已审核</span>
          </span>
        </el-col>
        <el-col class="form" :span="6">
          <span class="form_left">审核人员</span>
          <span class="form_right">
            {{ parseInt(inWarehouse_data.auditStatus) === 1 ? "--" : inWarehouse_data.auditName }}
          </span>
        </el-col>
        <el-col class="form" :span="6">
          <span class="form_left">审核时间</span>
          <span class="form_right">
            {{ parseInt(inWarehouse_data.auditStatus) === 1 ? "--" : $_common.formatDate(inWarehouse_data.auditTime) }}
          </span>
        </el-col>
        <el-col class="form" :span="6">
          <span class="form_left">入库类型</span>
          <span class="form_right">
            {{ inWarehouse_data.typeName }}
          </span>
        </el-col>
        <el-col class="form" :span="6" style="padding-left: 68px">
          <span class="form_left">仓库</span>
          <span class="form_right">
            {{ inWarehouse_data.warehouseName }}
          </span>
        </el-col>
        <el-col class="form" :span="6">
          <span class="form_left">入库说明</span>
          <span class="form_right">
            {{ inWarehouse_data.remark || "无" }}
          </span>
        </el-col>
      </el-row>
      <div class="order_bottom">
        <p class="text">入库商品</p>
        <el-table size="mini" :data="inWarehouse_data.details">
          <el-table-column prop="materielName" label="商品名称" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="materielCode" label="商品编码" min-width="140"></el-table-column>
          <el-table-column min-width="140" label="规格">
            <template slot-scope="scope"> {{ scope.row.unitName }};{{ scope.row.skuName }} </template>
          </el-table-column>
          <el-table-column min-width="100" prop="num" label="入库数量">
            <template slot-scope="scope">
              {{ $_common.formatNub(scope.row.num) }}
            </template>
          </el-table-column>
          <el-table-column min-width="100" prop="num" label="实际入库数量">
            <template slot-scope="scope">
              {{ $_common.formatNub(scope.row.inNum) }}
            </template>
          </el-table-column>
          <el-table-column width="140" show-overflow-tooltip prop="barCode" label="商品条码"></el-table-column>
          <el-table-column min-width="120" show-overflow-tooltip prop="storage" label="货架编码"></el-table-column>
          <!--          // 开启保质期设置，进行保质期验证-->
          <el-table-column v-if="shelfLifeSetUp === 5" label="生产日期" min-width="160">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.productionData"
                style="width: 100%"
                size="mini"
                placeholder="请选择生产日期"
                type="date"
                value-format="timestamp"
              ></el-date-picker>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div v-show="activeName === 'three'">
      <div class="order_bottom">
        <p class="text">应付详情</p>
        <el-table :data="handle_data" size="small" border>
          <el-table-column prop="no" label="单据编号" min-width="180" fixed="left"></el-table-column>
          <el-table-column prop="supplierName" label="供应商" min-width="160">
            <template slot-scope="scope">
              <p>{{ scope.row.supplierName }}</p>
              <p>{{ scope.row.supplierCode }}</p>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="单据日期" min-width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.createTime">
                {{ $_common.formatDate(scope.row.createTime, "yyyy-MM-dd") }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="financeType" label="应付类型" min-width="100"></el-table-column>
          <el-table-column prop="discountMoney" label="优惠金额(元)" min-width="100">
            <template slot-scope="scope">
              {{ $_common.formatNub(scope.row.discountMoney) }}
            </template>
          </el-table-column>
          <el-table-column prop="payMoney" label="实际应付金额(元)" min-width="120">
            <template slot-scope="scope">
              {{ $_common.formatNub(scope.row.payMoney) }}
            </template>
          </el-table-column>
          <el-table-column prop="shopName" label="商铺" min-width="150"></el-table-column>
          <el-table-column prop="receiptTypeId" label="单据类型" min-width="100">
            <template slot-scope="scope">
              {{ scope.row.receiptTypeId === 2 ? "采购订单" : "采购退货单" }}
            </template>
          </el-table-column>
          <el-table-column prop="auditStatus" label="状态" min-width="100">
            <template slot-scope="scope">
              <el-tag size="small" :type="scope.row.auditStatus === 1 ? 'warning' : 'success'">
                {{ scope.row.auditStatus === 1 ? "待审核" : "已审核" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="100">
            <template slot-scope="scope">
              <el-button
                v-if="$accessCheck($Access.HandleListUpdatePayStatus)"
                :disabled="scope.row.auditStatus === 2"
                type="text"
                @click="updateHandleAuditStatus(scope.row)"
              >
                审核
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div v-show="activeName === 'four'">
      <el-table border style="width: 98%; margin: 20px auto 0" :data="orderLog" size="small">
        <el-table-column prop="userName" label="操作人"></el-table-column>
        <el-table-column prop="actionType" label="操作类型"></el-table-column>
        <el-table-column prop="createTime" label="操作时间">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </ContainerTit>
</template>

<script>
import { getPurchaseInfoById, updatePurchaseStatus, reStockIn, retMoney } from "@/api/Purchase";
import { getPayInfo, updatePayStatus } from "@/api/Finance";
import { getPurchaseInInfoT, updatePurchaseInStatus } from "@/api/Stock";
export default {
  name: "PurchaseOrderLook",
  data() {
    return {
      activeName: "one",
      pusrchase_id: 0,
      purchase_detail: {},
      orderLog: [],
      handle_data: [],
      goods_list: [],
      inWarehouse_data: {
        details: [],
      },
      loading: false,
    };
  },
  async created() {
    this.pusrchase_id = parseInt(this.$route.params.id);
    await this.getPurchaseInfoById();
    if (
      parseInt(this.purchase_detail.auditStatus) === 2 &&
      this.$accessCheck(this.$Access.InventoryInGetInventoryInInfo)
    ) {
      await this.getPurchaseInInfo();
    }
    if (parseInt(this.inWarehouse_data.auditStatus) === 2 && this.$accessCheck(this.$Access.HandleListGetPayInfo)) {
      await this.getPayInfo();
    }
  },
  methods: {
    // 再次入库
    aginIn(id) {
      this.$confirm("是否确认订单再次入库?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const { data } = await reStockIn(this.pusrchase_id);
        this.$message.success("再次入库成功");
        await this.getPurchaseInfoById();
      });
    },
    // 一键完结
    returnMoney() {
      this.$confirm("是否确认一键完结?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const { data } = await retMoney(this.pusrchase_id);
        this.$message.success("操作成功");
        await this.getPurchaseInfoById();
      });
    },
    // 合计
    getSummaries(param) {
      return this.$_common.getSummaries(param, [
        "采购数量",
        "入库数量",
        "未入库数量",
        "可退数量",
        "退货数量",
        "小计金额",
      ]);
    },
    switchTabs() {},
    //  详情接口
    async getPurchaseInfoById() {
      const { data } = await getPurchaseInfoById(this.pusrchase_id);

      this.purchase_detail = data;
      this.goods_list = data.details.map((item) => {
        return {
          ...item,
          skuId: item.skuId,
          specGropName: item.skuName,
        };
      });
    },
    //  应付单详情接口
    async getPayInfo() {
      const { data } = await getPayInfo({
        purchaseId: this.pusrchase_id,
        createTime: this.inWarehouse_data.auditTime,
      });

      this.handle_data = [data];
    },
    //  应付单审核
    async updateHandleAuditStatus(row) {
      this.$confirm("确定要审核此应付单据吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updatePayStatus({
          id: row.id,
          createTime: row.createTime,
        });

        await this.getPayInfo();
        this.$message({
          type: "success",
          message: "审核成功",
        });
      });
    },
    // 审核状态
    async updatePurchase() {
      this.loading = true;
      const params = {
        auditStatus: "2",
        auditName: this.userName,
      };
      this.$confirm("确定审核该采购单", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const data = await updatePurchaseStatus(this.pusrchase_id, params);
            this.loading = false;
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            await this.getPurchaseInfoById();
          } catch {
            this.loading = false;
          }
          if (this.$accessCheck(this.$Access.InventoryInGetInventoryInInfo)) {
            this.activeName = "two";
            await this.getPurchaseInInfo();
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 获取入库单详情
    async getPurchaseInInfo() {
      const { data } = await getPurchaseInInfoT({
        originId: this.pusrchase_id,
        source: this.purchase_detail.type,
      });

      this.inWarehouse_data = {
        ...data,
        details: data.details.map((item) => {
          let productionData = 0;
          if (item.productionData) {
            productionData = item.productionData * 1000;
          } else {
            productionData = new Date().getTime();
          }
          return {
            ...item,
            productionData: productionData,
          };
        }),
      };
    },
    // 入库单审核
    updatePurchaseInStatus() {
      this.$router.push("/stock/OutIn/storageInfo/" + this.inWarehouse_data.id);
      return;
      // 开启保质期设置，进行保质期验证
      if (this.shelfLifeSetUp === 5) {
        let gindex = 0;
        let gdate = false;
        for (let i in this.queryData) {
          let item = this.queryData[i];
          if (!item.productionData) {
            gindex = i;
            gdate = true;
            break;
          }
        }
        if (gdate) {
          this.$message.warning(`请选择第${Number(gindex) + 1}行的生产日期`);
          return;
        }
      }
      this.$confirm("确定要审核该入库单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let params = {
          id: this.inWarehouse_data.id,
          auditName: this.userName,
        };
        // 开启保质期设置，进行保质期验证
        if (this.shelfLifeSetUp === 5) {
          let productionData = this.inWarehouse_data.details.map((item) => {
            return {
              id: item.id,
              productionData: parseInt(item.productionData / 1000),
            };
          });
          params.details = productionData;
        }
        const data = await updatePurchaseInStatus(params);

        this.$message({
          type: "success",
          message: "操作成功!",
        });
        await this.getPurchaseInInfo();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.Enunciate {
  width: 100%;
  height: 96px;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  padding: 24px 24px 32px;
  .Enunciate_cont {
    background-color: #fa6400;
    border-radius: 3px;
    padding: 0 24px;
  }
}
.PurchaseOrderLook {
  background-color: #ffffff;
  position: relative;
}
.btn-top-div {
  position: absolute;
  right: 10px;
  top: 16px;
  z-index: 999;
}
.de_label {
  width: 90px;
  display: inline-block;
  text-align: right;
}
.price-div {
  background-color: #f7f7f7;
  border: 1px solid #eeeeee;
  margin: 0 auto;
  padding: 20px;
  text-align: right;
  width: 98%;
  border-top: 0;
}
.price-div .de_label {
  width: auto;
}
.price-div .price-num {
  color: #f56c6c;
  margin-right: 30px;
}
</style>
<style>
.PurchaseOrderLook .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.PurchaseOrderLook .el-tabs__nav {
  margin-left: 24px;
}
.PurchaseOrderLook .is-active {
  font-weight: 700;
}
</style>
