<!--采购单-->
<template>
  <ContainerQuery>
    <div slot="left">
      <el-dropdown
        v-if="$accessCheck($Access.MerchantAddMerchant) && systemType === 1"
        type="primary"
        split-button
        @click="addData"
      >
        新增商户单
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-if="$accessCheck($Access.MerchantAllPurchase)">
            <div @click="getAllPurchase(1)">导出</div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div v-else>
        <el-button
          v-if="$accessCheck($Access.PurchaseOrderGetAllPurchase)"
          size="small"
          type="primary"
          plain
          @click="getAllPurchase(1)"
        >
          导出
        </el-button>
      </div>
    </div>
    <div slot="more">
      <el-form size="small" inline>
        <el-form-item>
          <el-select
            v-model="auditStatus"
            size="small"
            style="width: 150px"
            clearable
            placeholder="审核状态"
            @clear="delStatu"
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in shenhe_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <div>
            <el-input v-model="warehouseName" placeholder="采购仓库" style="width: 150px">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="openWarehouse()"></i>
            </el-input>
          </div>
        </el-form-item>

        <el-form-item v-if="systemType === 1">
          <div>
            <el-input v-model="Merchant" placeholder="商户" style="width: 150px">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="selectMerchant()"></i>
            </el-input>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" @row-dblclick="lookData">
      <el-table-column prop="id" label="ID" fixed="left" width="50"></el-table-column>
      <el-table-column prop="no" fixed="left" label="采购单号" min-width="180">
        <template slot-scope="scope">
          <span
            v-if="$accessCheck($Access.PurchaseOrderGetPurchaseInfoById)"
            class="click-div"
            @click="lookData(scope.row)"
          >
            {{ scope.row.no }}
          </span>
          <span v-else>{{ scope.row.no }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="商户" min-width="160" show-overflow-tooltip></el-table-column>
      <el-table-column v-if="purchaseAmount" prop="purchaseAmount" label="采购金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.purchaseAmount) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="personnelFlag"
        prop="warehouseName"
        label="仓库"
        min-width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="inStatus" label="入库状态" min-width="140">
        <template slot-scope="scope">
          {{ scope.row.inStatus == 4 ? "未入库" : scope.row.inStatus == 5 ? "已入库" : "部分入库" }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="personnelFlag"
        prop="buyerName"
        label="采购人员"
        min-width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        v-if="preparedByFlag"
        prop="operatorName"
        label="制单人员"
        min-width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column v-if="documentationDateFlag" prop="goodsData" label="制单日期" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime, "yyyy-MM-dd hh:mm:ss") }}
        </template>
      </el-table-column>
      <el-table-column v-if="auditFlag" prop="auditStatus" label="审核状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 2" class="success-status"> 已审核 </span>
          <span v-else class="warning-status">未审核</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="180">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span class="operation">操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <div v-if="systemType === 1">
            <el-button
              v-if="$accessCheck($Access.MerchantupdatePurchaseStatus)"
              :disabled="scope.row.auditStatus === 2"
              type="text"
              @click="updatePurchase(scope.row)"
            >
              审核
            </el-button>
            <el-button
              v-if="$accessCheck($Access.MerchantPurchaseOrderEdit)"
              :disabled="scope.row.auditStatus === 2"
              type="text"
              @click="editData(scope.row.id)"
            >
              编辑
            </el-button>
            <el-dropdown>
              <span class="el-dropdown-link">
                更多
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="$accessCheck($Access.MerchantdelPurchase) && scope.row.auditStatus !== 2">
                  <div class="dropdown-div" @click="delData(scope.row.id)">删除</div>
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="
                    $accessCheck($Access.MerchantPurchaseReturnOrderAdd) &&
                    !(
                      scope.row.returnStatus !== 0 ||
                      scope.row.inStatus !== 5 ||
                      scope.row.auditStatus !== 2 ||
                      scope.row.deleteStatus !== 5
                    )
                  "
                >
                  <div class="dropdown-div" @click="backData(scope.row.id)">退单</div>
                </el-dropdown-item>
                <el-dropdown-item v-if="$accessCheck($Access.PurchaseOrderGetPurchaseInfoById)">
                  <div class="dropdown-div" @click="openMerchantsProcurement(scope.row.id)">打印</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <el-button v-else type="text" @click="openMerchantsProcurement(scope.row.id)"> 打印 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <!--    选择仓库-->
    <WarehouseModel
      v-if="warehouse_show"
      :is-check="false"
      :is-show="warehouse_show"
      @confirm="selWarehouse"
      @cancel="warehouse_show = false"
    />
    <!--    选择商户-->
    <MearchantList
      v-if="mearchant_show"
      :is-show="mearchant_show"
      @confirmMerchant="confirmMerchant"
      @cancelMerchant="mearchant_show = false"
    />
  </ContainerQuery>
</template>

<script>
import { getAllPurchase, exportGetAllPurchase, delPurchase, updatePurchaseStatus } from "@/api/Purchase";
import { getAllMerchant } from "@/api/Merchants";
import MearchantList from "@/component/common/MearchantList.vue";
import WarehouseModel from "@/component/common/WarehouseModel.vue";
import { mapGetters } from "vuex";

export default {
  name: "PurchaseOrder",
  components: {
    // SelectShop,
    WarehouseModel,
    MearchantList,
  },
  data() {
    return {
      mearchant_show: false,
      purchaseNo: "",
      auditStatus: "",
      shenhe_options: [
        { value: 0, label: "全部状态" },
        { value: 1, label: "未审核" },
        { value: 2, label: "已审核" },
      ],
      tableData: [],
      total: 1,
      page: 1,
      pageSize: 10,
      shopId: "",
      checkList: ["采购金额", "仓库", "采购人员", "制单人员", "制单日期", "审核状态"],
      columns: [
        {
          label: "采购金额",
        },
        {
          label: "仓库",
        },
        {
          label: "入库状态",
        },
        {
          label: "采购人员",
        },
        {
          label: "制单人员",
        },
        {
          label: "制单日期",
        },
        {
          label: "审核状态",
        },
      ],
      purchaseAmount: true,
      storeFlag: true,
      personnelFlag: true,
      preparedByFlag: true,
      documentationDateFlag: true,
      auditFlag: true,
      warehouse_show: false,
      warehouseName: "",
      warehouseId: "",
      Merchant: "",
      merchantId: "",
    };
  },
  computed: {
    ...mapGetters({
      storeData: "MUser/storeData",
      systemType: "MUser/systemType",
    }),
  },
  created() {
    if (this.systemType === 3) {
      this.merchantId = this.storeData.merchantData.id;
    }
    this.getAllPurchase();
    this.getAllMerchant();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllPurchase();
  },
  methods: {
    cancelMerchant() {
      this.mearchant_show = false;
    },
    selectMerchant() {
      this.mearchant_show = true;
    },
    async getAllMerchant() {
      const res = await getAllMerchant({
        deleteStatus: 5,
        auditStatus: 2,
        enabledStatus: 5,
      });
      console.log(res);
    },
    selShop() {
      this.pageChange(1);
    },
    shopClear() {
      this.shopId = "";
      this.pageChange(1);
    },
    delStatu() {
      this.auditStatus = "";
      this.pageChange(1);
    },
    //  查看采购订单
    async getAllPurchase(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        shopId: this.shopId,
        auditStatus: this.auditStatus,
        no: this.purchaseNo,
        warehouseId: this.warehouseId,
        merchantId: this.merchantId,
        purchaseType: 5,
        ifMerchant: 5,
      };
      if (exports) {
        params.export = exports;
        const target = await exportGetAllPurchase({
          ...params,
        });
      } else {
        const data = await getAllPurchase({
          ...params,
        });
        this.total = data.pageTotal;
        this.tableData = data.data;
      }
    },
    //  编辑
    editData(id) {
      this.$router.push(`/Purchase/ManageP/EditMerchantPurchase/${id}`);
    },
    //  退单
    backData(id) {
      this.$router.push(`/Purchase/ManageP/PurchaseReturnOrderAdd/5?purchase_id=${id}&ifMerchant=5`);
    },
    // 查看
    lookData(row) {
      if (!this.$accessCheck(this.$Access.PurchaseOrderGetPurchaseInfoById)) {
        return;
      }
      this.$router.push(`/Purchase/ManageP/MerchantPurchaseDetail/${row.id}`);
    },
    delData(id) {
      this.$confirm("请确认是否删除该订单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delPurchase(id);

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.getAllPurchase();
      });
    },
    // 审核状态  updatePurchaseStatus
    async updatePurchase(row) {
      const params = {
        auditStatus: "2",
        auditName: this.userName,
      };
      this.$confirm("确定审核该采购单", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updatePurchaseStatus(row.id, params);

        this.$message({
          type: "success",
          message: "操作成功!",
        });
        await this.getAllPurchase();
      });
    },
    //  点击添加
    addData() {
      this.$router.push("/Purchase/ManageP/AddMerchantPurchase");
    },
    pageChange(page) {
      this.page = page;
      this.getAllPurchase();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    change() {
      this.purchaseAmount = this.checkList.some((item) => item === "采购金额");
      this.storeFlag = this.checkList.some((item) => item === "仓库");
      this.personnelFlag = this.checkList.some((item) => item === "采购人员");
      this.preparedByFlag = this.checkList.some((item) => item === "制单人员");
      this.documentationDateFlag = this.checkList.some((item) => item === "制单日期");
      this.auditFlag = this.checkList.some((item) => item === "审核状态");
    },
    openWarehouse() {
      this.warehouse_show = true;
    },
    selWarehouse(row) {
      this.warehouseName = row[0].warehouseName;
      this.warehouseId = row[0].id;
      this.getAllPurchase();
    },
    confirmMerchant(row) {
      this.Merchant = row.name;
      this.merchantId = row.id;
      this.getAllPurchase();
    },
    //打印商户入库单
    openMerchantsProcurement(id) {
      let routeData = this.$router.resolve({
        path: `/MerchantsProcurement/${id}`,
      });
      window.open(routeData.href, "_blank");
    },
  },
};
</script>

<style scoped lang="scss">
.mleft10 {
  padding-left: 10px;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
