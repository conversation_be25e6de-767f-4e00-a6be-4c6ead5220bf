<!--新增供应商-->
<template>
  <ContainerTit>
    <span v-if="supplierId" slot="pagetit">
      {{ isDetail ? "商户详情" : "编辑商户" }}
    </span>
    <div slot="headr">
      <el-button
        v-if="!look_path && !supplierId"
        :loading="loading"
        :disabled="$route.params.id"
        @click="delPauseSave(1)"
      >
        清除暂存
      </el-button>
      <el-button v-if="!look_path && !supplierId" :loading="loading" :disabled="$route.params.id" @click="AddTem">
        暂存
      </el-button>
      <el-button v-if="!look_path" type="primary" :loading="loading" @click="conserveSupplier"> 保存提交 </el-button>
    </div>
    <div style="background-color: #fff; padding: 20px">
      <el-form
        ref="add_from"
        :model="add_from"
        size="small"
        :rules="add_rules"
        label-width="160px"
        :disabled="look_path"
      >
        <div class="detail-tab-main">
          <el-form-item label="商户：" prop="code">
            <el-input v-model="add_from.code" placeholder="系统自动生成" disabled></el-input>
          </el-form-item>
          <el-form-item label="商户名称：" prop="title">
            <el-input v-model="add_from.title" placeholder="请输入供应商名称"></el-input>
          </el-form-item>
          <el-form-item label="联系人：" prop="realName">
            <el-input v-model="add_from.realName" placeholder="请输入姓名"></el-input>
          </el-form-item>
          <el-form-item label="联系电话：" prop="mobile">
            <el-input v-model="add_from.mobile" placeholder="请输入手机号码"></el-input>
          </el-form-item>
          <el-form-item label="所属区域：" prop="provinceCode">
            <RegionSelect v-model="area" style="width: 100%" size="medium" @change="regionChange" />
          </el-form-item>
          <el-form-item label="详细地址：">
            <el-input v-model="add_from.address" placeholder="请输入详细地址"></el-input>
          </el-form-item>
          <el-form-item label="联系人职务：" prop="position">
            <el-input v-model="add_from.position" placeholder="请输入职务"></el-input>
          </el-form-item>
          <el-form-item label="开户人：" prop="accountName">
            <el-input v-model="add_from.accountName" placeholder="请输入真实姓名"></el-input>
          </el-form-item>
          <el-form-item label="开户银行：" prop="bankName">
            <el-input v-model="add_from.bankName" placeholder="请输入开户银行"></el-input>
          </el-form-item>
          <el-form-item label="银行账号：" prop="bankCard">
            <el-input v-model="add_from.bankCard" placeholder="请输入银行账号"></el-input>
          </el-form-item>
          <el-form-item label="状态：">
            <el-radio-group v-model="add_from.enableStatus" @change="enableStatusChange">
              <el-radio :label="5">正常</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注：" prop="remark">
            <el-input v-model="add_from.remark" type="textarea" placeholder="请输入备注"></el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </ContainerTit>
</template>

<script>
import RegionSelect from "@/component/common/RegionSelectJSON";
import { getSupplierInfoById, editSupplier, addSupplier } from "@/api/Purchase";
import { addPauseSave, getPauseSave, delPauseSave } from "@/api/common";
export default {
  name: "SupplierAdd",
  components: {
    RegionSelect,
  },
  props: {
    supplierId: {
      type: [Number, String],
      default: 0,
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validatePhone = (rule, value, callback) => {
      const re = /^1[3456789]\d{9}$/; // 正则表达式
      if (value === "") {
        callback(new Error("请输入手机号"));
      } else if (!re.test(value)) {
        callback(new Error("手机号格式有误，请重新输入!"));
      } else {
        callback();
      }
    };
    return {
      loading: false,
      look_path: "",
      supplier_id: "",
      area_options: [],
      unit_list: [],
      brand_list: [],
      freight_model: [],
      area: [], // 所属区域
      add_from: {
        title: "",
        provinceCode: "",
        cityCode: "",
        districtCode: "",
        address: "",
        realName: "",
        mobile: "",
        enableStatus: 5,
        sex: 0,
        phone: "",
        position: "",
        email: "",
        remark: "",
        accountName: "",
        bankName: "",
        bankCard: "",
      },
      add_rules: {
        title: [{ required: true, message: "请输入供应商名称", trigger: "blur" }],
        provinceCode: [{ required: true, message: "请选择所属区域", trigger: "change" }],
        realName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
        mobile: [
          { required: true, message: "请填写联系人手机号", trigger: "blur" },
          { validator: validatePhone, trigger: "blur" },
        ],
        enableStatus: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
      brandName: "",
    };
  },
  created() {
    const path = this.$route.path;
    // 品牌新建供应商标识
    this.brandName = this.$route.path.includes("brand");
    this.look_path = path.substr(33, 12) === "SupplierLook";
    if (this.isDetail) {
      this.look_path = true;
    }
    if (this.supplierId) {
      this.supplier_id = this.supplierId;
      this.getSupplierInfoById();
    }
    if (this.$route.params.id) {
      this.supplier_id = this.$route.params.id;
      this.getSupplierInfoById();
    } else {
      this.getPauseSave();
    }
  },
  methods: {
    //  性别
    sexChange(val) {
      this.add_from.sex = val;
    },
    enableStatusChange(val) {
      this.add_from.enableStatus = val;
    },
    //  区域
    regionChange(val) {
      this.add_from.provinceCode = val[0];
      this.add_from.cityCode = val[1];
      this.add_from.districtCode = val[2];
    },
    //  查看详情
    async getSupplierInfoById() {
      const { data } = await getSupplierInfoById(this.supplier_id);

      this.add_from = data;
      this.area = [data.provinceCode, data.cityCode, data.districtCode].map((item) => {
        return parseInt(item);
      });
    },
    // 获取暂存信息
    async getPauseSave() {
      const { data } = await getPauseSave({
        key: "SupplierAdd",
      });

      if (JSON.stringify(data) === "{}") return;
      this.add_from = data;
      this.area = [data.provinceCode, data.cityCode, data.districtCode].map((item) => {
        return parseInt(item);
      });
    },
    // 点击暂存
    async AddTem() {
      this.loading = true;
      const data = await addPauseSave({
        key: "SupplierAdd",
        data: this.add_from,
      });

      await this.$message({
        type: "success",
        message: "暂存成功",
      });
      await this.$closeCurrentGoEdit("/Purchase/ManageP/Supplier");
    },
    // 清除暂存
    async delPauseSave(type) {
      const data = delPauseSave({
        key: "SupplierAdd",
      });

      if (type) {
        this.$message({
          type: "success",
          message: "清除暂存成功",
        });
        this.$closeCurrentGoEdit("/Purchase/ManageP/SupplierAdd");
      }
    },
    // 新增供应商
    async conserveSupplier() {
      if (!this.add_from.title.trim() || !this.add_from.realName.trim()) {
        this.$message.warning("必填项不能为空");
        return;
      }
      this.$refs.add_from.validate(async (valid) => {
        if (valid) {
          let target = {};

          this.loading = true;
          if (this.supplier_id) {
            target = await editSupplier(this.supplier_id, this.add_from);
            const data = target;
            this.loading = false;
            await this.delPauseSave();
            this.$emit("drawer_false");
          } else {
            target = await addSupplier(this.add_from);
            const data = target;
            this.loading = false;
            await this.delPauseSave();
            this.$closeCurrentGoEdit("/Purchase/ManageP/Supplier");
          }
          this.$message({
            type: "success",
            message: "提交成功",
          });
        }
      });
    },
  },
};
</script>

<style scoped>
.form-card {
  width: auto;
}
.form-card-head {
  font-size: 13px;
  color: #2a2a2a;
  font-weight: 600;
  padding-bottom: 8px;
  padding-left: 10px;
}
.detail-tab-main {
  width: 500px;
}
</style>
