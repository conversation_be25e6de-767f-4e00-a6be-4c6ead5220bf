<template>
  <div class="print-box">
    <el-row class="tem-row">
      <el-col :span="24">
        <div id="printTest" class="tem-img">
          <div class="clearfix" style="font-size: 12px">
            <div class="float_left">
              <span>打印时间：{{ no_time }}</span>
            </div>
            <div class="float_right">
              <p>制单日期:{{ $_common.formatDate(orderDetail.createTime) }}</p>
            </div>
          </div>
          <p style="font-size: 20px; text-align: center">
            <span>采购单</span>
          </p>
          <div class="infomation">
            <span>供应商:{{ orderDetail.supplierName }}</span>
            <span style="padding-left: 40px"> 采购单号:{{ orderDetail.serialNum }} </span>
            <span style="padding-left: 40px"> 采购人员:{{ orderDetail.buyerName }} </span>
            <span style="padding-left: 40px"> 采购仓库:{{ orderDetail.warehouseName }} </span>
            <br />
          </div>
          <div id="second">
            <table border="1" style="width: 100%; margin-top: 10px" class="goods-table">
              <tr>
                <th>序号</th>
                <th>商品编码</th>
                <th>商品名称</th>
                <th>单位</th>
                <th>属性</th>
                <th>采购数量</th>
                <th>采购单价</th>
                <th>采购总价</th>
                <th>商品条码</th>
              </tr>
              <tr v-for="(item, index) in orderDetail.details" :key="index">
                <td>{{ index + 1 }}</td>
                <td>{{ item.goodsCode }}</td>
                <td>{{ item.goodsName }}</td>
                <td>{{ item.unitName }}</td>
                <td>{{ item.skuName }}</td>
                <td>
                  {{ $_common.formatNub(item.buyerNum) }}
                  <span v-if="item.isEq === 5"> （ 其他单位:{{ $_common.formatNub(item.otherNum) }}） </span>
                </td>
                <td>{{ $_common.formattedNumber(item.buyerUnitPrice) }}</td>
                <td>{{ $_common.formattedNumber(item.subtotalPrice) }}</td>
                <td>{{ item.skuBarCode }}</td>
              </tr>
              <tr>
                <td colspan="1" style="text-align: center">合计：</td>
                <td>--</td>
                <td>--</td>
                <td>--</td>
                <td>--</td>
                <td>{{ totalNum }}</td>
                <td>--</td>
                <td>{{ totalMoney }}</td>
                <td>--</td>
              </tr>
              <tr>
                <td :colspan="9" class="remarks">
                  <pre>备注：{{ orderDetail.remark }}</pre>
                </td>
              </tr>
            </table>
          </div>
          <div class="sign clearfix">
            <span class="float_left">签字确认：</span>
            <span class="float_right">经手人：{{ userName }}</span>
          </div>
        </div>
      </el-col>
      <div style="text-align: center">
        <el-button v-print="'#printTest'" type="primary" @click="PrintNumPrintIncr">
          确认打印（已打印{{ orderDetail.printNum || 0 }}次）
        </el-button>
      </div>
    </el-row>
  </div>
</template>

<script>
import { getPurchaseInfoById } from "@/api/Purchase";
import { PrintNumPrintIncr } from "@/api/common";
export default {
  name: "StoragePrinting",
  data() {
    return {
      id: "",
      no_time: "",
      orderDetail: { details: [] },
    };
  },
  computed: {
    totalNum() {
      if (!this.orderDetail.details.length) {
        return 0;
      } else if (this.orderDetail.details.length === 1) {
        return this.orderDetail.details[0].buyerNum - 0;
      } else {
        let sum = 0;
        for (let i in this.orderDetail.details) {
          const item = this.orderDetail.details[i];
          sum = this.$NP.plus(sum, item.buyerNum - 0);
        }
        return sum;
      }
    },
    totalMoney() {
      if (!this.orderDetail.details.length) {
        return 0;
      } else if (this.orderDetail.details.length === 1) {
        return this.orderDetail.details[0].subtotalPrice - 0;
      } else {
        let sum = 0;
        for (let i in this.orderDetail.details) {
          const item = this.orderDetail.details[i];
          sum = this.$NP.plus(sum, item.subtotalPrice - 0);
        }
        return sum;
      }
    },
  },
  created() {
    this.no_time = this.$_common.formatDate(new Date().getTime());
    this.id = this.$route.params.id;
    this.getPurchaseInfoById();
  },
  methods: {
    async PrintNumPrintIncr() {
      const data = await PrintNumPrintIncr({
        objectNo: this.orderDetail.no,
        objectType: this.orderDetail.type,
      });
      this.orderDetail.printNum += 1;
      setTimeout(() => {
        window.close();
      }, 1000);
    },
    async getPurchaseInfoById() {
      const { data } = await getPurchaseInfoById(this.id);
      this.orderDetail = data;
    },
  },
};
</script>

<style scoped lang="scss">
.tem-row {
  padding: 20px 80px;
  position: relative;
}
.tem-img {
  width: 100%;
  /*padding: 20px 0;*/
}
.infomation {
  margin-bottom: 5px;
  font-size: 14px;
}
.sign {
  margin-top: 10px;
  font-size: 14px;
}
.add_num {
  margin-right: 10px;
}
.goods-table th,
.goods-table td {
  text-align: center;
  line-height: 28px;
  font-size: 14px;
}
.goods-table .remarks {
  line-height: 32px;
  text-align: left;
  padding-left: 5px;
  font-weight: bold;
}
.print-tag {
  position: absolute;
  right: 190px;
  top: 60px;
  img {
    width: 120px;
    opacity: 0.7;
  }
}
</style>
