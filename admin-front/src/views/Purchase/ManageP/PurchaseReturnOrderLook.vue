<template>
  <!--采购退货详情-->
  <div class="PurchaseReturnOrderLook">
    <div class="btn-top-div">
      <div v-if="activeName === 'one'">
        <el-button
          v-if="return_detail.auditStatus === 1 && $accessCheck($Access.PurchaseReturnOrderUpdateAuditStatus)"
          type="primary"
          size="small"
          :loading="loading"
          @click="updatePurchaseOut"
        >
          审核退货
        </el-button>
      </div>
      <el-button-group v-if="activeName === 'two'">
        <el-button
          v-if="$accessCheck($Access.InventoryOutUpdateInventoryOutStatus) && out_detail.auditStatus !== 2"
          type="primary"
          @click="updateSaleOutStatus"
        >
          出库审核
        </el-button>
      </el-button-group>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="退货详情" name="one">
        <el-row style="padding-bottom: 13px">
          <el-col :span="24">
            <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">退货单信息</p>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">退货单号</span>
            <span class="form_right">{{ return_detail.no }}</span>
          </el-col>
          <el-col class="form" :span="6" style="padding-left: 26px">
            <span class="form_left">源采购单号</span>
            <span class="form_right">{{ return_detail.originNo }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">创建时间</span>
            <span class="form_right">
              {{ $_common.formatDate(return_detail.createTime) }}
            </span>
          </el-col>
          <el-col class="form" :span="6" style="padding-left: 26px">
            <span class="form_left">供应商名称</span>
            <span class="form_right">
              {{ return_detail.supplierName }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">退货仓库</span>
            <span class="form_right">
              {{ return_detail.warehouseName }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">采购人员</span>
            <span class="form_right">
              {{ return_detail.buyerName }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">审核状态</span>
            <span class="form_right">
              <span v-if="return_detail.auditStatus === 1" class="warning-status"> 未审核 </span>
              <span v-else class="success-status">已审核</span>
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">审核人员</span>
            <span class="form_right">
              {{ return_detail.auditStatus === 1 ? "--" : return_detail.auditName }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">审核时间</span>
            <span class="form_right">
              {{ return_detail.auditStatus === 1 ? "--" : $_common.formatDate(return_detail.auditTime) }}
            </span>
          </el-col>
          <el-col class="form" :span="6" style="padding-left: 54px">
            <span class="form_left">制单人</span>
            <span class="form_right">
              {{ return_detail.operatorName }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">退货说明</span>
            <span class="form_right">
              {{ return_detail.remark || "无" }}
            </span>
          </el-col>
        </el-row>
        <div class="order_bottom">
          <p class="text">商品清单</p>
          <el-table :data="goods_list" size="small" show-summary :summary-method="getSummaries">
            <el-table-column prop="goodsCode" label="商品编码" min-width="130"></el-table-column>
            <el-table-column prop="goodsName" label="商品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="unitName" label="规格" min-width="130">
              <template slot-scope="scope"> {{ scope.row.unitName }};{{ scope.row.skuName }} </template>
            </el-table-column>

            <el-table-column prop="purchaseNum" label="采购数量" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formatNub(scope.row.purchaseNum) }}
              </template>
            </el-table-column>
            <el-table-column prop="buyerNum" label="退货数量" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formatNub(scope.row.buyerNum) }}
              </template>
            </el-table-column>
            <el-table-column prop="otherNum" label="其他单位" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formatNub(scope.row.otherNum) }}
              </template>
            </el-table-column>
            <el-table-column prop="buyerUnitPrice" label="退货单价" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.buyerUnitPrice) }}
              </template>
            </el-table-column>
            <el-table-column prop="subtotalPrice" label="小计金额" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.subtotalPrice) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane
        v-if="parseInt(return_detail.auditStatus) === 2 && $accessCheck($Access.InventoryOutGetInventoryOutInfo)"
        label="出库详情"
        name="two"
      ></el-tab-pane>
    </el-tabs>
    <!--    出库情况-->
    <div v-if="activeName === 'two' && this.$accessCheck(this.$Access.InventoryOutGetInventoryOutInfo)">
      <OutWarehouseInfo
        :audit-out="audit_out"
        :order-id="out_originId"
        :order-type="return_detail.type"
        @getOutDetail="getOutDetail"
      ></OutWarehouseInfo>
    </div>
    <div v-show="activeName === 'three'">
      <el-table style="width: 98%; margin: 20px auto" :data="orderLog" size="small">
        <el-table-column prop="userName" label="操作人"></el-table-column>
        <el-table-column prop="actionType" label="操作类型"></el-table-column>
        <el-table-column prop="createTime" label="操作时间">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import OutWarehouseInfo from "@/views/order/components/OutWarehouseInfo";
import { getPurchaseOutInfoById, updatePurchaseOut } from "@/api/Purchase";
export default {
  name: "PurchaseReturnOrderLook",
  components: {
    OutWarehouseInfo,
  },
  data() {
    return {
      audit_out: false,
      activeName: "one",
      orderLog: [],
      goods_list: [],
      return_detail: {},
      out_originId: "",
      out_id: "",
      out_detail: {},
      loading: false,
    };
  },
  async created() {
    this.pusrchaseOut_id = parseInt(this.$route.params.id);
    await this.getPurchaseOutInfoById();
  },
  async activated() {
    if (this.$_isInit()) return;
    this.pusrchaseOut_id = parseInt(this.$route.params.id);
    await this.getPurchaseOutInfoById();
  },
  methods: {
    updateSaleOutStatus() {
      this.audit_out = !this.audit_out;
    },
    getOutDetail(obj) {
      this.out_detail = obj;
    },
    // 合计
    getSummaries(param) {
      return this.$_common.getSummaries(param, ["采购数量", "退货数量", "小计金额"]);
    },
    //  获取详情 getPurchaseOutInfoById
    async getPurchaseOutInfoById() {
      const { data } = await getPurchaseOutInfoById(this.pusrchaseOut_id);

      this.return_detail = data;
      this.goods_list = data.details;
      this.out_originId = data.originId;
    },
    // 审核状态
    async updatePurchaseOut() {
      this.loading = true;
      const params = {
        auditStatus: "2",
        auditName: this.userName,
      };
      this.$confirm("确定审核该采购退货单", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const data = await updatePurchaseOut(this.pusrchaseOut_id, params);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            await this.getPurchaseOutInfoById();
            if (this.$accessCheck(this.$Access.InventoryOutGetInventoryOutInfo)) {
              this.activeName = "two";
            }
            this.loading = false;
          } catch {
            this.loading = false;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang="scss">
.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.PurchaseReturnOrderLook {
  position: relative;
  background-color: #ffffff;
}
.btn-top-div {
  position: absolute;
  right: 10px;
  top: 16px;
  z-index: 999;
}
.de_label {
  width: 90px;
  display: inline-block;
  text-align: right;
}
.price-div {
  background-color: #f7f7f7;
  border: 1px solid #eeeeee;
  margin: 0 auto;
  padding: 20px;
  text-align: right;
  width: 98%;
  border-top: 0;
}
.price-div .de_label {
  width: auto;
}
.price-div .price-num {
  color: #f56c6c;
  margin-right: 30px;
}
</style>
<style>
.PurchaseReturnOrderLook .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.PurchaseReturnOrderLook .is-active {
  font-weight: 700;
}
.PurchaseReturnOrderLook .el-tabs__nav {
  margin-left: 24px;
}
</style>
