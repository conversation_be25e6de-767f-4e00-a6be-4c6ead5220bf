<template>
  <ContainerQuery>
    <!--    <div slot="left">-->
    <!--      <el-button size="small" type="primary">导出</el-button>-->
    <!--    </div>-->
    <div slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input v-model="no" style="width: 220px" placeholder="订单号" clearable @clear="pageChange(1)">
            <el-button slot="append">
              <i class="el-icon-search" @click="pageChange(1)"></i>
            </el-button>
          </el-input>
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <el-input-->
        <!--            v-model="search_form.staff"-->
        <!--            clearable-->
        <!--            style="width: 150px"-->
        <!--            placeholder="业务员"-->
        <!--            @clear="staffClear"-->
        <!--            @blur="search_form.staff = ''"-->
        <!--          >-->
        <!--            <i-->
        <!--              slot="suffix"-->
        <!--              class="el-input__icon el-icon-search"-->
        <!--              @click="saleFn(true)"-->
        <!--            ></i>-->
        <!--          </el-input>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <SelectCustomer v-model="customer_name" @clear="customerClear" @change="customerSel" />
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="下单开始日期"
            end-placeholder="下单结束日期"
            @change="searchData"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <el-table ref="billData" :data="tableData" style="width: 100%">
      <el-table-column type="index" label="ID" width="60"></el-table-column>
      <el-table-column prop="customerName" label="客户名" min-width="160" show-overflow-tooltip></el-table-column>
      <el-table-column prop="salesManName" label="业务员" show-overflow-tooltip min-width="140"></el-table-column>
      <el-table-column prop="goodsName" label="商品" width="100">
        <template slot-scope="scope">
          <div class="sku-btn" @click="toggleRowExpansion(scope.row)">
            {{ `${scope.row.goodsData.length}件商品` }}
          </div>
        </template>
      </el-table-column>
      <el-table-column min-width="40" label="" type="expand">
        <template slot-scope="scope">
          <ul class="sku-ul">
            <li v-for="(item, index) in scope.row.goodsData" :key="index" class="sku-li">
              <div class="clearfix">
                <div class="float_left">
                  <img class="sku-img" :src="item.images[0]" alt="" />
                </div>
                <div class="sku-info float_left">
                  <p>{{ item.goodsName }}</p>
                  <p class="code">{{ item.goodsCode }}</p>
                  <p>
                    <span class="label">规格:</span>
                    {{ item.unitName }};

                    <span v-for="(sku, skuI) in item.specGroup" :key="skuI">
                      {{ sku.specValueName }}
                    </span>
                  </p>
                  <p>
                    <span class="label">库存:</span>
                    {{ $_common.formatNub(item.inventory) }}；
                    <span class="label">未发货:</span>
                    {{ $_common.formatNub(item.salesNum) }}
                  </p>
                </div>
              </div>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column prop="no" min-width="180" label="订单号"></el-table-column>
      <el-table-column prop="createTime" min-width="160" label="订单时间">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <staffListModal
      v-if="staff_show"
      :is-show="staff_show"
      :is-check="false"
      :isserch="isserch"
      @cancel="staff_show = false"
      @confirm="staffSel"
    />
  </ContainerQuery>
</template>
<script>
import { getDistributionAll } from "@/api/Order";
import SelectCustomer from "@/component/common/SelectCustomer.vue";
import staffListModal from "@/component/common/staffListModal";
export default {
  components: { SelectCustomer, staffListModal },
  data() {
    return {
      customer_name: "",
      isserch: true,
      staff_show: false,
      page: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
      time: [],
      no: "",
      userCenterId: "",
    };
  },
  created() {
    this.getDistributionAll();
  },
  methods: {
    customerSel(val, row) {
      this.userCenterId = row[0].userCenterId;
      this.pageChange(1);
    },
    customerClear() {
      this.customer_name = "";
      this.userCenterId = "";
      this.pageChange(1);
    },
    staffSel() {},
    staffClear() {},
    searchData() {
      this.page = 1;
      this.getDistributionAll();
    },
    pageChange(page) {
      this.page = page;
      this.getDistributionAll();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    // 用于可展开表格与树形表格，切换某一行的展开状态，如果使用了第二个参数，则是设置这一行展开与否（expanded 为 true 则展开）
    toggleRowExpansion(row) {
      this.$refs.billData.toggleRowExpansion(row);
    },
    async getDistributionAll() {
      const { data, pageTotal } = await getDistributionAll({
        page: this.page,
        pageSize: this.pageSize,
        no: this.no,
        startTime: this.time[0] / 1000,
        endTime: this.time[1] / 1000,
        userCenterId: this.userCenterId,
      });
      this.tableData = data;
      this.total = pageTotal;
    },
  },
};
</script>
<style lang="scss" scoped>
.sku-ul {
  .sku-li {
    display: inline-block;
    margin-right: 10px;
    border: 1px solid #ebeef5;
    padding: 10px;
    width: 294px;
    vertical-align: middle;
    .sku-img {
      width: 50px;
      margin-right: 8px;
    }
    .sku-info {
      line-height: 23px;
      color: #111111;
      .code {
        color: #666666;
        font-size: 12px;
      }
      .label {
        display: inline-block;
        /*width: 50px;*/
        color: #666666;
        text-align: right;
      }
    }
  }
}
.sku-btn {
  width: 71px;
  height: 22px;
  line-height: 22px;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #cad0d7;
  text-align: center;
  font-size: 12px;
  cursor: pointer;
}
</style>
