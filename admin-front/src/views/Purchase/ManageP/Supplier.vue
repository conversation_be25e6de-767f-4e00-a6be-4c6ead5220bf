<!--供应商管理-->
<template>
  <Container>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示： 1、新增供应商后，供应商只有在禁用状态下才可编辑，启用后无法进行编辑操作！
      2、只有在供应商管理页面新增了供应商才能在采购单进行创建采购单的操作！
    </div>
    <div slot="left" class="supplier-search">
      <div style="margin-right: 16px; vertical-align: middle; display: inline-block">
        <el-input
          v-model="keyword"
          style="width: 220px"
          placeholder="编码/供应商名称/联系人"
          size="small"
          clearable
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button
            v-if="$accessCheck($Access.SupplierAddSupplier)"
            slot="append"
            icon="el-icon-search"
            @click="pageChange(1)"
          ></el-button>
        </el-input>
      </div>
      <el-dropdown split-button type="primary" @click="addData">
        新增供应商
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <div class="dropdown-div" @click="$router.push('/Purchase/ManageP/SupplierImport')">导入</div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!--      <el-button size="small" type="primary" @click="addData">-->
      <!--        新增供应商-->
      <!--      </el-button>-->
    </div>
    <el-table :data="tableData" @row-dblclick="lookData">
      <el-table-column prop="code" align="left" label="编码">
        <template slot-scope="scope">
          <span v-if="$accessCheck($Access.SupplierUpdateEnableStatus)" class="click-div" @click="lookData(scope.row)">
            {{ scope.row.code }}
          </span>
          <span v-else>{{ scope.row.code }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="供应商名称" align="left"></el-table-column>
      <el-table-column prop="realName" label="联系人" align="left"></el-table-column>
      <el-table-column prop="mobile" label="联系电话" align="left">
        <template slot-scope="scope">
          <div>{{ scope.row.mobile }}</div>
          <el-button
            v-if="scope.row.mobile && !scope.row.userCenterId"
            type="text"
            size="mini"
            @click="addMobile(scope.row.id)"
          >
            创建账号
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="realName" align="left" label="联系地址" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.area.provinceName }}-{{ scope.row.area.cityName }}-{{ scope.row.area.districtName }}-{{
            scope.row.area.address
          }}
        </template>
      </el-table-column>

      <el-table-column prop="enableStatus" align="left" label="状态">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.SupplierUpdateEnableStatus)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="updateEnableStatus($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="left" width="180px" label="操作" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.SupplierGetSupplierInfoById) && $accessCheck($Access.SupplierEditSupplier)"
            :disabled="scope.row.enableStatus === 5"
            type="text"
            @click="editData(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.SupplierDelSupplier)"
            :disabled="scope.row.enableStatus === 5"
            type="text"
            @click="delData(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <el-drawer class="edit-drawer" size="40%" :title="drawer_tit" :visible.sync="drawer" @close="close_drawer">
      <SupplierEdit v-if="drawer" :is-detail="is_detail" :supplier-id="supplier_id" @drawer_false="close_drawer" />
    </el-drawer>
  </Container>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import SupplierEdit from "./SupplierAdd";
import { getAllSupplier, supplierEnableStatus, delSupplier, addSupplierUserCenter } from "@/api/Purchase";

export default {
  name: "Supplier",
  components: {
    FooterPage,
    SupplierEdit,
  },
  data() {
    return {
      drawer_tit: "",
      is_detail: false,
      drawer: false,
      tableData: [],
      supplier_id: 0,
      total: 0,
      page: 1,
      pageSize: 10,
      keyword: "",
      is_open: false,
    };
  },
  created() {
    this.getAllSupplier();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllSupplier();
  },
  methods: {
    // 添加供应商为用户
    async addMobile(id) {
      this.$confirm("确定要将该供应商添加为用户吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await addSupplierUserCenter(id);
        this.$message.success(data.data);
        this.getAllSupplier();
      });
    },
    //  获取供应商列表 getAllSupplier
    async getAllSupplier() {
      const data = await getAllSupplier({
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
      });

      this.tableData = data.data;
      this.total = data.pageTotal;
    },
    //  点击启用禁用  supplierEnableStatus
    async updateEnableStatus(val, row) {
      try {
        const data = await supplierEnableStatus(row.id, {
          enableStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        this.getAllSupplier();
      }
    },
    lookData(row) {
      if (!this.$accessCheck(this.$Access.SupplierGetSupplierInfoById)) {
        return;
      }
      this.drawer = true;
      this.is_detail = true;
      this.drawer_tit = row.title;
      this.supplier_id = row.id;
    },
    editData(row) {
      this.drawer = true;
      this.drawer_tit = row.title;
      this.supplier_id = row.id;
    },
    addData() {
      this.$router.push("/Purchase/ManageP/SupplierAdd");
    },
    //  刪除
    delData(id) {
      this.$confirm("请确认是否删除该条分类?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delSupplier(id);
        this.$message({
          type: "success",
          message: "删除成功!",
        });

        this.getAllSupplier();
      });
    },
    pageChange(val) {
      this.page = val;
      this.getAllSupplier();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getAllSupplier();
    },
    close_drawer() {
      this.drawer = false;
      this.pageChange(1);
    },
  },
};
</script>

<style>
.supplier-search .el-dropdown {
  vertical-align: middle;
}
</style>
