<!--采购退货单-->
<template>
  <ContainerQuery>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      1、已审核采购退货单无法进行编辑操作，只有未审核的采购退货单才能编辑！
      2、采购退货单审核后，自动在库存-出库管理-采购退货中生成对应的出库单！
    </div>
    <div slot="left">
      <el-dropdown
        v-if="$accessCheck($Access.PurchaseReturnOrderAddPurchaseOut)"
        type="primary"
        split-button
        @click="addData"
      >
        新增退货单
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-if="$accessCheck($Access.PurchaseReturnOrderGetAllPurchaseOut)">
            <div class="dropdown-div" @click="getAllPurchaseOut(1)">导出</div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div v-else>
        <el-button
          v-if="$accessCheck($Access.PurchaseReturnOrderGetAllPurchaseOut)"
          size="small"
          @click="getAllPurchaseOut(1)"
        ></el-button>
      </div>
    </div>
    <div slot="more" style="margin-bottom: 10px" class="clearfix">
      <el-form size="small" inline>
        <el-form-item>
          <el-input
            v-model="purchaseNo"
            placeholder="采购退货单号"
            size="small"
            style="width: 220px"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="auditStatus"
            style="width: 150px"
            size="small"
            placeholder="审核状态"
            clearable
            @change="pageChange(1)"
            @clear="delStatu"
          >
            <el-option
              v-for="item in shenhe_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <el-input-->
        <!--            v-model="goods"-->
        <!--            placeholder="请输入商品名称"-->
        <!--            style="width: 220px"-->
        <!--            clearable-->
        <!--            @keyup.enter.native="pageChange()"-->
        <!--            @clear="pageChange()"-->
        <!--          >-->
        <!--            <el-button-->
        <!--              slot="append"-->
        <!--              icon="el-icon-search"-->
        <!--              @click="pageChange()"-->
        <!--            ></el-button>-->
        <!--          </el-input>-->
        <!--        </el-form-item>-->
        <!--<el-form-item v-if="systemType === 1">
          <SelectShop
            v-model="shopId"
            size="small"
            placeholder="选择店铺"
            width="150"
            @change="selShop"
            @clear="shopClear"
          />
        </el-form-item>-->
      </el-form>
    </div>
    <el-tabs v-model="activeName" type="card" @tab-click="pageChange(1)">
      <el-tab-pane label="供应商退货单" name="4"></el-tab-pane>
      <el-tab-pane label="商户退货单" name="5"></el-tab-pane>
    </el-tabs>
    <el-table :data="tableData" @row-dblclick="lookData">
      <el-table-column prop="id" label="ID" fixed="left" width="50"></el-table-column>
      <el-table-column prop="no" label="退货单号" min-width="180">
        <template slot-scope="scope">
          <span
            v-if="$accessCheck($Access.PurchaseReturnOrderGetPurchaseOutInfoById)"
            class="click-div"
            @click="lookData(scope.row)"
          >
            {{ scope.row.no }}
          </span>
          <span v-else>{{ scope.row.no }}</span>
        </template>
      </el-table-column>
      <el-table-column min-width="150">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span v-if="activeName === '4'">供应商</span>
          <span v-if="activeName === '5'">商户</span>
        </template>
        <template slot-scope="scope">
          <span v-if="scope.row.purchaseStatus === 5">
            {{ scope.row.merchantName }}
          </span>
          <span v-else>
            {{ scope.row.supplierName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="salesReturnFlag" prop="purchaseAmount" label="退货金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.purchaseAmount, 2) }}
        </template>
      </el-table-column>
      <el-table-column v-if="purchaseFlag" prop="originNo" label="关联采购单" min-width="180"></el-table-column>
      <el-table-column
        v-if="storeFlag"
        prop="warehouseName"
        label="仓库"
        show-overflow-tooltip
        min-width="120"
      ></el-table-column>
      <el-table-column v-if="preparedByFlag" prop="operatorName" label="制单人员" min-width="120"></el-table-column>
      <el-table-column v-if="documentationDateFlag" prop="goodsData" label="制单日期" min-width="150">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime, "yyyy-MM-dd hh:mm:ss") }}
        </template>
      </el-table-column>
      <el-table-column v-if="auditFlag" prop="auditStatus" label="审核状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 2" class="success-status"> 已审核 </span>
          <span v-else class="warning-status">未审核</span>
        </template>
      </el-table-column>
      <el-table-column v-if="stockRemovalFlag" label="出库状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.outStatus === 5" class="success-status"> 已出库 </span>
          <span v-else class="warning-status">未出库</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="180">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span class="operation">操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.PurchaseReturnOrderUpdateAuditStatus)"
            :disabled="scope.row.auditStatus === 2"
            type="text"
            @click="updatePurchaseOut(scope.row)"
          >
            审核
          </el-button>
          <el-button
            v-if="
              $accessCheck($Access.PurchaseReturnOrderGetPurchaseOutInfoById) &&
              $accessCheck($Access.PurchaseReturnOrderEditPurchase)
            "
            type="text"
            :disabled="scope.row.auditStatus === 2"
            @click="editData(scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.PurchaseReturnOrderDelPurchaseOut)"
            type="text"
            @click="delData(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import { getAllPurchaseOut, exportGetAllPurchaseOut, updatePurchaseOut, delPurchaseOut } from "@/api/Purchase";

// import SelectShop from "@/component/goods/SelectShop.vue";

export default {
  name: "PurchaseReturnOrder",
  components: {
    // SelectShop,
  },
  data() {
    return {
      goods: "",
      purchaseNo: "",
      shopId: "",
      auditStatus: "",
      shenhe_options: [
        { value: 1, label: "未审核" },
        { value: 2, label: "已审核" },
      ],
      tableData: [],
      total: 1,
      page: 1,
      pageSize: 10,
      checkList: ["退货金额", "采购关联号", "店铺", "制单人员", "制单日期", "审核状态", "出库状态"],
      columns: [
        {
          label: "退货金额",
        },
        {
          label: "采购关联号",
        },
        {
          label: "店铺",
        },
        {
          label: "制单人员",
        },
        {
          label: "制单日期",
        },
        {
          label: "审核状态",
        },
        {
          label: "出库状态",
        },
      ],
      salesReturnFlag: true,
      purchaseFlag: true,
      storeFlag: true,
      preparedByFlag: true,
      documentationDateFlag: true,
      auditFlag: true,
      stockRemovalFlag: true,
      activeName: "4",
      name: "",
    };
  },
  created() {
    this.getAllPurchaseOut();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllPurchaseOut();
  },
  methods: {
    selShop() {
      this.pageChange(1);
    },
    shopClear() {
      this.shopId = "";
      this.pageChange(1);
    },
    delStatu() {
      this.auditStatus = "";
      this.pageChange(1);
    },
    //  获取退货但的列表
    async getAllPurchaseOut(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        auditStatus: this.auditStatus,
        shopId: this.shopId,
        sourceNo: this.purchaseNo,
        goodsName: this.goods,
        purchaseStatus: this.activeName,
      };
      if (exports) {
        params.export = exports;
        const target = await exportGetAllPurchaseOut(params);
      } else {
        const data = await getAllPurchaseOut(params);

        this.total = data.pageTotal;
        this.tableData = data.data;
      }
    },
    // 审核状态
    async updatePurchaseOut(row) {
      const params = {
        auditStatus: "2",
        auditName: this.userName,
      };
      this.$confirm("确定审核该订单", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updatePurchaseOut(row.id, params);

        this.$message({
          type: "success",
          message: "操作成功!",
        });
        await this.getAllPurchaseOut();
      });
    },
    //  编辑
    editData(id) {
      this.$router.push(`/Purchase/ManageP/PurchaseReturnOrderEdit/${id}/${this.activeName}`);
    },
    //  查看
    lookData(row) {
      if (!this.$accessCheck(this.$Access.PurchaseReturnOrderGetPurchaseOutInfoById)) {
        return;
      }
      this.$router.push(`/Purchase/ManageP/PurchaseReturnOrderLook/${row.id}`);
    },
    delData(id) {
      this.$confirm("请确认是否删除该条分类?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delPurchaseOut(id);

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.getAllPurchaseOut();
      });
    },
    addData() {
      this.$router.push(`/Purchase/ManageP/PurchaseReturnOrderAdd/${this.activeName}`);
    },
    pageChange(page) {
      this.page = page;
      this.getAllPurchaseOut();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    change() {
      this.salesReturnFlag = this.checkList.some((item) => item === "退货金额");
      this.purchaseFlag = this.checkList.some((item) => item === "采购关联号");
      this.storeFlag = this.checkList.some((item) => item === "店铺");
      this.preparedByFlag = this.checkList.some((item) => item === "制单人员");
      this.documentationDateFlag = this.checkList.some((item) => item === "制单日期");
      this.auditFlag = this.checkList.some((item) => item === "审核状态");
      this.stockRemovalFlag = this.checkList.some((item) => item === "出库状态");
    },
  },
};
</script>

<style scoped lang="scss">
.mleft10 {
  padding-left: 10px;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
