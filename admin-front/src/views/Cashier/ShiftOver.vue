<template>
  <Container>
    <div slot="left">
      <el-form :model="add_form" size="small" :inline="true">
        <el-form-item>
          <div class="block">
            <el-date-picker
              v-model="add_form.time"
              type="daterange"
              value-format="timestamp"
              range-separator="-"
              start-placeholder="交班开始日期"
              end-placeholder="交班结束日期"
              @change="orderTimeChange"
            ></el-date-picker>
          </div>
        </el-form-item>
        <el-form-item label="收银员">
          <el-select v-model="add_form.value" placeholder="全部" @change="pageChange(1)">
            <el-option value="" label="全部"></el-option>
            <el-option :value="4" label="操作员（超级管理员）"></el-option>
          </el-select>
        </el-form-item>
        <!--        <el-button size="mini" type="primary" plain>导出</el-button>-->
      </el-form>
    </div>
    <el-table :data="tableData">
      <el-table-column prop="staffName" label="收银员" width="180"></el-table-column>
      <el-table-column prop="atWorkTime" label="上班时间" width="180">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.atWorkTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="交班时间">
        <template slot-scope="scope">
          {{ scope.row.updateTime ? $_common.formatDate(scope.row.updateTime) : "" }}
        </template>
      </el-table-column>
      <el-table-column prop="collectionMoney" label="收款金额">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.collectionMoney) }}
        </template>
      </el-table-column>
      <el-table-column v-if="$accessCheck($Access.ShiftOvergetRecordInfo)" prop="address" label="操作" width="100px">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="getAll(scope.row.id)"> 详情 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { getRecordList } from "@/api/Cashier";
export default {
  name: "ShiftOver",
  data() {
    return {
      add_form: {
        time: "",
        value: "",
      },
      tableData: [],
      startTime: "",
      endTime: "",
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  created() {
    this.getRecordList();
  },
  methods: {
    pageChange(val) {
      this.page = val;
      this.getRecordList();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    orderTimeChange(val) {
      if (val && val.length) {
        this.startTime = val[0] / 1000;
        this.endTime = val[1] / 1000 + 86399;
      } else {
        this.startTime = "";
        this.endTime = "";
      }
      this.pageChange(1);
    },
    async getRecordList() {
      const data = await getRecordList({
        page: this.page,
        pageSize: this.pageSize,
        startTime: this.startTime,
        endTime: this.endTime,
        staffId: this.add_form.value,
      });

      this.tableData = data.data;
      this.total = data.pageTotal;
    },
    getAll(id) {
      this.$router.push("/Cashier/ShiftOverGetAll/" + id);
    },
  },
};
</script>

<style scoped></style>
