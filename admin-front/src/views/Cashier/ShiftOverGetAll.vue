<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" @click="submit">返回</el-button>
    </div>
    <el-form ref="add_form" :model="add_form" label-width="200px" :rules="base_rules" size="small">
      <el-card class="box-card" shadow="hover">
        <div slot="header">
          <span>收银员基本资料</span>
        </div>
        <el-form-item label="收银员">
          {{ add_form.staffName }}
        </el-form-item>
        <el-form-item label="手机号">
          {{ add_form.mobile }}
        </el-form-item>
        <el-form-item label="时间">
          {{ $_common.formatDate(add_form.atWorkTime) }} --
          {{ add_form.offDutyTime ? $_common.formatDate(add_form.offDutyTime) : "上班中" }}
        </el-form-item>
        <el-form-item label="上班时长">
          {{ add_form.hours || "上班中" }}
        </el-form-item>
        <el-form-item label="备注">
          {{ add_form.remark || "无" }}
        </el-form-item>
      </el-card>
      <el-card class="box-card" shadow="hover">
        <div slot="header">
          <span>收款金额</span>
          <span style="color: red"> ￥{{ add_form.collectionMoney || 0.0 }} </span>
        </div>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="cash" label="现金"></el-table-column>
          <el-table-column prop="wechat" label="微信"></el-table-column>
          <el-table-column prop="alipay" label="支付宝"></el-table-column>
          <el-table-column prop="balance" label="余额"></el-table-column>
          <el-table-column prop="other" label="其他"></el-table-column>
        </el-table>
      </el-card>
      <el-card class="box-card" shadow="hover">
        <div slot="header">
          <span>充值金额</span>
          <span style="color: red">￥{{ add_form.rechargeMoney || 0.0 }}</span>
        </div>
        <el-table :data="rechargeData" style="width: 100%">
          <el-table-column prop="cash" label="现金"></el-table-column>
          <el-table-column prop="wechat" label="微信"></el-table-column>
          <el-table-column prop="alipay" label="支付宝"></el-table-column>
          <el-table-column prop="other" label="其他"></el-table-column>
        </el-table>
      </el-card>
      <el-card class="box-card" shadow="hover">
        <div slot="header">
          <span>退款金额</span>
          <span style="color: red">￥{{ add_form.refundMoney || 0.0 }}</span>
        </div>
        <el-table :data="refundData">
          <el-table-column prop="cash" label="现金"></el-table-column>
          <el-table-column prop="wechat" label="微信"></el-table-column>
          <el-table-column prop="alipay" label="支付宝"></el-table-column>
          <el-table-column prop="balance" label="余额"></el-table-column>
        </el-table>
      </el-card>
    </el-form>
  </ContainerTit>
</template>

<script>
import { getRecordInfo } from "@/api/Cashier";
export default {
  name: "ShiftOverGetAllVue",
  data() {
    return {
      record_id: "",
      add_form: {},
      base_rules: {},
      tableData: [],
      rechargeData: [],
      refundData: [],
    };
  },
  created() {
    this.record_id = this.$route.params.id;
    this.getRecordInfo();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getRecordInfo();
  },
  methods: {
    submit() {
      this.$router.push("/Cashier/ShiftOver");
    },
    async getRecordInfo() {
      const data = await getRecordInfo(this.record_id);

      this.tableData.push(data.data.collectionData);
      this.rechargeData.push(data.data.rechargeData);
      this.refundData.push(data.data.refundData);
      this.add_form = data.data;
    },
  },
};
</script>

<style scoped></style>
