<template>
  <ContainerTit>
    <Container></Container>
    <div v-if="$accessCheck($Access.cashierSetset)" slot="headr">
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
    <el-form ref="add_form" :model="add_form" label-width="200px" :rules="base_rules" size="small">
      <div class="detail-tab-item">
        <p class="detail-tab-title">基础设置</p>
        <div class="detail-tab-main">
          <el-form-item
            v-if="$accessCheck($Access.CashierShop)"
            label="收银台地址："
            style="cursor: pointer; color: #1881f7"
          >
            <span @click="openUrl">{{ basic_url }}</span>
            <el-button type="text" style="margin-left: 10px" @click="openUrl"> 点击打开 </el-button>
            <!-- <el-button
              type="text"
              style="margin-left: 10px"
              v-clipboard:copy="add_form.basic.url"
              v-clipboard:success="copyUrl"
              v-clipboard:error="copyError"
            >
              复制123
            </el-button>-->
          </el-form-item>
          <el-form-item label="参与分销：">
            <el-radio-group v-model="add_form.basic.join_marketing">
              <el-radio :label="5">参与</el-radio>
              <el-radio :label="4">不参与</el-radio>
            </el-radio-group>
            <p class="form-tip">营销活动支持，优惠券。</p>
          </el-form-item>
          <el-form-item label="关联导购：">
            <el-radio-group v-model="add_form.basic.relevance_guide">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">不启用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="自动打印小票：">
            <el-radio-group v-model="add_form.basic.auto_print_receipts">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">不启用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="打印交班小票：">
            <el-radio-group v-model="add_form.basic.shift_exchange_print_receipts">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">不启用</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <div class="detail-tab-title">支付设置</div>
        <div class="detail-tab-main">
          <!--          <el-form-item label="微信支付：">-->
          <!--            <el-radio-group v-model="add_form.pay_set.wechat_payment_id">-->
          <!--              <el-radio :label="5">-->
          <!--                启用-->
          <!--              </el-radio>-->
          <!--              <el-radio :label="4">-->
          <!--                不启用-->
          <!--              </el-radio>-->
          <!--            </el-radio-group>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="支付宝：">-->
          <!--            <el-radio-group v-model="add_form.pay_set.alipay_payment_id">-->
          <!--              <el-radio :label="5">-->
          <!--                启用-->
          <!--              </el-radio>-->
          <!--              <el-radio :label="4">-->
          <!--                不启用-->
          <!--              </el-radio>-->
          <!--            </el-radio-group>-->
          <!--          </el-form-item>-->
          <!--<el-form-item label="余额支付：">
            <el-radio-group v-model="add_form.pay_set.balance_pay">
              <el-radio :label="5">
                启用
              </el-radio>
              <el-radio :label="4">
                不启用
              </el-radio>
            </el-radio-group>-->
          <!--          </el-form-item>-->
          <el-form-item label="现金支付：">
            <el-radio-group v-model="add_form.pay_set.cash_pay">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">不启用</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <div class="detail-tab-title">抹零设置</div>
        <div class="detail-tab-main">
          <el-form-item label="状态：">
            <el-radio-group v-model="add_form.zero_set.status">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">不启用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="抹零方式：">
            <el-radio-group v-model="add_form.zero_set.type">
              <el-radio :label="1">抹分</el-radio>
              <el-radio :label="2">抹角</el-radio>
              <el-radio :label="3">四舍五入到角</el-radio>
              <el-radio :label="4">四舍五入到元</el-radio>
            </el-radio-group>
            <p class="form-tip">
              例：1.86元，抹分后收款1.80元，抹角后收1.00元，四舍五入到角后收1.90元，四舍五入到元后收2.00元。
            </p>
          </el-form-item>
          <el-form-item label="自动抹零：">
            <el-radio-group v-model="add_form.zero_set.auto_zero">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">不启用</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <div class="detail-tab-title">收银员提成</div>
        <div class="detail-tab-main">
          <el-form-item label="状态：">
            <el-radio-group v-model="add_form.cashier_push_money.status">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">不启用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="提成设置：">
            <el-radio-group v-model="add_form.cashier_push_money.type">
              <el-radio :label="5">按金额</el-radio>
              <el-radio :label="4">按订单</el-radio>
            </el-radio-group>
            <br />
            <el-input
              v-if="add_form.cashier_push_money.type === 5"
              v-model="add_form.cashier_push_money.push_money_rate"
              placeholder="请输入内容"
              style="width: 240px"
            >
              <template slot="prepend">提成比例</template>
              <template slot="append">%</template>
            </el-input>
            <el-input
              v-else
              v-model="add_form.cashier_push_money.push_money_rate"
              placeholder="请输入内容"
              style="width: 240px"
            >
              <template slot="prepend">1单提成</template>
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <div class="detail-tab-title">导购提成</div>
        <div class="detail-tab-main">
          <el-form-item label="状态：">
            <el-radio-group v-model="add_form.guide_push_money.status">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">不启用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="提成设置：">
            <el-radio-group v-model="add_form.guide_push_money.type">
              <el-radio :label="5">按金额</el-radio>
              <el-radio :label="4">按订单</el-radio>
            </el-radio-group>
            <br />
            <el-input
              v-if="add_form.guide_push_money.type === 5"
              v-model="add_form.guide_push_money.push_money_rate"
              placeholder="请输入内容"
              style="width: 240px"
            >
              <template slot="prepend">提成比例</template>
              <template slot="append">%</template>
            </el-input>
            <el-input
              v-else
              v-model="add_form.guide_push_money.push_money_rate"
              placeholder="请输入内容"
              style="width: 240px"
            >
              <template slot="prepend">1单提成</template>
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </ContainerTit>
</template>

<script>
import { setSettings, getSettings } from "@/api/Cashier";
export default {
  name: "CashierList",
  data() {
    return {
      add_form: {
        basic: {
          url: "",
          join_marketing: 5,
          relevance_guide: 4,
          auto_print_receipts: 5,
          shift_exchange_print_receipts: 4,
        },
        pay_set: {
          cash_pay: 4,
          balance_pay: 4,
          alipay_payment_id: 4,
          wechat_payment_id: 4,
        },
        zero_set: {
          type: 4,
          status: 5,
          auto_zero: 4,
        },
        guide_push_money: {
          type: 4,
          status: 4,
          push_money_rate: 4,
        },
        cashier_push_money: {
          type: 4,
          status: 4,
          push_money_rate: 4,
        },
      },
      base_rules: {},
      origin_url: "",
      basic_url: "",
    };
  },
  created() {
    this.getSettings();
    // this.add_form.basic.url = window.location.origin + "/#/CashierShop";
    this.basic_url = window.location.origin + "/#/CashierShop";
  },
  methods: {
    // 复制地址
    copyUrl() {
      this.$message.success("复制成功");
    },
    copyError() {
      this.$message.warning("复制失败");
    },
    // 打开收银台
    openUrl() {
      let routeData = this.$router.resolve({
        path: `/CashierShop`,
      });
      window.open(routeData.href, "_blank");
    },
    async submit() {
      const data = await setSettings({
        add_form: this.add_form,
      });

      this.$message.success("保存成功");
      this.getSettings();
    },
    async getSettings() {
      if (!this.$accessCheck(this.Access.cashierSetget)) {
        return;
      }
      const { data } = await getSettings();

      if (data.add_form) {
        this.add_form = data.add_form;
      }
    },
  },
};
</script>

<style scoped></style>
