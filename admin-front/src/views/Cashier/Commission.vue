<template>
  <div class="commission">
    <div class="model-view">
      <div class="clearfix model-tit">
        <div class="float_left">提成概况</div>
        <div class="float_right">
          <el-date-picker
            v-model="time"
            size="small"
            type="daterange"
            value-format="timestamp"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="add_form.pickerOptions1"
            @change="orderTimeChange"
          ></el-date-picker>
        </div>
      </div>
      <div class="model-main" style="display: flex">
        <div class="top-data-li">
          <div class="grid-content clearfix">
            <div class="float_left left-icon">
              <i class="iconfont icon-kehu"></i>
            </div>
            <div class="float_left">
              <p class="price">收银员提成(元)</p>
              <p class="num" style="color: #0fcbcd">
                {{ $_common.formattedNumber(add_form.cashier_push_money) || 0 }}
              </p>
            </div>
          </div>
        </div>
        <div class="top-data-li">
          <div class="grid-content clearfix">
            <div class="float_left left-icon" style="background: linear-gradient(-125deg, #f6a16a, #fbd661)">
              <i class="iconfont icon-kehu"></i>
            </div>
            <div class="float_left">
              <p class="price">导购员提成(元)</p>
              <p class="num" style="color: #f6a16a">
                {{ $_common.formattedNumber(add_form.guide_push_money) || 0 }}
              </p>
            </div>
          </div>
        </div>
        <div class="top-data-li">
          <div class="grid-content clearfix">
            <div class="float_left left-icon" style="background: linear-gradient(-125deg, #f63057, #ee5f82)">
              <i class="iconfont icon-zu"></i>
            </div>
            <div class="float_left">
              <p class="price">订单付款金额(元)</p>
              <p class="num" style="color: #f63057">
                {{ $_common.formattedNumber(add_form.order_pay_price) || 0 }}
              </p>
            </div>
          </div>
        </div>
        <div class="top-data-li">
          <div class="grid-content clearfix">
            <div class="float_left left-icon" style="background: linear-gradient(-125deg, #00b29c, #38e8be)">
              <i class="iconfont icon-dingdan"></i>
            </div>
            <div class="float_left">
              <p class="price">订单数</p>
              <p class="num" style="color: #00b29c">
                {{ add_form.order_num || 0 }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="model-view">
      <div class="clearfix model-tit">
        <div class="float_left">提成统计</div>
        <div class="float_right">
          <div class="s-item">
            <span class="s-label">类型:</span>
            <el-select v-model="value" size="small" placeholder="类型" @change="staffChange">
              <el-option :value="1" label="收银员"></el-option>
              <el-option :value="2" label="导购员"></el-option>
            </el-select>
          </div>
          <div class="s-item">
            <span class="s-label">姓名:</span>
            <el-select v-model="cashier_name" size="small" clearable @change="pageChange(1)">
              <el-option
                v-for="item in shop_cashier"
                :key="item.id"
                :label="item.staffName"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="model-main">
        <el-table :data="tableData" style="margin-bottom: 20px">
          <el-table-column type="index" label="#"></el-table-column>
          <el-table-column prop="staffName" label="姓名"></el-table-column>
          <el-table-column prop="mobile" label="手机号"></el-table-column>
          <el-table-column prop="order_num" label="订单数"></el-table-column>
          <el-table-column prop="order_price" label="订单实付金额(元)">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.order_price) }}
            </template>
          </el-table-column>
          <el-table-column prop="order_price" label="提成金额(元)">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.order_price) }}
            </template>
          </el-table-column>
        </el-table>
        <FooterPage
          :page-size="pageSize"
          :total-page.sync="total"
          :current-page.sync="page"
          @pageChange="pageChange"
          @sizeChange="sizeChange"
        ></FooterPage>
      </div>
    </div>
  </div>
</template>

<script>
import { overView } from "@/api/Cashier";
import { getAllStaffStaff } from "@/api/Department";
export default {
  name: "Commission",
  data() {
    return {
      add_form: {
        pickerOptions1: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
        },
      },
      time: [],
      startTime: "",
      endTime: "",
      total: 0,
      page: 1,
      pageSize: 10,
      value: 1,
      cashier_name: "",
      tableData: [],
      deleteStatus: 5,
      keyword: "",
      signId: 1,
      shop_cashier: [],
    };
  },
  created() {
    this.overView();
    this.getAllStaffStaff();
  },
  activated() {
    if (this.$_isInit()) return;
    this.overView();
    this.getAllStaffStaff();
  },
  methods: {
    pageChange(val) {
      this.page = val;
      this.overView();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    orderTimeChange(val) {
      if (val && val.length) {
        this.startTime = val[0] / 1000;
        this.endTime = val[1] / 1000 + 86399;
      } else {
        this.startTime = "";
        this.endTime = "";
      }
      this.pageChange(1);
    },
    staffChange(val) {
      this.getAllStaffStaff();
      this.pageChange(1);
    },
    async overView() {
      const data = await overView({
        page: this.page,
        pageSize: this.pageSize,
        startTime: this.startTime,
        endTime: this.endTime,
        userCenterId: this.cashier_name,
        type: this.value,
      });

      this.add_form = data.data.statistics;
      this.tableData = data.data.list;
      this.total = data.pageTotal;
    },
    async getAllStaffStaff() {
      const data = await getAllStaffStaff({
        page: this.page,
        pageSize: this.pageSize,
        deleteStatus: this.deleteStatus,
        keyword: this.keyword,
        signId: this.value,
      });

      this.shop_cashier = data.data;
    },
  },
};
</script>

<style scoped>
.commission {
  background-color: #f7f7f7;
  height: calc(100vh - 70px);
  overflow: auto;
}
.num {
  font-size: 34px;
}
.price {
  color: #999999;
  font-size: 12px;
}
.grid-content {
  border-radius: 4px;
}
.left-icon {
  width: 60px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  background: linear-gradient(-125deg, #8de4e9, #41cdd6);
  color: #ffffff;
  border-radius: 6px;
  margin-right: 16px;
}
.left-icon .iconfont {
  font-size: 34px;
  opacity: 0.9;
}
.model-view {
  margin-bottom: 20px;
  background-color: #ffffff;
}
.model-tit {
  padding: 0 20px;
  line-height: 50px;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #f2f2f2;
}
.model-main {
  padding: 20px;
}
.s-item {
  display: inline-block;
  margin-left: 20px;
}
.s-label {
  font-weight: 400;
  color: #666666;
}
.top-data-li {
  flex: 4;
}
</style>
