<template>
  <ContainerTit>
    <div slot="headr">
      <el-button @click="editFn">返回</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
    <div class="page-div">
      <el-form ref="add_form" :model="add_form" label-width="200px" :rules="rules" size="small" style="width: 40%">
        <el-form-item label="账号（手机号）" prop="phone">
          <el-input v-model="add_form.mobile" placeholder="请输入员工账号" :disabled="true"></el-input>
        </el-form-item>
        <!--      <el-form-item-->
        <!--        label="绑定微信账号："-->
        <!--      >-->
        <!--        <span-->
        <!--          class="symbol"-->
        <!--          @click="symbolFn"-->
        <!--        >+</span>-->
        <!--        <p style="color: #999;font-size: 14px">-->
        <!--          用于接收商城微信通知-->
        <!--        </p>-->
        <!--      </el-form-item>-->
        <el-form-item label="员工姓名：">
          <el-input v-model="add_form.staffName"></el-input>
        </el-form-item>
        <!--<el-form-item
        label="单独设置提成"
      >
        <el-radio-group v-model="add_form.isSetRule">
          <el-radio :label="5">
            启用
          </el-radio>
          <el-radio :label="4">
            不启用
          </el-radio>
        </el-radio-group>
      </el-form-item>-->
        <el-form-item v-if="add_form.isSetRule === 5" label="提成规则">
          <el-radio-group v-model="add_form.rule.push_money_type">
            <el-radio :label="5">按金额</el-radio>
            <el-radio :label="4">按订单</el-radio>
          </el-radio-group>
          <br />
          <el-input
            v-if="add_form.rule.push_money_type === 5"
            v-model="add_form.rule.push_money_rate"
            placeholder="请输入内容"
            style="width: 240px"
          >
            <template slot="prepend">提成比例</template>
            <template slot="append">%</template>
          </el-input>
          <el-input v-else v-model="add_form.rule.push_money_rate" placeholder="请输入内容" style="width: 240px">
            <template slot="prepend">1单提成</template>
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <div class="dialog_body">
        <el-dialog
          :close-on-press-escape="false"
          :close-on-click-modal="false"
          title="会员"
          :visible.sync="dialogVisible"
          width="50%"
          :before-close="handleClose"
          class="el-dialog__body"
        >
          <div class="all_vip">
            <div class="all">
              <p>全部</p>
            </div>
            <div class="tips">
              <p>提示：使用接收消息的微信账号登录公众号商城，查看个人中心的会员名称，在此搜索，选择确认，关联成功</p>
              <el-input
                v-model="vip_all"
                placeholder="请输入会员名称"
                prefix-icon="el-icon-search"
                style="width: 40%"
                size="small"
              ></el-input>
              <FooterPage
                :page-size="pageSize"
                :total-page.sync="total"
                :current-page.sync="page"
                @pageChange="pageChange"
                @sizeChange="sizeChange"
              ></FooterPage>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogVisible = false"> 确 定 </el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </ContainerTit>
</template>

<script>
import { updateStaffStaff, getStaff } from "@/api/Department";
export default {
  name: "EditCashier",
  data() {
    const validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (this.ruleForm2.checkPass !== "") {
          this.$refs.ruleForm2.validateField("checkPass");
        }
        callback();
      }
    };
    const validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm2.pass) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      dialogVisible: false,
      total: 0,
      page: 1,
      pageSize: 10,
      vip_all: "",
      target_id: "",
      add_form: {
        staffCode: "",
        pass: "",
        checkPass: "",
        staffName: "操作员（超级管理员）",
        mobile: 18888888888,
        isSetRule: 5,
        rule: {
          push_money_type: 4,
          push_money_rate: 10,
        },
      },
      rules: {
        phone: [{ required: true, message: "请输入员工账号", trigger: "blur" }],
        name: [{ required: true, message: "请输入员工姓名", trigger: "blur" }],
        mobile: [{ required: true, message: "请输入联系方式", trigger: "blur" }],
        pass: [{ validator: validatePass, trigger: "blur" }],
      },
    };
  },
  mounted() {
    this.target_id = this.$route.params.id;
    this.getStaff();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getStaff();
  },
  methods: {
    editFn() {
      this.$router.push("/Cashier/Cashier");
    },
    symbolFn() {
      this.dialogVisible = true;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    pageChange(val) {
      this.page = val;
      // this.getUserList()
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    async getStaff() {
      const data = await getStaff(this.target_id);

      this.add_form = data.data;
      if (!data.data.rule.push_money_rate) {
        this.add_form.rule.push_money_rate = 10;
      }
      if (!data.data.rule.push_money_type) {
        this.add_form.rule.push_money_type = 4;
      }
    },
    async submit() {
      const data = await updateStaffStaff(this.target_id, {
        departmentId: this.add_form.departmentId,
        dataField: this.add_form.dataField,
        staffName: this.add_form.staffName,
        roleId: this.add_form.roleId,
        mobile: this.add_form.mobile,
        sex: this.add_form.sex,
        age: this.add_form.age,
        education: this.add_form.education,
        email: this.add_form.email,
        isSetRule: this.add_form.isSetRule,
        rule: {
          push_money_rate: this.add_form.rule.push_money_rate,
          push_money_type: this.add_form.rule.push_money_type,
        },
      });

      this.$message.success("保存成功");
      this.getStaff();
    },
  },
};
</script>
<style>
.dialog_body .el-dialog__body {
  padding: 0;
}
</style>
<style scoped>
.symbol {
  width: 40px;
  height: 40px;
  border: 1px dotted #dedede;
  text-align: center;
  line-height: 38px;
  border-radius: 20px;
  cursor: pointer;
  display: block;
  font-size: 32px;
  color: #dedede;
}
.all_vip {
  border-top: 1px solid #dedede;
  border-bottom: 1px solid #dedede;
  width: 100%;
  height: 500px;
  overflow: hidden;
  overflow-y: scroll;
  height: 400px;
}
.all {
  width: 20%;
  height: 500px;
  border-right: 1px solid #dedede;
  padding: 20px 0 0 10px;
  float: left;
}
.all p {
  color: #fb6638;
  background-color: #fff3ef;
  height: 32px;
  line-height: 32px;
  width: 128px;
  padding: 0 8px;
  border-radius: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
.el-dialog__body {
  padding: 0;
}
.tips {
  width: 70%;
  height: 500px;
  float: left;
  padding: 20px 0 0 20px;
}
.tips p {
  border: 1px solid #d8e8fc;
  background-color: #e5f0fe;
  padding: 8px 16px;
  border-radius: 6px;
  color: #666;
  font-size: 12px;
  line-height: 22px;
  margin-bottom: 10px;
}
</style>
