<template>
  <Container>
    <div slot="left">
      <!--      <el-button-->
      <!--        size="mini"-->
      <!--        type="primary"-->
      <!--        @click="addGuide"-->
      <!--        style="margin-bottom: 10px"-->
      <!--      >-->
      <!--        新增导购-->
      <!--      </el-button>-->
      <el-form slot="right" :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="keyword"
            clearable
            style="width: 220px"
            placeholder="手机号/姓名"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData">
      <el-table-column prop="staffName" label="姓名"></el-table-column>
      <el-table-column prop="mobile" label="手机号"></el-table-column>
      <el-table-column prop="address" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
          <span v-else class="info-status">禁用</span>
        </template>
      </el-table-column>
      <el-table-column v-if="$accessCheck($Access.ShoppingGuideupdateStaff)" prop="address" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="editFn(scope.row.id)">编辑</el-button>
          <!--            <el-button type="text">禁用</el-button>-->
          <!--            <el-button type="text">删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="编辑导购员"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="add_form" :model="add_form" label-width="200px" size="small" style="width: 80%">
        <el-form-item label="导购员姓名：" prop="name">
          <el-input v-model="add_form.staffName"></el-input>
        </el-form-item>
        <el-form-item label="联系方式：" prop="mobile">
          <el-input v-model="add_form.mobile"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="add_form.isSetRule">
            <el-radio :label="5">启用</el-radio>
            <el-radio :label="4">不启用</el-radio>
          </el-radio-group>
        </el-form-item>
        <!--        <el-form-item-->
        <!--          label="单独设置提成"-->
        <!--        >-->
        <!--          <el-radio-group v-model="add_form.isSetRule">-->
        <!--            <el-radio :label="5">-->
        <!--              启用-->
        <!--            </el-radio>-->
        <!--            <el-radio :label="4">-->
        <!--              不启用-->
        <!--            </el-radio>-->
        <!--          </el-radio-group>-->
        <!--        </el-form-item>-->
        <el-form-item v-if="add_form.isSetRule === 5" label="提成设置">
          <el-radio-group v-model="add_form.rule.push_money_type">
            <el-radio :label="5">按金额</el-radio>
            <el-radio :label="4">按订单</el-radio>
          </el-radio-group>
          <br />
          <el-input
            v-if="add_form.rule.push_money_type === 5"
            v-model="add_form.rule.push_money_rate"
            placeholder="请输入内容"
            style="width: 240px"
          >
            <template slot="prepend">提成比例</template>
            <template slot="append">%</template>
          </el-input>
          <el-input v-else v-model="add_form.rule.push_money_rate" placeholder="请输入内容" style="width: 240px">
            <template slot="prepend">1单提成</template>
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="primary">保存</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="创建导购员"
      :visible.sync="guide"
      width="50%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="200px" size="small" style="width: 80%">
        <el-form-item label="导购员姓名：" prop="guide_name">
          <el-input v-model="ruleForm.guide_name" palaceholder="请输入导购员姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系方式:" prop="phone">
          <el-input v-model="ruleForm.phone" palaceholder="请输入导购员联系方式"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="ruleForm.state">
            <el-radio :label="5">启用</el-radio>
            <el-radio :label="4">不启用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="单独设置提成">
          <el-radio-group v-model="ruleForm.com">
            <el-radio :label="5">启用</el-radio>
            <el-radio :label="4">不启用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="guide = false">取 消</el-button>
        <el-button type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { updateStaffStaff, getStaff, getAllStaffStaff } from "@/api/Department";
export default {
  name: "ShoppingGuide",
  data() {
    return {
      guide: false,
      dialogVisible: false,
      tableData: [],
      add_form: {
        staffName: "小w",
        mobile: 18888888888,
        status: 5,
        isSetRule: 5,
        rule: {
          push_money_type: 4,
          push_money_rate: 10,
        },
      },
      ruleForm: {
        guide_name: "",
        phone: "",
        state: 4,
        com: 5,
      },
      rules: {
        guide_name: [{ required: true, message: "请输入导购员姓名", trigger: "blur" }],
        phone: [
          {
            required: true,
            message: "请输入导购员联系方式",
            trigger: "blur",
          },
        ],
      },
      total: 0,
      page: 1,
      pageSize: 10,
      signId: 2,
      deleteStatus: 5,
      keyword: "",
      target_id: "",
    };
  },
  created() {
    this.getAllStaffStaff();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllStaffStaff();
  },
  methods: {
    addGuide() {
      this.guide = true;
    },
    pageChange(val) {
      this.page = val;
      this.getAllStaffStaff();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 导购员详情
    async editFn(id) {
      this.dialogVisible = true;
      this.target_id = id;

      const data = await getStaff(id);

      this.add_form = data.data;
      if (!data.data.rule.push_money_rate) {
        this.add_form.rule.push_money_rate = 10;
      }
      if (!data.data.rule.push_money_type) {
        this.add_form.rule.push_money_type = 4;
      }
    },
    // 修改导购员
    async primary() {
      const data = await updateStaffStaff(this.target_id, {
        departmentId: this.add_form.departmentId,
        dataField: this.add_form.dataField,
        staffName: this.add_form.staffName,
        roleId: this.add_form.roleId,
        mobile: this.add_form.mobile,
        sex: this.add_form.sex,
        age: this.add_form.age,
        education: this.add_form.education,
        email: this.add_form.email,
        isSetRule: this.add_form.isSetRule,
        rule: {
          push_money_rate: this.add_form.rule.push_money_rate,
          push_money_type: this.add_form.rule.push_money_type,
        },
      });

      this.$message.success("保存成功");
      this.dialogVisible = false;
    },
    // 导购员列表
    async getAllStaffStaff() {
      const data = await getAllStaffStaff({
        page: this.page,
        pageSize: this.pageSize,
        deleteStatus: this.deleteStatus,
        keyword: this.keyword,
        signId: this.signId,
      });

      this.tableData = data.data;
      this.total = data.pageTotal;
    },
  },
};
</script>

<style scoped></style>
