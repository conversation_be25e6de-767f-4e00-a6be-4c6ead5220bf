<template>
  <div class="page-div">
    <el-tabs v-model="activeName2" type="card" @tab-click="handleClick">
      <el-tab-pane label="收银员" name="first">
        <el-form :model="add_form" size="small" :inline="true">
          <el-form-item>
            <div class="block">
              <el-date-picker
                v-model="add_form.time"
                type="daterange"
                range-separator="-"
                start-placeholder="交易开始时间"
                end-placeholder="交易结束时间"
                @change="changeTime"
              ></el-date-picker>
            </div>
          </el-form-item>
          <el-form-item>
            <el-select v-model="add_form.cashier" placeholder="请选择收银员" @change="pageChange(1)">
              <el-option
                v-for="item in shop_cashier"
                :key="item.id"
                :label="item.staffName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <!--          <el-button size="small" type="primary">导出</el-button>-->
        </el-form>
        <el-table :data="tableData">
          <el-table-column prop="createTime" label="交易时间">
            <template slot-scope="scope">
              {{ $_common.formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="staffName" label="姓名" width="180"></el-table-column>
          <el-table-column prop="orderNo" label="订单号"></el-table-column>
          <el-table-column prop="orderMoney" label="订单金额">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.orderMoney) }}
            </template>
          </el-table-column>
          <el-table-column prop="pushMoney" label="提成">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.pushMoney) }}
            </template>
          </el-table-column>
          <el-table-column prop="pushMoneyRate" label="提成金额">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.pushMoneyRate) }}
            </template>
          </el-table-column>
        </el-table>
        <FooterPage
          :page-size="pageSize"
          :total-page.sync="total"
          :current-page.sync="page"
          @pageChange="pageChange"
          @sizeChange="sizeChange"
        ></FooterPage>
      </el-tab-pane>
      <el-tab-pane label="导购员" name="second">
        <el-form :model="add_form" size="small" :inline="true">
          <el-form-item>
            <div class="block">
              <el-date-picker
                v-model="add_form.time"
                type="daterange"
                range-separator="-"
                start-placeholder="交易开始时间"
                end-placeholder="交易结束时间"
                @change="changeTime"
              ></el-date-picker>
            </div>
          </el-form-item>
          <el-form-item>
            <el-select v-model="add_form.cashier" placeholder="请选择导购员" @change="pageChange(1)">
              <el-option v-for="item in shop_guide" :key="item.id" :label="item.staffName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <!--          <el-button size="small" type="primary">导出</el-button>-->
        </el-form>
        <el-table :data="tableData">
          <el-table-column prop="createTime" label="交易时间">
            <template slot-scope="scope">
              {{ $_common.formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="staffName" label="姓名" width="180"></el-table-column>
          <el-table-column prop="orderNo" label="订单号"></el-table-column>
          <el-table-column prop="orderMoney" label="订单金额">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.orderMoney) }}
            </template>
          </el-table-column>
          <el-table-column prop="pushMoney" label="提成">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.pushMoney) }}
            </template>
          </el-table-column>
        </el-table>
        <FooterPage
          :page-size="pageSize"
          :total-page.sync="total"
          :current-page.sync="page"
          @pageChange="pageChange"
          @sizeChange="sizeChange"
        ></FooterPage>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getAllPushMoney } from "@/api/Cashier";
import { getAllStaffStaff } from "@/api/Department";
export default {
  name: "Withdrawal",
  data() {
    return {
      activeName2: "first",
      tableData: [],
      tableData_one: [],
      add_form: {
        time: "",
        cashier: "",
        start: "",
        end: "",
      },
      add_form_one: {
        time: "",
        value: "",
      },
      total: 0,
      page: 1,
      pageSize: 10,
      pushType: 1,
      signId: 1,
      deleteStatus: 5,
      keyword: "",
      shop_guide: [], // 导购员
      shop_cashier: [], // 收银员
    };
  },
  created() {
    this.getAllPushMoney();
    this.getAllStaffStaff();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllPushMoney();
    this.getAllStaffStaff();
  },
  methods: {
    handleClick(tab, event) {
      this.add_form.cashier = "";
      if (this.activeName2 === "first") {
        this.pushType = "1";
        this.signId = "1";
      } else {
        this.pushType = "2";
        this.signId = "2";
        this.getAllStaffStaff();
      }
      this.pageChange(1);
    },
    pageChange(val) {
      this.page = val;
      this.getAllPushMoney();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    async getAllPushMoney() {
      const data = await getAllPushMoney({
        page: this.page,
        pageSize: this.pageSize,
        staffId: this.add_form.cashier,
        pushType: this.pushType,
        startTime: this.add_form.start,
        endTime: this.add_form.end,
        userCenterId: this.add_form.cashier,
      });

      this.tableData = data.data;
      this.total = data.pageTotal;
    },
    // 收银员,导购员列表
    async getAllStaffStaff() {
      const data = await getAllStaffStaff({
        page: 1,
        pageSize: 999,
        deleteStatus: this.deleteStatus,
        keyword: this.keyword,
        signId: this.signId,
      });

      if (data.data[0].signId === 1) {
        this.shop_cashier = data.data;
      } else {
        this.shop_guide = data.data;
      }
    },
    //  订单时间
    changeTime(val) {
      if (val && val.length) {
        this.add_form.start = val[0] / 1000;
        this.add_form.end = val[1] / 1000 + 86399;
      } else {
        this.add_form.start = "";
        this.add_form.end = "";
      }
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
