<template>
  <Container>
    <div slot="tip" class="page-tip-div" style="margin-top: 0">
      <i class="el-icon-info"></i>
      <span>温馨提示：收银员需要到设置-员工列表处，选择员工权限。</span>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="staffName" label="姓名" width="180"></el-table-column>
      <el-table-column prop="mobile" label="手机号" width="180"></el-table-column>
      <el-table-column prop="staffCode" label="账号"></el-table-column>
      <el-table-column prop="roleName" label="角色"></el-table-column>
      <el-table-column prop="createTime" label="创建时间">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="departmentName" label="添加人"></el-table-column>
      <el-table-column prop="address" label="状态"></el-table-column>
      <el-table-column v-if="$accessCheck($Access.CashiersupdateStaff)" prop="address" label="操作">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="editFn(scope.row.id)"> 编辑 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { getAllStaffStaff } from "@/api/Department";
export default {
  name: "Cashier",
  data() {
    return {
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      signId: 1,
      deleteStatus: 5,
      keyword: "",
    };
  },
  created() {
    this.getAllStaffStaff();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllStaffStaff();
  },
  methods: {
    pageChange(val) {
      this.page = val;
      this.getAllStaffStaff();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    editFn(id) {
      this.$router.push("/Cashier/EditCashier/" + id);
    },
    async getAllStaffStaff() {
      const data = await getAllStaffStaff({
        page: this.page,
        pageSize: this.pageSize,
        deleteStatus: this.deleteStatus,
        keyword: this.keyword,
        signId: this.signId,
      });

      this.tableData = data.data;
      this.total = data.pageTotal;
    },
  },
};
</script>

<style scoped></style>
