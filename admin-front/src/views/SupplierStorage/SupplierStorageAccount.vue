<template>
  <div class="supplier-storage-account">
    <div class="page-header">
      <h2>账户管理</h2>
      <p>修改您的登录密码</p>
    </div>

    <div class="account-container">
      <el-card class="password-card">
        <div slot="header" class="card-header">
          <span>修改密码</span>
        </div>

        <el-form
          ref="passwordForm"
          :model="passwordForm"
          :rules="passwordRules"
          label-width="120px"
          class="password-form"
        >
          <el-form-item label="原密码" prop="oldPassword">
            <el-input
              v-model="passwordForm.oldPassword"
              type="password"
              placeholder="请输入原密码"
              show-password
              autocomplete="off"
            />
          </el-form-item>

          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
              autocomplete="off"
            />
            <div class="password-tips">
              <p>密码要求：</p>
              <ul>
                <li>长度8-20位</li>
                <li>包含大小写字母、数字</li>
                <li>可包含特殊字符</li>
              </ul>
            </div>
          </el-form-item>

          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
              autocomplete="off"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              @click="handleSubmit"
              :loading="loading"
              size="medium"
            >
              修改密码
            </el-button>
            <el-button @click="handleReset" size="medium">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="info-card">
        <div slot="header" class="card-header">
          <span>安全提示</span>
        </div>

        <div class="security-tips">
          <div class="tip-item">
            <i class="el-icon-warning-outline"></i>
            <span>为了您的账户安全，请定期修改密码</span>
          </div>
          <div class="tip-item">
            <i class="el-icon-info"></i>
            <span>修改密码后需要重新登录</span>
          </div>
          <div class="tip-item">
            <i class="el-icon-lock"></i>
            <span>请勿在公共场所或他人面前输入密码</span>
          </div>
          <div class="tip-item">
            <i class="el-icon-shield"></i>
            <span>如发现账户异常，请及时联系管理员</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { changeSupplierPassword } from '@/api/SupplierStorage'

export default {
  name: 'SupplierStorageAccount',
  data() {
    const validatePassword = (_rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入新密码'))
      } else if (value.length < 8 || value.length > 20) {
        callback(new Error('密码长度应为8-20位'))
      } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
        callback(new Error('密码必须包含大小写字母和数字'))
      } else {
        if (this.passwordForm.confirmPassword !== '') {
          this.$refs.passwordForm.validateField('confirmPassword')
        }
        callback()
      }
    }

    const validateConfirmPassword = (_rule, value, callback) => {
      if (!value) {
        callback(new Error('请再次输入新密码'))
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPassword: [
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            const response = await changeSupplierPassword({
              oldPassword: this.passwordForm.oldPassword,
              newPassword: this.passwordForm.newPassword
            })

            if (response.errorcode === 0) {
              this.$message.success('密码修改成功，请重新登录')
              // 清空表单
              this.handleReset()
              // 延迟跳转到登录页
              setTimeout(() => {
                this.$router.push('/login')
              }, 2000)
            } else {
              this.$message.error(response.message || '密码修改失败')
            }
          } catch (error) {
            console.error('密码修改失败:', error)
            this.$message.error('密码修改失败')
          } finally {
            this.loading = false
          }
        }
      })
    },
    handleReset() {
      this.$refs.passwordForm.resetFields()
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    }
  }
}
</script>

<style scoped>
.supplier-storage-account {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.account-container {
  display: flex;
  gap: 30px;
  max-width: 1200px;
}

.password-card {
  flex: 1;
  max-width: 600px;
}

.info-card {
  flex: 0 0 350px;
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.password-form {
  max-width: 500px;
}

.password-tips {
  margin-top: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.password-tips p {
  margin: 0 0 8px 0;
  font-weight: 600;
}

.password-tips ul {
  margin: 0;
  padding-left: 16px;
}

.password-tips li {
  margin-bottom: 4px;
}

.security-tips {
  padding: 10px 0;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  color: #606266;
}

.tip-item i {
  margin-right: 12px;
  font-size: 16px;
  color: #909399;
}

.tip-item:last-child {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .account-container {
    flex-direction: column;
  }

  .info-card {
    flex: none;
  }
}
</style>
