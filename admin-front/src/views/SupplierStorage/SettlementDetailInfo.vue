<!--供应商分账明细详情组件-->
<template>
  <div class="settlement-detail-info">
    <el-card shadow="hover">
      <div slot="header">
        <span>分账明细详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>

      <div v-loading="loading">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="3" border>
          <el-descriptions-item label="分账明细ID">{{ settlementDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ settlementDetail.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="出库单号">{{ settlementDetail.outNo }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ settlementDetail.materielName }}</el-descriptions-item>
          <el-descriptions-item label="商品编码">{{ settlementDetail.materielCode }}</el-descriptions-item>
          <el-descriptions-item label="规格">{{ settlementDetail.skuName }}</el-descriptions-item>
          <el-descriptions-item label="单位">{{ settlementDetail.unitName }}</el-descriptions-item>
          <el-descriptions-item label="数量">{{ $_common.formatNub(settlementDetail.num) }}</el-descriptions-item>
          <el-descriptions-item label="单价">{{ $_common.formatNub(settlementDetail.unitPrice) }}</el-descriptions-item>
          <el-descriptions-item label="总价">{{ $_common.formatNub(settlementDetail.totalPrice) }}</el-descriptions-item>
          <el-descriptions-item label="仓库">{{ settlementDetail.warehouseName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="供应商">{{ settlementDetail.supplierName }}</el-descriptions-item>
        </el-descriptions>

        <!-- 分账信息 -->
        <el-descriptions title="分账信息" :column="3" border style="margin-top: 20px">
          <el-descriptions-item label="分账金额">{{ $_common.formatNub(settlementDetail.settlementAmount) }}</el-descriptions-item>
          <el-descriptions-item label="分账类型">固定金额</el-descriptions-item>
          <el-descriptions-item label="分账比例">{{ $_common.formatNub(settlementDetail.settlementRate) }}%</el-descriptions-item>
          <el-descriptions-item label="结算状态">
            <el-tag :type="getSettlementStatusType(settlementDetail.settlementStatus)">
              {{ getSettlementStatusText(settlementDetail.settlementStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="结算单号">{{ settlementDetail.settlementNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="结算时间">{{ settlementDetail.settlementTime ? formatTime(settlementDetail.settlementTime) : '-' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 时间信息 -->
        <el-descriptions title="时间信息" :column="2" border style="margin-top: 20px">
          <el-descriptions-item label="创建时间">{{ formatTime(settlementDetail.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatTime(settlementDetail.updateTime) }}</el-descriptions-item>
        </el-descriptions>

        <!-- 规则快照信息 -->
        <div v-if="settlementDetail.ruleSnapshot" style="margin-top: 20px" ref="ruleSnapshotSection">
          <el-descriptions title="规则快照信息" :column="1" border>
            <el-descriptions-item label="规则名称">{{ ruleSnapshotData.ruleName || "-" }}</el-descriptions-item>
            <el-descriptions-item label="规则类型">固定金额</el-descriptions-item>
            <el-descriptions-item label="快照时间">{{ formatTime(ruleSnapshotData.snapshotTime) }}</el-descriptions-item>
          </el-descriptions>

          <!-- SKU固定金额表格 -->
          <div v-if="ruleSnapshotData.ruleContent && ruleSnapshotData.ruleContent.skuFixedAmounts">
            <h4 style="margin: 20px 0 10px 0; color: #303133">SKU固定金额配置</h4>
            <div v-if="ruleSnapshotData.ruleContent.skuFixedAmounts.length > 10" style="margin-bottom: 10px">
              <el-button type="text" style="color: #409eff" @click="toggleSkuTableExpanded">
                {{ skuTableExpanded ? "收起" : "展开全部" }}（共{{
                  ruleSnapshotData.ruleContent.skuFixedAmounts.length
                }}条）
              </el-button>
            </div>
            <el-table :data="displaySkuData" border stripe style="width: 100%" :max-height="skuTableExpanded ? 600 : 400">
              <el-table-column prop="goodsCode" label="商品编码" width="140" show-overflow-tooltip></el-table-column>
              <el-table-column prop="goodsName" label="商品名称" min-width="150" show-overflow-tooltip></el-table-column>
              <el-table-column prop="specGroup" label="规格组详情" width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span v-if="scope.row.specGroup">{{ formatSpecGroup(scope.row.specGroup) }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="unitName" label="单位名称" width="100" align="center"></el-table-column>
              <el-table-column prop="fixedAmount" label="固定金额" width="120" align="right">
                <template slot-scope="scope">
                  {{ $_common.formatNub(scope.row.fixedAmount) }}
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 其他规则内容 -->
          <div v-else-if="ruleSnapshotData.ruleContent">
            <h4 style="margin: 20px 0 10px 0; color: #303133">规则内容</h4>
            <pre class="rule-content-pre">{{ JSON.stringify(ruleSnapshotData.ruleContent, null, 2) }}</pre>
          </div>
        </div>

        <!-- 无规则快照提示 -->
        <div v-else style="margin-top: 20px">
          <el-descriptions title="规则快照信息" :column="1" border>
            <el-descriptions-item label="规则快照">
              <span style="color: #909399">暂无规则快照数据</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getSupplierSettlementDetailInfo } from "@/api/SupplierConsignment";

export default {
  name: "SettlementDetailInfo",

  data() {
    return {
      // 加载状态
      loading: false,
      // 分账明细ID
      settlementId: null,
      // 分账明细数据
      settlementDetail: {},
      // 规则快照数据
      ruleSnapshotData: {},
      // SKU表格展开状态
      skuTableExpanded: false,
    };
  },

  computed: {
    // 显示的SKU数据（根据展开状态决定显示数量）
    displaySkuData() {
      if (
        !this.ruleSnapshotData ||
        !this.ruleSnapshotData.ruleContent ||
        !this.ruleSnapshotData.ruleContent.skuFixedAmounts
      ) {
        return [];
      }
      const skuData = this.ruleSnapshotData.ruleContent.skuFixedAmounts;
      if (skuData.length <= 10 || this.skuTableExpanded) {
        return skuData;
      }
      return skuData.slice(0, 10);
    },
  },

  created() {
    this.settlementId = this.$route.query.id;
    if (this.settlementId) {
      this.getDetail();
    } else {
      this.$message.error("缺少分账明细ID参数");
      this.goBack();
    }
  },

  methods: {
    // 获取分账明细详情
    getDetail() {
      this.loading = true;
      getSupplierSettlementDetailInfo(this.settlementId)
        .then((response) => {
          if (response.errorcode === 0) {
            this.settlementDetail = response.data;
            
            // 解析规则快照数据
            if (this.settlementDetail.ruleSnapshot) {
              try {
                this.ruleSnapshotData = typeof this.settlementDetail.ruleSnapshot === 'string'
                  ? JSON.parse(this.settlementDetail.ruleSnapshot)
                  : this.settlementDetail.ruleSnapshot;
              } catch (e) {
                console.error("解析规则快照数据失败:", e);
                this.ruleSnapshotData = {};
              }
            }

            // 如果URL参数中包含tab=ruleSnapshot，自动滚动到规则快照部分
            if (this.$route.query.tab === 'ruleSnapshot') {
              this.$nextTick(() => {
                this.scrollToRuleSnapshot();
              });
            }
          } else {
            this.$message.error(response.data || "获取分账明细详情失败");
            this.goBack();
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$message.error("获取分账明细详情失败");
          this.goBack();
        });
    },

    // 返回列表页
    goBack() {
      this.$router.push({ path: "/SupplierStorage/SettlementDetailList" });
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return "-";
      const date = new Date(timestamp * 1000);
      return date.toLocaleString();
    },

    // 获取结算状态文本
    getSettlementStatusText(status) {
      const statusMap = {
        1: "待结算",
        2: "结算中",
        3: "已结算",
        4: "结算失败",
      };
      return statusMap[status] || "未知状态";
    },

    // 获取结算状态类型
    getSettlementStatusType(status) {
      const typeMap = {
        1: "info",
        2: "warning",
        3: "success",
        4: "danger",
      };
      return typeMap[status] || "";
    },

    // 切换SKU表格展开状态
    toggleSkuTableExpanded() {
      this.skuTableExpanded = !this.skuTableExpanded;
    },

    // 格式化规格组信息
    formatSpecGroup(specGroup) {
      if (!specGroup) return "-";
      if (typeof specGroup === 'string') {
        try {
          specGroup = JSON.parse(specGroup);
        } catch (e) {
          return specGroup;
        }
      }
      if (Array.isArray(specGroup)) {
        return specGroup.map(item => `${item.specName}: ${item.specValue}`).join(', ');
      }
      return JSON.stringify(specGroup);
    },

    // 滚动到规则快照部分
    scrollToRuleSnapshot() {
      if (this.$refs.ruleSnapshotSection) {
        this.$refs.ruleSnapshotSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
        // 添加高亮效果
        this.$refs.ruleSnapshotSection.style.backgroundColor = '#f0f9ff';
        setTimeout(() => {
          if (this.$refs.ruleSnapshotSection) {
            this.$refs.ruleSnapshotSection.style.backgroundColor = '';
          }
        }, 2000);
      }
    },
  },
};
</script>

<style scoped>
.settlement-detail-info {
  padding: 20px;
}

.el-descriptions {
  margin-bottom: 20px;
}

.rule-content-pre {
  background-color: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  font-size: 12px;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
