<template>
  <div class="supplier-storage-overview">
    <div class="page-header">
      <h2>供应商概览</h2>
      <p>查看您的基础仓储统计信息</p>
    </div>

    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-goods"></i>
            </div>
            <div class="stat-content">
              <h3>{{ overviewData.statistics?.totalInventory || 0 }}</h3>
              <p>当前库存总量</p>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-upload"></i>
            </div>
            <div class="stat-content">
              <h3>{{ overviewData.statistics?.monthlyInbound || 0 }}</h3>
              <p>本月入库数量</p>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-download"></i>
            </div>
            <div class="stat-content">
              <h3>{{ overviewData.statistics?.monthlyOutbound || 0 }}</h3>
              <p>本月出库数量</p>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-money"></i>
            </div>
            <div class="stat-content">
              <h3>¥{{ overviewData.statistics?.pendingSettlement || 0 }}</h3>
              <p>待结算金额</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="refresh-section">
      <el-button type="primary" @click="refreshData" :loading="loading">
        <i class="el-icon-refresh"></i>
        刷新数据
      </el-button>
      <span class="last-update">最后更新：{{ lastUpdateTime }}</span>
    </div>
  </div>
</template>

<script>
import { getSupplierOverviewData } from '@/api/SupplierStorage'

export default {
  name: 'SupplierStorageOverview',
  data() {
    return {
      loading: false,
      overviewData: {
        supplierInfo: {},
        consignmentStatus: {},
        depositAccount: {},
        statistics: {
          totalInventory: 0,
          monthlyInbound: 0,
          monthlyOutbound: 0,
          pendingSettlement: 0
        }
      },
      lastUpdateTime: ''
    }
  },
  mounted() {
    this.loadOverviewData()
  },
  methods: {
    async loadOverviewData() {
      this.loading = true
      try {
        const response = await getSupplierOverviewData()
        if (response.errorcode === 0) {
          this.overviewData = response.data
          this.lastUpdateTime = new Date().toLocaleString()
        } else {
          this.$message.error(response.message || '获取概览数据失败')
        }
      } catch (error) {
        console.error('获取概览数据失败:', error)
        this.$message.error('获取概览数据失败')
      } finally {
        this.loading = false
      }
    },
    refreshData() {
      this.loadOverviewData()
    }
  }
}
</script>

<style scoped>
.supplier-storage-overview {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.overview-cards {
  margin-bottom: 30px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-icon i {
  font-size: 24px;
  color: #fff;
}

.stat-content h3 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.stat-content p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.refresh-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.last-update {
  color: #909399;
  font-size: 14px;
}
</style>
