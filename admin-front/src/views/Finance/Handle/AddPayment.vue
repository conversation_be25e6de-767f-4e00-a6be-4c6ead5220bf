<!--添加基础资料-->
<template>
  <ContainerTit class="AddPayment">
    <div class="page-tip-div" style="margin: 0; position: relative">
      温馨提示： 1、退款时在金额输入框输入负数即可！ 2、付款时，在金额输入框输入正数即可！
      3、商家预付货款时，在新增选择单据类型为采购预付后不用选择原单据号即可创建！
      <div class="btn-top-div">
        <el-button
          v-if="$accessCheck($Access.PaymentListGetTempPaidData)"
          :loading="loading"
          :disabled="isEdit || isLook"
          @click="temData(true)"
        >
          暂存
        </el-button>
        <el-button v-if="!isLook" type="primary" :loading="loading" @click="addData(false)"> 保存并提交 </el-button>
      </div>
    </div>
    <el-form ref="base_form" label-width="120px" size="small" :model="form" :rules="rules" :disabled="isLook">
      <el-tabs v-model="activeName">
        <el-tab-pane label="基础信息" name="one" style="position: relative">
          <el-row style="padding-bottom: 13px">
            <el-col :span="24">
              <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">付款信息</p>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="收款单位" prop="supplierName" style="min-width: 400px">
                <SelectSupplier v-model="form.supplierId" :clearable="false" width="180" @change="selUnitSupplier" />
                <el-button size="mini" type="text" @click="$router.push('/Purchase/ManageP/SupplierAdd')">
                  【新建供应商】
                </el-button>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="所属店铺" prop="shopName" style="min-width: 400px">
                <SelectShop
                  v-model="form.shopId"
                  width="180"
                  :clearable="false"
                  placeholder="选择商铺"
                  @change="selShop"
                />
                <el-button size="mini" type="text" @click="$router.push('/SystemSettings/liansuoguanli/AddShop')">
                  【新建商铺】
                </el-button>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="应付金额" prop="money">
                <el-input v-model="money" disabled style="width: 180px" placeholder="不可编辑"></el-input>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="付款人" prop="currentAccountName">
                <el-input v-model="form.currentAccountName" style="width: 180px" placeholder="当前账户"></el-input>
              </el-form-item>
            </el-col>

            <el-col class="form" :span="6">
              <el-form-item label="单据日期" prop="brandId">
                <template>
                  <div class="block">
                    <el-date-picker
                      v-model="form.receiptTime"
                      style="width: 180px"
                      type="date"
                      placeholder="选择日期"
                      value-format="timestamp"
                    ></el-date-picker>
                  </div>
                </template>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="12">
              <el-form-item label="业务类型" prop="financeTypeId" style="min-width: 450px">
                <el-select
                  v-model="form.financeTypeId"
                  style="width: 180px"
                  clearable
                  placeholder="选择类型"
                  @change="typeChange"
                >
                  <el-option
                    v-for="(item, index) in financeTypeList"
                    :key="index"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <el-button size="mini" type="text" @click="getAllFinanceType"> 【刷新】 </el-button>
                <el-button size="mini" type="text" @click="show_model = true"> 【新建类型】 </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      <div class="order_bottom">
        <p class="text">付款单明细</p>
        <el-table :data="form.accountList">
          <el-table-column prop="name" label="结算账户" min-width="160">
            <template slot-scope="scope">
              <el-input v-model="scope.row.accountName" readonly size="small" placeholder="结算账户">
                <i slot="suffix" class="el-input__icon el-icon-search" @click="openAccount(scope.$index)"></i>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="money" label="付款金额" min-width="120">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.money"
                :controls="false"
                placeholder="付款金额"
                size="small"
                style="width: 100%"
                @blur="moneyBlur"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="优惠金额" min-width="120">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.discountMoney"
                :controls="false"
                placeholder="优惠金额"
                size="small"
                style="width: 100%"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="实际付款金额" min-width="120" align="center">
            <template slot-scope="scope">
              {{ $NP.minus(scope.row.money || 0, scope.row.discountMoney || 0) }}
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="200">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark" size="small" placeholder="备注"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="操作" width="130px" align="center">
            <template slot-scope="scope">
              <el-button
                :disabled="form.accountList.length === 1"
                size="mini"
                type="text"
                @click="delAccount(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-b-div">
          <div class="table-b-div-cont" @click="addAccount">
            <el-button type="text" size="mini" @click="addAccount">
              <i class="el-icon-plus"></i>
              新增
            </el-button>
          </div>
        </div>
      </div>
      <div class="order_bottom">
        <p class="text">核销明细</p>
        <div class="Enunciate">
          <div class="Enunciate_cont clearfix">
            <div class="float_left">
              <span> 未核销金额：{{ $_common.formattedNumber(offSetNotTotal) }} </span>
              <span style="margin: 0 20px"> 本次核销金额：{{ $_common.formattedNumber(offSetTotal) }} </span>
              <span> 核销差额：{{ $_common.formattedNumber(offSetTotal - moneyTotal) }} </span>
            </div>
            <div class="float_right">
              <el-button size="mini" @click="offsetGet">自动核销</el-button>
            </div>
          </div>
        </div>
        <el-table ref="multipleTable" :data="tableData" @selection-change="selectionChange">
          <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
          <el-table-column prop="financeType" label="单据类型" min-width="100">
            <template slot-scope="scope">
              {{ scope.row.receiptTypeId === 2 ? "采购订单" : "采购退货单" }}
            </template>
          </el-table-column>
          <el-table-column prop="no" label="单据编号" min-width="140"></el-table-column>
          <el-table-column prop="address" label="单据日期" min-width="120">
            <template slot-scope="scope">
              {{ $_common.formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="address" label="金额" min-width="100">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.payMoney) }}
            </template>
          </el-table-column>
          <el-table-column prop="address" label="未核销金额" min-width="100">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.notOffsetMoney) }}
            </template>
          </el-table-column>
          <el-table-column prop="address" label="本次核销金额" min-width="100">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.offsetMoney"
                :controls="false"
                placeholder="本次核销金额"
                :max="Number(scope.row.notOffsetMoney)"
                style="width: 100%"
                size="small"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column v-if="form.financeTypeId != 7" prop="purchaseNo" label="来源单据" min-width="100">
            <template slot-scope="scope">
              <span class="click-div" @click="goDetail(scope.row)">
                {{ scope.row.purchaseNo }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="supplierName" label="往来单位名称" min-width="140"></el-table-column>
        </el-table>
      </div>
    </el-form>
    <AccountType
      v-if="account_show"
      :is-check="false"
      :is-show="account_show"
      :shop-id="form.shopId"
      @cancel="account_show = false"
      @confirm="accountsel"
    />
    <Handle
      v-if="handle_show"
      :is-check="false"
      :is-show="handle_show"
      :supplier="form.supplierId"
      @cancel="handle_show = false"
      @confirm="handlesel"
    />
    <!--    新增付款类型-->
    <el-dialog
      name="新增财务类型"
      :visible.sync="show_model"
      width="40%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="show_model = false"
    >
      <el-form ref="form" :model="add_form" :rules="add_rule" size="small" label-width="100px">
        <el-form-item label="类型" prop="name">
          <el-input v-model="add_form.name" placeholder="请输入类型名称"></el-input>
        </el-form-item>
        <el-form-item label="归属单据" prop="link">
          <el-select v-model="add_form.link" placeholder="请选择">
            <el-option v-for="item in form_type" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否默认" prop="defaultStatus">
          <el-switch v-model="add_form.isDefault" :active-value="5" :inactive-value="4"></el-switch>
        </el-form-item>
        <el-form-item label="是否禁用" prop="enableStatus">
          <el-radio-group v-model="add_form.enableStatus">
            <el-radio :label="4">是</el-radio>
            <el-radio :label="5">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="show_model = false">取 消</el-button>
        <el-button type="primary" size="small" @click="addType"> 确 定 </el-button>
      </span>
    </el-dialog>
  </ContainerTit>
</template>

<script>
import Handle from "@/component/Finance/Handle.vue";
import SelectSupplier from "@/component/common/SelectSupplier.vue";
import AccountType from "../AccountType";

import {
  addFinanceType,
  addPaid,
  editFinanceType,
  editPaid,
  getAllFinanceTypeNoPage,
  getAllPay,
  getPaidInfo,
  getPayInfo,
  getTempPaidData,
} from "@/api/Finance";
import { getSupplierInfoById } from "@/api/Purchase";
import { getAllPayment } from "@/api/System";
import SelectShop from "@/component/goods/SelectShop.vue";

export default {
  name: "AddPayment",
  components: {
    SelectSupplier,
    AccountType,
    Handle,
    SelectShop,
  },
  data() {
    const validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择收款单位"));
      } else {
        callback();
      }
    };
    const validateNo = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择原单据号"));
      } else {
        callback();
      }
    };
    const validateShop = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择所属商铺"));
      } else {
        callback();
      }
    };
    return {
      tableData: [],
      // 新增财务类型
      form_type: [
        {
          label: "应收单",
          value: 1,
        },
        {
          label: "应付单",
          value: 2,
        },
      ],
      show_model: false,
      add_form: {
        name: "",
        link: "",
        isDefault: 4,
        enableStatus: 5,
      },
      add_rule: {
        name: [{ required: true, message: "请输入类型名称" }],
        link: [{ required: true, message: "请选择归属单据" }],
      },
      loading: false,
      code: "",
      customerName: "",
      financeTypeList: [],
      pay_index: 0,
      pay_type_list: [],
      handle_show: false,
      show_shop: false, // 打开店铺选择弹窗
      account_show: false,
      money: "",
      no: "",
      form: {
        supplierId: "",
        supplierName: "",
        sourceNo: "",
        sourceNoMoney: "",
        currentAccountName: "",
        financeType: "",
        financeTypeId: "",
        shopId: "",
        shopName: "",
        receiptTime: "",
        createTime: "",
        tempSave: false,
        accountList: [
          {
            accountId: "",
            accountNumber: "",
            accountName: "",
            money: "",
            discountMoney: "",
            finalMoney: "",
            payWay: "",
            remark: "",
          },
        ],
      },
      rules: {
        supplierName: [{ required: true, validator: validateName }],
        sourceNo: [{ required: true, validator: validateNo }],
        currentAccountName: [{ required: true, message: "请输入付款人", trigger: "blur" }],
        // shopName: [{ required: true, message: "请选择商铺", trigger: "blur" }],
      },
      paid: "",
      createTime: "",
      isLook: false,
      isEdit: false,
      choose_data: [],
      check_money: 0, // 勾选后合计
      id: "",
      purchaseId: "",
      createTime1: "",
      activeName: "one",
    };
  },
  computed: {
    // 付款总金额
    moneyTotal() {
      if (!this.form.accountList.length) {
        return 0;
      } else if (this.form.accountList.length === 1) {
        return this.$NP.minus(Number(this.form.accountList[0].money), Number(this.form.accountList[0].discountMoney));
      } else {
        let sum = 0;
        this.form.accountList.forEach((item) => {
          const money = this.$NP.minus(Number(item.money), Number(item.discountMoney));
          sum = this.$NP.plus(sum, money);
        });
        return sum;
      }
    },
    // 核销总金额
    offSetTotal() {
      if (!this.choose_data.length) {
        return 0;
      } else if (this.choose_data.length === 1) {
        return Number(this.choose_data[0].offsetMoney);
      } else {
        let sum = 0;
        this.choose_data.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.offsetMoney) || 0);
        });
        return sum;
      }
    },
    // 未核销总金额
    offSetNotTotal() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return Number(this.tableData[0].notOffsetMoney);
      } else {
        let sum = 0;
        this.tableData.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.notOffsetMoney) || 0);
        });
        return sum;
      }
    },
  },
  created() {
    this.initial();
  },
  activated() {
    if (this.$_isInit()) return;
    this.initial();
  },
  methods: {
    // 初始化
    async initial() {
      this.isLook = this.$route.path.indexOf("LookPayment") > -1;
      this.isEdit = this.$route.path.indexOf("editPayment") > -1;
      // 收款人默认当前账户
      this.form.currentAccountName = this.userName;
      if (this.$route.params.createTime) {
        this.form.createTime = this.$route.params.createTime;
      }
      this.form.receiptTime = new Date().getTime();
      await this.getAllFinanceType();
      await this.getAllPayment();
      if (this.$route.params.id) {
        // 编辑页面
        this.paid = this.$route.params.id;
        // 付款单详情
        await this.getPaidInfo();
      } else if (this.$route.query.id) {
        // 应付单 进入 新增付款单
        this.id = this.$route.query.id;
        this.createTime1 = this.$route.query.createTime;
        if (this.$route.query.purchaseId !== "null") {
          this.purchaseId = this.$route.query.purchaseId;
        }
        // 应付详情
        await this.getPayInfo();
      } else {
        // 新增
        await this.getTempPaidData();
      }
      if (this.$route.query.supplierId) {
        await this.getSupplierInfoById(this.$route.query.supplierId);
      }
    },
    // 批量选择
    selectionChange(val) {
      this.choose_data = val;
      let num = 0;
      this.choose_data.forEach((item) => {
        num += Number(item.offsetMoney);
      });
      console.log("勾选余额", num);
      this.check_money = num;
    },
    // 获取应付单详情
    async getPayInfo(obj) {
      const { data } = await getPayInfo({
        id: this.id,
        purchaseId: this.purchaseId,
        createTime: this.createTime1,
      });
      this.form.supplierId = data.supplierId;
      this.form.supplierName = data.supplierName;
      this.form.sourceNo = data.no;
      this.form.sourceNoMoney = data.payMoney;
      this.form.shopName = data.shopName;
      this.form.shopId = data.shopId;
      this.form.financeTypeId = data.financeTypeId;
      this.form.financeType = data.financeType;
      this.form.accountList = [
        {
          accountId: "",
          accountNumber: "",
          accountName: "",
          money: data.payMoney,
          discountMoney: 0,
          finalMoney: "",
          payWay: "",
          remark: "",
        },
      ];
      this.tableData = [{ ...data, offsetMoney: Number(data.notOffsetMoney) }];
      // 添加调用标识，1表示不是纯新增页面
      await this.getSupplierInfoById(data.supplierId, 1);
    },
    // 获取供应商详情
    async getSupplierInfoById(id, isEdit) {
      const { data } = await getSupplierInfoById(id);
      this.money = data.money || 0;
      if (!isEdit) {
        this.form.supplierName = data.title;
        this.form.supplierId = data.id;
        await this.getAllPay();
      }
    },
    // async getPayInfo1() {
    //   // let params = {
    //   //   page: 1,
    //   //   pageSize: 999,
    //   //   supplierId: this.form.supplierId,
    //   //   offsetStatus: 4,
    //   // };
    //   const { data, pageTotal } = await getPayInfo({
    //     id: this.id,
    //     purchaseId: this.purchaseId,
    //     createTime: this.createTime1,
    //   });
    //   let arr = [];
    //   arr.push(data);
    //   this.tableData = arr;
    //   this.tableData[0].offsetMoney = data.notOffsetMoney;
    // },
    // 列表
    async getAllPay() {
      let params = {
        page: 1,
        pageSize: 999,
        supplierId: this.form.supplierId,
        offsetStatus: [3, 4],
      };
      const { data, pageTotal } = await getAllPay(params);
      this.tableData = data.map((item) => {
        return { ...item, offsetMoney: Number(item.notOffsetMoney) };
      });
    },
    addType() {
      if (!this.add_form.name.trim()) {
        this.$message.warning("必填项不能为空");
        return;
      }
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          let target = {};
          if (!this.is_edit) {
            target = await addFinanceType({
              ...this.add_form,
            });
          } else {
            target = await editFinanceType(this.pay_id, {
              ...this.add_form,
            });
          }
          const data = target;

          this.show_model = false;
        }
      });
    },
    openHandelShow() {
      if (!this.form.supplierId) {
        this.$message.warning("请选择供应商");
        return;
      }
      this.handle_show = true;
    },
    handlesel(val) {
      this.form.sourceNo = val[0].no;
      this.form.sourceNoMoney = val[0].payMoney;
      this.form.shopName = val[0].shopName;
      this.form.shopId = val[0].shopId;
      this.form.accountList[0].money = val[0].payMoney;
    },
    // 选择商铺
    selShop(val, row) {
      this.form.shopName = row[0].name;
    },
    addAccount() {
      this.form.accountList.push({
        accountId: "",
        accountNumber: "",
        accountName: "",
        money: "",
        discountMoney: "",
        finalMoney: "",
        payWay: "",
        remark: "",
      });
    },
    // 暂存数据
    async temData(tempSave) {
      let receiptOffsetData = this.choose_data.map((item) => {
        return {
          payReceiptId: item.id,
          offsetMoney: item.offsetMoney,
        };
      });
      const params = {
        ...this.form,
        receiptTime: parseInt(this.form.receiptTime / 1000),
        tempSave: tempSave,
        receiptOffsetData: receiptOffsetData,
      };
      this.loading = true;
      const data = await addPaid(params);
      this.loading = false;

      this.$message({
        message: "暂存成功",
        type: "success",
      });
      this.$closeCurrentGoEdit("/Finance/Handle/PaymentList");
    },
    //  保存
    async addData(tempSave) {
      this.$refs["base_form"].validate(async (valid) => {
        if (valid) {
          if (!tempSave) {
            let isSub = true;
            for (let i in this.form.accountList) {
              let item = this.form.accountList[i];
              if (!item.accountName) {
                isSub = false;
                this.$message.warning("请选择结算账户");
                break;
              }
              if (!item.money) {
                isSub = false;
                this.$message.warning("请输入付款金额");
                break;
              }
              // if (!item.payWay) {
              //   isSub = false;
              //   this.$message.warning("请选择结算方式");
              //   break;
              // }
              // if (this.offSetTotal > this.moneyTotal) {
              //   isSub = false;
              //   this.$message.warning("核销金额不能大于付款金额");
              //   break;
              // }
            }
            if (!isSub) {
              return;
            }
          }
          if (this.choose_data.length) {
            let receiptOffsetData = this.choose_data.map((item) => {
              return {
                payReceiptId: item.id,
                offsetMoney: item.offsetMoney,
                payCreateTime: item.createTime,
              };
            });
            const params = {
              ...this.form,
              receiptTime: parseInt(this.form.receiptTime / 1000),
              tempSave: tempSave,
              receiptOffsetData: receiptOffsetData,
            };
            this.loading = true;
            try {
              if (this.paid) {
                const data = await editPaid(this.paid, params);
              } else {
                const data = await addPaid(params);
              }
              this.loading = false;
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              this.$closeCurrentGoEdit("/Finance/Handle/PaymentList");
            } catch {
              this.loading = false;
            }
          } else {
            //新增
            let message = this.form.financeType;
            const params = {
              ...this.form,
              receiptTime: parseInt(this.form.receiptTime / 1000),
              tempSave: tempSave,
              financeTypeId: this.form.financeTypeId,
              financeType: this.form.financeType,
            };
            this.$confirm("确定要将该单据作为" + message + "？", "提示", {
              //this.$confirm("确定要将该单据作为采购预付款？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(async () => {
                this.loading = true;
                try {
                  if (this.paid) {
                    const data = await editPaid(this.paid, params);
                  } else {
                    const data = await addPaid(params);
                  }
                  this.loading = false;
                  this.$message({
                    type: "success",
                    message: "操作成功!",
                  });
                  this.$closeCurrentGoEdit("/Finance/Handle/PaymentList");
                } catch {
                  this.loading = false;
                }
              })
              .catch(() => {
                this.loading = false;
              });
          }
        }
      });
    },
    //  详情
    async getPaidInfo() {
      const { data } = await getPaidInfo({
        id: this.paid,
        createTime: this.form.createTime,
      });

      this.form = {
        ...data,
        receiptTime: data.receiptTime * 1000,
        accountList: data.accountList,
      };
      if (data.offsetDate) {
        this.tableData = data.offsetDate;
        this.choose_data = data.offsetDate;
        setTimeout(() => {
          this.tableData.forEach((item) => {
            if (item.offsetMoney > 0) {
              this.$refs.multipleTable.toggleRowSelection(item);
            }
          });
        }, 200);
      }
    },
    // 类型AddPromotion
    typeChange(val) {
      const target = this.financeTypeList.find((item) => {
        return item.id === val;
      });
      this.form.financeType = target.name;
    },
    //  供应商
    selUnitSupplier(val, list) {
      this.form.supplierName = list[0].title;
      this.money = list[0].money;
      this.getAllPay();
    },
    //  获取财务类型
    async getAllFinanceType() {
      const { data } = await getAllFinanceTypeNoPage(2);
      this.financeTypeList = data;
      if (this.form.financeTypeId == 4) {
        const finance = data.find((item) => {
          return item.name === "采购付款";
        });
        this.form.financeTypeId = finance.id;
        this.form.financeType = finance.name;
      }
    },
    delAccount(index) {
      this.form.accountList.splice(index, 1);
    },
    // 获取支付方式
    async getAllPayment() {
      const data = await getAllPayment({
        page: 1,
        pageSize: 9,
      });

      this.pay_type_list = data.data;
    },
    openAccount(index) {
      this.account_index = index;
      if (!this.form.supplierId) {
        this.$message.warning("请选择供应商");
        return;
      }
      if (!this.form.financeTypeId != 7) {
        if (!this.form.shopId) {
          this.$message.warning("请选择商铺");
          return;
        }
      }
      this.account_show = true;
    },
    // 结算账户
    accountsel(val) {
      this.form.accountList[this.account_index].accountId = val[0].id;
      this.form.accountList[this.account_index].accountNumber = val[0].accountNumber;
      this.form.accountList[this.account_index].accountName = val[0].name;
    },
    //  暂存
    async getTempPaidData() {
      const { data } = await getTempPaidData();

      if (JSON.stringify(data) === "{}") return;
      this.form = {
        ...data,
        receiptTime: parseInt(data.receiptTime * 1000),
        accountList: data.accountList.map((item) => {
          return item;
        }),
      };
      if (data.offsetDate) {
        this.tableData = data.offsetDate;
        this.choose_data = data.offsetDate;
        setTimeout(() => {
          this.tableData.forEach((item) => {
            this.$refs.multipleTable.toggleRowSelection(item);
          });
        }, 200);
      }
    },
    // 自动核销
    offsetGet() {
      let total = 0;
      for (let i = 0; i < this.tableData.length; i++) {
        let item = this.tableData[i];
        // 把本次核销金额相加
        total += Number(item.offsetMoney);
        // 勾选可以核销的数据
        this.$refs.multipleTable.toggleRowSelection(item);
        // 如果合计核销金额大于付款总金额，停止循环，并把停止循环前的一条核销数据修改为差值
        if (total > this.moneyTotal || total === this.moneyTotal) {
          item.offsetMoney = this.moneyTotal - (total - item.offsetMoney);
          break;
        }
      }
    },
    // 付款金额失去焦点后重新核销
    moneyBlur() {
      this.$refs.multipleTable.clearSelection();
      this.tableData = this.tableData.map((item) => {
        return {
          ...item,
          offsetMoney: Number(item.payMoney),
        };
      });
    },
    goDetail(row) {
      this.$router.push(`/Purchase/ManageP/PurchaseOrderLook/${row.purchaseId}`);
    },
  },
};
</script>
<style lang="scss" scoped>
.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.table-b-div {
  height: 80px;
  width: 100%;
  padding: 16px 24px 32px 25px;
  border: 1px solid #ebeef5;
  text-align: center;
  line-height: 40px;
  border-top: 0 none;
  cursor: pointer;
  .table-b-div-cont {
    border: 1px dashed #2153d4;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.Enunciate {
  width: 100%;
  height: 72px;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  padding: 16px 24px;
  border-top: 1px solid #dee2ee;
  .Enunciate_cont {
    background-color: #fa6400;
    border-radius: 3px;
    padding: 0 24px;
  }
}
.btn-top-div {
  position: absolute;
  right: 20px;
  top: 45px;
  z-index: 999;
}
</style>
<style>
.AddPayment {
  background-color: #fff;
}
.AddPayment .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.AddPayment .is-active {
  font-weight: 700;
  color: #000;
}
.AddPayment .el-tabs__nav {
  margin-left: 24px;
}
</style>
