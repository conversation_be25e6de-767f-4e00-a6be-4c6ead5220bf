<!--供应商余额表-->
<template>
  <ContainerQuery>
    <div slot="left">
      <el-button type="primary" size="small" plain @click="getAllSupplierBalance(true)"> 导出 </el-button>
    </div>
    <el-form slot="more" size="small" :inline="true" style="margin-bottom: 0">
      <el-form-item>
        <SelectSupplier v-model="searchDate.supplierId" @clear="supplierClear" @change="selSupplier" />
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="searchDate.time"
          clearable
          type="daterange"
          value-format="timestamp"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="orderDate"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <el-table :data="tabelData">
      <el-table-column prop="supplierId" align="left" label="ID" min-width="50"></el-table-column>
      <el-table-column prop="title" label="供应商名称" align="left" min-width="120"></el-table-column>
      <el-table-column prop="openingBalance" label="期初余额" align="left" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.openingBalance) }}
        </template>
      </el-table-column>
      <el-table-column prop="purchase" align="left" label="采购金额" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.purchase) }}
        </template>
      </el-table-column>
      <el-table-column prop="payment" align="left" label="付款金额" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.payment) }}
        </template>
      </el-table-column>
      <el-table-column prop="endingBalance" label="期末金额" align="left" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.endingBalance) }}
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" label="操作" min-width="100" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.SupplierBalanceDetailsGetAllSupplierBalanceDetail)"
            type="text"
            @click="$router.push(`/Finance/Handle/SupplierBalanceDetails?id=${scope.row.supplierId}`)"
          >
            明细
          </el-button>
          <el-button type="text" @click="$router.push(`/Finance/Handle/AddPayment?supplierId=${scope.row.supplierId}`)">
            付款
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import SelectSupplier from "@/component/common/SelectSupplier.vue";
import { getAllSupplierBalance, exportGetAllSupplierBalance } from "@/api/Finance";
export default {
  name: "SupplierBalance",
  components: {
    SelectSupplier,
  },
  data() {
    return {
      supplier_show: false,
      searchDate: {
        time: [],
        start: "",
        end: "",
        supplierId: "",
      },
      customer_show: false,
      tabelData: [],
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  created() {
    this.getAllSupplierBalance();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllSupplierBalance();
  },
  methods: {
    // 获取列表
    async getAllSupplierBalance(isExport) {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        supplierId: this.searchDate.supplierId,
        start: this.searchDate.start,
        end: this.searchDate.start,
      };
      if (isExport) {
        const data = await exportGetAllSupplierBalance({
          page: this.page,
          pageSize: this.pageSize,
          isExport: true,
        });
      } else {
        const { data, pageTotal } = await getAllSupplierBalance(params);

        this.tabelData = data;
        this.total = pageTotal;
      }
    },
    //  获取供应商 selSupplier
    selSupplier(val) {
      this.pageChange(1);
    },
    supplierClear() {
      this.searchDate.supplierId = "";
      this.pageChange(1);
    },
    //  时间
    orderDate(val) {
      if (val && val.length) {
        this.searchDate.start = val[0] / 1000;
        this.searchDate.end = val[1] / 1000 + 86399;
      } else {
        this.searchDate.start = "";
        this.searchDate.end = "";
      }
      this.pageChange(1);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllSupplierBalance();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
  },
};
</script>
<style scoped></style>
