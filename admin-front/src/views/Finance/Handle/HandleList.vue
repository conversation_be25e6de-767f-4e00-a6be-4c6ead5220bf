<template>
  <!--  应付单列表-->
  <ContainerQuery>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示：应付单是在采购入库单/采退出库单审核后会自动生成，应付单系统自动审核！
    </div>
    <div slot="left">
      <el-button type="primary" size="small" @click="getAllPay(1)"> 导出 </el-button>
    </div>
    <el-form
      v-if="$accessCheck($Access.HandleListSearch)"
      slot="more"
      style="margin-bottom: 0"
      :inline="true"
      size="small"
    >
      <el-form-item>
        <el-input
          v-model="sourceNo"
          style="width: 220px"
          placeholder="源订单号"
          clearable
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" @click="pageChange(1)">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <SelectSupplier v-model="search_form.supplierId" @clear="supplierClear" @change="selSupplier" />
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="time"
          clearable
          type="daterange"
          value-format="timestamp"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="orderDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="search_form.auditStatus"
          style="width: 200px"
          clearable
          placeholder="单据状态"
          @change="pageChange(1)"
        >
          <el-option v-for="(item, index) in doc_Status" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <!--      <el-form-item>-->
      <!--        <el-select-->
      <!--          v-model="search_form.offsetStatus"-->
      <!--          style="width: 150px"-->
      <!--          clearable-->
      <!--          placeholder="核销状态"-->
      <!--          @change="pageChange(1)"-->
      <!--        >-->
      <!--          <el-option-->
      <!--            v-for="(item, index) in offset_Status"-->
      <!--            :key="index"-->
      <!--            :label="item.label"-->
      <!--            :value="item.value"-->
      <!--          ></el-option>-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-select
          v-model="search_form.offsetStatus"
          multiple
          style="width: 200px"
          placeholder="核销状态"
          @change="pageChange(1)"
        >
          <el-option
            v-for="item in offset_Status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table ref="filterTable" :data="tableData">
      <el-table-column prop="id" align="left" label="ID" fixed="left" min-width="50"></el-table-column>
      <el-table-column
        prop="no"
        label="单据编号"
        align="left"
        min-width="180"
        fixed="left"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column prop="sourceNo" label="源订单号" align="left" min-width="180" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span
            v-if="
              $accessCheck($Access.PurchaseReturnOrderGetPurchaseOutInfoById) && scope.row.financeType === '采购退款单'
            "
            class="click-div"
            @click="$router.push(`/Purchase/ManageP/PurchaseReturnOrderLook/${scope.row.purchaseId} `)"
          >
            {{ scope.row.sourceNo }}
          </span>
          <span
            v-else-if="$accessCheck($Access.PurchaseOrderAddPurchase) && scope.row.financeType === '采购单'"
            class="click-div"
            @click="$router.push(`/Purchase/ManageP/PurchaseOrderLook/${scope.row.purchaseId} `)"
          >
            {{ scope.row.sourceNo }}
          </span>
          <span v-else>{{ scope.row.sourceNo }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="supplierName" label="供应商" min-width="160">
        <template slot-scope="scope">
          <p>{{ scope.row.supplierName }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="supplierName" label="供应商编号" min-width="160">
        <template slot-scope="scope">
          <p>{{ scope.row.supplierCode }}</p>
        </template>
      </el-table-column>
      <el-table-column v-if="timeFlag" prop="createTime" label="单据日期" align="left" min-width="140">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="dealWithFlag"
        prop="financeType"
        label="应付类型"
        align="left"
        min-width="100"
      ></el-table-column>
      <el-table-column v-if="discountsFlag" prop="discountMoney" label="优惠金额" align="left" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.discountMoney) }}
        </template>
      </el-table-column>
      <el-table-column v-if="practicalFlag" prop="payMoney" label="应付金额" align="left" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.payMoney) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="storeFlag"
        prop="warehouseName"
        label="仓库"
        align="left"
        min-width="150"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column v-if="receiptsFlag" prop="receiptTypeId" label="单据类型" align="left" min-width="100">
        <template slot-scope="scope">
          <!-- {{ scope.row.receiptTypeId === 2 ? "采购订单" : "采购退货单" }} -->
          {{ scope.row.receiptTypeName }}
        </template>
      </el-table-column>
      <el-table-column v-if="stateFlag" prop="auditStatus" label="状态" align="left" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 1" class="info-status"> 待审核 </span>
          <span v-else class="success-status">已审核</span>
        </template>
      </el-table-column>
      <el-table-column v-if="stateFlagoffSet" prop="offsetStatus" label="核销状态" align="left" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.offsetStatus === 5" class="success-status"> 已核销 </span>
          <span v-else-if="scope.row.offsetStatus === 4" class="info-status"> 待核销 </span>
          <span v-else class="warning-status">部分核销</span>
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" fixed="right" min-width="140">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.HandleListUpdatePayStatus)"
            :disabled="scope.row.auditStatus === 2"
            type="text"
            @click="updateAuditStatus(scope.row)"
          >
            审核
          </el-button>
          <!--已审核或者核销状态为已核销或者应付金额大于0 有一条为true 就不能付款-->
          <el-button
            v-if="$accessCheck($Access.PaymentListAddPaid)"
            :disabled="
              parseInt(scope.row.auditStatus) !== 2 ||
              parseInt(scope.row.offsetStatus) === 5 ||
              Number(scope.row.payMoney) <= 0
            "
            type="text"
            @click="payPage(scope.row)"
          >
            付款
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>
<script>
import { exportGetAllPay, getAllPay, updatePayStatus } from "@/api/Finance";
import SelectSupplier from "@/component/common/SelectSupplier.vue";
export default {
  name: "Handle",
  components: {
    SelectSupplier,
  },
  data() {
    return {
      total: 0,
      page: 1,
      pageSize: 10,
      show_shop: false,
      tableData: [],
      doc_Status: [
        { id: 1, name: "待审核" },
        { id: 2, name: "已审核" },
      ],
      offset_Status: [
        { value: 4, label: "未核销" },
        { value: 5, label: "已核销" },
        { value: 3, label: "部分核销" },
      ],
      doc_type: [
        { id: 2, name: "采购订单" },
        { id: 4, name: "采购退货单" },
      ],
      shop: "",
      time: [],
      search_form: {
        keyword: "",
        shopId: "",
        auditStatus: "",
        receiptTypeId: "",
        start: "",
        end: "",
        supplierId: "",
        offsetStatus: [],
      },
      checkList: ["单据日期", "应付类型", "优惠金额", "实际应付金额", "仓库", "单据类型", "状态", "核销状态"],
      columns: [
        {
          label: "单据日期",
        },
        {
          label: "应付类型",
        },
        {
          label: "优惠金额",
        },
        {
          label: "实际应付金额",
        },
        {
          label: "仓库",
        },
        {
          label: "单据类型",
        },
        {
          label: "状态",
        },
        {
          label: "核销状态",
        },
      ],
      timeFlag: true,
      dealWithFlag: true,
      discountsFlag: true,
      practicalFlag: true,
      storeFlag: true,
      receiptsFlag: true,
      stateFlag: true,
      stateFlagoffSet: true,
      sourceNo: "",
    };
  },
  created() {
    this.getAllPay();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllPay();
  },
  methods: {
    //  获取供应商 selSupplier
    selSupplier(val) {
      this.pageChange(1);
    },
    supplierClear() {
      this.search_form.supplierId = "";
      this.pageChange(1);
    },
    // 列表
    async getAllPay(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.search_form.keyword,
        warehouseId: "",
        auditStatus: this.search_form.auditStatus,
        payReceiptIds: "",
        start: this.search_form.start,
        end: this.search_form.end,
        supplierId: this.search_form.supplierId,
        offsetStatus: this.search_form.offsetStatus || 0,
        sourceNo: this.sourceNo,
      };
      if (exports) {
        const data = await exportGetAllPay({
          isExport: true,
          ...params,
        });
      } else {
        const { data, pageTotal } = await getAllPay(params);

        this.tableData = data;
        this.total = pageTotal;
      }
    },
    //  审核
    async updateAuditStatus(row) {
      this.$confirm("确定要进行付款操作吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updatePayStatus({
          id: row.id,
          createTime: row.createTime,
        });

        this.getAllPay();
        this.$message({
          type: "success",
          message: "操作成功",
        });
      });
    },
    payPage(row) {
      this.$router.push(
        `/Finance/Handle/AddPayment?id=${row.id}&createTime=${row.createTime}&purchaseId=${row.purchaseId}`
      );
    },
    // 选择商铺
    selShop(val) {
      this.pageChange(1);
    },
    shopClear() {
      this.search_form.shopId = "";
      this.pageChange(1);
    },
    //  订单时间
    orderDate(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    pageChange(val) {
      this.page = val;
      this.getAllPay();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    change() {
      this.timeFlag = this.checkList.some((item) => item === "单据日期");
      this.dealWithFlag = this.checkList.some((item) => item === "应付类型");
      this.discountsFlag = this.checkList.some((item) => item === "优惠金额");
      this.practicalFlag = this.checkList.some((item) => item === "实际应付金额");
      this.storeFlag = this.checkList.some((item) => item === "仓库");
      this.receiptsFlag = this.checkList.some((item) => item === "单据类型");
      this.stateFlag = this.checkList.some((item) => item === "状态");
      this.stateFlagoffSet = this.checkList.some((item) => item === "和消状态");
    },
  },
};
</script>

<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
