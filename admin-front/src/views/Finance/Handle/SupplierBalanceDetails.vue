<!--供应商余额表-->
<template>
  <ContainerQuery>
    <div slot="left">
      <el-button size="small" type="primary" plain @click="getData(1)"> 导出 </el-button>
    </div>
    <div slot="more">
      <el-form size="small" :inline="true">
        <el-form-item>
          <SelectSupplier v-model="searchDate.supplierId" @clear="supplierClear" @change="selSupplier" />
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <span style="color: #000">-->
        <!--            {{ searchDate.contactName || "暂无" }}-->
        <!--          </span>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item>-->
        <!--          <span style="color: #000">-->
        <!--            {{ searchDate.mobile || "暂无" }}-->
        <!--          </span>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <el-date-picker
            v-model="time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="orderDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <!--    <div slot="left"><el-button type="primary">导出</el-button></div>-->
    <!--     show-summary
      :summary-method="getSummaries"-->
    <div v-if="tableData.length">
      <el-table :data="tableData">
        <el-table-column prop="id" label="ID" fixed="left" min-width="50"></el-table-column>
        <el-table-column prop="receiptTime" label="单据日期" fixed="left" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.receiptTime ? $_common.formatDate(scope.row.receiptTime, "yyyy-MM-dd") : "" }}
          </template>
        </el-table-column>
        <el-table-column prop="no" fixed="left" label="单据编号" min-width="180"></el-table-column>
        <el-table-column prop="sourceNo" label="源订单销货号" min-width="180"></el-table-column>
        <el-table-column prop="financeType" label="业务类别" min-width="120"></el-table-column>
        <el-table-column prop="salesAmount" label="采购金额" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.salesAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="discountMoney" label="优惠金额" min-width="120">
          <template slot-scope="scope">
            <span style="color: #ff4040">
              {{ $_common.formattedNumber(scope.row.discountMoney) }}
            </span>
          </template>
        </el-table-column>
        <!--      <el-table-column-->
        <!--        prop="supplierAmount"-->
        <!--        label="供应商承担金额(元)"-->
        <!--        -->
        <!--        min-width="140"-->
        <!--      >-->
        <!--        <template slot-scope="scope">-->
        <!--          {{ $_common.formatNub(scope.row.supplierAmount,4) || 0 }}-->
        <!--        </template>-->
        <!--      </el-table-column>-->
        <el-table-column prop="shouldPayAmount" label="应付金额" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.shouldPayAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="actualPaidAmount" label="实付金额" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.actualPaidAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="shouldPayBalance" label="应付余额" min-width="120" fixed="right">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.shouldPayBalance) || 0 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注"
          fixed="right"
          show-overflow-tooltip
          min-width="200"
        ></el-table-column>
      </el-table>
      <el-row class="total-table">
        <el-col :span="12" class="total-col">合计</el-col>
        <el-col :span="6" class="total-col"> 实际付款金额：{{ $_common.formattedNumber(actualPayTotal) || 0 }} </el-col>
        <el-col :span="6" class="total-col">
          应付款余额：

          {{ $_common.formattedNumber(shouldPayTotal) || 0 }}
        </el-col>
      </el-row>
      <FooterPage
        :page-size="pageSize"
        :total-page.sync="total"
        :current-page.sync="page"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      ></FooterPage>
    </div>
    <div v-else class="empty-view">
      <img class="empty-img" :src="require('@/assets/img/no_enterprise.png')" alt="" />
      <p>快去选择供应商进行查看吧！</p>
    </div>
  </ContainerQuery>
</template>

<script>
import SelectSupplier from "@/component/common/SelectSupplier";
import { exportgetAllSupplierBalanceDetail, getAllSupplierBalanceDetail } from "@/api/Finance";
import { getSupplierInfoById } from "@/api/Purchase";
export default {
  name: "SupplierBalanceDetails",
  components: {
    SelectSupplier,
  },
  data() {
    return {
      shouldPayTotal: "",
      actualPayTotal: "",
      supplier_show: false,
      time: [],
      searchDate: {
        supplierId: "",
        supplierName: "",
        contactName: "",
        mobile: "",
        start: "",
        area: {},
        end: "",
      },
      customer_show: false,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  async created() {
    if (this.$route.query.id) {
      // 获取id
      this.searchDate.supplierId = this.$route.query.id;
      await this.getSupplierInfoById();
      await this.getData();
    }
  },
  async activated() {
    if (this.$_isInit()) return;
    if (this.$route.query.id) {
      // 获取id
      this.searchDate.supplierId = this.$route.query.id;
      await this.getSupplierInfoById();
      await this.getData();
    } else {
      this.dialogFormVisible = true;
    }
  },
  mounted() {},
  methods: {
    //  确定按钮
    confirm() {
      if (this.searchDate.supplierId) {
        this.dialogFormVisible = false;
        this.getData();
      } else {
        this.$message("请选择供应商");
      }
    },
    //  获取供应商详情
    async getSupplierInfoById() {
      const { data } = await getSupplierInfoById(this.searchDate.supplierId);

      this.searchDate = {
        ...data,
        supplierId: data.id,
        supplierName: data.title,
        contactName: data.realName,
        start: "",
        end: "",
      };
    },
    // 获取列表
    async getData(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        supplierId: this.searchDate.supplierId,
        start: this.searchDate.start,
        end: this.searchDate.end,
      };

      if (exports) {
        params.export = 1;
        const target = await exportgetAllSupplierBalanceDetail({
          ...params,
        });
      } else {
        const { data, pageTotal, endingBalance, openingBalance, actualPayTotal, shouldPayTotal } =
          await getAllSupplierBalanceDetail({
            ...params,
          });
        this.tableData = data;
        this.tableData.push({
          no: "期末余额",
          shouldPayBalance: endingBalance,
        });
        this.tableData.unshift({
          no: "期初余额",
          shouldPayBalance: openingBalance,
        });
        this.shouldPayTotal = shouldPayTotal;
        this.actualPayTotal = actualPayTotal;
        this.total = pageTotal;
      }
      // const {
      //   data,
      //   pageTotal,
      //   endingBalance,
      //   openingBalance,
      //   actualPayTotal,
      //   shouldPayTotal,
      // } = await getAllSupplierBalanceDetail({
      //   ...params
      // });
    },
    //  时间
    orderDate(val) {
      if (val && val.length) {
        this.searchDate.start = val[0] / 1000;
        this.searchDate.end = val[1] / 1000 + 86399;
      } else {
        this.searchDate.start = "";
        this.searchDate.end = "";
      }
      this.pageChange(1);
    },
    //  获取供应商 selSupplier
    selSupplier(val, list) {
      this.searchDate.supplierId = list[0].id;
      this.searchDate.supplierName = list[0].title;
      this.searchDate.contactName = list[0].realName;
      this.searchDate.mobile = list[0].mobile;
      this.searchDate.area = list[0].area;
      this.pageChange(1);
    },
    supplierClear() {
      this.searchDate.supplierId = "";
      this.searchDate.supplierName = "";
      this.searchDate.contactName = "";
      this.searchDate.mobile = "";
      this.searchDate.area = "";
      this.tableData = [];
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 合计
    getSummaries(param) {
      return this.$_common.getSummaries(param, ["期初余额", "实际收款金额", "应收款余额"]);
    },
  },
};
</script>
<style scoped>
.total-table {
  border: 1px solid #eee;
  font-size: 13px;
}
.total-col {
  border-right: 1px solid #eee;
  padding: 5px 12px;
}
</style>
