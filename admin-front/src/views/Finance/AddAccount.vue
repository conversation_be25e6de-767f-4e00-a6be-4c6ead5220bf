<template>
  <div>
    <el-drawer
      class="edit-drawer"
      :title="isEdit ? '修改账户' : '新建账户'"
      size="35%"
      :visible.sync="visible"
      direction="rtl"
      @close="close"
    >
      <el-form ref="form" :rules="rules" :model="form" label-width="120px">
        <el-form-item label="账户类型">
          <el-select v-model="form.type" placeholder="账户类型">
            <el-option
              v-for="(item, index) in account_type"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账户编号" prop="departmentName">
          <el-input v-model="accountNon" disabled placeholder="系统自动生成"></el-input>
        </el-form-item>
        <el-form-item label="账户名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入账户名称"></el-input>
        </el-form-item>
        <el-form-item label="账户号" prop="accountNumber">
          <el-input v-model="form.accountNumber" placeholder="请输入账户号"></el-input>
        </el-form-item>
        <el-form-item v-if="form.type === 4" label="开户行名称">
          <el-input v-model="form.bankName" placeholder="请输入开户行名称"></el-input>
        </el-form-item>
        <el-form-item v-if="form.type === 4" label="开户人名称">
          <el-input v-model="form.bankAccount" placeholder="开户人名称"></el-input>
        </el-form-item>
        <el-form-item label="期初余额" prop="beginMoney">
          <el-input-number
            v-model="form.beginMoney"
            :disabled="!!id"
            :controls="false"
            placeholder="请输入期初余额"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="所属商铺" prop="shopName">
          <el-input v-model="form.shopName" readonly style="width: 240px" placeholder="所属商铺">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="sel_shop = true"></i>
          </el-input>
          <el-button size="mini" type="text" @click="$router.push('/SystemSettings/liansuoguanli/AddShop')">
            【新建商铺】
          </el-button>
        </el-form-item>
        <el-form-item label="启用/禁用">
          <el-switch
            v-model="form.enableStatus"
            active-color="#36B365"
            inactive-color="#ff4949"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
          ></el-switch>
        </el-form-item>
        <!--        <el-form-item-->
        <!--          label="默认状态"-->
        <!--        >-->
        <!--          <el-radio-->
        <!--            v-model="form.isDefault"-->
        <!--            :label="5"-->
        <!--          >-->
        <!--            是-->
        <!--          </el-radio>-->
        <!--          <el-radio-->
        <!--            v-model="form.isDefault"-->
        <!--            :label="4"-->
        <!--          >-->
        <!--            否-->
        <!--          </el-radio>-->
        <!--          <div style="font-size: 12px;font-weight: normal;color: #E6A23C">-->
        <!--            温馨提示：在线支付的订单会自动计入默认账户-->
        <!--          </div>-->
        <!--        </el-form-item>-->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入备注"></el-input>
        </el-form-item>
        <el-form-item style="text-align: right; padding-right: 10px">
          <el-button type="primary" @click="confirm">确 定</el-button>
          <el-button @click="close">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>
    <GoodsChooseShop
      v-if="sel_shop"
      :is-show-add="true"
      :is-check="false"
      :dialog-visible="sel_shop"
      @close="sel_shop = false"
      @confirm="shopConfirm"
    />
  </div>
</template>

<script>
import GoodsChooseShop from "@/component/goods/GoodsChooseShop.vue";
import { addAccount, editAccount, getAccountInfo } from "@/api/Finance";

export default {
  name: "AddAccount",
  components: {
    GoodsChooseShop,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      sel_shop: false,
      department_position: [],
      accountNon: "",
      form: {
        type: 1,
        isDefault: 5, // 默认状态
        name: "",
        accountNumber: "",
        beginMoney: 0,
        shopId: "",
        shopName: "",
        enableStatus: 5,
        remark: "",
        bankName: "",
        bankAccount: "",
      },
      Department_data: [],
      rules: {
        name: [{ required: true, message: "请输入账户名称", trigger: "blur" }],
        accountNumber: [{ required: true, message: "请输入账户号", trigger: "blur" }],
        beginMoney: [{ required: true, message: "请输入期初余额", trigger: "blur" }],
        shopName: [{ required: true, message: "请选择所属商铺", trigger: "change" }],
      },

      account_type: [
        { label: "普通账户", value: 0 },
        { label: "支付宝账户", value: 2 },
        { label: "微信账户", value: 1 },
        { label: "银行账户", value: 4 },
        { label: "余额账户", value: 7 },
      ],
    };
  },
  created() {
    if (this.id) {
      this.getAccountInfo();
    } else {
      this.form = {
        type: 0,
        name: "",
        accountNumber: "",
        beginMoney: 0,
        shopId: "",
        shopName: "",
        enableStatus: 5,
        remark: "",
      };
    }
  },
  methods: {
    close() {
      this.$emit("close");
    },
    shopConfirm(val) {
      this.form.shopName = val[0].name;
      this.form.shopId = val[0].id;
    },
    confirm() {
      if (this.form.type === "") {
        this.$message.warning("请选择账户类型");
        return;
      }
      // if (this.form.type === 4 && !this.form.bankName) {
      //   this.$message.warning("开户行名称不能为空");
      //   return;
      // }
      if (!this.form.name.trim() || !this.form.accountNumber.trim()) {
        this.$message.warning("必填项不能为空");
        return;
      }
      if (!this.id) {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            this.$confirm("期初余额只能填写一次,提交后不能修改, 是否继续?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(async () => {
                const data = await addAccount(this.form);

                this.$message({
                  message: "提交成功",
                  type: "success",
                });
                this.close();
                this.$emit("confirm");
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "请修改期初余额",
                });
              });
          }
        });
      } else {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            const data = await editAccount(this.id, this.form);

            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.close();
            this.$emit("confirm");
          }
        });
      }
    },
    // 获取详情
    async getAccountInfo() {
      const { data } = await getAccountInfo(this.id);

      this.form = data;
      this.accountNon = data.accountCode;
    },
  },
};
</script>

<style scoped>
.creat-shop {
  font-size: 12px;
  color: #1890ff;
}
</style>
