<!--账户列表弹窗-->
<template>
  <el-dialog
    :title="title"
    :visible="isShow"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="60%"
    @close="cancel"
  >
    <vxe-table ref="xTable1" border="inner" :data="tableData" @radio-change="radioChangeEvent">
      <vxe-table-column v-if="isRadio" type="radio" width="60"></vxe-table-column>
      <vxe-table-column min-width="140" field="accountCode" title="账户编号"></vxe-table-column>
      <vxe-table-column min-width="140" field="name" title="账户名称"></vxe-table-column>
      <vxe-table-column field="accountNumber" min-width="160" title="账户号"></vxe-table-column>
      <vxe-table-column field="money" title="当前余额" min-width="110">
        <template #default="{ row }">
          {{ $_common.formattedNumber(row.money) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="createTime" title="建账日期" min-width="160">
        <template #default="{ row }">
          {{ $_common.formatDate(row.createTime) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="beginMoney" title="期初余额" min-width="110">
        <template #default="{ row }">
          {{ $_common.formattedNumber(row.beginMoney) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="shopName" show-overflow title="所属商铺" min-width="140"></vxe-table-column>
      <vxe-table-column v-if="!isRadio" fixed="right" title="选择" width="90">
        <template #default="{ row }">
          <el-button icon="el-icon-check" size="mini" @click="dbSelect(row)"></el-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="isRadio" slot="btn-div">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>

<script>
import { getAllAccount } from "@/api/Finance";
import FooterPage from "@/component/common/FooterPage";
export default {
  name: "AccountType",
  components: {
    FooterPage,
  },
  props: {
    title: {
      type: String,
      default: "账户列表",
    },
    isShow: {
      type: Boolean,
      default: false,
    },
    isRadio: {
      type: Boolean,
      default: false,
    },
    shopId: {
      type: Number,
      default: 0,
    },
    type: {
      type: [Number, String],
      default: "",
    },
    isMem: {
      type: [Number, String],
      default: "",
    },
  },
  data() {
    return {
      choose_data: [],
      clientList: [],
      tableData: [],
      pre_page: 10,
      page: 1,
      // modalShow: this.clientModalShow,
      selectedIndex: null,
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      selectedClient: {},
    };
  },
  created() {
    this.getAllAccount();
  },
  methods: {
    radioChangeEvent({ row }) {
      this.choose_data = row;
      console.log("单选事件");
    },
    //  获取列表
    async getAllAccount() {
      let params = {
        page: this.page,
        pageSize: this.pre_page,
        enableStatus: 5,
        isMem: this.isMem,
      };
      if (this.shopId) {
        params.shopId = this.shopId;
      }
      if (this.type) {
        params.type = this.type;
      }
      const { data, pageTotal } = await getAllAccount(params);

      this.tableData = data;
      this.total = pageTotal;
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    confirm() {
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getAllAccount();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
