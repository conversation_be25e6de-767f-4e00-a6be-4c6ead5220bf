<!--添加基础资料-->
<template>
  <div class="page-div" style="padding: 0">
    <el-tabs v-model="activeName">
      <el-tab-pane label="收款单详情" name="one">
        <el-row style="padding-bottom: 13px">
          <el-col :span="24">
            <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">收款信息</p>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">单据编号</span>
            <span class="form_right">{{ form.no }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">收款单位</span>
            <span class="form_right">{{ form.customerName }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">收款类型</span>
            <span class="form_right">{{ form.financeType }}</span>
          </el-col>
          <el-col class="form" :span="6" style="padding-left: 54px">
            <span class="form_left">收款人</span>
            <span class="form_right">{{ form.currentAccountName }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">单据日期</span>
            <span class="form_right">
              {{ $_common.formatDate(form.receiptTime) }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">核销状态</span>
            <span class="form_right">
              <span class="success-status">已核销</span>
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">所属店铺</span>
            <span class="form_right">{{ form.shopName }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">当前余额</span>
            <span class="form_right">
              {{ $_common.formattedNumber(form.currentBalanceMoney) || 0 }}
            </span>
          </el-col>
        </el-row>
        <div class="order_bottom">
          <p class="text">结算账户</p>
          <el-table :data="form.accountList">
            <el-table-column prop="accountNumber" label="结算账户" min-width="160">
              <template slot-scope="scope">
                {{ scope.row.accountName || scope.row.accountNumber }}
              </template>
            </el-table-column>
            <el-table-column prop="money" label="收款金额" min-width="120">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.money) }}
              </template>
            </el-table-column>
            <el-table-column prop="discountMoney" label="优惠金额" min-width="120">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.discountMoney) }}
              </template>
            </el-table-column>
            <el-table-column prop="finalMoney" label="实际收款金额" min-width="120" align="center">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.finalMoney) }}
              </template>
            </el-table-column>
            <el-table-column label="收款时间">
              <template slot-scope="scope">
                {{ $_common.formatDate(scope.row.payTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="200"></el-table-column>
          </el-table>
          <div class="Enunciate">
            <div class="Enunciate_cont clearfix">
              <div class="float_left">
                <span> 收款合计：{{ $_common.formattedNumber(moneyTotal) }} </span>
                <span style="margin: 0 10px">+</span>
                <span>现金折扣：0.00</span>
                <span style="margin: 0 10px">=</span>
                <span>
                  可核销金额：
                  <span style="font-weight: 700">
                    {{ $_common.formattedNumber(moneyTotal) }}
                  </span>
                </span>
              </div>
              <div class="float_right">本次核销金额：{{ $_common.formattedNumber(offSetTotal) }}</div>
            </div>
          </div>
        </div>
        <div class="order_bottom">
          <p class="text">核销单据</p>
          <el-table :data="tableData" style="width: 100%">
            <el-table-column label="单据类型" min-width="100">
              <template slot-scope="scope">
                <span @click="scope">应收单</span>
              </template>
            </el-table-column>
            <el-table-column prop="no" label="单据编号" min-width="140"></el-table-column>
            <el-table-column prop="address" label="单据日期" min-width="120">
              <template slot-scope="scope">
                {{ $_common.formatDate(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="address" label="金额" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.money) }}
              </template>
            </el-table-column>
            <el-table-column prop="address" label="未核销金额" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.notOffsetMoney) }}
              </template>
            </el-table-column>
            <el-table-column prop="offsetMoney" label="本次核销金额" min-width="100">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.offsetMoney) }}
              </template>
            </el-table-column>
            <el-table-column prop="originNo" label="销售订单" min-width="100">
              <template slot-scope="scope">
                <span class="click-div" @click="goDetail(scope.row)">
                  {{ scope.row.originNo }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="customerName" label="客户" min-width="100"></el-table-column>
            <!--            <el-table-column-->
            <!--              prop="address"-->
            <!--              label="业务员"-->
            <!--              min-width="100"-->
            <!--            ></el-table-column>-->
            <!--            <el-table-column-->
            <!--              prop="address"-->
            <!--              label="备注"-->
            <!--              min-width="100"-->
            <!--            ></el-table-column>-->
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getReceivedInfo, getAllReceivedOffset } from "@/api/Finance";
import { getAllPayment } from "@/api/System";
export default {
  name: "LookReceipt",
  data() {
    return {
      form: { accountList: [] },
      pay_type_list: [],
      tableData: [],
      activeName: "one",
    };
  },
  computed: {
    // 收款总金额
    moneyTotal() {
      if (!this.form.accountList.length) {
        return 0;
      } else if (this.form.accountList.length === 1) {
        return this.$NP.minus(Number(this.form.accountList[0].money), Number(this.form.accountList[0].discountMoney));
      } else {
        let sum = 0;
        this.form.accountList.forEach((item) => {
          const money = this.$NP.minus(Number(item.money), Number(item.discountMoney));
          sum = this.$NP.plus(sum, money);
        });
        return sum;
      }
    },
    // 核销总金额
    offSetTotal() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return Number(this.tableData[0].offsetMoney);
      } else {
        let sum = 0;
        this.tableData.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.offsetMoney) || 0);
        });
        return sum;
      }
    },
  },
  async created() {
    //  获取详情 收款单详情
    await this.getReceivedInfo();
    //  获取详情 核销单据
    await this.getAllReceivedOffset();
  },
  methods: {
    //  收款单详情
    async getReceivedInfo() {
      const { data } = await getReceivedInfo({
        id: this.$route.params.id,
        createTime: this.$route.params.createTime,
      });
      this.form = data;
      // if (data.offsetDate) {
      //   this.tableData = data.offsetDate;
      // }
    },
    async getAllReceivedOffset() {
      const { data } = await getAllReceivedOffset({
        receivedId: this.$route.params.id,
      });
      this.tableData = data;
    },
    goDetail(row) {
      this.$router.push({
        path: `/order/manageO/OrderDetails/${row.customerId}/${row.originId}`,
      });
    },
  },
};
</script>
<style>
.page-div .el-tabs__nav {
  margin-left: 24px;
  height: 64px;
  line-height: 64px;
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 13px 0 27px 0;
}
.page-div .is-active {
  width: 32px;
  font-size: 16px;
  font-weight: 700;
  color: #2d405e;
}
</style>
<style lang="scss" scoped>
.creat-custorm {
  color: #1890ff;
  font-size: 12px;
}
.tip {
  margin: 0 10px;
  display: inline-block;
}

.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
  .order_p {
    font-size: 14px;
    text-align: right;
    line-height: 58px;
    padding-right: 38px;
    color: #2d405e;
    .order_p_money {
      color: #fa6400;
      margin: 0 3px;
    }
  }
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.Enunciate {
  width: 100%;
  height: 96px;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  padding: 24px 24px 32px;
  .Enunciate_cont {
    background-color: #fa6400;
    border-radius: 3px;
    padding: 0 24px;
  }
}
</style>
