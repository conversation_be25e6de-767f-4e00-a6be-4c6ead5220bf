<template>
  <ContainerQuery>
    <el-button
      v-if="$accessCheck($Access.addApplyReceipt)"
      slot="left"
      size="small"
      type="primary"
      @click="$router.push('/Finance/Receivable/AddApplyReceipt')"
    >
      新增
    </el-button>
    <div slot="more">
      <el-form size="small" :inline="true">
        <el-form-item>
          <SelectCustomer
            v-model="searchDate.custormerName"
            :clearable="true"
            @change="customerSel"
            @clear="customerDel"
          />
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <el-date-picker-->
        <!--            v-model="searchDate.time"-->
        <!--            clearable-->
        <!--            type="daterange"-->
        <!--            value-format="timestamp"-->
        <!--            range-separator="-"-->
        <!--            start-placeholder="开始日期"-->
        <!--            end-placeholder="结束日期"-->
        <!--            @change="timeChange"-->
        <!--          ></el-date-picker>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <el-select
            v-model="searchDate.auditStatus"
            style="width: 150px"
            clearable
            placeholder="单据状态"
            @change="pageChange(1)"
          >
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <SelectCustomer-->
        <!--            v-model="customer_name"-->
        <!--            @clear="customerClear"-->
        <!--            @change="customerSel"-->
        <!--          />-->
        <!--        </el-form-item>-->
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="id" label="ID" min-width="80"></el-table-column>
      <el-table-column prop="name" label="单据号" min-width="160">
        <template slot-scope="scope">
          <span class="click-div" @click="goDetail(scope.row)">
            {{ scope.row.no }}
          </span>
        </template>
      </el-table-column>
      <!--      <el-table-column-->
      <!--        prop="sourceNo"-->
      <!--        label="源单据号"-->
      <!--        min-width="180"-->
      <!--      ></el-table-column>-->
      <el-table-column prop="createTime" label="单据日期" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime, "yyyy-MM-dd") }}
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="往来单位" min-width="120"></el-table-column>
      <el-table-column v-if="moneyFlag" prop="totalCollectionAmount" label="金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalCollectionAmount, 2) }}
        </template>
      </el-table-column>
      <el-table-column v-if="discountsFlag" prop="totalPreferentialAmount" label="优惠金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalPreferentialAmount) }}
        </template>
      </el-table-column>
      <el-table-column v-if="practicalFlag" prop="totalActualAmount" label="实收金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalActualAmount) }}
        </template>
      </el-table-column>
      <el-table-column v-if="auditFlag" prop="address" label="申请状态" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 2" class="success-status"> 已审核 </span>
          <span v-else class="warning-status">待审核</span>
        </template>
      </el-table-column>
      <el-table-column prop="operatorName" label="创建人" min-width="120"></el-table-column>
      <el-table-column header- min-width="120" fixed="right">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.auditApplyReceipt)"
            :disabled="parseInt(scope.row.auditStatus) === 2"
            type="text"
            @click="updateReceiptRequisitionStatus(scope.row)"
          >
            审核
          </el-button>
          <el-button
            v-if="$accessCheck($Access.editApplyReceipt)"
            :disabled="parseInt(scope.row.auditStatus) === 2"
            type="text"
            @click="$router.push(`/Finance/Receivable/editApplyReceipt/${scope.row.id}`)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import SelectCustomer from "@/component/common/SelectCustomer";

import { getAllReceiptRequisition, updateReceiptRequisitionStatus } from "@/api/Finance";
export default {
  name: "ApplyReceipt",
  components: { SelectCustomer },
  data() {
    return {
      searchDate: {
        keyword: "",
        custormerId: "",
        custormerName: "",
        auditStatus: "",
        time: "",
        start: "",
        end: "",
      },
      statusList: [
        { value: 1, label: "待审核" },
        { value: 2, label: "已审核" },
      ],
      total: 0,
      page: 1,
      pageSize: 10,
      customer_name: "",
      tableData: [],
      checkList: ["金额", "优惠金额", "实际收款金额", "审核状态"],
      columns: [
        {
          label: "金额",
        },
        {
          label: "优惠金额",
        },
        {
          label: "实际收款金额",
        },
        {
          label: "审核状态",
        },
      ],
      moneyFlag: true,
      discountsFlag: true,
      practicalFlag: true,
      auditFlag: true,
    };
  },
  created() {
    this.getAllReceiptRequisition();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllReceiptRequisition();
  },
  methods: {
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllReceiptRequisition();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 选择客户
    customerSel(val, list) {
      this.searchDate.custormerId = list[0].id;
      this.searchDate.custormerName = list[0].name;
      this.pageChange(1);
    },
    customerDel() {
      this.searchDate.custormerId = "";
      this.searchDate.custormerName = "";
      this.pageChange(1);
    },
    customerClear() {
      this.searchDate.custormerName = "";
      this.customer_name = "";
      this.pageChange(1);
    },
    timeChange(val) {
      if (val && val.length) {
        this.searchDate.start = parseInt(val[0] / 1000);
        this.searchDate.end = parseInt(val[1] / 1000) + 86399;
      } else {
        this.searchDate.start = "";
        this.searchDate.end = "";
      }
      this.pageChange(1);
    },
    async getAllReceiptRequisition() {
      const data = await getAllReceiptRequisition({
        page: this.page,
        pageSize: this.pageSize,
        operatorName: this.searchDate.keyword,
        custormerId: this.searchDate.custormerId,
        auditStatus: this.searchDate.auditStatus,
      });
      this.tableData = data.data;
      this.total = data.pageTotal;
    },
    change() {
      this.moneyFlag = this.checkList.some((item) => item === "金额");
      this.discountsFlag = this.checkList.some((item) => item === "优惠金额");
      this.practicalFlag = this.checkList.some((item) => item === "实际收款金额");
      this.auditFlag = this.checkList.some((item) => item === "审核状态");
    },
    goDetail(row) {
      this.$router.push(`/Finance/Receivable/ApplyReceiptInfo/${row.id}`);
    },
    updateReceiptRequisitionStatus(row) {
      this.$confirm("是否通过审核并生成收款单？", "提示", {
        distinguishCancelAndClose: true,
        confirmButtonText: "审核并生成",
        cancelButtonText: "只审核",
        type: "warning",
      })
        .then(async () => {
          const { data } = await updateReceiptRequisitionStatus({
            id: row.id,
            createTime: row.createTime,
            autoType: 5,
          });
          this.$message.success("审核成功并生成收款单");
          await this.getAllReceiptRequisition();
        })
        .catch(async (action) => {
          if (action === "cancel") {
            const { data } = await updateReceiptRequisitionStatus({
              id: row.id,
              createTime: row.createTime,
              autoType: 4,
            });
            this.$message.success("审核成功");
            await this.getAllReceiptRequisition();
          }
        });
    },
  },
};
</script>

<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
