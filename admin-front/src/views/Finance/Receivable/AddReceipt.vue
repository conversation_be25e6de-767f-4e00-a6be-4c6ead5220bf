<!--新增收款单-->
<template>
  <ContainerTit class="AddReceipt">
    <div class="page-tip-div" style="margin: 0; position: relative">
      温馨提示：1、退款时在金额输入框输入负数即可！2、收款时，在金额输入框输入正数即可！3、客户给商家预付货款时，在新增选择单据类型为预存收款后不用选择原单据号即可创建！
      <div class="btn-top-div">
        <el-button
          v-if="$accessCheck($Access.ReceiptListGetTempReceivedData)"
          :loading="loading"
          :disabled="isLook || isEdit"
          @click="temData(true)"
        >
          暂存
        </el-button>
        <el-button v-if="!isLook" type="primary" :loading="loading" @click="addData(false)"> 保存并提交 </el-button>
      </div>
    </div>
    <el-form ref="base_form" label-width="120px" :model="form" size="small" :rules="rules" :disabled="isLook">
      <el-tabs v-model="activeName">
        <el-tab-pane label="基础信息" name="one" style="position: relative">
          <el-row style="padding-bottom: 13px">
            <el-col :span="24">
              <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">收款信息</p>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="收款单位" prop="customerName" style="min-width: 400px">
                <SelectCustomer v-model="form.customerName" :clearable="false" width="150" @change="customerSel" />
                <el-button size="mini" type="text" @click="$router.push('/Customer/CustomerAdmin/AddCustomer')">
                  【新建客户】
                </el-button>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="单据日期" prop="brandId">
                <template>
                  <div class="block">
                    <el-date-picker
                      v-model="form.receiptTime"
                      style="width: 210px"
                      type="datetime"
                      placeholder="选择日期"
                      value-format="timestamp"
                    ></el-date-picker>
                  </div>
                </template>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="所属店铺" prop="shopName" style="min-width: 400px">
                <SelectShop
                  v-model="form.shopId"
                  width="160"
                  :clearable="false"
                  placeholder="选择商铺"
                  @change="selShop"
                />
                <el-button size="mini" type="text" @click="$router.push('/SystemSettings/liansuoguanli/AddShop')">
                  【新建商铺】
                </el-button>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="当前应收" prop="money">
                <el-input v-model="form.money" disabled style="width: 210px" placeholder="不可编辑"></el-input>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="业务类型">
                <el-select
                  v-model="form.financeTypeId"
                  style="width: 180px"
                  clearable
                  placeholder="选择类型"
                  @change="typeChange"
                >
                  <el-option
                    v-for="(item, index) in financeTypeList"
                    :key="index"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <el-button v-if="financeTypeList.length === 0" size="mini" type="text" @click="getAllFinanceType">
                  【刷新】
                </el-button>
                <el-button v-if="financeTypeList.length === 0" size="mini" type="text" @click="show_model = true">
                  【新建类型】
                </el-button>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="收款人">
                {{ form.currentAccountName }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      <div class="order_bottom">
        <p class="text">收款单明细</p>
        <el-table :data="form.accountList">
          <el-table-column prop="accountId" label="结算账户" min-width="160">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.accountName"
                readonly
                style="width: 100%"
                size="small"
                placeholder="结算账户"
              >
                <i slot="suffix" class="el-input__icon el-icon-search" @click="openAccount(scope.$index)"></i>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="money" label="收款金额" min-width="120">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.money"
                :controls="false"
                placeholder="收款金额"
                size="small"
                style="width: 100%"
                :min="0"
                @blur="moneyBlur"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="优惠金额" min-width="120">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.discountMoney"
                :controls="false"
                placeholder="优惠金额"
                style="width: 100%"
                size="small"
                :max="Number(scope.row.money)"
                :min="0"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="实际收款金额" min-width="120" align="center">
            <template slot-scope="scope">
              {{ $NP.minus(scope.row.money || 0, scope.row.discountMoney || 0) }}
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="200">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark" style="width: 100%" size="small" placeholder="备注"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="操作" width="140" align="center">
            <template slot-scope="scope">
              <el-button
                :disabled="form.accountList.length === 1"
                size="mini"
                type="text"
                @click="delAccount(scope.$index)"
              >
                删除
              </el-button>
              <!--              <el-button size="mini" type="text" @click="addAccount">-->
              <!--                新增-->
              <!--              </el-button>-->
            </template>
          </el-table-column>
        </el-table>
        <div class="table-b-div">
          <div class="table-b-div-cont" @click="addAccount">
            <el-button type="text" size="mini" @click="addAccount">
              <i class="el-icon-plus"></i>
              新增
            </el-button>
          </div>
        </div>
      </div>
      <div class="order_bottom">
        <p class="text">核销明细</p>
        <div class="Enunciate">
          <div class="Enunciate_cont clearfix">
            <div class="float_left">
              <span> 未核销金额：{{ $_common.formattedNumber(offSetNotTotal) }} </span>
              <span style="margin: 0 20px"> 本次核销金额：{{ $_common.formattedNumber(offSetTotal) }} </span>
              <span> 核销差额：{{ $_common.formattedNumber(offSetTotal - moneyTotal) }} </span>
            </div>
            <div class="float_right">
              <el-button size="mini" @click="offsetGet">自动核销</el-button>
            </div>
          </div>
        </div>
        <el-table ref="multipleTable" :data="tableData" style="width: 100%" @selection-change="selectionChange">
          <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
          <el-table-column prop="receiptTypeId" label="单据类型" min-width="100">
            <template slot-scope="scope">
              {{ scope.row.receiptTypeId === 6 ? "销售退货单" : scope.row.receiptTypeId === 1 ? "销售订单" : "" }}
            </template>
          </el-table-column>
          <el-table-column prop="no" label="单据编号" min-width="140"></el-table-column>
          <el-table-column prop="address" label="单据日期" min-width="120">
            <template slot-scope="scope">
              {{ $_common.formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="金额" min-width="100">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.receiveMoney) }}
            </template>
          </el-table-column>
          <el-table-column prop="address" label="未核销金额" min-width="100">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.notOffsetMoney) }}
            </template>
          </el-table-column>
          <el-table-column prop="address" label="本次核销金额" min-width="100">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.offsetMoney"
                :controls="false"
                placeholder="本次核销金额"
                :max="Number(scope.row.notOffsetMoney)"
                style="width: 100%"
                size="small"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="originNo" label="销售订单号" min-width="100">
            <template slot-scope="scope">
              <span class="click-div" @click="goOtherDetail(scope.row.originId)">
                {{ scope.row.originNo }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="customerName" label="往来单位名称" min-width="100"></el-table-column>
        </el-table>
      </div>
    </el-form>
    <AccountType
      v-if="account_show"
      :shop-id="form.shopId"
      :is-check="false"
      :is-show="account_show"
      @cancel="account_show = false"
      @confirm="accountsel"
    />
    <Receivable
      v-if="receivable_show"
      :id="form.customerId"
      :is-check="false"
      :is-show="receivable_show"
      @cancel="receivable_show = false"
      @confirm="receivablesel"
    />
    <!--    新增财务类型-->
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      name="新增财务类型"
      :visible.sync="show_model"
      width="40%"
      @close="show_model = false"
    >
      <el-form ref="form" :model="add_form" :rules="add_rule" size="small" label-width="100px">
        <el-form-item label="类型" prop="name">
          <el-input v-model="add_form.name" placeholder="请输入类型名称"></el-input>
        </el-form-item>
        <el-form-item label="归属单据" prop="link">
          <el-select v-model="add_form.link" placeholder="请选择">
            <el-option v-for="item in form_type" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否默认" prop="defaultStatus">
          <el-switch v-model="add_form.isDefault" :active-value="5" :inactive-value="4"></el-switch>
        </el-form-item>
        <el-form-item label="是否禁用" prop="enableStatus">
          <el-radio-group v-model="add_form.enableStatus">
            <el-radio :label="4">是</el-radio>
            <el-radio :label="5">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="show_model = false">取 消</el-button>
        <el-button type="primary" size="small" @click="addType"> 确 定 </el-button>
      </span>
    </el-dialog>
  </ContainerTit>
</template>

<script>
import Receivable from "@/component/Finance/Receivable";
import AccountType from "../AccountType";

import { getAllPayment } from "@/api/System";
import { getCustomerInfo } from "@/api/Customer";

import {
  addFinanceType,
  editFinanceType,
  getReceivedInfo,
  addReceived,
  editReceived,
  getTempReceivedData,
  getAllFinanceTypeNoPage,
  getReceiveInfo,
  getAllReceive,
  editPaid,
  addPaid,
} from "@/api/Finance";
import SelectShop from "@/component/goods/SelectShop.vue";
import SelectCustomer from "@/component/common/SelectCustomer.vue";
import dayjs from "dayjs";
export default {
  name: "AddReceipt",
  components: {
    Receivable,
    AccountType,
    SelectCustomer,
    SelectShop,
  },
  data() {
    const validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择收款单位"));
      } else {
        callback();
      }
    };
    const validateNo = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择原单据号"));
      } else {
        callback();
      }
    };
    const validateShopName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择店铺"));
      } else {
        callback();
      }
    };
    return {
      tableData: [],
      // 新增财务类型
      form_type: [
        {
          label: "应收单",
          value: 1,
        },
        {
          label: "应付单",
          value: 2,
        },
      ],
      show_model: false,
      add_form: {
        name: "",
        link: "",
        isDefault: 4,
        enableStatus: 5,
      },
      add_rule: {
        name: [{ required: true, message: "请输入类型名称" }],
        link: [{ required: true, message: "请选择归属单据" }],
      },
      loading: false,
      account_index: 0,
      financeTypeList: [],
      pay_type_list: [],
      code: "",
      account_show: false,
      receivable_show: false,
      no: "",
      form: {
        customerId: "",
        customerName: "",
        sourceNo: "",
        sourceNoMoney: "",
        currentAccountName: "",
        financeType: "",
        financeTypeId: "",
        shopId: "",
        money: "",
        shopName: "",
        receiptTime: "",
        createTime: "",
        accountList: [
          {
            accountId: "",
            accountNumber: "",
            accountName: "",
            money: "",
            discountMoney: "",
            finalMoney: "",
            payWay: "",
            remark: "",
          },
        ],
      },
      rules: {
        customerName: [{ required: true, validator: validateName }],
        sourceNo: [{ required: true, validator: validateNo }],
        shopName: [{ required: true, validator: validateShopName }],
      },
      account_id: "",
      isLook: false,
      isEdit: false,
      show_shop: false,
      choose_data: [],
      check_money: 0, // 勾选后合计
      id: "",
      time: "",
      delAccountList: [],
      flag: false,
      activeName: "one",
    };
  },
  computed: {
    // 核销总金额
    total() {
      if (!this.form.accountList.length) {
        return 0;
      } else if (this.form.accountList.length === 1) {
        return Number(this.form.accountList[0].money);
      } else {
        let sum = 0;
        this.form.accountList.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.money));
        });
        return sum;
      }
    },
    // 收款总金额
    moneyTotal() {
      if (!this.form.accountList.length) {
        return 0;
      } else if (this.form.accountList.length === 1) {
        return this.$NP.minus(Number(this.form.accountList[0].money), Number(this.form.accountList[0].discountMoney));
      } else {
        let sum = 0;
        this.form.accountList.forEach((item) => {
          const money = this.$NP.minus(Number(item.money), Number(item.discountMoney));
          sum = this.$NP.plus(sum, money);
        });
        return sum;
      }
    },
    // 核销总金额
    offSetTotal() {
      if (!this.choose_data.length) {
        return 0;
      } else if (this.choose_data.length === 1) {
        return Number(this.choose_data[0].offsetMoney);
      } else {
        let sum = 0;
        this.choose_data.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.offsetMoney));
        });
        return sum;
      }
    },
    // 未核销总金额
    offSetNotTotal() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return Number(this.tableData[0].offsetMoney);
      } else {
        let sum = 0;
        this.tableData.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.offsetMoney));
        });
        return sum;
      }
    },
  },
  created() {
    this.initial();
  },
  activated() {
    if (this.$_isInit()) return;
    this.initial();
  },
  methods: {
    // 初始化
    async initial() {
      await this.getAllFinanceType();
      await this.getAllPayment();
      this.isLook = this.$route.path.indexOf("LookReceipt") > -1;
      this.isEdit = this.$route.path.indexOf("editReceipt") > -1;
      // 收款人默认当前账户
      this.form.currentAccountName = this.userName;
      if (this.$route.params.createTime) {
        this.form.createTime = this.$route.params.createTime;
      }
      if (this.$route.params.id) {
        // 编辑收款单
        this.account_id = this.$route.params.id;
        //  获取详情 收款单详情
        await this.getReceivedInfo();
      } else if (this.$route.query.id) {
        // 从应收单点击收款按钮进来 新增收款单
        //  获取应收单详情
        this.id = this.$route.query.id;
        this.time = this.$route.query.createTime;
        await this.getReceiveInfo();
      } else {
        // 新增收款单
        await this.getTempReceivedData(); // 获取暂存数据
        this.form.receiptTime = new Date().getTime();
      }

      if (this.$route.query.customerId) {
        await this.getCustomerInfo(this.$route.query.customerId);
      }
    },
    goOtherDetail(orderId) {
      this.$router.push("/order/manageO/OrderDetails/1/" + orderId);
    },
    // 批量选择
    selectionChange(val) {
      this.choose_data = val;
      let num = 0;
      this.choose_data.forEach((item) => {
        num += Number(item.offsetMoney);
      });
      // console.log("勾选余额", num);
      this.check_money = num;
    },
    // 获取应收单详情
    async getReceiveInfo(obj) {
      const { data } = await getReceiveInfo({
        id: this.id,
        createTime: this.time,
      });
      this.id = data.id;
      this.form.customerId = data.customerId;
      this.form.customerName = data.customerName;

      this.form.sourceNo = data.no;
      this.form.sourceNoMoney = data.receiveMoney;

      this.form.shopName = data.shopName;
      this.form.shopId = data.shopId;
      this.form.receiptTime = data.receiptTime * 1000;
      if (data.receiptTypeId === 6) {
        const finance = this.financeTypeList.find((item) => {
          return item.name === "销售退货退款";
        });
        this.form.financeTypeId = finance.id;
        this.form.financeType = finance.name;
      }
      this.form.accountList = [
        {
          accountId: 0,
          accountNumber: "",
          accountName: "",
          money: data.receiveMoney,
          discountMoney: "",
          finalMoney: "",
          payWay: "",
          remark: "",
        },
      ];
      this.tableData = [
        {
          ...data,
          offsetMoney: Number(data.notOffsetMoney),
        },
      ];
      // 在应收单列表点击收款按钮打开的新增收款单页面，核销明细自动核销。
      setTimeout(() => {
        this.offsetGet();
      }, 200);
      // await this.getCustomerInfo(data.customerId);
    },
    // 获取客户详情
    async getCustomerInfo(id) {
      const { data } = await getCustomerInfo(id);
      this.form.money = data.money || 0;
      this.form.customerId = data.id;

      this.form.customerName = data.name;
      await this.getAllReceive();
    },
    //  应收单列表
    async getAllReceive() {
      let params = {
        page: 1,
        pageSize: 999,
        customerId: this.form.customerId,
        offsetStatus: [3, 4],
        moneyType: 1,
        // auditStatus: 2,
        // financeTypeId: this.form.financeTypeId,
      };
      const { data } = await getAllReceive(params);
      this.tableData = data.map((item) => {
        return { ...item, offsetMoney: Number(item.notOffsetMoney) };
      });
    },
    // 新增财务类型
    async addType() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          let target = {};
          if (!this.is_edit) {
            target = await addFinanceType({
              ...this.add_form,
            });
          } else {
            target = await editFinanceType(this.pay_id, {
              ...this.add_form,
            });
          }
          const data = target;

          this.show_model = false;
        }
      });
    },
    // 选择商铺
    selShop(val, row) {
      this.form.shopName = row[0].name;
      // this.form.shopId = row[0].id
    },
    receivablesel(val) {
      this.form.sourceNo = val[0].no;
      this.form.sourceNoMoney = val[0].receiveMoney;
      this.form.shopName = val[0].shopName;
      this.form.shopId = val[0].shopId;
    },
    //  收款单详情
    async getReceivedInfo() {
      const { data } = await getReceivedInfo({
        id: this.account_id,
        createTime: this.form.createTime,
      });

      this.form = {
        ...data,
        receiptTime: data.receiptTime * 1000,
        accountList: data.accountList,
      };
      if (data.offsetDate) {
        this.tableData = data.offsetDate;
        this.choose_data = data.offsetDate;
        setTimeout(() => {
          this.tableData.forEach((item) => {
            if (item.offsetMoney > 0) {
              this.$refs.multipleTable.toggleRowSelection(item);
            }
          });
        }, 200);
      }
    },
    // 暂存数据
    async temData(tempSave) {
      let receiptOffsetData = this.choose_data.map((item) => {
        return {
          receiveReceiptId: item.id,
          offsetMoney: item.offsetMoney,
        };
      });
      const params = {
        ...this.form,
        receiptTime: parseInt(this.form.receiptTime / 1000),
        tempSave: tempSave,
        receiptOffsetData: receiptOffsetData,
      };
      this.loading = true;
      const data = await addReceived(params);
      this.loading = false;

      this.$message({
        message: "暂存成功",
        type: "success",
      });
      this.$closeCurrentGoEdit("/Finance/Receivable/ReceiptList");
    },
    //  添加
    async addData(tempSave) {
      this.$refs["base_form"].validate(async (valid) => {
        if (valid) {
          if (!tempSave) {
            if (this.flag) {
              this.form.accountList = [...this.form.accountList, ...this.delAccountList];
            }
            let isSub = true;
            for (let i in this.form.accountList) {
              let item = this.form.accountList[i];
              if (!item.accountId) {
                isSub = false;
                this.$message.warning("请选择结算账户");
                break;
              }
              if (!item.money) {
                isSub = false;
                this.$message.warning("请输入收款金额");
                break;
              }
              /* if (!item.payWay) {
                  isSub = false;
                  this.$message.warning("请选择结算方式");
                  break;
                }*/
              if (this.offSetTotal > this.total) {
                isSub = false;
                this.$message.warning("核销金额不能大于收款金额");
                break;
              }
            }
            if (!isSub) {
              return;
            }
          }
          if (this.choose_data.length) {
            let receiptOffsetData = this.choose_data.map((item) => {
              return {
                receiveReceiptId: item.id,
                offsetMoney: item.offsetMoney,
                receiveCreateTime: item.createTime,
              };
            });
            const params = {
              ...this.form,
              receiptTime: parseInt(this.form.receiptTime / 1000),
              tempSave: tempSave,
              receiptOffsetData: receiptOffsetData,
              sourceId: this.id,
            };
            this.loading = true;
            try {
              if (this.account_id) {
                const data = await editReceived(this.account_id, params);
              } else {
                const data = await addReceived(params);
              }
              this.loading = false;
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              this.$closeCurrentGoEdit("/Finance/Receivable/ReceiptList");
            } catch {
              this.loading = false;
            }
          } else {
            if (this.form.financeTypeId === 1) {
              this.form.financeType = "销售退货退款";
            }
            if (this.form.financeTypeId === 2) {
              this.form.financeType = "销售收款";
            }
            if (this.form.financeTypeId === 3) {
              this.form.financeType = "预存收款";
            }
            const form = this.$_common.deepClone(this.form);
            delete form.sourceNo;
            const params = {
              ...form,
              receiptTime: parseInt(this.form.receiptTime / 1000),
              tempSave: tempSave,
              financeTypeId: this.form.financeTypeId,
              financeType: this.form.financeType,
            };
            this.$confirm("确定要将该单据作为预收款吗？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(async () => {
                this.loading = true;
                try {
                  if (this.account_id) {
                    const data = await editReceived(this.account_id, params);
                  } else {
                    const data = await addReceived(params);
                  }
                  this.loading = false;
                  this.$message({
                    type: "success",
                    message: "操作成功!",
                  });
                  this.$closeCurrentGoEdit("/Finance/Receivable/ReceiptList");
                } catch {
                  this.loading = false;
                }
              })
              .catch(() => {
                this.loading = false;
              });
          }
        }
      });
    },
    //  获取暂存数据
    async getTempReceivedData() {
      const { data } = await getTempReceivedData();

      if (JSON.stringify(data) === "{}") return;
      this.form = {
        ...data,
        receiptTime: data.receiptTime * 1000,
        accountList: data.accountList.map((item) => {
          return {
            ...item,
            payWay: parseInt(item.payWay),
          };
        }),
      };
      if (data.offsetDate) {
        this.tableData = data.offsetDate;
        this.choose_data = data.offsetDate;
        setTimeout(() => {
          this.tableData.forEach((item) => {
            this.$refs.multipleTable.toggleRowSelection(item);
          });
        }, 200);
      }
    },
    openAccount(index) {
      this.account_index = index;
      if (this.form.shopId) {
        this.account_show = true;
      } else {
        this.$message.warning("请选择所属店铺");
      }
    },
    // 结算账户
    accountsel(val) {
      this.form.accountList[this.account_index].accountId = val[0].id;
      this.form.accountList[this.account_index].accountNumber = val[0].accountNumber;
      this.form.accountList[this.account_index].accountName = val[0].name;
    },
    // 类型
    typeChange(val) {
      const target = this.financeTypeList.find((item) => {
        return item.id === val;
      });
      this.form.financeType = target.name;
    },
    //  获取财务类型
    async getAllFinanceType() {
      const { data } = await getAllFinanceTypeNoPage(1);

      this.financeTypeList = data;
      const finance = data.find((item) => {
        return item.name === "销售收款";
      });
      this.form.financeTypeId = finance.id;
      this.form.financeType = finance.name;
    },
    addAccount() {
      this.form.accountList.push({
        accountId: "",
        accountNumber: "",
        accountName: "",
        money: "",
        discountMoney: "",
        finalMoney: "",
        payWay: "",
        remark: "",
      });
    },
    delAccount(index) {
      if (this.account_id) {
        this.flag = true;
        if (this.form.accountList[index].accountId && this.form.accountList[index].money) {
          this.form.accountList[index].deleteStatus = 4;
          this.delAccountList.push(this.form.accountList[index]);
        }
      }
      console.log(this.delAccountList);
      this.form.accountList.splice(index, 1);
    },
    // 获取支付方式
    async getAllPayment() {
      const data = await getAllPayment({
        page: 1,
        pageSize: 99,
      });

      this.pay_type_list = data.data;
    },
    //  客户选择
    customerSel(val, row) {
      this.form.customerId = row[0].id;
      this.form.money = row[0].money;
      this.form.sourceNo = "";
      this.form.shopName = row[0].shopName;
      this.form.shopId = row[0].shopId || "";
      this.getAllReceive();
    },
    // 自动核销
    offsetGet() {
      let total = 0;
      for (let i = 0; i < this.tableData.length; i++) {
        let item = this.tableData[i];
        // 把本次核销金额相加
        total += Number(item.offsetMoney);
        // 勾选可以核销的数据
        this.$refs.multipleTable.toggleRowSelection(item);
        // 如果合计核销金额大于收款总金额，停止循环，并把停止循环前的一条核销数据修改为差值
        if (total > this.moneyTotal || total === this.moneyTotal) {
          item.offsetMoney = this.moneyTotal - (total - item.offsetMoney);
          break;
        }
      }
    },
    // 收款金额失去焦点
    moneyBlur() {
      // 收款金额失去焦点后自动核销
      this.offsetGet();
      // 收款金额失去焦点后重新核销
      // this.$refs.multipleTable.clearSelection();
    },
  },
};
</script>
<style lang="scss" scoped>
.creat-custorm {
  color: #1890ff;
  font-size: 12px;
}
.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.table-b-div {
  height: 80px;
  width: 100%;
  padding: 16px 24px 32px 25px;
  border: 1px solid #ebeef5;
  text-align: center;
  line-height: 40px;
  border-top: 0 none;
  cursor: pointer;
  .table-b-div-cont {
    border: 1px dashed #2153d4;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.Enunciate {
  width: 100%;
  height: 72px;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  padding: 16px 24px;
  border-top: 1px solid #dee2ee;
  .Enunciate_cont {
    background-color: #fa6400;
    border-radius: 3px;
    padding: 0 24px;
  }
}
.btn-top-div {
  position: absolute;
  right: 20px;
  top: 45px;
  z-index: 999;
}
</style>
<style>
.AddReceipt {
  background-color: #fff;
}
.AddReceipt .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.AddReceipt .is-active {
  font-weight: 700;
  color: #000;
}
.AddReceipt .el-tabs__nav {
  margin-left: 24px;
}
</style>
