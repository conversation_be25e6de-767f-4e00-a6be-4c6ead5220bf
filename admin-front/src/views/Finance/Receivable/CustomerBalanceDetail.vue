<!--资金转账-->
<template>
  <ContainerQuery>
    <div slot="left">
      <el-button size="small" type="primary" plain @click="getData(1)"> 导出 </el-button>
    </div>
    <div slot="more">
      <el-form size="small" :inline="true">
        <el-form-item>
          <SelectCustomer v-model="searchData.custormerName" :clearable="false" @change="customerSel" />
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <span style="color: #000">-->
        <!--            {{ searchData.contactName || "暂无" }}-->
        <!--          </span>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="电话">-->
        <!--          <span style="color: #000">-->
        <!--            {{ searchData.mobile || "暂无" }}-->
        <!--          </span>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <el-date-picker
            v-model="time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="orderDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <!--    <div slot="left"><el-button type="primary">导出</el-button></div>-->
    <div v-if="tableData.length">
      <el-table :data="tableData">
        <el-table-column prop="id" label="ID" fixed="left" width="60"></el-table-column>
        <el-table-column prop="receiptTime" label="单据日期" min-width="140" fixed="left">
          <template slot-scope="scope">
            {{ scope.row.receiptTime ? $_common.formatDate(scope.row.receiptTime, "yyyy-MM-dd hh:mm:ss") : "" }}
          </template>
        </el-table-column>
        <el-table-column prop="sourceNo" label="单据编号" fixed="left" min-width="160">
          <template slot-scope="scope">
            <span class="click-div" @click="goNoDetail(scope.row.financeType, scope.row)">
              {{ scope.row.sourceNo }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkList.indexOf('业务类别') > -1"
          prop="financeType"
          label="业务类别"
          min-width="120"
        ></el-table-column>
        <el-table-column
          v-if="checkList.indexOf('源销货订单号') > -1"
          prop="originNo"
          label="源销货订单号"
          min-width="180"
        >
          <template slot-scope="scope">
            <span
              v-if="$accessCheck($Access.newOrderLitGetOrderInfoById)"
              class="click-div"
              @click="originNoGoNoDetail(scope.row.financeType, scope.row)"
            >
              {{ scope.row.originNo }}
            </span>
          </template>
        </el-table-column>
        <el-table-column v-if="checkList.indexOf('销售金额') > -1" prop="salesAmount" label="销售金额" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.salesAmount !== "--" ? $_common.formattedNumber(scope.row.salesAmount) : "--" }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkList.indexOf('优惠金额') > -1"
          prop="discountMoney"
          label="优惠金额"
          min-width="120"
        >
          <template slot-scope="scope">
            <span style="color: #ff4040">
              {{ scope.row.discountMoney !== "--" ? $_common.formattedNumber(scope.row.discountMoney) : "--" }}
            </span>
          </template>
        </el-table-column>
        <!--      <el-table-column-->
        <!--        prop="customerAmount"-->
        <!--        label="客户承担金额(元)"-->
        <!--        -->
        <!--        min-width="120"-->
        <!--      >-->
        <!--        <template slot-scope="scope">-->
        <!--          {{ $_common.formattedNumber(scope.row.customerAmount) || 0 }}-->
        <!--        </template>-->
        <!--      </el-table-column>-->
        <el-table-column
          v-if="checkList.indexOf('应收金额') > -1"
          prop="receivableAmount"
          label="应收金额"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.receivableAmount !== "--" ? $_common.formattedNumber(scope.row.receivableAmount) : "--" }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkList.indexOf('实收金额') > -1"
          prop="actualReceivableAmount"
          label="实收金额"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.actualReceivableAmount) }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkList.indexOf('实收金额') > -1"
          prop="actualReceivableAmount"
          label="实退金额"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.refundMoney) }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkList.indexOf('应收余额') > -1"
          prop="receivableBalance"
          label="应收余额"
          fixed="right"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.receivableBalance) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" show-overflow-tooltip fixed="right" min-width="200">
          <template slot="header" slot-scope="scope">
            <span v-if="false">{{ scope.$index }}</span>
            <span class="operation">备注</span>
            <el-popover popper-class="custom-table-checkbox" trigger="click">
              <el-checkbox-group v-model="checkList">
                <el-checkbox
                  v-for="(item, index) in columns"
                  :key="index"
                  :label="item.label"
                  @change="change"
                ></el-checkbox>
              </el-checkbox-group>
              <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <el-row class="total-table">
        <el-col :span="12" class="total-col">合计</el-col>
        <el-col :span="6" class="total-col">
          实际收款金额：{{ $_common.formattedNumber(actualReceiveTotal) || 0 }}
        </el-col>
        <el-col :span="6" class="total-col">
          应收款余额：{{ $_common.formattedNumber(shouldReceiveTotal) || 0 }}
        </el-col>
      </el-row>
      <FooterPage
        :page-size="pageSize"
        :total-page.sync="total"
        :current-page.sync="page"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      ></FooterPage>
    </div>
    <div v-else class="empty-view">
      <img class="empty-img" :src="require('@/assets/img/no_enterprise.png')" />
      <p>快去选择客户进行查看吧！</p>
    </div>
  </ContainerQuery>
</template>

<script>
import SelectCustomer from "@/component/common/SelectCustomer";
import { getCustomerInfo } from "@/api/Customer";
import {
  exportgetAllCustomerBalance,
  getAllCustomerBalance,
  getAllCustomerBalanceDetail,
  exportgetAllCustomerBalanceDetail,
} from "@/api/Finance";

export default {
  name: "CustomerBalanceDetail",
  components: {
    SelectCustomer,
  },
  data() {
    return {
      checkList: ["业务类别", "源销货订单号", "销售金额", "优惠金额", "应收金额", "实收金额", "应收余额"],
      columns: [
        {
          label: "业务类别",
        },
        {
          label: "源销货订单号",
        },
        {
          label: "销售金额",
        },
        {
          label: "优惠金额",
        },
        {
          label: "应收金额",
        },
        {
          label: "实收金额",
        },
        {
          label: "应收余额",
        },
      ],
      shouldReceiveTotal: 0,
      actualReceiveTotal: 0,
      dialogFormVisible: false,
      time: [],
      searchData: {
        customerId: "",
        custormerName: "",
        contactName: "",
        mobile: "",
        start: "",
        area: {},
        end: "",
      },
      customer_show: false,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  async created() {
    if (this.$route.query.id) {
      //  获取客户的id
      this.searchData.customerId = this.$route.query.id;
      await this.getCustomerInfo();
      await this.getData();
    }
  },
  async activated() {
    if (this.$_isInit()) return;
    if (this.$route.query.id) {
      //  获取客户的id
      this.searchData.customerId = this.$route.query.id;
      await this.getCustomerInfo();
      await this.getData();
    } else {
      this.dialogFormVisible = true;
    }
  },
  methods: {
    goSourceNoDetail(type, id) {
      switch (type) {
        case "销售收款":
          // 销售收款
          break;
        case "销售单":
          //  订单详情
          this.$router.push("/order/manageO/OrderDetails/1/" + id);
          break;
        case "客户退款":
          // 客户退款
          break;
        case "银行打款收款":
          // 客户退款
          break;
        case "订单完结退款":
          // 客户退款
          break;
      }
    },
    goNoDetail(type, row) {
      switch (type) {
        case "销售收款":
          // 销售收款
          this.$router.push(`/Finance/Receivable/LookReceipt/${row.sourceId}/${row.createTime}`);
          break;
        case "销售单":
          //  订单详情
          // this.$router.push("/order/manageO/OrderDetails/1/" + id);
          break;
        case "客户退款":
          // 客户退款
          break;
        case "订单完结退款":
          this.$router.push(`/Finance/Cashier/RefundDetail/${row.sourceId}/${row.createTime}`);
          // 客户退款
          break;
        case "银行打款收款":
          this.$router.push(`/Finance/Receivable/LookReceipt/${row.sourceId}/${row.createTime}`);
          // 客户退款
          break;
        case "订单退货退款":
          this.$router.push(`/Finance/Cashier/RefundDetail/${row.sourceId}/${row.createTime}`);
          break;
        case "线上支付收款":
          this.$router.push(`/Finance/Receivable/LookReceipt/${row.sourceId}/${row.createTime}`);
          break;
      }
    },
    originNoGoNoDetail(type, row) {
      switch (type) {
        case "销售退货单":
          this.$router.push("/stock/OutIn/storageInfo/" + row.originId);
          break;
        case "订单退货退款":
          this.$router.push("/stock/OutIn/storageInfo/" + row.originId);
          break;
        // case "销售单":
        //   this.$router.push("/stock/OutIn/outgoingInfo/" + row.sourceId);
        //   break;
      }
      this.$router.push("/order/manageO/OrderDetails/1/" + row.originId);
      // if (type === "销售退货单" || type === "订单退货退款") {
      //   this.$router.push("/stock/OutIn/storageInfo/" + row.originId);
      // } else if (type === "销售单") {
      //   this.$router.push("/stock/OutIn/outgoingInfo/" + row.sourceId);
      // } else {
      //   this.$router.push("/order/manageO/OrderDetails/1/" + row.originId);
      // }
    },
    confirm() {
      if (this.searchData.customerId) {
        this.dialogFormVisible = false;
        this.getData();
      } else {
        this.$message("请选择客户");
      }
    },
    // 弹窗取消后关闭当前页面
    close() {
      this.dialogFormVisible = false;
    },
    //  获取客户详情
    async getCustomerInfo() {
      const { data } = await getCustomerInfo(this.searchData.customerId);
      this.searchData = {
        custormerName: data.name,
        contactName: data.contact[0].name,
        customerId: data.id,
        mobile: data.mobile,
        start: "",
        area: data.area,
        end: "",
      };
    },
    // 获取列表
    async getData(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        customerId: this.searchData.customerId,
        start: this.searchData.start,
        end: this.searchData.end,
      };
      if (exports) {
        params.export = 1;
        const target = await exportgetAllCustomerBalanceDetail({
          ...params,
        });
      } else {
        const { data, pageTotal, openingBalance, endingBalance, actualReceiveTotal, shouldReceiveTotal } =
          await getAllCustomerBalanceDetail({
            ...params,
          });
        this.tabelData = data;
        this.total = pageTotal;
        this.tableData = data;
        this.tableData.push({
          no: "期末余额",
          receivableBalance: endingBalance,
        });
        this.tableData.unshift({
          no: "期初余额",
          receivableBalance: openingBalance,
        });
        this.shouldReceiveTotal = shouldReceiveTotal;
        this.actualReceiveTotal = actualReceiveTotal;
        this.total = pageTotal;
      }
      // const {
      //   data,
      //   pageTotal,
      //   openingBalance,
      //   endingBalance,
      //   actualReceiveTotal,
      //   shouldReceiveTotal,
      // } = await getAllCustomerBalanceDetail({
      //   ...params,
      // });
    },
    //   时间
    orderDate(val) {
      if (val && val.length) {
        this.searchData.start = val[0] / 1000;
        this.searchData.end = val[1] / 1000 + 86399;
      } else {
        this.searchData.start = "";
        this.searchData.end = "";
      }
      this.pageChange(1);
    },
    //  获取客户
    customerSel(val, list) {
      this.searchData.customerId = list[0].id;
      // this.searchData.custormerName = list[0].name;
      this.searchData.contactName = list[0].contact[0].name;
      this.searchData.mobile = list[0].mobile;
      this.searchData.area = list[0].area;
      this.pageChange(1);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
  },
};
</script>
<style scoped lang="scss">
.total-table {
  border: 1px solid #eee;
  font-size: 13px;
}
.total-col {
  border-right: 1px solid #eee;
  padding: 5px 12px;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
