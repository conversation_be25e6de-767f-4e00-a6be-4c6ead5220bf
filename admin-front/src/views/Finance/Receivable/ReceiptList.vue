<template>
  <ContainerQuery>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示：1、在线支付订单会自动生成收款单。 2、银行打款订单会在订单审核后自动生成收款单。
      3、收款申请单审核后会自动生成收款单。
    </div>
    <div slot="left">
      <el-dropdown
        v-if="$accessCheck($Access.ReceiptListAddReceived)"
        size="small"
        type="primary"
        split-button
        @click="$router.push('/Finance/Receivable/AddReceipt')"
      >
        新增收款单
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <div class="dropdown-div" @click="getAllReceived(1)">导出</div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!--      <el-button-->
      <!--        v-if="$accessCheck($Access.ReceiptListAddReceived)"-->
      <!--        slot="left"-->
      <!--        size="small"-->
      <!--        type="primary"-->
      <!--        @click="$router.push('/Finance/Receivable/AddReceipt')"-->
      <!--      >-->
      <!--        新增收款单-->
      <!--      </el-button>-->
      <!--      <el-button @click="getAllReceived(1)">导出</el-button>-->
    </div>

    <div v-if="$accessCheck($Access.ReceiptListSearch)" slot="more">
      <el-form size="small" :inline="true">
        <el-form-item>
          <el-input
            v-model="searchDate.keyword"
            clearable
            style="width: 220px"
            placeholder="单据编号"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="searchDate.time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="收款单开始时间"
            end-placeholder="收款单结束时间"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchDate.financeTypeId"
            style="width: 150px"
            clearable
            placeholder="选择类型"
            @change="pageChange(1)"
          >
            <el-option
              v-for="(item, index) in customerTypeList"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchDate.auditStatus"
            style="width: 150px"
            clearable
            placeholder="单据状态"
            @change="pageChange(1)"
          >
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <SelectCustomer v-model="customer_name" @clear="customerClear" @change="customerSel" />
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="payment_list" @row-dblclick="goDetail">
      <el-table-column prop="id" label="ID" min-width="80" fixed="left"></el-table-column>
      <el-table-column prop="no" label="单据号" min-width="180" fixed="left">
        <template slot-scope="scope">
          <span v-if="$accessCheck($Access.ReceiptListGetReceivedInfo)" class="click-div" @click="goDetail(scope.row)">
            {{ scope.row.no }}
          </span>
          <span v-else>{{ scope.row.no }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sourceNo" label="源单据号" min-width="180">
        <template slot-scope="scope">
          <span class="click-div" @click="SalesDetalis(scope.row)">
            {{ scope.row.sourceNo }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="receiptTime" label="单据日期" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.receiptTime, "yyyy-MM-dd") }}
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="客户" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.customerName || "收银用户" }}
        </template>
      </el-table-column>
      <el-table-column v-if="gatheringFlag" prop="financeType" label="收款类型" min-width="100"></el-table-column>
      <el-table-column v-if="moneyFlag" prop="totalMoney" label="金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalMoney, 2) }}
        </template>
      </el-table-column>
      <el-table-column v-if="discountsFlag" prop="totalDiscountMoney" label="优惠金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalDiscountMoney) }}
        </template>
      </el-table-column>
      <el-table-column v-if="practicalFlag" prop="totalFinalMoney" label="实收金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalFinalMoney) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="correlationFlag"
        prop="shopName"
        label="相关店铺"
        min-width="120"
        :show-overflow-tooltip="true"
      ></el-table-column>

      <el-table-column v-if="auditFlag" prop="auditStatus" label="审核状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 2" class="success-status"> 已审核 </span>
          <span v-else class="info-status">待审核</span>
        </template>
      </el-table-column>
      <el-table-column width="180" fixed="right">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <div class="btn-div">
            <el-button
              v-if="$accessCheck($Access.ReceiptListUpdateReceivedStatus)"
              :disabled="parseInt(scope.row.auditStatus) === 2"
              type="text"
              @click="updateAuditStatus(scope.row)"
            >
              审核
            </el-button>
            <el-button
              v-if="$accessCheck($Access.ReceiptListGetReceivedInfo)"
              :disabled="parseInt(scope.row.auditStatus) === 2"
              type="text"
              @click="$router.push(`/Finance/Receivable/editReceipt/${scope.row.id}/${scope.row.createTime}`)"
            >
              编辑
            </el-button>
            <el-dropdown v-if="parseInt(scope.row.auditStatus) !== 2 || (scope.row.extends && scope.row.extends.image)">
              <span class="el-dropdown-link">
                更多
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="parseInt(scope.row.auditStatus) !== 2">
                  <div class="dropdown-div" @click="deleteReceived(scope.row.id)">删除</div>
                </el-dropdown-item>
                <el-dropdown-item v-if="scope.row.extends && scope.row.extends.image">
                  <div class="dropdown-div" @click="extendsOpen(scope.row.extends.image)">查看凭证</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <!-- <el-image
              v-if="scope.row.extends"
              style="width: 66px; height: 16px; display: inline-block"
              :src="scope.row.extends.image"
              :preview-src-list="[scope.row.extends.image]"
            >
              <div slot="error" style="opacity: 0">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>-->
          </div>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <div v-if="show_extends_img" class="img-big-out">
      <span class="close-icon el-icon-circle-close" @click="show_extends_img = false"></span>
      <img :src="extends_img" alt="" />
    </div>
  </ContainerQuery>
</template>

<script>
import SelectCustomer from "@/component/common/SelectCustomer";
import {
  getAllFinanceTypeNoPage,
  getAllReceived,
  // ReceivedSearch,
  deleteReceived,
  updateReceivedStatus,
  exportsgetAllReceived,
} from "@/api/Finance";
import { exportGetAllOrder, getAllOrder } from "@/api/Order";

export default {
  name: "ReceiptList",
  components: {
    SelectCustomer,
  },
  data() {
    return {
      show_extends_img: false,
      extends_img: "",
      pic_show: false,
      customerTypeList: [],
      customerType: [],
      mobile: "",
      customer_name: "",
      searchDate: {
        keyword: "",
        auditStatus: "",
        financeTypeId: "",
        time: "",
        start: "",
        end: "",
        customerId: "",
      },
      customer_show: false,
      payment_list: [],
      statusList: [
        { value: 1, label: "待审核" },
        { value: 2, label: "已审核" },
      ],
      total: 0,
      page: 1,
      pageSize: 10,
      checkList: ["收款类型", "金额", "优惠金额", "实际收款金额", "相关店铺", "审核状态"],
      columns: [
        {
          label: "收款类型",
        },
        {
          label: "金额",
        },
        {
          label: "优惠金额",
        },
        {
          label: "实际收款金额",
        },
        {
          label: "相关店铺",
        },
        {
          label: "审核状态",
        },
      ],
      gatheringFlag: true,
      moneyFlag: true,
      discountsFlag: true,
      practicalFlag: true,
      correlationFlag: true,
      auditFlag: true,
    };
  },
  created() {
    this.getAllReceived();
    this.getAllFinanceType();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    extendsOpen(src) {
      this.extends_img = src;
      this.show_extends_img = true;
    },
    goDetail(row) {
      if (!this.$accessCheck(this.$Access.newOrderLitGetOrderInfoById)) {
        return;
      }
      this.$router.push(`/Finance/Receivable/LookReceipt/${row.id}/${row.createTime}`);
    },
    SalesDetalis(row) {
      if (row.sourceId) {
        this.$router.push(`/order/manageO/OrderDetails/1/${row.sourceId}`);
      }
    },
    //  获取财务类型
    async getAllFinanceType() {
      const { data } = await getAllFinanceTypeNoPage(1);

      this.customerTypeList = data;
    },
    // 选择客户
    customerSel(val, list) {
      this.searchDate.customerId = list[0].id;
      this.pageChange(1);
    },
    customerClear() {
      this.searchDate.customerId = "";
      this.customer_name = "";
      this.pageChange(1);
    },
    //  获取列表
    async getAllReceived(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        financeTypeId: this.searchDate.financeTypeId,
        start: this.searchDate.start,
        end: this.searchDate.end,
        auditStatus: this.searchDate.auditStatus,
        customerId: this.searchDate.customerId,
        no: this.searchDate.keyword,
      };
      if (exports) {
        params.export = 1;
        const target = await exportsgetAllReceived({
          ...params,
        });
      } else {
        const { data, pageTotal } = await getAllReceived({
          ...params,
        });
        this.payment_list = data;
        this.total = pageTotal;
      }
    },
    //  收款单搜索
    // async ReceivedSearch() {
    //   const { data, pageTotal } = await ReceivedSearch({
    //     page: this.page,
    //     pageSize: this.pageSize,
    //     keyword: this.searchDate.keyword,
    //     financeTypeId: this.searchDate.financeTypeId,
    //     auditStatus: this.searchDate.auditStatus,
    //     customerId: this.searchDate.customerId,
    //     start: this.searchDate.start,
    //     end: this.searchDate.end,
    //   });
    //   this.payment_list = data;
    //   this.total = pageTotal;
    // },
    getData() {
      // const isKey = this.$_common.isSerch(this.searchDate);
      // if (isKey) {
      //   this.ReceivedSearch();
      // } else {
      //   this.getAllReceived();
      // }
      this.getAllReceived();
    },
    timeChange(val) {
      if (val && val.length) {
        this.searchDate.start = parseInt(val[0] / 1000);
        this.searchDate.end = parseInt(val[1] / 1000) + 86399;
      } else {
        this.searchDate.start = "";
        this.searchDate.end = "";
      }
      this.pageChange(1);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 审核订单
    updateAuditStatus(row) {
      this.$confirm("请确认审核该单据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateReceivedStatus({
          id: row.id,
          createTime: row.createTime,
        });

        this.$message({
          type: "success",
          message: "审核成功!",
        });
        this.getData();
      });
    },
    // 删除收款单
    deleteReceived(id) {
      this.$confirm("请确认是否删除该单据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await deleteReceived(id);
        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.getData();
      });
    },
    change() {
      this.gatheringFlag = this.checkList.some((item) => item === "收款类型");
      this.moneyFlag = this.checkList.some((item) => item === "金额");
      this.discountsFlag = this.checkList.some((item) => item === "优惠金额");
      this.practicalFlag = this.checkList.some((item) => item === "实际收款金额");
      this.correlationFlag = this.checkList.some((item) => item === "相关店铺");
      this.auditFlag = this.checkList.some((item) => item === "审核状态");
    },
  },
};
</script>
<style>
.btn-div .el-image__preview {
  opacity: 0;
}
</style>
<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
.img-big-out {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.5);
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 999999;
  text-align: center;
  .close-icon {
    position: absolute;
    cursor: pointer;
    top: 5vh;
    right: 150px;
    color: #ffffff;
    font-size: 40px;
  }
  img {
    display: inline-block;
    height: 90vh;
    margin-top: 5vh;
  }
}
</style>
