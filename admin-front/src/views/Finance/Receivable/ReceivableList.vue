<template>
  <!--  应收单列表-->
  <ContainerQuery>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示：应收单是在销售出库单审核后会自动生成，应收单为系统自动审核！
    </div>
    <div slot="left">
      <!--      <el-button-->
      <!--        type="primary"-->
      <!--        size="small"-->
      <!--        @click="$router.push('/Finance/Receivable/AddReceivable')"-->
      <!--      >-->
      <!--        新增应收单-->
      <!--      </el-button>-->
      <el-button type="primary" size="small" @click="getData(1)"> 导出 </el-button>
    </div>
    <el-form slot="more" :inline="true" size="small">
      <!--      <el-form-item>-->
      <!--        <el-input-->
      <!--          v-model="search_form.keyword"-->
      <!--          placeholder="客户名称"-->
      <!--          style="width: 220px"-->
      <!--          clearable-->
      <!--          @keyup.enter.native="pageChange(1)"-->
      <!--          @clear="pageChange(1)"-->
      <!--        >-->
      <!--          <el-button-->
      <!--            slot="append"-->
      <!--            icon="el-icon-search"-->
      <!--            @click="pageChange(1)"-->
      <!--          ></el-button>-->
      <!--        </el-input>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <SelectCustomer v-model="customer_name" @clear="customerClear" @change="customerSel" />
      </el-form-item>
      <el-form-item>
        <SelectShop
          v-model="search_form.shopId"
          placeholder="选择商铺"
          width="150"
          @change="selShop"
          @clear="shopClear"
        />
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="time"
          clearable
          type="daterange"
          value-format="timestamp"
          range-separator="-"
          start-placeholder="应收单开始时间"
          end-placeholder="应收单结束时间"
          @change="orderDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="search_form.auditStatus"
          style="width: 200px"
          clearable
          placeholder="单据状态"
          @change="pageChange(1)"
        >
          <el-option v-for="(item, index) in doc_Status" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="search_form.offsetStatus"
          multiple
          style="width: 200px"
          placeholder="核销状态"
          @change="pageChange(1)"
        >
          <el-option
            v-for="item in offset_Status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table :data="tableData">
      <el-table-column prop="id" align="left" label="ID" fixed="left" min-width="70"></el-table-column>
      <el-table-column
        prop="no"
        label="单据编号"
        align="cleft"
        fixed="left"
        min-width="190"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column label="客户" min-width="160" prop="customerCode">
        <template slot-scope="scope">
          <p>{{ scope.row.customerName || "收银用户" }}</p>
        </template>
      </el-table-column>
      <el-table-column label="客户编号" min-width="160" prop="customerCode">
        <template slot-scope="scope">
          <p>{{ scope.row.customerCode }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="originNo" label="源订单号" align="left" min-width="180" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span
            v-if="$accessCheck($Access.newOrderLitGetOrderInfoById) && scope.row.financeType === '销售单'"
            class="click-div"
            @click="$router.push('/order/manageO/OrderDetails/1/' + scope.row.originId)"
          >
            {{ scope.row.originNo }}
          </span>
          <span
            v-else-if="
              $accessCheck($Access.ReturnWarehousingOrderGetOrderReturn) && scope.row.financeType === '销售退货单'
            "
            class="click-div"
            @click="$router.push('/order/manageO/LookWareOrder/' + scope.row.originId)"
          >
            {{ scope.row.originNo }}
          </span>
          <span
            v-else-if="
              ($accessCheck($Access.ReturnWarehousingOrderGetOrderReturn) && scope.row.financeType === '客户退款') ||
              scope.row.financeType === '销售退款单'
            "
            class="click-div"
            @click="$router.push(`/Finance/Cashier/RefundDetail/${scope.row.id}/${scope.row.createTime}`)"
          >
            {{ scope.row.originNo }}
          </span>
          <span v-else>{{ scope.row.originNo }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="timeFlag" prop="createTime" label="创建时间" align="left" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="accountsReceivableTypeFlag"
        prop="financeType"
        label="应收类型"
        align="left"
        min-width="100"
      ></el-table-column>
      <el-table-column v-if="discountsFlag" prop="discountMoney" label="优惠金额" align="left" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.discountMoney) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="practicalFlag"
        prop="receiveMoney"
        label="销售金额"
        align="left"
        min-width="100"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.receiveMoney) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="shopFlag"
        prop="shopName"
        label="商铺"
        align="left"
        min-width="150"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column v-if="typeOfDocumentFlag" prop="receiptTypeId" label="单据类型" align="left" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.receiptTypeId === 1 ? "销售订单" : "销售退货单" }}
        </template>
      </el-table-column>
      <el-table-column v-if="stateFlag" prop="auditStatus" label="状态" align="left" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 2" class="success-status"> 已审核 </span>
          <span v-else class="info-status">待审核</span>
        </template>
      </el-table-column>
      <el-table-column v-if="stateFlagoffSet" prop="offsetStatus" label="核销状态" align="left" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.offsetStatus === 5" class="success-status"> 已核销 </span>
          <span v-else-if="scope.row.offsetStatus === 4" class="info-status"> 待核销 </span>
          <span v-else class="warning-status">部分核销</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" min-width="140" label="操作">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span class="operation">操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.ReceivableListUpdateReceiveStatus)"
            :disabled="parseInt(scope.row.auditStatus) === 2"
            type="text"
            @click="updateAuditStatus(scope.row)"
          >
            审核
          </el-button>
          <el-button
            v-if="$accessCheck($Access.ReceiptListAddReceived)"
            :disabled="
              parseInt(scope.row.auditStatus) !== 2 ||
              parseInt(scope.row.offsetStatus) === 5 ||
              Number(scope.row.receiveMoney) <= 0
            "
            type="text"
            @click="receiptPage(scope.row)"
          >
            收款
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import SelectShop from "@/component/goods/SelectShop.vue";
import SelectCustomer from "@/component/common/SelectCustomer";
import {
  getAllReceive,
  exportGetAllReceive,
  searchAllReceive,
  exportSearchAllReceive,
  updateReceiveStatus,
} from "@/api/Finance";
export default {
  name: "Receivable",
  components: {
    SelectShop,
    SelectCustomer,
  },
  data() {
    return {
      total: 0,
      page: 1,
      pageSize: 10,
      show_shop: false,
      customer_name: "",
      tableData: [],
      doc_Status: [
        { id: 1, name: "待审核" },
        { id: 2, name: "已审核" },
      ],
      doc_type: [
        { id: 1, name: "销售订单" },
        { id: 6, name: "销售退货单" },
      ],
      shop: "",
      time: [],
      search_form: {
        keyword: "",
        shopId: "",
        auditStatus: "",
        receiptTypeId: "",
        start: "",
        end: "",
        offsetStatus: [],
        customerId: "",
      },
      checkList: ["创建时间", "应收类型", "优惠金额", "实际应收金额", "商铺", "单据类型", "状态", "核销状态"],
      offset_Status: [
        { value: 4, label: "未核销" },
        { value: 5, label: "已核销" },
        { value: 3, label: "部分核销" },
      ],
      columns: [
        {
          label: "创建时间",
        },
        {
          label: "应收类型",
        },
        {
          label: "优惠金额",
        },
        {
          label: "实际应收金额",
        },
        {
          label: "商铺",
        },
        {
          label: "单据类型",
        },
        {
          label: "状态",
        },
        {
          label: "核销状态",
        },
      ],
      timeFlag: true,
      accountsReceivableTypeFlag: true,
      discountsFlag: true,
      shopFlag: true,
      typeOfDocumentFlag: true,
      stateFlag: true,
      practicalFlag: true,
      stateFlagoffSet: true,
    };
  },
  created() {
    this.getAllReceive();
    // 到期提示
    this.enterExpireTime();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllReceive();
  },
  methods: {
    // 选择客户
    customerSel(val, list) {
      this.search_form.customerId = list[0].id;
      this.pageChange(1);
    },
    customerClear() {
      this.search_form.customerId = "";
      this.customer_name = "";
      this.pageChange(1);
    },
    //  应收单列表
    async getAllReceive(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        customerId: this.search_form.customerId,
        offsetStatus: this.search_form.offsetStatus,
        keyword: this.search_form.keyword,
        shopId: this.search_form.shopId,
        auditStatus: this.search_form.auditStatus,
        receiptTypeId: this.search_form.receiptTypeId,
        start: this.search_form.start,
        end: this.search_form.end,
      };
      if (exports) {
        const data = await exportGetAllReceive({
          isExport: 1,
          ...params,
        });
      } else {
        const { data, pageTotal } = await getAllReceive(params);
        this.tableData = data;
        this.total = pageTotal;
      }
    },
    // //  应收单搜索
    // async searchAllReceive(exports) {
    //   let params = {
    //     page: this.page,
    //     pageSize: this.pageSize,
    //     keyword: this.search_form.keyword,
    //     shopId: this.search_form.shopId,
    //     auditStatus: this.search_form.auditStatus,
    //     receiptTypeId: this.search_form.receiptTypeId,
    //     start: this.search_form.start,
    //     end: this.search_form.end,
    //   };
    //   if (exports) {
    //     //
    //     const data = await exportSearchAllReceive({
    //       ...params,
    //       isExport: 1,
    //     });
    //     //
    //   } else {
    //     const { data, pageTotal } = await searchAllReceive(params);
    //
    //     this.tableData = data;
    //     this.total = pageTotal;
    //   }
    // },
    //  判断
    getData(exports) {
      const isKey = this.$_common.isSerch(this.search_form);
      if (isKey) {
        this.searchAllReceive(exports);
      } else {
        this.getAllReceive(exports);
      }
    },
    //  审核
    async updateAuditStatus(row) {
      this.$confirm("确定要进行收款操作吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateReceiveStatus({
          id: row.id,
          createTime: row.createTime,
        });

        this.pageChange();
        this.$message({
          type: "success",
          message: "操作成功,请新增收款单",
        });
      });
    },
    receiptPage(row) {
      this.$router.push(`/Finance/Receivable/AddReceipt?id=${row.id}&createTime=${row.createTime}`);
    },
    // 选择商铺
    selShop(val, row) {
      this.pageChange(1);
    },
    shopClear() {
      this.search_form.shopId = "";
      this.pageChange(1);
    },
    //  订单时间
    orderDate(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    pageChange(val) {
      this.page = val;
      this.getAllReceive();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    change() {
      this.timeFlag = this.checkList.some((item) => item === "创建时间");
      this.accountsReceivableTypeFlag = this.checkList.some((item) => item === "应收类型");
      this.discountsFlag = this.checkList.some((item) => item === "优惠金额");
      this.shopFlag = this.checkList.some((item) => item === "商铺");
      this.typeOfDocumentFlag = this.checkList.some((item) => item === "单据类型");
      this.stateFlag = this.checkList.some((item) => item === "状态");
      this.practicalFlag = this.checkList.some((item) => item === "实际应收金额");
      this.stateFlagoffSet = this.checkList.some((item) => item === "和消状态");
    },
  },
};
</script>

<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
