<template>
  <div class="ApplyReceiptInfo">
    <el-tabs v-model="activeName">
      <el-tab-pane label="收款申请单详情" name="one">
        <el-row style="padding-bottom: 13px">
          <el-col :span="24">
            <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">收款申请单信息</p>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">收款单位</span>
            <span class="form_right">{{ form.customerName }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">所属店铺</span>
            <span class="form_right">{{ form.shopName }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">当前应收</span>
            <span class="form_right">{{ form.receivable }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">收款人</span>
            <span class="form_right">{{ form.operatorName }}</span>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <div class="order_bottom">
      <p class="text">报损单</p>
      <el-table :data="form.receiptRequisitionAccountDate">
        <el-table-column type="index" label="序号" min-width="60" align="center"></el-table-column>
        <el-table-column prop="collectionAmount" label="收款金额" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.collectionAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="preferentialAmount" label="优惠金额" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.preferentialAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="actualAmount" label="实际收款金额" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.actualAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="accountName" label="结算账户" min-width="160"></el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200"></el-table-column>
      </el-table>
      <div class="Enunciate">
        <div class="Enunciate_cont clearfix">
          <div class="float_left">
            <span>收款总额:</span>
            <span>
              {{ $_common.formattedNumber(totalCollectionAmount) }}
            </span>
            <span style="margin: 0 10px"></span>
            <span>优惠总额:</span>
            <span>
              {{ $_common.formattedNumber(totalPreferentialAmount) }}
            </span>
            <span style="margin: 0 10px"></span>
            <span>实收总额:</span>
            <span>
              {{ $_common.formattedNumber(totalActualAmount) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getReceiptRequisitionInfo } from "@/api/Finance";
export default {
  data() {
    return {
      account_id: "",
      form: {
        receiptRequisitionAccountDate: [],
      },
      activeName: "one",
    };
  },
  computed: {
    // 收款总额
    totalCollectionAmount() {
      if (!this.form.receiptRequisitionAccountDate.length) {
        return 0;
      } else if (this.form.receiptRequisitionAccountDate.length === 1) {
        return Number(this.form.receiptRequisitionAccountDate[0].collectionAmount);
      } else {
        let sum = 0;
        this.form.receiptRequisitionAccountDate.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.collectionAmount) || 0);
        });
        return sum;
      }
    },
    // 优惠总额
    totalPreferentialAmount() {
      if (!this.form.receiptRequisitionAccountDate.length) {
        return 0;
      } else if (this.form.receiptRequisitionAccountDate.length === 1) {
        return Number(this.form.receiptRequisitionAccountDate[0].preferentialAmount);
      } else {
        let sum = 0;
        this.form.receiptRequisitionAccountDate.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.preferentialAmount) || 0);
        });
        return sum;
      }
    },
    // 实收总额
    totalActualAmount() {
      if (!this.form.receiptRequisitionAccountDate.length) {
        return 0;
      } else if (this.form.receiptRequisitionAccountDate.length === 1) {
        return Number(this.form.receiptRequisitionAccountDate[0].actualAmount);
      } else {
        let sum = 0;
        this.form.receiptRequisitionAccountDate.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.actualAmount) || 0);
        });
        return sum;
      }
    },
  },
  created() {
    if (this.$route.params.id) {
      this.account_id = this.$route.params.id;
      //  获取详情 收款单详情
      this.getReceiptRequisitionInfo();
    }
  },
  methods: {
    //  收款申请单详情
    async getReceiptRequisitionInfo() {
      const { data } = await getReceiptRequisitionInfo(this.account_id);
      this.form = {
        ...data,
        receiptRequisitionAccountDate: data.receiptRequisitionAccountDate.map((item) => {
          return {
            ...item,
          };
        }),
      };
    },
  },
};
</script>
<style scoped lang="scss">
.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.Enunciate {
  width: 100%;
  height: 96px;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  padding: 24px 24px 32px;
  .Enunciate_cont {
    background-color: #fa6400;
    border-radius: 3px;
    padding: 0 24px;
  }
}
</style>
<style>
.ApplyReceiptInfo {
  background-color: #fff;
}
.ApplyReceiptInfo .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.ApplyReceiptInfo .is-active {
  font-weight: 700;
  color: #000;
}
.ApplyReceiptInfo .el-tabs__nav {
  margin-left: 24px;
}
</style>
