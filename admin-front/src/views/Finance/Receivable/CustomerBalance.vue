<!--客户余额表-->
<template>
  <ContainerQuery>
    <div slot="left">
      <el-button size="small" type="primary" plain @click="getAllCustomerBalance(1)"> 导出 </el-button>
    </div>
    <div slot="more">
      <el-form size="small" :inline="true" :model="search_data" class="demo-form-inline">
        <el-form-item>
          <SelectCustomer v-model="custormerName" width="170" @clear="delCustormer" @change="customerSel" />
        </el-form-item>
        <el-form-item label="">
          <el-date-picker
            v-model="search_data.time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="orderDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_data.tag"
            style="width: 150px"
            placeholder="财务往来"
            clearable
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in disputeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <!--    <div slot="left"><el-button type="primary">导出</el-button></div>-->
    <el-table :data="tabelData">
      <el-table-column prop="customerId" label="ID" fixed="left" min-width="50" align="left"></el-table-column>
      <el-table-column prop="name" label="客户名称" align="left" min-width="120"></el-table-column>
      <el-table-column prop="openingBalance" label="期初余额" align="left" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.openingBalance) }}
        </template>
      </el-table-column>
      <el-table-column prop="saleMoney" align="left" label="销售金额" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.saleMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="collectionMoney" align="left" label="收款金额" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.collectionMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="endingBalance" label="期末金额" align="left" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.endingBalance) }}
        </template>
      </el-table-column>
      <el-table-column prop="interimBalance" align="left" label="会员余额" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.memberBalance) }}
        </template>
      </el-table-column>

      <el-table-column align="left" label="操作" width="255" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.CustomerBalanceDetailGetAllCustomerBalanceDetail)"
            type="text"
            @click="$router.push(`/Finance/Receivable/CustomerBalanceDetail?id=${scope.row.customerId}`)"
          >
            明细
          </el-button>

          <el-button
            v-if="$accessCheck($Access.CustomerBalanceAddReceipt)"
            type="text"
            @click="$router.push(`/Finance/Receivable/AddReceipt?customerId=${scope.row.customerId}`)"
          >
            收款
          </el-button>
          <el-button type="text" @click="balanceAdjustmentFlag(scope.row)"> 余额调整 </el-button>
          <el-button
            v-if="$accessCheck($Access.CustomerBalanceSaldoDetail)"
            type="text"
            @click="
              $router.push('/Customer/CustomerAdmin/SaldoDetail?id=' + scope.row.customerId + '&name=' + scope.row.name)
            "
          >
            余额流水
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <vxe-modal
      v-model="balanceAdjustment"
      title="余额调整"
      width="500"
      min-height="400"
      resize
      remember
      @close="closeBalanceAdjustment('formData')"
    >
      <template #default>
        <el-form ref="formData" :model="formData" :rules="rules" label-width="100px">
          <el-form-item label="客户名称:" prop="warehouseName">
            {{ formData.name }}
          </el-form-item>
          <el-form-item label="类型:">
            <el-select v-model="formData.type" placeholder="类型:" style="width: 300px">
              <el-option label="会员余额充值" :value="5"></el-option>
              <el-option label="会员余额扣除" :value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="金额:" prop="money">
            <el-input-number
              v-model="formData.money"
              :controls="false"
              style="width: 300px"
              :min="0"
              placeholder="请输入金额"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="收款账户:" prop="accountName">
            <el-input v-model="formData.accountName" readonly style="width: 300px" size="small" placeholder="结算账户">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="account_show = true"></i>
            </el-input>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input
              v-model="formData.explain"
              type="textarea"
              :rows="3"
              style="width: 300px"
              placeholder="请输入备注"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="closeBalanceAdjustment('formData')"> 取 消 </el-button>
            <el-button type="primary" @click="rechargeMemberBalance"> 确 定 </el-button>
          </el-form-item>
        </el-form>
      </template>
    </vxe-modal>
    <AccountType
      v-if="account_show"
      :is-check="false"
      :is-show="account_show"
      :is-mem="1"
      @cancel="account_show = false"
      @confirm="accountsel"
    />
  </ContainerQuery>
</template>

<script>
import SelectCustomer from "@/component/common/SelectCustomer.vue";
import { getAllCustomerBalance, exportgetAllCustomerBalance } from "@/api/Finance";
import { rechargeMemberBalance } from "@/api/Customer";
import AccountType from "../AccountType";

// import { exportgetAllAllocate, getAllAllocate } from "@/api/Stock";
export default {
  name: "CustomerBalance",
  components: {
    SelectCustomer,
    AccountType,
  },
  data() {
    return {
      custormerName: "",
      search_data: {
        customerId: "",
        start: "",
        end: "",
        tag: "",
      },
      customer_show: false,
      tabelData: [],
      disputeList: [
        { value: 4, label: "无财务往来" },
        { value: 5, label: "有财务往来" },
      ],
      total: 0,
      page: 1,
      pageSize: 10,
      balanceAdjustment: false,
      formData: {
        purpose: "",
        customerId: "",
        name: "",
        explain: "",
        type: 5,
        money: "",
        accountName: "",
        accountId: "",
        accountNumber: "",
      },
      rules: {
        money: [{ required: true, message: "请输入金额", trigger: "blur" }],
        accountName: [{ required: true, message: "请选择收款账户", trigger: "blur" }],
      },
      account_show: false,
    };
  },
  created() {
    this.getAllCustomerBalance();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllCustomerBalance();
  },
  methods: {
    delCustormer() {
      this.search_data.customerId = "";
      this.custormerName = "";
      this.pageChange(1);
    },
    //  选择客户
    customerSel(val, list) {
      this.search_data.customerId = list[0].id;
      this.pageChange(1);
    },
    // 获取列表
    async getAllCustomerBalance(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        customerId: this.search_data.customerId,
        start: this.search_data.start,
        end: this.search_data.end,
        tag: this.search_data.tag,
      };
      if (exports) {
        params.export = 1;
        const target = await exportgetAllCustomerBalance({
          ...params,
        });
      } else {
        const { data, pageTotal } = await getAllCustomerBalance({
          ...params,
        });
        this.tabelData = data;
        this.total = pageTotal;
      }
      // const { data, pageTotal } = await getAllCustomerBalance({
      //   ...params,
      // });
    },
    //  时间
    orderDate(val) {
      if (this.search_data.customerId) {
        if (val && val.length) {
          this.search_data.start = val[0] / 1000;
          this.search_data.end = val[1] / 1000 + 86399;
        } else {
          this.search_data.start = "";
          this.search_data.end = "";
        }
        this.pageChange(1);
      } else {
        this.$message("请选择客户");
      }
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllCustomerBalance();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    accountsel(val) {
      this.formData.accountName = val[0].name;
      this.formData.accountId = val[0].id;
      this.formData.accountNumber = val[0].accountNumber;
    },
    balanceAdjustmentFlag(row) {
      this.formData.customerId = row.customerId;
      this.balanceAdjustment = true;
      this.formData.name = row.name;
    },
    closeBalanceAdjustment(formData) {
      this.balanceAdjustment = false;
      this.formData = {
        customerId: "",
        name: "",
        type: 5,
        money: "",
        accountName: "",
        accountId: "",
        accountNumber: "",
        explain: "",
      };
      this.$refs[formData].resetFields();
      this.pageChange(1);
    },
    async rechargeMemberBalance() {
      if (this.formData.type === 5) {
        this.formData.purpose = "会员余额充值";
      } else if (this.formData.type === 4) {
        this.formData.purpose = "会员余额扣除";
      }
      if (!this.formData.accountId || !this.formData.money) {
        this.$message.warning("金额和收款账户不能为空");
        return;
      }
      const { data } = await rechargeMemberBalance(this.formData);
      this.$message.success("调整成功");
      this.closeBalanceAdjustment("formData");
    },
  },
};
</script>
<style scoped></style>
