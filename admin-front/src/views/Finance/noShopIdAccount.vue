<!--账户列表弹窗-->
<template>
  <el-dialog
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    title="账户列表"
    :visible="isShow"
    width="60%"
    @close="cancel"
  >
    <el-table :data="tableData" size="small" @row-dblclick="dbSelect">
      <el-table-column prop="accountCode" label="账户编号" min-width="140" fixed="left"></el-table-column>
      <el-table-column prop="name" label="账户名称" show-overflow-tooltip min-width="120"></el-table-column>
      <el-table-column prop="accountNumber" label="账户号" show-overflow-tooltip min-width="150"></el-table-column>
      <el-table-column prop="money" label="当前余额" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.money) }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="建账日期" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="beginMoney" label="期初余额" min-width="110">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.beginMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="shopName" label="所属商铺" show-overflow-tooltip min-width="120"></el-table-column>
      <el-table-column label="选择" fixed="right" width="90">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" plain type="primary" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <div v-if="isCheck" slot="footer" style="width: 100%; border-top: 1px solid #eee; padding-top: 10px">
      <el-button size="small" type="primary" @click="confirm">确定</el-button>
      <el-button size="small" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { getAllAccount } from "@/api/Finance";
export default {
  name: "NoShopIdAccount",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      choose_data: [],
      clientList: [],
      tableData: [],
      pre_page: 10,
      page: 1,
      // modalShow: this.clientModalShow,
      selectedIndex: null,
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      selectedClient: {},
    };
  },
  created() {
    this.getAllAccount();
  },
  methods: {
    //  获取列表
    async getAllAccount() {
      const { data, pageTotal } = await getAllAccount({
        page: this.page,
        pageSize: this.pre_page,
        enableStatus: 5,
      });

      this.tableData = data;
      this.total = pageTotal;
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    confirm() {
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getAllAccount();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
