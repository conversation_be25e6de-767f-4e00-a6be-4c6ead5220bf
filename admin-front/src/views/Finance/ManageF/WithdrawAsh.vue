<template>
  <ContainerQuery>
    <el-form slot="more" style="margin-bottom: 0" :inline="true" size="small">
      <el-form-item>
        <SelectCustomer v-model="customer_name" placeholder="客户" @clear="customerClear" @change="customerSel" />
      </el-form-item>
      <el-form-item>
        <el-select v-model="type" placeholder="提现方式" clearable @change="cut">
          <el-option
            v-for="(item, index) in type_options"
            :key="index"
            :label="item.label"
            :value="item.label"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <div class="block">
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="timestamp"
            @change="timeChange"
          ></el-date-picker>
        </div>
      </el-form-item>
    </el-form>
    <!--    <div slot="left"><el-button type="primary">导出</el-button></div>-->
    <el-tabs v-model="orderStatus" type="card" @tab-click="cut">
      <el-tab-pane v-for="item in order_status" :key="item.value" :label="item.label" :name="item.value"></el-tab-pane>
    </el-tabs>
    <el-table :data="withdrawals_data" style="width: 100%">
      <el-table-column prop="id" label="ID" width="180"></el-table-column>
      <el-table-column prop="name" label="客户" width="180"></el-table-column>
      <el-table-column prop="reflectType" label="提现方式"></el-table-column>
      <el-table-column prop="money" label="提现金额"></el-table-column>
      <el-table-column prop="reflectInfo" label="账户信息">
        <template slot-scope="scope">
          <span>姓名:{{ scope.row.reflectInfo.name }}</span>
          <br />
          <span>账号:{{ scope.row.reflectInfo.account }}</span>
          <br />
          <span v-if="scope.row.reflectInfo.bankName"> 开户行:{{ scope.row.reflectInfo.bankName }} </span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="申请时间">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column v-if="orderStatus !== 'have_money' && orderStatus !== 'invalid'" prop="address" label="操作">
        <template slot-scope="scope">
          <el-button
            v-if="orderStatus === 'to_audi' && $accessCheck($Access.WithdrawAshupdateReflectDetail)"
            type="text"
            @click="examine(scope.row.id, scope.row.createTime)"
          >
            审核
          </el-button>
          <el-button
            v-if="orderStatus === 'to_play_with' && $accessCheck($Access.WithdrawAshmakeMoney)"
            type="text"
            @click="Payment(scope.row.id, scope.row.createTime)"
          >
            打款
          </el-button>
          <el-button
            v-if="orderStatus === 'to_play_with' && $accessCheck($Access.WithdrawAshupdaterefuseMoney)"
            type="text"
            @click="refuse(scope.row.id, scope.row.createTime)"
          >
            拒绝
          </el-button>
        </template>
      </el-table-column>
      <el-table-column v-if="orderStatus === 'invalid'" prop="address" label="拒绝原因"></el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>
<script>
import SelectCustomer from "@/component/common/SelectCustomer.vue";

import { getAllReflectDetail, updateReflectDetail } from "@/api/Commission";
export default {
  components: { SelectCustomer },
  data() {
    return {
      orderStatus: "to_audi",
      order_status: [
        {
          label: "待审核",
          value: "to_audi",
        },
        {
          label: "待打款",
          value: "to_play_with",
        },
        {
          label: "已打款",
          value: "have_money",
        },
        { label: "无效", value: "invalid" },
      ],
      type: "",
      type_options: [
        {
          label: "微信钱包",
        },
        {
          label: "支付宝",
        },
        {
          label: "银行卡",
        },
      ],
      time: "",
      withdrawals_data: [],
      page: 1,
      pageSize: 10,
      total: 0,
      customer_name: "",
      customerId: "",
      start: "",
      end: "",
    };
  },
  created() {
    this.getAllReflectDetail();
  },
  methods: {
    // 审核
    async examine(id, time) {
      this.$confirm("是否要审核当前打款信息?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await updateReflectDetail({
          id: id,
          createTime: time,
          auditStatus: 2,
        });
        this.$message.success("审核成功");
        this.getAllReflectDetail();
      });
    },
    // 打款，
    async Payment(id, time) {
      this.$confirm("确定要打款吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await updateReflectDetail({
          id: id,
          createTime: time,
          reflectStatus: 5,
        });
        this.$message.success("已打款");
        this.getAllReflectDetail();
      });
    },
    //拒绝
    async refuse(id, time) {
      this.$confirm("确定要拒绝吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await updateReflectDetail({
          id: id,
          createTime: time,
          reflectStatus: 4,
        });
        this.$message.success("已拒绝");
        this.getAllReflectDetail();
      });
    },
    async getAllReflectDetail() {
      let params = {};
      switch (this.orderStatus) {
        case "to_audi":
          params.auditStatus = 1;
          params.reflectStatus = 4;
          break;
        case "to_play_with":
          params.auditStatus = 2;
          params.reflectStatus = 4;
          break;
        case "have_money":
          params.auditStatus = 2;
          params.reflectStatus = 5;
          break;
        case "invalid":
          params.auditStatus = 2;
          params.reflectStatus = 3;
          break;
      }
      const res = await getAllReflectDetail({
        page: this.page,
        pageSize: this.pageSize,
        customerId: this.customerId,
        start: this.start,
        end: this.end,
        reflectType: this.type,
        ...params,
      });
      this.withdrawals_data = res.data;
      this.total = res.pageTotal;
    },
    timeChange(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.pageChange(1);
    },
    cut(val) {
      this.pageChange(1);
    },
    pageChange(val) {
      this.page = val;
      this.getAllReflectDetail();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    customerSel(val, row) {
      this.customerId = row[0].id;
      this.pageChange(1);
    },
    customerClear() {
      this.customer_name = "";
      this.customerId = "";
      this.pageChange(1);
    },
  },
};
</script>
<style></style>
