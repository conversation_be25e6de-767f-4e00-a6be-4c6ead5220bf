<template>
  <Container>
    <div slot="left">
      <el-button
        v-if="$accessCheck($Access.FinanceTypeAddFinanceType)"
        size="small"
        type="primary"
        @click="openModel(false)"
      >
        新增
      </el-button>
    </div>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示：系统默认财务类型不允许操作
    </div>
    <el-table :data="type_data">
      <el-table-column label="ID" width="80px" prop="id"></el-table-column>
      <el-table-column prop="name" label="类型"></el-table-column>
      <el-table-column prop="name" label="归属单据">
        <template slot-scope="scope">
          {{ scope.row.link === 1 ? "应收单" : scope.row.link === 2 ? "应付单" : "退款单" }}
        </template>
      </el-table-column>
      <el-table-column prop="enableStatus" label="状态">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.FinanceTypeUpdateFinanceTypeStatus)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            :disabled="scope.row.isSystem === 5"
            @change="statusSet($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="enableStatus" label="系统默认">
        <template slot-scope="scope">
          <span v-if="scope.row.isSystem === 5" class="success-status">是</span>
          <span v-else class="info-status">否</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.FinanceTypeEditFinanceType)"
            :disabled="scope.row.isSystem === 5 || scope.row.enableStatus === 5"
            type="text"
            @click="openModel(true, scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.FinanceTypeDelFinanceType)"
            :disabled="scope.row.isSystem === 5 || scope.row.enableStatus === 5"
            type="text"
            @click="delData(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <el-dialog
      :title="(is_edit ? '编辑' : '新增') + '财务类型'"
      :visible.sync="show_model"
      width="35%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="show_model = false"
    >
      <el-form ref="form" :model="add_form" :rules="rules" size="small" label-width="100px">
        <el-form-item label="类型：" prop="name">
          <el-input v-model="add_form.name" placeholder="请输入类型名称" style="width: 250px"></el-input>
        </el-form-item>
        <el-form-item label="归属单据：" prop="link">
          <el-select v-model="add_form.link" style="width: 250px" placeholder="请选择">
            <el-option v-for="item in form_type" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否默认：" prop="defaultStatus">
          <el-switch v-model="add_form.isDefault" :active-value="5" :inactive-value="4"></el-switch>
        </el-form-item>
        <el-form-item label="是否禁用：" prop="enableStatus">
          <el-radio-group v-model="add_form.enableStatus">
            <el-radio :label="4">是</el-radio>
            <el-radio :label="5">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="show_model = false">取 消</el-button>
        <el-button type="primary" size="small" @click="addData"> 确 定 </el-button>
      </span>
    </el-dialog>
  </Container>
</template>

<script>
import {
  getAllFinanceType,
  addFinanceType,
  editFinanceType,
  delFinanceType,
  updateFinanceTypeDefaultStatus,
  updateFinanceTypeStatus,
} from "@/api/Finance";
export default {
  name: "FinanceType",
  data() {
    return {
      form_type: [
        {
          label: "应收单",
          value: 1,
        },
        {
          label: "应付单",
          value: 2,
        },
        {
          label: "退款单",
          value: 3,
        },
      ],
      pageSize: 10,
      page: 1,
      total: 0,
      show_model: false,
      is_edit: false,
      pay_id: 0,
      add_form: {
        name: "",
        link: "",
        isDefault: 4,
        enableStatus: 5,
      },
      rules: {
        name: [{ required: true, message: "请输入类型名称" }],
        link: [{ required: true, message: "请选择归属单据" }],
      },
      type_data: [],
    };
  },
  created() {
    this.getAllFinanceType();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllFinanceType();
  },
  methods: {
    async getAllFinanceType() {
      const { data, pageTotal } = await getAllFinanceType({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.type_data = data;
      this.total = pageTotal;
    },

    async addData() {
      if (!this.add_form.name.trim()) {
        this.$message.warning("类型名称不能为空");
        return;
      }
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          let target = {};
          if (!this.is_edit) {
            target = await addFinanceType({
              ...this.add_form,
            });
          } else {
            target = await editFinanceType(this.pay_id, {
              ...this.add_form,
            });
          }
          const data = target;

          this.show_model = false;
          this.pageChange(1);
        }
      });
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    pageChange(page) {
      this.page = page;
      this.getAllFinanceType();
    },
    openModel(isEdit, row) {
      this.add_form = {
        link: "",
        name: "",
        isDefault: 5,
        enableStatus: 5,
      };
      this.show_model = true;
      this.is_edit = isEdit;
      if (row) {
        this.pay_id = row.id;
        this.add_form = {
          link: row.link,
          name: row.name,
          isDefault: row.isDefault,
          enableStatus: row.enableStatus,
        };
      }
    },
    delData(id) {
      this.$confirm("确定要删除该财务类型吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delFinanceType(id);

        this.getAllFinanceType();
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
    defaultData(id) {
      this.$confirm("确定要将该财务类型设为默认吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateFinanceTypeDefaultStatus({
          id: id,
          isDefault: "5",
        });

        this.getAllFinanceType();
        this.$message({
          type: "success",
          message: "设置成功!",
        });
      });
    },
    async statusSet(val, row) {
      try {
        const data = await updateFinanceTypeStatus({
          id: row.id,
          enableStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        this.getAllFinanceType();
      }
    },
    handleClose() {},
  },
};
</script>

<style scoped></style>
