<template>
  <Container>
    <el-button
      v-if="$accessCheck($Access.CostTypesaddCostType)"
      slot="left"
      size="small"
      type="primary"
      @click="addAccounts"
    >
      新增
    </el-button>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="账目类型"></el-table-column>
      <el-table-column prop="type" label="收支类别">
        <template slot-scope="scope">
          {{ scope.row.type === 5 ? "收入" : "支出" }}
        </template>
      </el-table-column>
      <el-table-column prop="remarks" label="备注"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button v-if="$accessCheck($Access.CostTypeseditCostType)" type="text" @click="addAccounts(scope.row)">
            编辑
          </el-button>
          <el-button v-if="$accessCheck($Access.CostTypesdeleteCostType)" type="text" @click="delAccounts(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-drawer title="新增" :visible.sync="drawer" @close="close">
      <el-form :model="formInline" class="demo-form-inline" style="padding: 0 20px" label-width="90px">
        <el-form-item label="账目类型">
          <el-input v-model="formInline.name" style="width: 215px"></el-input>
        </el-form-item>
        <el-form-item label="收入类别">
          <el-select v-model="formInline.type" style="width: 215px" placeholder="收入类别">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formInline.remarks"
            type="textarea"
            style="width: 215px"
            :rows="4"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
        <el-button type="primary" class="float_right" :loading="loading" @click="confirmAccounts"> 确 定 </el-button>
      </el-form>
    </el-drawer>
  </Container>
</template>
<script>
import { getAllAccountType, addAccountType, delAccountType, getAccountTypeInfo, editAccountType } from "@/api/Finance";
export default {
  data() {
    return {
      drawer: false,
      formInline: {
        name: "",
        type: "",
        remarks: "",
        id: "",
      },
      tableData: [],
      addAccounts_flag: false,
      options: [
        {
          value: 4,
          label: "支出",
        },
        {
          value: 5,
          label: "收入",
        },
      ],
      loading: false,
    };
  },
  created() {
    this.getAllAccountType();
  },
  methods: {
    async getAllAccountType() {
      const { data } = await getAllAccountType({ type: 0 });
      this.tableData = data;
    },
    async addAccounts(row) {
      this.drawer = true;
      if (row.id) {
        this.addAccounts_flag = true;
        const { data } = await getAccountTypeInfo(row.id);
        this.formInline = data[0];
      }
    },
    async confirmAccounts() {
      this.loading = true;
      if (this.formInline.name.trim() && this.formInline.type) {
        if (this.addAccounts_flag) {
          try {
            const { data } = await editAccountType({
              id: this.formInline.id,
              name: this.formInline.name,
              type: this.formInline.type - 0,
              remarks: this.formInline.remarks,
            });
            this.$message.success("编辑成功");
            await this.getAllAccountType();
            this.drawer = false;
            this.loading = false;
          } catch {
            this.loading = false;
          }
        } else {
          try {
            const { data } = await addAccountType({
              name: this.formInline.name,
              type: this.formInline.type - 0,
              remarks: this.formInline.remarks,
            });
            await this.getAllAccountType();
            this.$message.success("添加成功");
            this.drawer = false;
            this.loading = false;
          } catch {
            this.loading = false;
          }
        }
      } else {
        this.$message.warning("账目类型和收入类别必须得填写");
        this.loading = false;
      }
    },
    delAccounts(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const { data } = await delAccountType(row.id);
        await this.getAllAccountType();
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
    close() {
      this.formInline = {
        name: "",
        type: "",
        remarks: "",
      };
      this.addAccounts_flag = false;
    },
  },
};
</script>
<style></style>
