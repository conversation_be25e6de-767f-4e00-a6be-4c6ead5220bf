<!--新增退款单-->
<template>
  <ContainerTit class="RefundDetail">
    <el-tabs v-model="activeName">
      <el-tab-pane label="退款单详情" name="one">
        <el-row style="padding-bottom: 13px">
          <el-col :span="24">
            <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">退款单信息</p>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">业务类型</span>
            <span class="form_right">{{ refund_detail.financeType }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">退款单位</span>
            <span class="form_right">
              {{ refund_detail.type === 5 ? "客户" : "供应商" }}：{{ refund_detail.unitName }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">所属店铺</span>
            <span class="form_right">
              {{ refund_detail.shopName }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">单据日期</span>
            <span class="form_right">
              {{ $_common.formatDate(refund_detail.refundTime) }}
            </span>
          </el-col>
          <el-col class="form" :span="6" style="padding-left: 54px">
            <span class="form_left">制单人</span>
            <span class="form_right">
              {{ refund_detail.currentAccountName }}
            </span>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <div class="order_bottom">
      <p class="text">退款单明细</p>
      <el-table :data="accountList">
        <el-table-column prop="accountName" label="结算账户" min-width="160"></el-table-column>
        <el-table-column prop="money" label="实际退款金额" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.money) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200"></el-table-column>
      </el-table>
    </div>
  </ContainerTit>
</template>

<script>
import { getRefundInfo } from "@/api/Finance";
export default {
  name: "RefundDetail",
  data() {
    return {
      refund_detail: {},
      createTime: "",
      account_id: "",
      activeName: "one",
      accountList: [],
    };
  },

  async created() {
    if (this.$route.params.id) {
      this.account_id = this.$route.params.id;
      this.createTime = this.$route.params.createTime;
      //  获取详情 退款单详情
      await this.getRefundInfo();
    }
  },
  methods: {
    goOtherDetail(orderId) {
      this.$router.push("/order/manageO/OrderDetails/1/" + orderId);
    },

    //  退款单详情
    async getRefundInfo() {
      const { data } = await getRefundInfo({
        id: this.account_id,
        createTime: this.createTime,
      });
      this.refund_detail = data;
      if (Array.isArray(data.accountList)) {
        this.accountList = data.accountList;
      } else {
        this.accountList = [data.accountList];
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.creat-custorm {
  color: #1890ff;
  font-size: 12px;
}
.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
</style>
<style>
.RefundDetail {
  background-color: #fff;
}
.RefundDetail .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.RefundDetail .is-active {
  font-weight: 700;
  color: #000;
}
.RefundDetail .el-tabs__nav {
  margin-left: 24px;
}
</style>
