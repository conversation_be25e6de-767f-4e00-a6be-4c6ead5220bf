<template>
  <ContainerTit class="AddCostSheet">
    <div style="position: relative">
      <div class="btn-top-div">
        <el-button type="primary" :loading="loading" @click="addData"> 保存并提交 </el-button>
      </div>
    </div>
    <el-form ref="base_form" label-width="120px" :model="form" size="small" :rules="rules">
      <el-tabs v-model="form.payType" @tab-click="change">
        <el-tab-pane label="日常收入" name="5">
          <el-row style="padding-bottom: 13px">
            <el-col :span="24">
              <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">日常收入信息</p>
            </el-col>
            <el-col class="form" :span="6" style="margin-bottom: 0">
              <el-form-item label="往来单位:" prop="currentUnit" style="min-width: 400px">
                <el-select v-model="options_value" placeholder="请选择" style="width: 85px" @change="btypeChange">
                  <el-option
                    v-for="item in options_list"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <SelectCustomer
                  v-if="options_value === 4"
                  v-model="form.currentUnit"
                  :clearable="false"
                  width="120"
                  @change="customerSel"
                />
                <SelectSupplier
                  v-if="options_value === 5"
                  v-model="form.currentUnit"
                  width="150"
                  @change="selSupplier"
                />
                <el-input
                  v-if="options_value === 3"
                  v-model="form.currentUnit"
                  clearable
                  style="width: 150px"
                  placeholder="请选择员工"
                  @clear="staff = ''"
                  @blur="staff = ''"
                >
                  <i slot="suffix" class="el-input__icon el-icon-search" @click="saleFn(true)"></i>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6" style="margin-bottom: 0">
              <el-form-item label="单据日期:">
                <template>
                  <div class="block">
                    <el-date-picker
                      v-model="form.billTime"
                      style="width: 240px"
                      type="date"
                      placeholder="选择日期"
                      value-format="timestamp"
                    ></el-date-picker>
                  </div>
                </template>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6" style="margin-bottom: 0">
              <el-form-item label="费用类型:" prop="consumeType">
                <el-select v-model="form.consumeTypeId" placeholder="费用类型">
                  <el-option
                    v-for="(item, index) in options"
                    :key="index"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="经手人:">
                {{ form.manager }}
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="选择店铺" prop="shopName">
                <SelectShop
                  v-model="form.shopId"
                  width="235"
                  :clearable="false"
                  placeholder="选择商铺"
                  @change="selShop"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="order_bottom">
            <p class="text">日常收入明细</p>
            <el-table :data="form.expenseSingleAccountDate">
              <el-table-column prop="expenseName" min-width="160">
                <template slot="header">
                  <span style="color: red">*</span>
                  <span>收入费用名称</span>
                </template>
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.expenseName"
                    style="width: 100%"
                    size="small"
                    placeholder="费用名称"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="amount" min-width="120">
                <template slot="header">
                  <span style="color: red">*</span>
                  <span>费用金额</span>
                </template>
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.amount"
                    :controls="false"
                    placeholder="费用金额"
                    size="small"
                    style="width: 100%"
                    :min="1"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="优惠金额" min-width="120">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.preferentialAmount"
                    :controls="false"
                    placeholder="优惠金额"
                    style="width: 100%"
                    size="small"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="实际收款金额" min-width="120" align="center">
                <template slot-scope="scope">
                  {{ $NP.minus(scope.row.amount, scope.row.preferentialAmount) }}
                </template>
              </el-table-column>
              <el-table-column min-width="160">
                <template slot="header">
                  <span style="color: red">*</span>
                  <span>结算方式</span>
                </template>
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.settlementMethodId"
                    clearable
                    size="small"
                    placeholder="请选择结算方式"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, index) in pay_type_list"
                      :key="index"
                      :label="item.title"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="settlementAccount" label="结算账户" min-width="160">
                <template slot="header">
                  <span style="color: red">*</span>
                  <span>结算账户</span>
                </template>
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.settlementAccount"
                    readonly
                    style="width: 100%"
                    size="small"
                    placeholder="结算账户"
                  >
                    <i slot="suffix" class="el-input__icon el-icon-search" @click="openAccount(scope.$index)"></i>
                  </el-input>
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="200">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.remark" style="width: 100%" size="small" placeholder="备注"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="操作" width="140" align="center">
                <template slot-scope="scope">
                  <el-button
                    :disabled="form.expenseSingleAccountDate.length === 1"
                    size="mini"
                    type="text"
                    @click="delAccount(scope.$index)"
                  >
                    删除
                  </el-button>
                  <!--                  <el-button size="mini" type="text" @click="addAccount">-->
                  <!--                    新增-->
                  <!--                  </el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <div class="table-b-div">
              <div class="table-b-div-cont" @click="addAccount">
                <el-button type="text" size="mini" @click="addAccount">
                  <i class="el-icon-plus"></i>
                  新增
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="日常支出" name="4">
          <el-row style="padding-bottom: 13px">
            <el-col :span="24">
              <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">日常支出信息</p>
            </el-col>
            <el-col class="form" :span="6" style="margin-bottom: 0">
              <el-form-item label="往来单位:" prop="currentUnit" style="min-width: 400px">
                <el-select v-model="options_value" placeholder="请选择" style="width: 85px" @change="btypeChange">
                  <el-option
                    v-for="item in options_list"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <SelectCustomer
                  v-if="options_value === 4"
                  v-model="form.currentUnit"
                  :clearable="false"
                  width="120"
                  @change="customerSel"
                />
                <SelectSupplier
                  v-if="options_value === 5"
                  v-model="form.currentUnit"
                  width="150"
                  @change="selSupplier"
                />
                <el-input
                  v-if="options_value === 3"
                  v-model="form.currentUnit"
                  clearable
                  style="width: 150px"
                  placeholder="请选择员工"
                  @clear="staff = ''"
                  @blur="staff = ''"
                >
                  <i slot="suffix" class="el-input__icon el-icon-search" @click="saleFn(true)"></i>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6" style="margin-bottom: 0">
              <el-form-item label="单据日期:">
                <template>
                  <div class="block">
                    <el-date-picker
                      v-model="form.billTime"
                      style="width: 240px"
                      type="date"
                      placeholder="选择日期"
                      value-format="timestamp"
                    ></el-date-picker>
                  </div>
                </template>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6" style="margin-bottom: 0">
              <el-form-item label="费用类型:" prop="consumeType">
                <el-select v-model="form.consumeTypeId" placeholder="费用类型">
                  <el-option
                    v-for="(item, index) in options"
                    :key="index"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="经手人:">
                {{ form.manager }}
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="选择店铺" prop="shopName">
                <SelectShop
                  v-model="form.shopId"
                  width="235"
                  :clearable="false"
                  placeholder="选择商铺"
                  @change="selShop"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="order_bottom">
            <p class="text">日常支出明细</p>
            <el-table :data="form.expenseSingleAccountDate">
              <el-table-column prop="expenseName" label="支出费用名称" min-width="160">
                <template slot="header">
                  <span style="color: red">*</span>
                  <span>支出费用名称</span>
                </template>
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.expenseName"
                    style="width: 100%"
                    size="small"
                    placeholder="费用名称"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="费用金额" min-width="120">
                <template slot="header">
                  <span style="color: red">*</span>
                  <span>费用金额</span>
                </template>
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.amount"
                    :controls="false"
                    placeholder="费用金额"
                    size="small"
                    style="width: 100%"
                    :min="1"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="优惠金额" min-width="120">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.preferentialAmount"
                    :controls="false"
                    placeholder="优惠金额"
                    style="width: 100%"
                    size="small"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="实际收款金额" min-width="120" align="center">
                <template slot-scope="scope">
                  {{ scope.row.amount - scope.row.preferentialAmount }}
                </template>
              </el-table-column>
              <el-table-column label="结算方式" min-width="160">
                <template slot="header">
                  <span style="color: red">*</span>
                  <span>结算方式</span>
                </template>
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.settlementMethodId"
                    clearable
                    size="small"
                    placeholder="请选择结算方式"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, index) in pay_type_list"
                      :key="index"
                      :label="item.title"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="settlementAccount" label="结算账户" min-width="160">
                <template slot="header">
                  <span style="color: red">*</span>
                  <span>结算账户</span>
                </template>
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.settlementAccount"
                    readonly
                    style="width: 100%"
                    size="small"
                    placeholder="结算账户"
                  >
                    <i slot="suffix" class="el-input__icon el-icon-search" @click="openAccount(scope.$index)"></i>
                  </el-input>
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="200">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.remark" style="width: 100%" size="small" placeholder="备注"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="操作" width="140" align="center">
                <template slot-scope="scope">
                  <el-button
                    :disabled="form.expenseSingleAccountDate.length === 1"
                    size="mini"
                    type="text"
                    @click="delAccount(scope.$index)"
                  >
                    删除
                  </el-button>
                  <!--                  <el-button size="mini" type="text" @click="addAccount">-->
                  <!--                    新增-->
                  <!--                  </el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <div class="table-b-div">
              <div class="table-b-div-cont" @click="addAccount">
                <el-button type="text" size="mini" @click="addAccount">
                  <i class="el-icon-plus"></i>
                  新增
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!--      <div v-if="form.payType === '5'">-->
      <!--        <div class="detail-tab-item">-->
      <!--          <p class="detail-tab-title">基础信息</p>-->
      <!--          <div class="detail-tab-main">-->
      <!--            <el-row>-->
      <!--              <el-col :span="6" style="display: flex">-->
      <!--                <el-form-item label="往来单位:" prop="currentUnit">-->
      <!--                  <el-select-->
      <!--                    v-model="options_value"-->
      <!--                    placeholder="请选择"-->
      <!--                    style="width: 85px"-->
      <!--                    @change="btypeChange"-->
      <!--                  >-->
      <!--                    <el-option-->
      <!--                      v-for="item in options_list"-->
      <!--                      :key="item.value"-->
      <!--                      :label="item.label"-->
      <!--                      :value="item.value"-->
      <!--                    ></el-option>-->
      <!--                  </el-select>-->
      <!--                  <SelectCustomer-->
      <!--                    v-if="options_value === 4"-->
      <!--                    v-model="form.currentUnit"-->
      <!--                    :clearable="false"-->
      <!--                    width="120"-->
      <!--                    @change="customerSel"-->
      <!--                  />-->
      <!--                  <SelectSupplier-->
      <!--                    v-if="options_value === 5"-->
      <!--                    v-model="form.currentUnit"-->
      <!--                    width="150"-->
      <!--                    @change="selSupplier"-->
      <!--                  />-->
      <!--                  <el-input-->
      <!--                    v-if="options_value === 3"-->
      <!--                    v-model="form.currentUnit"-->
      <!--                    clearable-->
      <!--                    style="width: 150px"-->
      <!--                    placeholder="请选择员工"-->
      <!--                    @clear="staff = ''"-->
      <!--                    @blur="staff = ''"-->
      <!--                  >-->
      <!--                    <i-->
      <!--                      slot="suffix"-->
      <!--                      class="el-input__icon el-icon-search"-->
      <!--                      @click="saleFn(true)"-->
      <!--                    ></i>-->
      <!--                  </el-input>-->
      <!--                </el-form-item>-->
      <!--              </el-col>-->
      <!--              <el-col :span="6">-->
      <!--                <el-form-item label="单据日期:">-->
      <!--                  <template>-->
      <!--                    <div class="block">-->
      <!--                      <el-date-picker-->
      <!--                        v-model="form.billTime"-->
      <!--                        style="width: 240px"-->
      <!--                        type="date"-->
      <!--                        placeholder="选择日期"-->
      <!--                        value-format="timestamp"-->
      <!--                      ></el-date-picker>-->
      <!--                    </div>-->
      <!--                  </template>-->
      <!--                </el-form-item>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-form-item label="经手人:">-->
      <!--                  {{ form.manager }}-->
      <!--                </el-form-item>-->
      <!--              </el-col>-->
      <!--              <el-col :span="6">-->
      <!--                <el-form-item label="费用类型:" prop="consumeType">-->
      <!--                  <el-select-->
      <!--                    v-model="form.consumeTypeId"-->
      <!--                    placeholder="费用类型"-->
      <!--                  >-->
      <!--                    <el-option-->
      <!--                      v-for="(item, index) in options"-->
      <!--                      :key="index"-->
      <!--                      :label="item.name"-->
      <!--                      :value="item.id"-->
      <!--                    ></el-option>-->
      <!--                  </el-select>-->
      <!--                </el-form-item>-->
      <!--              </el-col>-->
      <!--              <el-col :span="6">-->
      <!--                <el-form-item label="选择店铺" prop="shopName">-->
      <!--                  <SelectShop-->
      <!--                    v-model="form.shopId"-->
      <!--                    width="235"-->
      <!--                    :clearable="false"-->
      <!--                    placeholder="选择商铺"-->
      <!--                    @change="selShop"-->
      <!--                  />-->
      <!--                </el-form-item>-->
      <!--              </el-col>-->
      <!--            </el-row>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--        <div class="detail-tab-item">-->
      <!--          <p class="detail-tab-title">日常收入</p>-->
      <!--          <div class="detail-tab-main">-->
      <!--            <el-table :data="form.expenseSingleAccountDate">-->
      <!--              <el-table-column prop="expenseName" min-width="160">-->
      <!--                <template slot="header">-->
      <!--                  <span style="color: red">*</span>-->
      <!--                  <span>收入费用名称</span>-->
      <!--                </template>-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-input-->
      <!--                    v-model="scope.row.expenseName"-->
      <!--                    style="width: 100%"-->
      <!--                    size="small"-->
      <!--                    placeholder="费用名称"-->
      <!--                  ></el-input>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column prop="amount" min-width="120">-->
      <!--                <template slot="header">-->
      <!--                  <span style="color: red">*</span>-->
      <!--                  <span>费用金额</span>-->
      <!--                </template>-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-input-number-->
      <!--                    v-model="scope.row.amount"-->
      <!--                    :controls="false"-->
      <!--                    placeholder="费用金额"-->
      <!--                    size="small"-->
      <!--                    style="width: 100%"-->
      <!--                    :min="1"-->
      <!--                  ></el-input-number>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column label="优惠金额" min-width="120">-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-input-number-->
      <!--                    v-model="scope.row.preferentialAmount"-->
      <!--                    :controls="false"-->
      <!--                    placeholder="优惠金额"-->
      <!--                    style="width: 100%"-->
      <!--                    size="small"-->
      <!--                  ></el-input-number>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column-->
      <!--                label="实际收款金额"-->
      <!--                min-width="120"-->
      <!--                align="center"-->
      <!--              >-->
      <!--                <template slot-scope="scope">-->
      <!--                  {{-->
      <!--                    $NP.minus(scope.row.amount, scope.row.preferentialAmount)-->
      <!--                  }}-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column min-width="160">-->
      <!--                <template slot="header">-->
      <!--                  <span style="color: red">*</span>-->
      <!--                  <span>结算方式</span>-->
      <!--                </template>-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-select-->
      <!--                    v-model="scope.row.settlementMethodId"-->
      <!--                    clearable-->
      <!--                    size="small"-->
      <!--                    placeholder="请选择结算方式"-->
      <!--                    style="width: 100%"-->
      <!--                  >-->
      <!--                    <el-option-->
      <!--                      v-for="(item, index) in pay_type_list"-->
      <!--                      :key="index"-->
      <!--                      :label="item.title"-->
      <!--                      :value="item.id"-->
      <!--                    ></el-option>-->
      <!--                  </el-select>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column-->
      <!--                prop="settlementAccount"-->
      <!--                label="结算账户"-->
      <!--                min-width="160"-->
      <!--              >-->
      <!--                <template slot="header">-->
      <!--                  <span style="color: red">*</span>-->
      <!--                  <span>结算账户</span>-->
      <!--                </template>-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-input-->
      <!--                    v-model="scope.row.settlementAccount"-->
      <!--                    readonly-->
      <!--                    style="width: 100%"-->
      <!--                    size="small"-->
      <!--                    placeholder="结算账户"-->
      <!--                  >-->
      <!--                    <i-->
      <!--                      slot="suffix"-->
      <!--                      class="el-input__icon el-icon-search"-->
      <!--                      @click="openAccount(scope.$index)"-->
      <!--                    ></i>-->
      <!--                  </el-input>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column label="备注" min-width="200">-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-input-->
      <!--                    v-model="scope.row.remark"-->
      <!--                    style="width: 100%"-->
      <!--                    size="small"-->
      <!--                    placeholder="备注"-->
      <!--                  ></el-input>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column prop="remark" label="操作" width="140">-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-button-->
      <!--                    :disabled="form.expenseSingleAccountDate.length === 1"-->
      <!--                    size="mini"-->
      <!--                    type="text"-->
      <!--                    @click="delAccount(scope.$index)"-->
      <!--                  >-->
      <!--                    删除-->
      <!--                  </el-button>-->
      <!--                  <el-button size="mini" type="text" @click="addAccount">-->
      <!--                    新增-->
      <!--                  </el-button>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--            </el-table>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
      <!--      <div v-if="form.payType === '4'">-->
      <!--        <div class="detail-tab-item">-->
      <!--          <p class="detail-tab-title">基础信息</p>-->
      <!--          <div class="detail-tab-main">-->
      <!--            <el-row>-->
      <!--              <el-col :span="6" style="display: flex">-->
      <!--                <el-form-item label="往来单位:" prop="currentUnit">-->
      <!--                  <el-select-->
      <!--                    v-model="options_value"-->
      <!--                    placeholder="请选择"-->
      <!--                    style="width: 85px"-->
      <!--                    @change="btypeChange"-->
      <!--                  >-->
      <!--                    <el-option-->
      <!--                      v-for="item in options_list"-->
      <!--                      :key="item.value"-->
      <!--                      :label="item.label"-->
      <!--                      :value="item.value"-->
      <!--                    ></el-option>-->
      <!--                  </el-select>-->
      <!--                  <SelectCustomer-->
      <!--                    v-if="options_value === 4"-->
      <!--                    v-model="form.currentUnit"-->
      <!--                    :clearable="false"-->
      <!--                    width="120"-->
      <!--                    @change="customerSel"-->
      <!--                  />-->
      <!--                  <SelectSupplier-->
      <!--                    v-if="options_value === 5"-->
      <!--                    v-model="form.currentUnit"-->
      <!--                    width="150"-->
      <!--                    @change="selSupplier"-->
      <!--                  />-->
      <!--                  <el-input-->
      <!--                    v-if="options_value === 3"-->
      <!--                    v-model="form.currentUnit"-->
      <!--                    clearable-->
      <!--                    style="width: 150px"-->
      <!--                    placeholder="请选择员工"-->
      <!--                    @clear="staff = ''"-->
      <!--                    @blur="staff = ''"-->
      <!--                  >-->
      <!--                    <i-->
      <!--                      slot="suffix"-->
      <!--                      class="el-input__icon el-icon-search"-->
      <!--                      @click="saleFn(true)"-->
      <!--                    ></i>-->
      <!--                  </el-input>-->
      <!--                </el-form-item>-->
      <!--              </el-col>-->
      <!--              <el-col :span="6">-->
      <!--                <el-form-item label="单据日期:">-->
      <!--                  <template>-->
      <!--                    <div class="block">-->
      <!--                      <el-date-picker-->
      <!--                        v-model="form.billTime"-->
      <!--                        style="width: 240px"-->
      <!--                        type="date"-->
      <!--                        placeholder="选择日期"-->
      <!--                        value-format="timestamp"-->
      <!--                      ></el-date-picker>-->
      <!--                    </div>-->
      <!--                  </template>-->
      <!--                </el-form-item>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-form-item label="经手人:">-->
      <!--                  {{ form.manager }}-->
      <!--                </el-form-item>-->
      <!--              </el-col>-->
      <!--              <el-col :span="6">-->
      <!--                <el-form-item label="费用类型:" prop="consumeType">-->
      <!--                  <el-select-->
      <!--                    v-model="form.consumeTypeId"-->
      <!--                    placeholder="费用类型"-->
      <!--                  >-->
      <!--                    <el-option-->
      <!--                      v-for="(item, index) in options"-->
      <!--                      :key="index"-->
      <!--                      :label="item.name"-->
      <!--                      :value="item.id"-->
      <!--                    ></el-option>-->
      <!--                  </el-select>-->
      <!--                </el-form-item>-->
      <!--              </el-col>-->
      <!--              <el-col :span="6">-->
      <!--                <el-form-item label="选择店铺" prop="shopName">-->
      <!--                  <SelectShop-->
      <!--                    v-model="form.shopId"-->
      <!--                    width="235"-->
      <!--                    :clearable="false"-->
      <!--                    placeholder="选择商铺"-->
      <!--                    @change="selShop"-->
      <!--                  />-->
      <!--                </el-form-item>-->
      <!--              </el-col>-->
      <!--            </el-row>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--        <div class="detail-tab-item">-->
      <!--          <p class="detail-tab-title">日常支出</p>-->
      <!--          <div class="detail-tab-main">-->
      <!--            <el-table :data="form.expenseSingleAccountDate">-->
      <!--              <el-table-column-->
      <!--                prop="expenseName"-->
      <!--                label="支出费用名称"-->
      <!--                min-width="160"-->
      <!--              >-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-input-->
      <!--                    v-model="scope.row.expenseName"-->
      <!--                    style="width: 100%"-->
      <!--                    size="small"-->
      <!--                    placeholder="费用名称"-->
      <!--                  ></el-input>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column prop="amount" label="费用金额" min-width="120">-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-input-number-->
      <!--                    v-model="scope.row.amount"-->
      <!--                    :controls="false"-->
      <!--                    placeholder="费用金额"-->
      <!--                    size="small"-->
      <!--                    style="width: 100%"-->
      <!--                    :min="1"-->
      <!--                  ></el-input-number>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column label="优惠金额" min-width="120">-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-input-number-->
      <!--                    v-model="scope.row.preferentialAmount"-->
      <!--                    :controls="false"-->
      <!--                    placeholder="优惠金额"-->
      <!--                    style="width: 100%"-->
      <!--                    size="small"-->
      <!--                  ></el-input-number>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column-->
      <!--                label="实际收款金额"-->
      <!--                min-width="120"-->
      <!--                align="center"-->
      <!--              >-->
      <!--                <template slot-scope="scope">-->
      <!--                  {{ scope.row.amount - scope.row.preferentialAmount }}-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column label="结算方式" min-width="160">-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-select-->
      <!--                    v-model="scope.row.settlementMethodId"-->
      <!--                    clearable-->
      <!--                    size="small"-->
      <!--                    placeholder="结算方式"-->
      <!--                    style="width: 100%"-->
      <!--                  >-->
      <!--                    <el-option-->
      <!--                      v-for="(item, index) in pay_type_list"-->
      <!--                      :key="index"-->
      <!--                      :label="item.title"-->
      <!--                      :value="item.id"-->
      <!--                    ></el-option>-->
      <!--                  </el-select>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column-->
      <!--                prop="settlementAccount"-->
      <!--                label="结算账户"-->
      <!--                min-width="160"-->
      <!--              >-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-input-->
      <!--                    v-model="scope.row.settlementAccount"-->
      <!--                    readonly-->
      <!--                    style="width: 100%"-->
      <!--                    size="small"-->
      <!--                    placeholder="结算账户"-->
      <!--                  >-->
      <!--                    <i-->
      <!--                      slot="suffix"-->
      <!--                      class="el-input__icon el-icon-search"-->
      <!--                      @click="openAccount(scope.$index)"-->
      <!--                    ></i>-->
      <!--                  </el-input>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column label="备注" min-width="200">-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-input-->
      <!--                    v-model="scope.row.remark"-->
      <!--                    style="width: 100%"-->
      <!--                    size="small"-->
      <!--                    placeholder="备注"-->
      <!--                  ></el-input>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--              <el-table-column prop="remark" label="操作" width="140">-->
      <!--                <template slot-scope="scope">-->
      <!--                  <el-button-->
      <!--                    :disabled="form.expenseSingleAccountDate.length === 1"-->
      <!--                    size="mini"-->
      <!--                    type="text"-->
      <!--                    @click="delAccount(scope.$index)"-->
      <!--                  >-->
      <!--                    删除-->
      <!--                  </el-button>-->
      <!--                  <el-button size="mini" type="text" @click="addAccount">-->
      <!--                    新增-->
      <!--                  </el-button>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <!--            </el-table>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
    </el-form>
    <AccountType
      v-if="account_show"
      :is-check="false"
      :is-show="account_show"
      @cancel="account_show = false"
      @confirm="accountsel"
    />
    <staffListModal
      v-if="staff_show"
      :is-show="staff_show"
      :is-check="false"
      :isserch="isserch"
      @cancel="staff_show = false"
      @confirm="staffSel"
    />
  </ContainerTit>
</template>
<style></style>

<script>
import { getAllAccountType, addExpenseSingle, getExpenseSingleInfo, editExpenseSingle } from "@/api/Finance";
import SelectCustomer from "@/component/common/SelectCustomer.vue";
import { getAllPayment } from "@/api/System";
import AccountType from "../AccountType";
import staffListModal from "@/component/common/staffListModal";
import SelectSupplier from "@/component/common/SelectSupplier.vue";
import SelectShop from "@/component/goods/SelectShop.vue";
export default {
  components: {
    SelectCustomer,
    AccountType,
    staffListModal,
    SelectSupplier,
    SelectShop,
  },
  data() {
    return {
      form: {
        shopName: "", //商铺名称
        shopId: "", // 商铺有id
        billTime: new Date().getTime(), //单据日期
        currentUnit: "", //往来单位
        // money: "", //金额
        currentUnitId: "", //往来单位id
        payType: "5", // 页面tab切换
        consumeTypeId: "", // 费用类型id
        consumeTypeName: "",
        manager: "", //经手人
        totalCollectionAmount: 0, // 总费用金额
        totalPreferentialAmount: 0, // 优惠总金额
        totalActualAmount: 0, // 实付总金额
        expenseSingleAccountDate: [
          {
            expenseName: "", // 费用名称
            amount: 0, // 金额
            settlementMethodId: "", //结算方式
            accountId: 0, // 结算账户id
            remark: "", // 备注
            settlementAccount: "", // 结算账户
            preferentialAmount: 0, //优惠金额
          },
        ],
      },
      options: [],
      pay_type_list: [],
      account_show: false,
      account_index: 0,
      cost_id: "",
      loading: false,
      options_value: 4,
      options_list: [
        { label: "客户", value: 4 },
        { label: "供应商", value: 5 },
        { label: "员工", value: 3 },
      ],
      staff: "",
      isserch: true,
      staff_show: false,
      supplierId: "",
      rules: {
        currentUnit: [{ required: true, trigger: "blur" }],
        consumeType: [{ required: true, trigger: "blur" }],
        shopName: [{ required: true, trigger: "blur" }],
      },
    };
  },
  async created() {
    this.cost_id = this.$route.params.id;
    this.form.manager = this.userName;
    await this.getAllAccountType(this.form.payType);
    await this.getAllPayment();
    if (this.cost_id) {
      await this.getExpenseSingleInfo(this.cost_id);
    }
  },
  methods: {
    addAccount() {
      this.form.expenseSingleAccountDate.push({
        expenseName: "", // 费用名称
        amount: 0, // 金额
        settlementMethodId: "", //结算方式
        accountId: 0, // 结算账户id
        remark: "", // 备注
        settlementAccount: "", // 结算账户
        preferentialAmount: 0, //优惠金额
      });
    },
    delAccount(index) {
      this.form.expenseSingleAccountDate.splice(index, 1);
    },
    async getAllAccountType(id) {
      const { data } = await getAllAccountType({ type: id - 0 });
      this.options = data;
    },
    change() {
      this.getAllAccountType(this.form.payType);
      this.form.consumeTypeId = "";
    },
    openAccount(index) {
      this.account_index = index;
      if (this.form.currentUnitId) {
        this.account_show = true;
      } else {
        this.$message.warning("请选择往来单位");
      }
    },
    customerSel(val, row) {
      this.form.currentUnitId = row[0].id;
    },
    // 获取支付方式
    async getAllPayment() {
      const data = await getAllPayment({
        page: 1,
        pageSize: 99,
      });

      this.pay_type_list = data.data;
    },
    // 结算账户
    accountsel(val) {
      this.form.expenseSingleAccountDate[this.account_index].accountId = val[0].id;
      this.form.expenseSingleAccountDate[this.account_index].settlementAccount = val[0].name;
    },
    async addData() {
      this.loading = true;
      if (this.form.consumeTypeId === "" || this.form.currentUnitId === "") {
        this.$message.warning("往来单位和费用类型不能为空");
        this.loading = false;
        return;
      }
      if (!this.form.shopName) {
        this.$message.warning("请选择店铺");
        this.loading = false;
        return;
      }
      // this.form.totalCollectionAmount = 0;
      // this.form.totalPreferentialAmount = 0;
      // this.form.totalActualAmount = 0;
      let flag = false;
      this.form.expenseSingleAccountDate.forEach((item) => {
        if (!item.expenseName.trim()) {
          this.$message.warning("费用名称不能为空");
          flag = true;
          return;
        }
        if (!item.amount) {
          this.$message.warning("费用金额不能为空");
          flag = true;
          return;
        }
        if (!item.settlementMethodId) {
          this.$message.warning("请选择结算方式");
          flag = true;
          return;
        }
        if (!item.accountId) {
          this.$message.warning("请选择结算账户");
          flag = true;
          return;
        }
      });
      if (flag) {
        this.loading = false;
        return;
      }
      this.options.forEach((item) => {
        if (item.id === this.form.consumeTypeId) this.form.consumeTypeName = item.name;
      });
      this.form.expenseSingleAccountDate.forEach((item) => {
        // 费用
        this.form.totalCollectionAmount = this.$NP.plus(this.form.totalCollectionAmount, item.amount);
        // 优惠
        this.form.totalPreferentialAmount = this.$NP.plus(this.form.totalPreferentialAmount, item.preferentialAmount);
      });
      // 实付
      this.form.totalActualAmount = this.$NP.minus(this.form.totalCollectionAmount, this.form.totalPreferentialAmount);
      try {
        if (this.cost_id) {
          const { data } = await editExpenseSingle(this.cost_id, {
            ...this.form,
            type: this.options_value,
          });
          this.$message.success("编辑成功");
          this.loading = false;
        } else {
          const { data } = await addExpenseSingle({
            ...this.form,
            type: this.options_value,
          });
          this.$message.success("新增成功");
          this.loading = false;
        }
        await this.$router.push("/Finance/Cashier/CostSheet");
      } catch {
        this.loading = false;
      }
    },
    async getExpenseSingleInfo(id) {
      const { data } = await getExpenseSingleInfo(id);
      this.form = data;
      this.form.payType = data.payType + "";
      this.form.billTime = data.createTime * 1000;
      this.form.expenseSingleAccountDate = data.accountList;
      this.options_value = data.type;
    },
    saleFn(isserch) {
      this.staff_show = true;
      this.isserch = isserch;
    },
    // 选择员工
    staffSel(val) {
      const row = val[0];
      this.form.currentUnit = row.staffName;
      this.form.currentUnitId = row.id;
    },
    //  获取供应商 selSupplier
    selSupplier(val, row) {
      this.form.currentUnit = row[0].title;
      this.form.currentUnitId = val;
    },
    btypeChange() {
      this.form.currentUnit = "";
    },
    // 选择商铺
    selShop(val, row) {
      this.form.shopName = row[0].name;
    },
  },
};
</script>
<style lang="scss" scoped>
.order_bottom {
  position: relative;
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .order_bottom_btn {
    position: absolute;
    top: 15px;
    right: 20px;
  }
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.btn-top-div {
  position: absolute;
  right: 20px;
  top: 15px;
  z-index: 999;
}
.table-b-div {
  height: 80px;
  width: 100%;
  padding: 16px 24px 32px 25px;
  border: 1px solid #ebeef5;
  text-align: center;
  line-height: 40px;
  border-top: 0 none;
  cursor: pointer;
  .table-b-div-cont {
    border: 1px dashed #2153d4;
  }
}
</style>
<style>
.AddCostSheet {
  background-color: #fff;
}
.AddCostSheet .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.AddCostSheet .is-active {
  font-weight: 700;
  color: #000;
}
.AddCostSheet .el-tabs__nav {
  margin-left: 24px;
}
</style>
