<!--账户列表-->
<template>
  <ContainerQuery>
    <el-button
      v-if="$accessCheck($Access.AccountListAddAccount)"
      slot="left"
      size="small"
      type="primary"
      @click="openModel"
    >
      新增账户
    </el-button>
    <el-form slot="more" size="small" :inline="true">
      <el-form-item>
        <el-date-picker
          v-model="time"
          clearable
          type="daterange"
          value-format="timestamp"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="orderDate"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <el-table :data="tableData">
      <el-table-column prop="id" align="left" label="ID" width="50"></el-table-column>
      <el-table-column prop="accountCode" align="left" label="账户编号" min-width="130"></el-table-column>
      <el-table-column
        prop="name"
        align="left"
        show-overflow-tooltip
        label="账户名称"
        min-width="100"
      ></el-table-column>
      <el-table-column prop="type" align="left" label="账户类型" min-width="130">
        <template slot-scope="scope">
          {{
            scope.row.type === 2
              ? "支付宝账户"
              : scope.row.type === 1
              ? "微信账户"
              : scope.row.type === 4
              ? "银行账户"
              : scope.row.type === 0
              ? "普通账户"
              : scope.row.type === 7
              ? "余额账户"
              : "其他"
          }}
        </template>
      </el-table-column>

      <el-table-column
        prop="accountNumber"
        align="left"
        label="账户号"
        show-overflow-tooltip
        min-width="160"
      ></el-table-column>
      <el-table-column v-if="beginFlag" prop="beginMoney" label="期初余额" align="left" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.beginMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="income" label="本期收入" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.income) }}
        </template>
      </el-table-column>
      <el-table-column prop="expend" label="本期支出" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.expend) }}
        </template>
      </el-table-column>
      <el-table-column v-if="balanceFlag" prop="money" align="left" label="期末余额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.money) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="belongToFlag"
        prop="shopName"
        label="所属商铺"
        align="left"
        min-width="140"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column v-if="switchFlag" prop="enableStatus" label="账户状态" align="left" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.AccountListUpdateAccountStatus)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="updateStatus($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="setUpAccountsFlag" prop="createTime" label="建账/审核日期" align="left" min-width="140">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime, "yyyy-MM-dd") }}
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" width="170" fixed="right">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.AccountDetailsGetAllAccountDetail)"
            type="text"
            @click="
              $router.push(
                `/Finance/Cashier/AccountDetails?id=${scope.row.id}&shopId=${scope.row.shopId}&shopName=${scope.row.shopName}`
              )
            "
          >
            明细
          </el-button>
          <el-button
            v-if="$accessCheck($Access.AccountListEditAccount)"
            type="text"
            :disabled="scope.row.enableStatus === 5"
            @click="openEditModel(scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.AccountListDelAccount) && !!scope.row.shopId"
            type="text"
            :disabled="scope.row.enableStatus === 5"
            @click="delAccount(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <AddAccount
      v-if="add_show"
      :id="is_edit ? account_id : 0"
      :visible="add_show"
      :is-edit="is_edit"
      @close="add_show = false"
      @confirm="AddConfirm"
    />
  </ContainerQuery>
</template>

<script>
import { getAllAccount, updateAccountStatus, delAccount } from "@/api/Finance";
import AddAccount from "../AddAccount";
export default {
  name: "AccountList",
  components: {
    AddAccount,
  },
  data() {
    return {
      add_show: false,
      is_edit: false,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      account_id: 0,
      checkList: ["期末余额", "建帐日期(审核日期)", "期初余额", "所属商铺", "开启/禁用"],
      columns: [
        {
          label: "期末余额",
        },
        {
          label: "建帐日期(审核日期)",
        },
        {
          label: "期初余额",
        },
        {
          label: "所属商铺",
        },
        {
          label: "开启/禁用",
        },
      ],
      balanceFlag: true,
      setUpAccountsFlag: true,
      beginFlag: true,
      belongToFlag: true,
      switchFlag: true,
      time: "",
      start: "",
      end: "",
    };
  },
  created() {
    this.getAllAccount();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllAccount();
  },
  methods: {
    //  获取列表
    async getAllAccount() {
      const { data, pageTotal } = await getAllAccount({
        start: this.start,
        end: this.end,
        page: this.page,
        pageSize: this.pageSize,
      });

      this.tableData = data;
      this.total = pageTotal;
    },
    AddConfirm() {
      this.getAllAccount();
    },
    openModel() {
      this.add_show = true;
      this.is_edit = false;
    },
    //  编辑账户
    openEditModel(id) {
      this.account_id = id;
      this.add_show = true;
      this.is_edit = true;
    },
    /** 启用或禁用功能 */
    async updateStatus(val, row) {
      try {
        const data = await updateAccountStatus({
          id: row.id,
          enableStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        await this.getAllAccount();
      }
    },
    // 删除 delAccount
    delAccount(id) {
      this.$confirm("确定删除该账户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delAccount(id);

        await this.getAllAccount();
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
    // 切页
    pageChange(val) {
      this.page = val;
      // this.getData()
      this.getAllAccount();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    change() {
      this.balanceFlag = this.checkList.some((item) => item === "期末余额");
      this.setUpAccountsFlag = this.checkList.some((item) => item === "建帐日期(审核日期)");
      this.beginFlag = this.checkList.some((item) => item === "期初余额");
      this.belongToFlag = this.checkList.some((item) => item === "所属商铺");
      this.switchFlag = this.checkList.some((item) => item === "开启/禁用");
    },
    orderDate(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.pageChange(1);
    },
  },
};
</script>
<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
