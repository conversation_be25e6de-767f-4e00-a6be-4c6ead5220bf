<!--明细查询-->
<template>
  <Container>
    <div slot="left">
      <el-form ref="form" size="small" :inline="true" :model="searchDate" :rules="rules">
        <el-form-item prop="AccountName">
          <el-input v-model="searchDate.AccountName" placeholder="选择账户" size="small" style="width: 150px">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="account_show = true"></i>
          </el-input>
        </el-form-item>
        <el-form-item v-if="!id" prop="shopId">
          <SelectShop
            v-model="searchDate.shopId"
            :clearable="false"
            placeholder="选择商铺"
            width="150"
            @change="shopChange"
          />
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="tableData.length">
      <el-table :data="tableData" size="mini" show-summary :summary-method="getSummaries">
        <el-table-column prop="id" label="ID" width="70"></el-table-column>
        <el-table-column prop="accountCode" label="账户" min-width="180">
          <template slot-scope="scope">
            <div class="order-info-p">
              <span class="order-info-label">编号:</span>
              {{ scope.row.accountCode }}
            </div>
            <div class="order-info-p">
              <span class="order-info-label">名称:</span>
              {{ scope.row.accountName }}
            </div>
            <div class="order-info-p">
              <span class="order-info-label">账户号:</span>
              {{ scope.row.accountNumber }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="单据日期" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.createTime, "yyyy-MM-dd") }}
          </template>
        </el-table-column>
        <el-table-column prop="sourceNo" label="单据编号" min-width="180">
          <template slot-scope="scope">
            <span class="click-div" @click="goNoDetail(scope.row)">
              {{ scope.row.sourceNo }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="financeType" label="业务类型" min-width="100"></el-table-column>
        <el-table-column prop="shopName" label="所属商铺" min-width="140"></el-table-column>
        <el-table-column prop="contactUnit" label="往来单位" min-width="160"></el-table-column>
        <el-table-column prop="beginBalance" label="期初余额" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.beginBalance) }}
          </template>
        </el-table-column>
        <el-table-column prop="income" label="收入" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.income) }}
          </template>
        </el-table-column>
        <el-table-column prop="expend" label="支出" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.expend) }}
          </template>
        </el-table-column>
        <el-table-column prop="endBalance" label="账户金额" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.endBalance) }}
          </template>
        </el-table-column>

        <el-table-column prop="receiveOrPayPerson" label="收/付款人" min-width="120"></el-table-column>
        <el-table-column prop="remark" label="备注" show-overflow-tooltip min-width="160"></el-table-column>
      </el-table>
      <FooterPage
        :page-size="pageSize"
        :total-page.sync="total"
        :current-page.sync="page"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      ></FooterPage>
    </div>
    <div v-else class="empty-view">
      <img class="empty-img" :src="require('@/assets/img/no_enterprise.png')" alt="" />
      <p>快去选择账户进行查看吧！</p>
    </div>
    <!--    选择账户-->
    <AccountType
      v-if="account_show"
      :id="searchDate.shopId"
      :is-check="false"
      :is-show="account_show"
      @cancel="account_show = false"
      @confirm="selAccount"
    />
  </Container>
</template>

<script>
import SelectShop from "@/component/goods/SelectShop.vue";

import AccountType from "../AccountType";
import { getAllAccountDetail, getAccountInfo } from "@/api/Finance";
export default {
  name: "AccountList",
  components: {
    SelectShop,
    AccountType,
  },
  props: {
    id: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    const validateShop = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择商铺"));
      } else {
        callback();
      }
    };
    const validateAccoun = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择账户名称"));
      } else {
        callback();
      }
    };
    return {
      sel_shop: false,
      time: [],
      searchDate: {
        AccountId: "",
        AccountName: "",
        AccountNumber: "",
        shopId: "",
        start: "",
        end: "",
      },

      customer_show: false,
      account_show: false,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      rules: {
        // shopId: [{ required: true, validator: validateShop }],
        AccountName: [{ required: true, validator: validateAccoun }],
      },
    };
  },
  created() {
    if (this.$route.query.id) {
      this.searchDate.shopId = parseInt(this.$route.query.shopId) || "";
      this.getAccountInfo();
    }
  },
  activated() {
    if (this.$_isInit()) return;
    if (this.$route.query.id) {
      this.searchDate.shopId = parseInt(this.$route.query.shopId) || "";
      this.getAccountInfo();
    }
  },
  methods: {
    goSourceNoDetail(type, id) {
      switch (type) {
        case "销售收款":
          // 销售收款
          // this.$router.push(
          //   `/Finance/Receivable/LookReceipt/${id}/**********`
          // );
          break;
        case "销售单":
          //  订单详情
          this.$router.push("/order/manageO/OrderDetails/1/" + id);
          break;
        case "客户退款":
          // 客户退款
          break;
        case "销售退款单":
          // 客户退款
          break;
        case "订单退款":
          // 客户退款
          break;
      }
    },
    shopChange() {
      this.pageChange(1);
    },
    // 选择账户
    selAccount(val) {
      this.searchDate.AccountId = val[0].id;
      this.searchDate.AccountName = val[0].name;
      this.searchDate.AccountNumber = val[0].accountNumber;
      this.pageChange(1);
    },
    clear() {
      this.searchDate.AccountId = "";
      this.searchDate.AccountName = "";
      this.searchDate.AccountNumber = "";
      this.pageChange(1);
    },
    async getAllAccountDetail() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          const { data, pageTotal } = await getAllAccountDetail({
            accountId: this.searchDate.AccountId,
            shopId: this.searchDate.shopId,
            start: this.searchDate.start,
            end: this.searchDate.end,
            page: this.page,
            pageSize: this.pageSize,
          });

          this.tableData = data;
          this.total = pageTotal;
        }
      });
    },
    // 获取详情
    async getAccountInfo() {
      const { data } = await getAccountInfo(this.$route.query.id);

      this.searchDate.AccountId = data.id;
      this.searchDate.AccountName = data.name;
      this.searchDate.AccountNumber = data.accountNumber;
      this.pageChange(1);
    },
    //  时间选择
    timeChange(val) {
      if (this.searchDate.AccountId) {
        this.searchDate.start = val[0] / 1000;
        this.searchDate.end = val[1] / 1000 + 86399;
      }
      this.pageChange(1);
    },
    getSummaries() {},
    pageChange(val) {
      this.page = val;
      this.getAllAccountDetail();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    goNoDetail(row) {
      //余额支付收款
      if (["预存收款", "余额支付收款", "银行打款收款", "线上支付收款", "销售收款"].includes(row.financeType)) {
        this.$router.push(`/Finance/Receivable/LookReceipt/${row.sourceId}/${row.createTime}`);
      }
      if (row.financeType === "采购预付" || row.financeType === "采购付款") {
        this.$router.push(`/Finance/Handle/LookPayment/${row.sourceId}/${row.createTime}`);
      }

      if (row.financeType === "退款单退款") {
        this.$router.push(`/Finance/Cashier/RefundDetail/${row.sourceId}/${row.createTime}`);
      }

      if (row.financeType === "费用单支出" || row.financeType === "费用单收款") {
        this.$router.push(`/Finance/Cashier/CostSheetParticulars/${row.sourceId}`);
      }
    },
  },
};
</script>
<style scoped></style>
