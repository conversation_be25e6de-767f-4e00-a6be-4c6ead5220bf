<template>
  <ContainerTit class="AddFunTransfer">
    <div style="position: relative">
      <div class="btn-top-div">
        <el-button
          v-if="!$route.params.id && $accessCheck($Access.FunTransferGetTempAccountTransferData)"
          :loading="loading"
          @click="subData(true)"
        >
          暂存
        </el-button>
        <el-button type="primary" :loading="loading" @click="subData(false)"> 保存 </el-button>
      </div>
    </div>
    <el-form ref="form" :inline="true" :rules="rules" :model="form" label-width="120px">
      <el-tabs v-model="activeName">
        <el-tab-pane label="新建资金转账单" name="one">
          <el-row style="padding-bottom: 13px">
            <el-col :span="24">
              <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">资金转账单信息</p>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="所属商铺：" prop="shopName" style="min-width: 360px">
                <el-input v-model="form.shopName" readonly style="width: 240px" placeholder="所属商铺">
                  <i slot="suffix" class="el-input__icon el-icon-search" @click="sel_shop = true"></i>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="单据日期：" prop="title">
                {{ $_common.formatDate(time, "yyyy-MM-dd") }}
              </el-form-item>
            </el-col>
            <el-col class="form" :span="6">
              <el-form-item label="制单人：" prop="code">
                {{ form.operatorName }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      <div class="order_bottom">
        <p class="text">转账账户明细</p>
        <el-table border :data="form.records">
          <el-table-column prop="out_account" label="转出账户">
            <template slot-scope="scope">
              <el-input v-model="scope.row.outAccountNumber" placeholder="转出账户" size="small" readonly>
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="transferAccount('out', scope.$index)"
                ></i>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="in_account" label="转入账户">
            <template slot-scope="scope">
              <el-input v-model="scope.row.inAccountNumber" placeholder="转入账户" size="small" readonly>
                <i slot="suffix" class="el-input__icon el-icon-search" @click="inferAccount('in', scope.$index)"></i>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="money" label="金额">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.money"
                :controls="false"
                style="width: 100%"
                placeholder="金额"
                size="small"
                :min="1"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark" size="small" placeholder="备注"></el-input>
            </template>
          </el-table-column>
          <!--            <el-table-column-->
          <!--              prop="remark"-->
          <!--              label="操作"-->
          <!--              width="100px"-->
          <!--            >-->
          <!--              <template slot-scope="scope">-->
          <!--                <el-button-->
          <!--                  :disabled="form.records.length===1"-->
          <!--                  size="mini"-->
          <!--                  type="danger"-->
          <!--                  icon="el-icon-delete"-->
          <!--                  @click="delAccount(scope.$index)"-->
          <!--                ></el-button>-->
          <!--              </template>-->
          <!--            </el-table-column>-->
        </el-table>
      </div>
    </el-form>
    <GoodsChooseShop
      v-if="sel_shop"
      :is-check="false"
      :dialog-visible="sel_shop"
      @close="sel_shop = false"
      @confirm="shopConfirm"
    />
    <!--    账户管理列表-->
    <ChooseType
      v-if="account_show"
      :id="form.shopId"
      :is-check="false"
      :is-show="account_show"
      @cancel="account_show = false"
      @confirm="selAccount"
    />
  </ContainerTit>
</template>

<script>
import GoodsChooseShop from "@/component/goods/GoodsChooseShop.vue";
import ChooseType from "../AccountType";
import {
  editAccountTransfer,
  addAccountTransfer,
  getTempAccountTransferData,
  getAccountTransferInfo,
} from "@/api/Finance";
export default {
  name: "AddPayment",
  components: {
    GoodsChooseShop,
    ChooseType,
  },
  data() {
    return {
      loading: false,
      time: "",
      rules: {
        shopName: [{ required: true, message: "请选择所属商铺", trigger: "change" }],
      },
      form: {
        shopId: "",
        shopName: "",
        operatorName: "",
        records: [
          {
            outAccountId: "",
            outAccountNumber: "",
            outAccountName: "",
            inAccountId: "",
            inAccountNumber: "",
            inAccountName: "",
            money: "",
            remark: "",
          },
        ],
      },
      accountName: "",
      funTransfer_id: "",
      accountIndex: 0,
      sel_shop: false, // 打开店铺选择弹窗
      account_show: false,
      activeName: "one",
    };
  },
  created() {
    if (this.$route.params.id) {
      this.funTransfer_id = this.$route.params.id;
      this.getAccountTransferInfo();
    } else {
      this.form.operatorName = this.userName;
      this.time = parseInt(new Date().getTime() / 1000);
      this.getTempAccountTransferData();
    }
  },
  methods: {
    // 提交数据
    async subData(tempSave) {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (!tempSave) {
            // 保存验证账户
            if (!this.checkSub()) {
              return;
            }
          }
          try {
            this.loading = true;
            const obj = {
              ...this.form,
              tempSave: tempSave,
            };
            if (this.funTransfer_id) {
              const data = await editAccountTransfer(this.funTransfer_id, obj);
            } else {
              const data = await addAccountTransfer(obj);
            }
            this.loading = false;
            this.$message({
              message: tempSave ? "暂存成功" : "提交成功",
              type: "success",
            });
            this.$closeCurrentGoEdit("/Finance/Cashier/FunTransfer");
          } finally {
            this.loading = false;
          }
        }
      });
    },
    checkSub() {
      let isOut = true;
      let isIn = true;
      let ismoney = true;
      for (let i in this.form.records) {
        let item = this.form.records[i];
        if (!item.outAccountId) {
          isOut = false;
          break;
        }
        if (!item.inAccountId) {
          isIn = false;
          break;
        }
        if (!item.money) {
          ismoney = false;
          break;
        }
      }
      if (!ismoney) {
        this.$message.warning("金额必需大于0");
      }
      if (!isIn) {
        this.$message.warning("请选择转入账户");
      }
      if (!isOut) {
        this.$message.warning("请选择转出账户");
      }
      return ismoney && isIn && isOut;
    },
    //  获取暂存数据
    async getTempAccountTransferData() {
      const { data } = await getTempAccountTransferData();

      if (JSON.stringify(data) === "{}") return;
      this.form = {
        shopId: data.shopId,
        shopName: data.shopName,
        operatorName: this.userName,
        records: data.records,
      };
    },
    //  获取详情
    async getAccountTransferInfo() {
      const { data } = await getAccountTransferInfo(this.funTransfer_id);

      this.form = {
        shopId: data.shopId,
        shopName: data.shopName,
        operatorName: data.operatorName,
        records: data.records,
      };
      this.time = data.createTime;
    },
    addAccount() {
      this.form.records.push({
        outAccountId: "",
        outAccountNumber: "",
        outAccountName: "",
        inAccountId: "",
        inAccountNumber: "",
        inAccountName: "",
        money: "",
        remark: "",
      });
    },
    //  获取转出账户的下表
    transferAccount(name, index) {
      if (!this.form.shopId) {
        this.$message.warning("请选择所属商铺");
        return;
      }
      this.accountName = name;
      this.account_show = true;
      this.accountIndex = index;
    },
    inferAccount(name, index) {
      if (!this.form.shopId) {
        this.$message.warning("请选择所属商铺");
        return;
      }
      this.accountName = name;
      this.account_show = true;
      this.accountIndex = index;
    },
    //  选择账户
    selAccount(val) {
      if (this.accountName === "out") {
        if (val[0].id === this.form.records[this.accountIndex].inAccountId) {
          this.$message.warning("抱歉，转出账户与转入账户重复");
          return;
        }
        //  转出账户
        this.form.records[this.accountIndex].outAccountId = val[0].id;
        this.form.records[this.accountIndex].outAccountNumber = val[0].accountNumber;
        this.form.records[this.accountIndex].outAccountName = val[0].name;
        return;
      }
      if (this.accountName === "in") {
        if (val[0].id === this.form.records[this.accountIndex].outAccountId) {
          this.$message.warning("抱歉，转出账户与转入账户重复");
          return;
        }
        // 转入账户
        this.form.records[this.accountIndex].inAccountId = val[0].id;
        this.form.records[this.accountIndex].inAccountNumber = val[0].accountNumber;
        this.form.records[this.accountIndex].inAccountName = val[0].name;
      }
    },
    // 商铺选择
    shopConfirm(val) {
      this.form.shopName = val[0].name;
      this.form.shopId = val[0].id;
    },
  },
};
</script>
<style lang="scss" scoped>
.order_bottom {
  position: relative;
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .order_bottom_btn {
    position: absolute;
    top: 15px;
    right: 20px;
  }
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.btn-top-div {
  position: absolute;
  right: 20px;
  top: 15px;
  z-index: 999;
}
</style>
<style>
.AddFunTransfer {
  background-color: #fff;
}
.AddFunTransfer .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.AddFunTransfer .is-active {
  font-weight: 700;
  color: #000;
}
.AddFunTransfer .el-tabs__nav {
  margin-left: 24px;
}
</style>
