<template>
  <ContainerQuery>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示：1、订单一键退款后会自动生成退款单。 2、销售退货入库单审核后会自动生成退款单。
      3、采退出库单审核后会自动生成退款单。
    </div>
    <el-button
      v-if="$accessCheck($Access.RefundForm_AddRefundForm)"
      slot="left"
      size="small"
      type="primary"
      @click="$router.push(`/Finance/Cashier/AddRefundForm/${type}`)"
    >
      新增退款单
    </el-button>
    <div slot="more">
      <el-form size="small" :inline="true">
        <el-form-item>
          <el-input
            v-model="searchDate.keyword"
            clearable
            style="width: 220px"
            placeholder="单据编号"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <!--          <el-select-->
          <!--            v-model="searchDate.type"-->
          <!--            style="width: 90px; margin-right: 2px"-->
          <!--            @change="customerClear"-->
          <!--          >-->
          <!--            <el-option label="客户" :value="5"></el-option>-->
          <!--            <el-option label="供应商" :value="4"></el-option>-->
          <!--          </el-select>-->
          <SelectCustomer v-if="type === '1'" v-model="unitName" @clear="customerClear" @change="customerSel" />
          <SelectSupplier
            v-if="type === '2'"
            v-model="unitName"
            width="180"
            @clear="customerClear"
            @change="customerSel"
          />
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <el-select-->
        <!--            v-model="searchDate.financeTypeId"-->
        <!--            style="width: 150px"-->
        <!--            clearable-->
        <!--            placeholder="业务类型"-->
        <!--            @change="pageChange(1)"-->
        <!--          >-->
        <!--            <el-option-->
        <!--              v-for="(item, index) in type_options"-->
        <!--              :key="index"-->
        <!--              :label="item.name"-->
        <!--              :value="item.id"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <el-select
            v-model="searchDate.auditStatus"
            style="width: 150px"
            clearable
            placeholder="单据状态"
            @change="pageChange(1)"
          >
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="searchDate.time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <!--    @row-dblclick="goDetail"-->
    <el-table :data="payment_list">
      <el-table-column prop="id" label="ID" width="60" fixed="left"></el-table-column>
      <el-table-column prop="no" label="单据号" min-width="160" fixed="left">
        <template slot-scope="scope">
          <span
            class="click-div"
            @click="$router.push(`/Finance/Cashier/RefundDetail/${scope.row.id}/${scope.row.createTime}`)"
          >
            {{ scope.row.no }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="originNo" label="源单据号" min-width="160">
        <template slot-scope="scope">
          <!--<span class="click-div" @click="lookData(scope.row)">
            {{ scope.row.sourceNo }}
          </span>-->
          <span
            v-if="$accessCheck($Access.InventoryInGetInventoryInInfo)"
            class="click-div"
            @click="goDetail1(scope.row)"
          >
            {{ scope.row.sourceNo }}
          </span>
          <span v-else>{{ scope.row.sourceNo }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="gatheringFlag" prop="financeType" label="单据类型" min-width="120"></el-table-column>
      <el-table-column
        v-if="discountsFlag"
        prop="unitName"
        label="退款单位"
        min-width="120"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column v-if="practicalFlag" prop="money" label="退款金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.money) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="correlationFlag"
        prop="shopName"
        label="相关店铺"
        min-width="140"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column v-if="moneyFlag" prop="receiptTime" label="创建时间" min-width="140">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column v-if="auditFlag" prop="auditStatus" label="单据状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 2" class="success-status"> 已审核 </span>
          <span v-else class="info-status">待审核</span>
        </template>
      </el-table-column>
      <el-table-column header- min-width="120" fixed="right">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.RefundFormupdateRefundStatus)"
            :disabled="parseInt(scope.row.auditStatus) === 2"
            type="text"
            @click="updateRefundStatus(scope.row)"
          >
            审核
          </el-button>
          <!--<el-button
            v-if="$accessCheck($Access.RefundFormEditRefundForm)"
            :disabled="parseInt(scope.row.auditStatus) === 2"
            type="text"
            @click="
              $router.push(
                `/Finance/Cashier/EditRefundForm/${scope.row.id}/${scope.row.createTime}`
              )
            "
          >
            编辑
          </el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <AccountType
      v-if="account_show"
      title="请选择资金账户"
      :shop-id="shopId"
      :is-radio="true"
      :is-show="account_show"
      @cancel="account_show = false"
      @confirm="accountsel"
    />
  </ContainerQuery>
</template>

<script>
import AccountType from "../AccountType";
import SelectCustomer from "@/component/common/SelectCustomer";
import { getAllFinanceTypeNoPage, getAllRefund, updateRefundStatus } from "@/api/Finance";
import SelectSupplier from "@/component/common/SelectSupplier.vue";
export default {
  name: "RefundForm",
  components: {
    SelectCustomer,
    SelectSupplier,
    AccountType,
  },
  data() {
    return {
      audit_params: {},
      account_show: false,
      shopId: "",
      type_options: [],
      customerTypeList: [],
      customerType: [],
      mobile: "",
      unitName: "",
      searchDate: {
        // type: 5,
        keyword: "",
        auditStatus: "",
        financeTypeId: "",
        time: "",
        start: "",
        end: "",
        unitId: "",
      },
      customer_show: false,
      payment_list: [],
      statusList: [
        { value: 1, label: "待审核" },
        { value: 2, label: "已审核" },
      ],
      total: 0,
      page: 1,
      pageSize: 10,
      checkList: ["单据类型", "创建时间", "往来单位", "退款金额", "相关店铺", "单据状态"],
      columns: [
        {
          label: "单据类型",
        },
        {
          label: "创建时间",
        },
        {
          label: "往来单位",
        },
        {
          label: "退款金额",
        },
        {
          label: "相关店铺",
        },
        {
          label: "单据状态",
        },
      ],
      gatheringFlag: true,
      moneyFlag: true,
      discountsFlag: true,
      practicalFlag: true,
      correlationFlag: true,
      auditFlag: true,
      type: "",
    };
  },
  async created() {
    this.type = this.$route.params.type;
    await this.getAllFinanceType();
    await this.getAllRefund();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
    this.type = this.$route.params.type;
  },
  methods: {
    lookData(row) {
      // if (!this.$accessCheck(this.$Access.InventoryOutGetInventoryOutInfo)) {
      //   return;
      // }
      if (row.financeType === "采购退款") {
        this.$router.push("/stock/OutIn/outgoingInfo/" + row.sourceId);
      } else {
        this.$router.push("/order/OutIn/storageInfo/" + row.sourceId);
      }
    },
    goDetail(row) {
      // if (!this.$accessCheck(this.$Access.ReceiptListGetReceivedInfo)) {
      //   return;
      // }
      this.$router.push(`/Finance/Receivable/LookReceipt/${row.id}/${row.createTime}`);
    },
    goDetail1(row) {
      if (row.financeType === "订单退货退款") {
        this.$router.push("/stock/OutIn/storageInfo/" + row.sourceId);
        return;
      }
      this.$router.push({
        path: `/order/manageO/OrderDetails/${1}/${row.sourceId}`,
      });
    },
    // 选择客户
    customerSel(val, list) {
      this.searchDate.unitId = list[0].id;
      this.pageChange(1);
    },
    customerClear() {
      this.searchDate.unitId = "";
      this.unitName = "";
      this.pageChange(1);
    },
    //  获取列表
    async getAllRefund() {
      const { data, pageTotal } = await getAllRefund({
        page: this.page,
        pageSize: this.pageSize,
        financeTypeId: this.searchDate.financeTypeId,
        start: this.searchDate.start,
        end: this.searchDate.end,
        auditStatus: this.searchDate.auditStatus,
        unitId: this.searchDate.unitId,
        no: this.searchDate.keyword,
      });

      this.payment_list = data;
      this.total = pageTotal;
    },
    //  收款单搜索
    // async ReceivedSearch() {
    //   const { data, pageTotal } = await ReceivedSearch({
    //     page: this.page,
    //     pageSize: this.pageSize,
    //     keyword: this.searchDate.keyword,
    //     financeTypeId: this.searchDate.financeTypeId,
    //     auditStatus: this.searchDate.auditStatus,
    //     unitId: this.searchDate.unitId,
    //     start: this.searchDate.start,
    //     end: this.searchDate.end,
    //   });
    //   this.payment_list = data;
    //   this.total = pageTotal;
    // },
    getData() {
      // const isKey = this.$_common.isSerch(this.searchDate);
      // if (isKey) {
      //   this.ReceivedSearch();
      // } else {
      //   this.getAllRefund();
      // }
      this.getAllRefund();
    },
    timeChange(val) {
      if (val && val.length) {
        this.searchDate.start = parseInt(val[0] / 1000);
        this.searchDate.end = parseInt(val[1] / 1000) + 86399;
      } else {
        this.searchDate.start = "";
        this.searchDate.end = "";
      }
      this.pageChange(1);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 审核订单
    updateRefundStatus(row) {
      // this.shopId = row.shopId;
      // this.account_show = true;
      this.$confirm("确认要审核该退款单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.audit_params = {
          id: row.id,
          createTime: row.createTime,
        };
        this.accountsel();
      });
    },
    // 选择账户
    async accountsel(obj) {
      const data = await updateRefundStatus(this.audit_params);
      this.$message({
        type: "success",
        message: "审核成功!",
      });
      this.getData();
    },
    change() {
      this.gatheringFlag = this.checkList.some((item) => item === "单据类型");
      this.moneyFlag = this.checkList.some((item) => item === "创建时间");
      this.discountsFlag = this.checkList.some((item) => item === "往来单位");
      this.practicalFlag = this.checkList.some((item) => item === "退款金额");
      this.correlationFlag = this.checkList.some((item) => item === "相关店铺");
      this.auditFlag = this.checkList.some((item) => item === "单据状态");
    },
    //  获取财务类型
    async getAllFinanceType() {
      const { data } = await getAllFinanceTypeNoPage(3);
      this.type_options = data;
      // this.financeTypeList = data;
      // const finance = data.find((item) => {
      //   return item.name === "销售收款";
      // });
      // this.searchDate.financeTypeId = finance.id;
      // this.form.financeType = finance.name;
    },
    unitChange() {},
  },
};
</script>
<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
