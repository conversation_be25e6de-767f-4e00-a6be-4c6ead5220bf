<!--资金转账-->
<template>
  <ContainerQuery>
    <el-button
      v-if="$accessCheck($Access.FunTransferAddAccountTransfer)"
      slot="left"
      size="small"
      type="primary"
      @click="$router.push('/Finance/Cashier/AddFunTransfer')"
    >
      新增
    </el-button>
    <div slot="more">
      <el-form size="small" :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-input
            v-model="no"
            clearable
            style="width: 220px"
            placeholder="单据编号"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="orderDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="outAccountNumber"
            clearable
            placeholder="付款账户"
            style="width: 150px"
            @clear="AccountClear('out')"
          >
            <i slot="suffix" class="el-input__icon el-icon-search" @click="transferAccount('out')"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="inAccountNumber"
            clearable
            style="width: 150px"
            placeholder="收款账户"
            @clear="AccountClear('inAccount')"
          >
            <i slot="suffix" class="el-input__icon el-icon-search" @click="inferAccount('in')"></i>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData">
      <el-table-column prop="id" label="ID" fixed="left" min-width="50" align="left"></el-table-column>
      <el-table-column prop="no" label="单据编号" align="left" min-width="180" fixed="left"></el-table-column>
      <el-table-column prop="createTime" label="单据日期" align="left" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime, "yyyy-MM-dd") || "" }}
        </template>
      </el-table-column>
      <el-table-column prop="outAccountNumber" label="转出账户" align="left" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.records[0].outAccountNumber }}
        </template>
      </el-table-column>
      <el-table-column prop="inAccountNumber" label="转入账户" align="left" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.records[0].inAccountNumber }}
        </template>
      </el-table-column>
      <el-table-column v-if="moneyFlag" prop="money" align="left" label="金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.records[0].money) }}
        </template>
      </el-table-column>
      <el-table-column v-if="totalFlag" prop="totalMoney" label="合计金额" align="left" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalMoney) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="preparedByFlag"
        prop="operatorName"
        label="制单人"
        align="left"
        min-width="110"
      ></el-table-column>
      <el-table-column v-if="remarkFlag" prop="remark" label="备注" show-overflow-tooltip align="left" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.records[0].remark }}
        </template>
      </el-table-column>
      <el-table-column v-if="stateFlag" prop="auditStatus" label="状态" align="left" min-width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 1" class="info-status"> 待审核 </span>
          <span v-else class="success-status">已审核</span>
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" label="操作" min-width="160" fixed="right">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.AccountListEditAccountTransfer)"
            :disabled="scope.row.auditStatus === 2"
            type="text"
            @click="$router.push(`/Finance/Cashier/EditFunTransfer/${scope.row.id}`)"
          >
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.FunTransferUpdateAccountTransferStatus)"
            :disabled="scope.row.auditStatus === 2"
            type="text"
            @click="updateStatus(scope.row)"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <AccountType
      v-if="account_show"
      :is-check="false"
      :is-show="account_show"
      @cancel="account_show = false"
      @confirm="selAccount"
    />
  </ContainerQuery>
</template>

<script>
import AccountType from "../noShopIdAccount";
import { getAllAccountTransfer, updateAccountTransferStatus } from "@/api/Finance";

export default {
  name: "FunTransfer",
  components: {
    AccountType,
  },
  data() {
    return {
      account_show: false,
      inAccountName: "",
      outAccountName: "",
      inAccountNumber: "",
      outAccountNumber: "",
      time: "",
      inAccountId: "",
      outAccountId: "",
      no: "",
      start: "",
      end: "",

      tableData: [],
      total: 0,
      page: 1,
      accountName: "",
      pageSize: 10,
      checkList: ["金额", "合计金额", "制单人", "备注", "状态"],
      columns: [
        {
          label: "金额",
        },
        {
          label: "合计金额",
        },
        {
          label: "制单人",
        },
        {
          label: "备注",
        },
        {
          label: "状态",
        },
      ],
      moneyFlag: true,
      totalFlag: true,
      preparedByFlag: true,
      remarkFlag: true,
      stateFlag: true,
    };
  },
  created() {
    this.pageChange(1);
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllAccountTransfer();
  },
  methods: {
    // 获取列表
    async getAllAccountTransfer() {
      const { data, pageTotal } = await getAllAccountTransfer({
        page: this.page,
        pageSize: this.pageSize,
        start: this.start,
        end: this.end,
        inAccountId: this.inAccountId,
        outAccountId: this.outAccountId,
        no: this.no,
      });

      this.tableData = data;
      this.total = pageTotal;
    },
    //  审核
    updateStatus(row) {
      this.$confirm("确定要审核此账单", "提示", {
        confirmButtonText: "确定",
        cancelButtonClass: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateAccountTransferStatus(row.id);

        this.getAllAccountTransfer();
        this.$message({
          type: "success",
          message: "审核成功",
        });
      });
    },
    //  获取转出账户的下表
    transferAccount(name) {
      this.accountName = name;
      this.account_show = true;
    },
    inferAccount(name) {
      this.accountName = name;
      this.account_show = true;
    },
    AccountClear(val) {
      if (val === "out") {
        this.outAccountId = "";
        this.outAccountNumber = "";
        this.outAccountName = "";
        this.pageChange(1);
      } else {
        this.inAccountId = "";
        this.inAccountNumber = "";
        this.inAccountName = "";
        this.pageChange(1);
      }
    },
    selAccount(val) {
      if (this.accountName === "out") {
        //  转出账户
        this.outAccountId = val[0].id;
        this.outAccountNumber = val[0].accountNumber;
        this.outAccountName = val[0].name;
        this.pageChange(1);
        return;
      }
      if (this.accountName === "in") {
        // 转入账户
        this.inAccountId = val[0].id;
        this.inAccountNumber = val[0].accountNumber;
        this.inAccountName = val[0].name;
        this.pageChange(1);
        return;
      }
    },
    //  订单时间
    orderDate(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.pageChange(1);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllAccountTransfer();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    change() {
      this.moneyFlag = this.checkList.some((item) => item === "金额");
      this.totalFlag = this.checkList.some((item) => item === "合计金额");
      this.preparedByFlag = this.checkList.some((item) => item === "制单人");
      this.remarkFlag = this.checkList.some((item) => item === "备注");
      this.stateFlag = this.checkList.some((item) => item === "状态");
    },
  },
};
</script>
<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
