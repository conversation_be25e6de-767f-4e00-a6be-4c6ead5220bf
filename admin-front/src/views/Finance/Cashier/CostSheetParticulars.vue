<template>
  <ContainerTit class="CostSheetParticulars">
    <!--      <div class="detail-tab-item" style="padding: 0 20px">-->
    <!--        <el-tabs v-model="form.payType">-->
    <!--          <el-tab-pane-->
    <!--            v-if="form.payType === '5'"-->
    <!--            label="日常收入"-->
    <!--            name="5"-->
    <!--          ></el-tab-pane>-->
    <!--          <el-tab-pane v-else label="日常支出" name="4"></el-tab-pane>-->
    <!--        </el-tabs>-->
    <!--      </div>-->
    <el-tabs v-model="activeName">
      <el-tab-pane :label="label" name="one">
        <el-row style="padding-bottom: 13px">
          <el-col :span="24">
            <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">基础信息</p>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">往来单位</span>
            <span class="form_right">
              {{ form.currentUnit }}
              <span v-if="form.type === 3">（员工）</span>
              <span v-if="form.type === 4">（客户）</span>
              <span v-if="form.type === 5">（供应商）</span>
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">单据日期</span>
            <span class="form_right">
              {{ $_common.formatDate(form.billTime) }}
            </span>
          </el-col>
          <el-col class="form" :span="6" style="padding-left: 54px">
            <span class="form_left">经手人</span>
            <span class="form_right">
              {{ form.manager }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">费用类型</span>
            <span class="form_right">
              {{ form.consumeTypeName }}
            </span>
          </el-col>
          <el-col class="form" :span="6" style="padding-left: 68px">
            <span class="form_left">店铺</span>
            <span class="form_right">
              {{ form.shopName }}
            </span>
          </el-col>
        </el-row>
        <div class="order_bottom">
          <p class="text">{{ label }}</p>
          <el-table :data="form.expenseSingleAccountDate">
            <el-table-column prop="expenseName" min-width="160">
              <template slot="header">
                <span>收入费用名称</span>
              </template>
              <template slot-scope="scope">
                {{ scope.row.expenseName }}
              </template>
            </el-table-column>
            <el-table-column prop="amount" min-width="120">
              <template slot="header">
                <span>费用金额</span>
              </template>
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.amount) }}
              </template>
            </el-table-column>
            <el-table-column label="优惠金额" min-width="120">
              <template slot-scope="scope">
                {{ $_common.formattedNumber(scope.row.preferentialAmount) }}
              </template>
            </el-table-column>
            <el-table-column label="实际收款金额" min-width="120">
              <template slot-scope="scope">
                {{ $NP.minus(scope.row.amount, scope.row.preferentialAmount) }}
              </template>
            </el-table-column>
            <el-table-column min-width="160">
              <template slot="header">
                <span>结算方式</span>
              </template>
              <template slot-scope="scope">
                {{ scope.row.settlementMethodName }}
              </template>
            </el-table-column>
            <el-table-column prop="settlementAccount" label="结算账户" min-width="160">
              <template slot="header">
                <span>结算账户</span>
              </template>
              <template slot-scope="scope">
                {{ scope.row.settlementAccount }}
              </template>
            </el-table-column>
            <el-table-column label="备注" min-width="200">
              <template slot-scope="scope">
                {{ scope.row.remark }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
    <!--    <div v-if="form.payType === '5'">-->
    <!--      <div class="detail-tab-item">-->
    <!--        <p class="detail-tab-title">基础信息</p>-->
    <!--        <div class="detail-tab-main">-->
    <!--          <el-row>-->
    <!--            <el-col :span="6" style="display: flex">-->
    <!--              <el-form-item label="往来单位:" prop="currentUnit">-->
    <!--                {{ form.currentUnit }}-->
    <!--                <span v-if="form.type === 3">（员工）</span>-->
    <!--                <span v-if="form.type === 4">（客户）</span>-->
    <!--                <span v-if="form.type === 5">（供应商）</span>-->
    <!--              </el-form-item>-->
    <!--            </el-col>-->
    <!--            <el-col :span="6">-->
    <!--              <el-form-item label="单据日期:">-->
    <!--                <template>-->
    <!--                  <div class="block">-->
    <!--                    {{ $_common.formatDate(form.billTime) }}-->
    <!--                  </div>-->
    <!--                </template>-->
    <!--              </el-form-item>-->
    <!--            </el-col>-->
    <!--            <el-col :span="5">-->
    <!--              <el-form-item label="经手人:">-->
    <!--                {{ form.manager }}-->
    <!--              </el-form-item>-->
    <!--            </el-col>-->
    <!--            <el-col :span="6">-->
    <!--              <el-form-item label="费用类型:" prop="consumeType">-->
    <!--                {{ form.consumeTypeName }}-->
    <!--              </el-form-item>-->
    <!--            </el-col>-->
    <!--            <el-col :span="6">-->
    <!--              <el-form-item label="选择店铺" prop="shopName">-->
    <!--                {{ form.shopName }}-->
    <!--              </el-form-item>-->
    <!--            </el-col>-->
    <!--          </el-row>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div class="detail-tab-item">-->
    <!--        <p class="detail-tab-title">日常收入</p>-->
    <!--        <div class="detail-tab-main">-->
    <!--          <el-table :data="form.expenseSingleAccountDate">-->
    <!--            <el-table-column prop="expenseName" min-width="160">-->
    <!--              <template slot="header">-->
    <!--                <span style="color: red">*</span>-->
    <!--                <span>收入费用名称</span>-->
    <!--              </template>-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ scope.row.expenseName }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column prop="amount" min-width="120">-->
    <!--              <template slot="header">-->
    <!--                <span style="color: red">*</span>-->
    <!--                <span>费用金额</span>-->
    <!--              </template>-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ $_common.formattedNumber(scope.row.amount) }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column label="优惠金额" min-width="120">-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ $_common.formattedNumber(scope.row.preferentialAmount) }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column-->
    <!--              label="实际收款金额"-->
    <!--              min-width="120"-->
    <!--              align="center"-->
    <!--            >-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ $NP.minus(scope.row.amount, scope.row.preferentialAmount) }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column min-width="160">-->
    <!--              <template slot="header">-->
    <!--                <span style="color: red">*</span>-->
    <!--                <span>结算方式</span>-->
    <!--              </template>-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ scope.row.settlementMethodName }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column-->
    <!--              prop="settlementAccount"-->
    <!--              label="结算账户"-->
    <!--              min-width="160"-->
    <!--            >-->
    <!--              <template slot="header">-->
    <!--                <span style="color: red">*</span>-->
    <!--                <span>结算账户</span>-->
    <!--              </template>-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ scope.row.settlementAccount }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column label="备注" min-width="200">-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ scope.row.remark }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--          </el-table>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div v-if="form.payType === '4'">-->
    <!--      <div class="detail-tab-item">-->
    <!--        <p class="detail-tab-title">基础信息</p>-->
    <!--        <div class="detail-tab-main">-->
    <!--          <el-row>-->
    <!--            <el-col :span="6" style="display: flex">-->
    <!--              <el-form-item label="往来单位:" prop="currentUnit">-->
    <!--                <span v-if="form.type === 3">员工：</span>-->
    <!--                <span v-if="form.type === 4">客户：</span>-->
    <!--                <span v-if="form.type === 5">供应商：</span>-->
    <!--                {{ form.currentUnit }}-->
    <!--              </el-form-item>-->
    <!--            </el-col>-->
    <!--            <el-col :span="6">-->
    <!--              <el-form-item label="单据日期:">-->
    <!--                <template>-->
    <!--                  <div class="block">-->
    <!--                    {{ $_common.formatDate(form.billTime) }}-->
    <!--                  </div>-->
    <!--                </template>-->
    <!--              </el-form-item>-->
    <!--            </el-col>-->
    <!--            <el-col :span="5">-->
    <!--              <el-form-item label="经手人:">-->
    <!--                {{ form.manager }}-->
    <!--              </el-form-item>-->
    <!--            </el-col>-->
    <!--            <el-col :span="6">-->
    <!--              <el-form-item label="费用类型:" prop="consumeType">-->
    <!--                {{ form.consumeTypeName }}-->
    <!--              </el-form-item>-->
    <!--            </el-col>-->
    <!--          </el-row>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div class="detail-tab-item">-->
    <!--        <p class="detail-tab-title">日常收入</p>-->
    <!--        <div class="detail-tab-main">-->
    <!--          <el-table :data="form.expenseSingleAccountDate">-->
    <!--            <el-table-column prop="expenseName" min-width="160">-->
    <!--              <template slot="header">-->
    <!--                <span style="color: red">*</span>-->
    <!--                <span>收入费用名称</span>-->
    <!--              </template>-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ scope.row.expenseName }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column prop="amount" min-width="120">-->
    <!--              <template slot="header">-->
    <!--                <span style="color: red">*</span>-->
    <!--                <span>费用金额</span>-->
    <!--              </template>-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ $_common.formattedNumber(scope.row.amount) }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column label="优惠金额" min-width="120">-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ $_common.formattedNumber(scope.row.preferentialAmount) }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column-->
    <!--              label="实际收款金额"-->
    <!--              min-width="120"-->
    <!--              align="center"-->
    <!--            >-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ $NP.minus(scope.row.amount, scope.row.preferentialAmount) }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column min-width="160">-->
    <!--              <template slot="header">-->
    <!--                <span style="color: red">*</span>-->
    <!--                <span>结算方式</span>-->
    <!--              </template>-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ scope.row.settlementMethodName }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column-->
    <!--              prop="settlementAccount"-->
    <!--              label="结算账户"-->
    <!--              min-width="160"-->
    <!--            >-->
    <!--              <template slot="header">-->
    <!--                <span style="color: red">*</span>-->
    <!--                <span>结算账户</span>-->
    <!--              </template>-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ scope.row.settlementAccount }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--            <el-table-column label="备注" min-width="200">-->
    <!--              <template slot-scope="scope">-->
    <!--                {{ scope.row.remark }}-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--          </el-table>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
  </ContainerTit>
</template>
<style></style>

<script>
import { getExpenseSingleInfo } from "@/api/Finance";
export default {
  data() {
    return {
      form: {
        billTime: new Date().getTime(), //单据日期
        currentUnit: "", //往来单位
        // money: "", //金额
        currentUnitId: "", //往来单位id
        payType: "5", // 页面tab切换
        consumeTypeId: "", // 费用类型id
        consumeTypeName: "",
        manager: "", //经手人
        totalCollectionAmount: 0, // 总费用金额
        totalPreferentialAmount: 0, // 优惠总金额
        totalActualAmount: 0, // 实付总金额
        expenseSingleAccountDate: [
          {
            expenseName: "", // 费用名称
            amount: 0, // 金额
            settlementMethod: "", //结算方式
            accountId: 0, // 结算账户id
            remark: "", // 备注
            settlementAccount: "", // 结算账户
            preferentialAmount: 0, //优惠金额
          },
        ],
      },
      rules: {
        currentUnit: [{ required: true, trigger: "blur" }],
        consumeType: [{ required: true, trigger: "blur" }],
        shopName: [{ required: true, trigger: "blur" }],
      },
      activeName: "one",
      label: "",
    };
  },
  async created() {
    this.cost_id = this.$route.params.id;
    this.form.manager = this.userName;
    if (this.cost_id) {
      await this.getExpenseSingleInfo(this.cost_id);
    }
  },
  methods: {
    async getExpenseSingleInfo(id) {
      const { data } = await getExpenseSingleInfo(id);
      this.form = data;
      this.form.payType = data.payType + "";
      this.form.billTime = data.createTime * 1000;
      this.form.expenseSingleAccountDate = data.accountList;
      if (this.form.payType === "5") {
        this.label = "日常收入";
      } else {
        this.label = "日常支出";
      }
    },
  },
};
</script>
<style scoped lang="scss">
.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
</style>
<style>
.CostSheetParticulars {
  background-color: #fff;
}
.CostSheetParticulars .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.CostSheetParticulars .is-active {
  font-weight: 700;
  color: #000;
}
.CostSheetParticulars .el-tabs__nav {
  margin-left: 24px;
}
</style>
