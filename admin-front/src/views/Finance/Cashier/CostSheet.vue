<template>
  <ContainerQuery>
    <el-button
      v-if="$accessCheck($Access.CostSheetaddCostSheet)"
      slot="left"
      size="small"
      type="primary"
      :loading="loading"
      @click="addCost"
    >
      新增
    </el-button>
    <el-form slot="more" size="small" :inline="true">
      <el-form-item>
        <el-date-picker
          v-model="time"
          clearable
          type="daterange"
          value-format="timestamp"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="orderDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select v-model="options_value" placeholder="费用类型" @change="pageChange(1)">
          <el-option v-for="(item, index) in options" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <SelectCustomer v-model="customer_name" placeholder="往来单位" @clear="customerClear" @change="customerSel" />
      </el-form-item>
      <el-form-item>
        <el-select v-model="expenditure_type_value" placeholder="支出类型" @change="pageChange(1)">
          <el-option
            v-for="(item, index) in expenditure_type"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="order_status_value" placeholder="订单状态" @change="pageChange(1)">
          <el-option
            v-for="(item, index) in order_status"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="id" label="ID" width="50"></el-table-column>
      <el-table-column prop="no" label="单据编号" width="140">
        <template slot-scope="scope">
          <span class="click-div" @click="$router.push(`/Finance/Cashier/CostSheetParticulars/${scope.row.id}`)">
            {{ scope.row.no }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="currentUnit" label="往来单位" min-width="160" show-overflow-tooltip></el-table-column>
      <el-table-column prop="consumeTypeName" min-width="140" label="费用类型"></el-table-column>
      <el-table-column prop="totalActualAmount" label="费用总金额" min-width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.payType === 5" class="success-status">
            +{{ $_common.formattedNumber(scope.row.totalActualAmount) }}
          </span>
          <span v-else class="danger-status"> -{{ $_common.formattedNumber(scope.row.totalActualAmount) }} </span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="manager"></el-table-column>
      <el-table-column prop="auditStatus" label="单据状态" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 2" class="success-status"> 已审核 </span>
          <span v-if="scope.row.auditStatus === 1" class="warning-status"> 待审核 </span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作" width="200">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.CostSheetaudtCostSheet)"
            type="text"
            :disabled="scope.row.auditStatus === 2"
            @click="updateExpenseStatus(scope.row)"
          >
            审核
          </el-button>
          <el-button
            v-if="$accessCheck($Access.CostSheeteditCostSheet)"
            type="text"
            :disabled="scope.row.auditStatus === 2"
            @click="$router.push(`/Finance/Cashier/EditCostSheet/${scope.row.id}`)"
          >
            编辑
          </el-button>
          <el-button type="text" @click="$router.push(`/Finance/Cashier/CostSheetParticulars/${scope.row.id}`)">
            详情
          </el-button>
          <el-button
            v-if="$accessCheck($Access.CostSheetdeleteCostSheet)"
            type="text"
            :disabled="scope.row.auditStatus === 2"
            @click="delExpenseSingle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>
<script>
import { getAllExpenseSingle, delExpenseSingle, updateExpenseStatus, getAllAccountType } from "@/api/Finance";
import SelectCustomer from "@/component/common/SelectCustomer.vue";
export default {
  components: {
    SelectCustomer,
  },
  data() {
    return {
      tableData: [],
      loading: false,
      time: "",
      start: "",
      end: "",
      customerId: "",
      customer_name: "",
      options_value: "",
      options: [],
      order_status: [
        { label: "已审核", value: 2 },
        { label: "未审核", value: 1 },
      ],
      order_status_value: "",
      page: 1,
      pageSize: 10,
      total: 0,
      expenditure_type: [
        { label: "收入", value: 5 },
        { label: "支出", value: 4 },
      ],
      expenditure_type_value: "",
    };
  },
  created() {
    this.getAllExpenseSingle();
    this.getAllAccountType();
  },
  methods: {
    async getAllExpenseSingle() {
      const { data } = await getAllExpenseSingle({
        page: this.page,
        pageSize: this.pageSize,
        payType: this.expenditure_type_value,
        currentUnitId: this.customerId,
        consumeTypeId: this.options_value,
        auditStatus: this.order_status_value,
        start: this.start,
        end: this.end,
      });
      this.total = data.pageTotal;
      this.tableData = data;
    },
    delExpenseSingle(id) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const { data } = await delExpenseSingle(id);
        await this.getAllExpenseSingle();
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
    addCost() {
      this.loading = true;
      try {
        this.$router.push("/Finance/Cashier/AddCostSheet");
        this.loading = false;
      } catch {
        this.$message.warning("发生了未知错误，请重试");
        this.loading = false;
      }
    },
    updateExpenseStatus(row) {
      this.$confirm("确定要通过审核？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const { data } = await updateExpenseStatus({
          id: row.id,
          createTime: row.createTime,
        });
        this.$message.success("审核通过");
        await this.getAllExpenseSingle();
      });
    },
    // 选择客户
    customerSel(val, list) {
      this.customerId = list[0].id;
      this.pageChange(1);
    },
    customerClear() {
      this.customerId = "";
      this.customer_name = "";
      this.pageChange(1);
    },
    //  订单时间
    orderDate(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.pageChange(1);
    },
    async getAllAccountType() {
      const { data } = await getAllAccountType({ type: 0 });
      this.options = data;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllExpenseSingle();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.getAllExpenseSingle();
    },
  },
};
</script>
<style></style>
