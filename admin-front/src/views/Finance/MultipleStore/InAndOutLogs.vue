<template>
  <ContainerQuery>
    <!--    <div slot="left">-->
    <!--      <el-button type="primary" @click="getAllMerchantDetail(1)">-->
    <!--        导出-->
    <!--      </el-button>-->
    <!--    </div>-->
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <el-date-picker
          v-model="time"
          clearable
          type="daterange"
          value-format="timestamp"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="timeChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="auditStatus"
          size="small"
          style="width: 150px"
          clearable
          placeholder="收入/支出"
          @clear="delStatu"
          @change="pageChange(1)"
        >
          <el-option
            v-for="item in shenhe_options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <vxe-table :data="tableData" border="inner">
      <vxe-table-column field="id" title="ID" width="40px"></vxe-table-column>
      <vxe-table-column field="originNo" title="订单"></vxe-table-column>
      <vxe-table-column v-if="auditStatus == ''" field="money" title="支出/收入">
        <template v-slot="{ row }">
          <span v-if="row.type === 5" class="success-status"> +{{ $_common.formatNub(row.money) }} </span>
          <span v-else class="danger-status"> -{{ $_common.formatNub(row.money) }} </span>
        </template>
      </vxe-table-column>
      <vxe-table-column v-if="auditStatus == 4" field="money" title="支出">
        <template v-slot="{ row }">
          <span v-if="auditStatus == 4" class="danger-status"> -{{ $_common.formatNub(row.money) }} </span>
          <!--          <span v-if="auditStatus == 4">-{{ row.money }}</span>-->
        </template>
      </vxe-table-column>
      <vxe-table-column v-if="auditStatus == 5" field="money" title="收入">
        <template v-slot="{ row }">
          <span v-if="auditStatus === 5" class="success-status"> +{{ row.money }} </span>
          <!--          <span v-if="auditStatus == 5">+{{ row.money }}</span>-->
        </template>
      </vxe-table-column>
      <vxe-table-column field="afterMoney" title="变动后金额">
        <template v-slot="{ row }">
          <span v-if="row.type === 5" style="color: red">
            {{ $_common.formattedNumber(row.afterMoney) }}
          </span>
          <span v-else style="color: red">
            {{ $_common.formattedNumber(row.afterMoney) }}
          </span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="remark" title="备注"></vxe-table-column>
      <vxe-table-column field="type" title="类型">
        <template v-slot="{ row }">
          <span v-if="row.type === 5">收入</span>
          <span v-else>支出</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="createTime" title="创建时间">
        <template v-slot="{ row }">
          {{ $_common.formatDate(row.createTime) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="updateTime" title="完成时间">
        <template v-slot="{ row }">
          {{ $_common.formatDate(row.updateTime) }}
        </template>
      </vxe-table-column>
    </vxe-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import { mapGetters } from "vuex";

import { getAllMerchantDetail, exportgetAllMerchantDetail } from "@/api/Merchants";
import { exportSearchAllPurchaseDetails, searchAllPurchaseDetails } from "@/api/Purchase";
export default {
  name: "InAndOutLogs",
  data() {
    return {
      auditStatus: "",
      shenhe_options: [
        { value: 5, label: "收入" },
        { value: 4, label: "支出" },
      ],
      tableData: [],
      time: [],
      search_form: {
        start: "",
        end: "",
        keyword: "",
      },
      total: 0,
      page: 1,
      pageSize: 10,
      start: "",
      end: "",
      merchantId: "",
      spanArr: "",
      pos: "",
    };
  },
  computed: {
    ...mapGetters({ storeData: "MUser/storeData" }),
  },
  created() {
    if (this.systemType === 3) {
      this.merchantId = this.storeData.merchantData.id;
    }
    this.getAllMerchantDetail();
  },
  methods: {
    delStatu() {
      this.auditStatus = "";
      this.pageChange(1);
    },
    async getAllMerchantDetail(exports) {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        merchantId: this.merchantId,
        type: this.auditStatus,
        start: this.start,
        end: this.end,
      };
      if (exports) {
        params.export = exports;
        const target = await exportgetAllMerchantDetail({
          ...params,
        });
      } else {
        const res = await getAllMerchantDetail({
          ...params,
        });
        console.log(res.data);
        this.tableData = res.data;
        this.total = res.pageTotal;
      }
      // const res = await getAllMerchantDetail({
      //   ...params,
      // });
      console.log(this.tableData);
      // 合并单元格
      const getSpanArr = this.$_common.getSpanArr(this.tableData, "no");
      // this.spanArr = getSpanArr.spanArr;
      // this.pos = getSpanArr.pos;
    },
    timeChange(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.getAllMerchantDetail();
    },
    // 切页
    pageChange(val) {
      this.page = val;

      console.log(this.type);
      this.getAllMerchantDetail();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
