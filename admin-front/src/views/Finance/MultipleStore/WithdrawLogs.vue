<template>
  <ContainerQuery>
    <div slot="left">
      <span style="margin-right: 10px">
        账户余额：
        <span style="color: #ff4400">
          {{ $_common.formattedNumber(balance) }}
        </span>
      </span>
      <el-button type="primary" @click="Withdrawal">提现</el-button>
      <!--      <el-button type="primary">导出</el-button>-->
    </div>
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <el-date-picker
          v-model="time"
          clearable
          type="daterange"
          value-format="timestamp"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="timeChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="type"
          size="small"
          style="width: 150px"
          clearable
          placeholder="提现类型"
          @change="pageChange(1)"
        >
          <el-option
            v-for="item in shenhe_options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <vxe-table :data="tableData" border="inner">
      <vxe-table-column field="id" title="ID"></vxe-table-column>
      <vxe-table-column field="money" title="提现金额">
        <template #default="{ row }">
          {{ $_common.formattedNumber(row.money) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="no" title="订单号" width="215px"></vxe-table-column>
      <vxe-table-column field="type" title="提现类型">
        <template #default="{ row }">
          <span v-if="row.type === 1">微信</span>
          <span v-if="row.type === 2">支付宝</span>
          <span v-if="row.type === 3">银行</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="auditStatus" title="提现状态">
        <template #default="{ row }">
          <span v-if="row.auditStatus === 1" class="info-status">已申请</span>
          <span v-if="row.auditStatus === 2" class="success-status"> 已打款 </span>
          <span v-if="row.auditStatus === 3" class="warning-status"> 已拒绝 </span>
          <span v-if="row.auditStatus === 4" class="success-status"> 待打款 </span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="createTime" title="提现时间">
        <template #default="{ row }">
          {{ $_common.formatDate(row.createTime) }}
        </template>
      </vxe-table-column>
      <vxe-table-column title="操作">
        <template #default="{ row }">
          <el-button type="text" @click="examine(row)">查看</el-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <WithdrawMode :show="show" :balance="balance" @cancel="cancel" @confirm="confirm"></WithdrawMode>
    <vxe-modal v-model="examine_flag" show-footer>
      <template #title>
        <span>明细</span>
      </template>
      <template #default>
        <p class="order-info-p">
          <span class="de_label">姓名：</span>
          <span class="de_val">{{ examine_value.accountContent.name }}</span>
        </p>
        <p class="order-info-p">
          <span class="de_label">{{ label_value }}：</span>
          <span class="de_val">
            {{ examine_value.accountContent.accountNum }}
          </span>
        </p>
        <p v-if="examine_value.type === 3" class="order-info-p">
          <span class="de_label">开户行 ：</span>
          <span class="de_val">
            {{ examine_value.accountContent.openingBank }}
          </span>
        </p>
      </template>
    </vxe-modal>
  </ContainerQuery>
</template>

<script>
import WithdrawMode from "./WithdrawMode.vue";
import { getAllMerchantWithdraw, getInfoMerchant } from "@/api/Merchants";
import { mapGetters } from "vuex";
export default {
  name: "WithdrawLogs",
  components: {
    WithdrawMode,
  },
  data() {
    return {
      show: false,
      tableData: [],
      time: [],
      search_form: {
        start: "",
        end: "",
        keyword: "",
      },
      type: "",
      shenhe_options: [
        { value: "", label: "全部" },
        { value: 1, label: "微信" },
        { value: 2, label: "支付宝" },
        { value: 3, label: "银行" },
      ],
      total: 0,
      page: 1,
      pageSize: 10,
      examine_flag: false,
      examine_value: {},
      label_value: "",
      merchantId: "", // 多商户商户ID
      balance: "", //商户金额
      start: "",
      end: "",
    };
  },
  computed: {
    ...mapGetters({ storeData: "MUser/storeData" }),
  },
  async created() {
    if (this.systemType === 3) {
      this.merchantId = this.storeData.merchantData.id;
    }
    await this.getAllMerchantWithdraw();
    await this.getInfoMerchant();
  },
  methods: {
    Withdrawal() {
      this.show = true;
    },
    cancel() {
      this.show = false;
    },
    async confirm() {
      this.show = false;
      await this.getInfoMerchant();
      await this.getAllMerchantWithdraw();
    },
    timeChange(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.getAllMerchantWithdraw();
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllMerchantWithdraw();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.getAllMerchantWithdraw();
    },
    async getAllMerchantWithdraw() {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        auditStatus: "",
        type: this.type,
        merchantId: this.merchantId,
        start: this.start,
        end: this.end,
      };
      const { data, pageTotal } = await getAllMerchantWithdraw({
        ...params,
      });
      this.tableData = data;
      this.total = pageTotal;
    },
    examine(row) {
      this.examine_flag = true;
      this.examine_value = row;
      if (this.examine_value.type === 1) {
        this.label_value = "微信账号";
      } else if (this.examine_value.type === 2) {
        this.label_value = "支付宝账号";
      } else if (this.examine_value.type === 3) {
        this.label_value = "银行卡号";
      }
    },
    async getInfoMerchant() {
      const { data } = await getInfoMerchant(this.merchantId);
      this.balance = data.balance;
    },
  },
};
</script>

<style scoped></style>
