<template>
  <ContainerQuery>
    <div slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="LocationFrom"
          ></el-date-picker>
        </el-form-item>
        <el-form-item prop="warehouseName">
          <el-input v-model="warehouseName" placeholder="仓库" readonly>
            <i slot="suffix" class="el-input__icon el-icon-search" @click="warehouse_show = true"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="auditStatus"
            size="small"
            style="width: 150px"
            clearable
            placeholder="单据类型"
            @clear="delStatu"
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in shenhe_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <vxe-table :data="tableData" border="inner">
      <vxe-table-column field="time" title="出库时间"></vxe-table-column>
      <vxe-table-column field="no" title="单据编号"></vxe-table-column>
      <vxe-table-column field="type" title="单据类型"></vxe-table-column>
      <vxe-table-column field="1" title="结算仓库"></vxe-table-column>
      <vxe-table-column field="2" title="商品编码"></vxe-table-column>
      <vxe-table-column field="3" title="商品名称"></vxe-table-column>
      <vxe-table-column field="4" title="出库数量"></vxe-table-column>
      <vxe-table-column field="5" title="出库均价"></vxe-table-column>
      <vxe-table-column field="6" title="结算金额"></vxe-table-column>
      <vxe-table-column field="7" title="结算状态"></vxe-table-column>
    </vxe-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <WarehouseModel
      v-if="warehouse_show"
      :is-check="false"
      :is-show="warehouse_show"
      @confirm="selWarehouse"
      @cancel="warehouse_show = false"
    />
  </ContainerQuery>
</template>
<script>
import WarehouseModel from "@/component/common/WarehouseModel.vue";
export default {
  components: {
    WarehouseModel,
  },
  data() {
    return {
      time: "",
      warehouseName: "",
      warehouseId: "",
      warehouse_show: false,
      auditStatus: "",
      shenhe_options: [
        { value: 0, label: "全部状态" },
        { value: 1, label: "未审核" },
        { value: 2, label: "已审核" },
      ],
      activeName: "1",
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
    };
  },
  methods: {
    LocationFrom() {},
    selWarehouse(row) {
      this.warehouseName = row[0].warehouseName;
      this.warehouseId = row[0].id;
    },
    pageChange() {},
    delStatu() {
      this.auditStatus = "";
      this.pageChange(1);
    },
    sizeChange() {},
  },
};
</script>
<style scoped></style>
