<template>
  <el-dialog title="申请提现" :visible.sync="show" width="30%" :before-close="cancel">
    <div>
      <el-form ref="add_form" :model="add_form" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="提现方式">
          <el-select v-model="type" placeholder="请选择" @change="change">
            <el-option v-for="item in type_options" :key="item.type" :label="item.label" :value="item.type"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提现金额">
          <el-input-number v-model="money" :controls="false" :min="0" style="width: 215px"></el-input-number>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="add_form.name"></el-input>
        </el-form-item>
        <el-form-item :label="typeName">
          <el-input v-model="add_form.accountNum"></el-input>
        </el-form-item>
        <el-form-item v-if="type === 3" label="开户行">
          <el-input v-model="add_form.openingBank"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="addMerchantWithdraw">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addMerchantWithdraw } from "@/api/Merchants";
import { mapGetters } from "vuex";
export default {
  name: "WithdrawMode",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    balance: {
      type: [String, Number],
      default: 0,
    },
  },
  data() {
    return {
      typeName: "微信账号",
      rules: {},
      type: 1,
      add_form: {
        name: "",
        accountNum: "",
        openingBank: "", //开户行
      },
      money: "",
      type_options: [
        {
          label: "微信钱包",
          type: 1,
        },
        {
          label: "支付宝",
          type: 2,
        },
        {
          label: "银行打款",
          type: 3,
        },
      ],
      merchantId: "", // 多商户商户ID
    };
  },
  computed: {
    ...mapGetters({ storeData: "MUser/storeData" }),
  },
  created() {
    if (this.systemType === 3) {
      this.merchantId = this.storeData.merchantData.id;
    }
  },
  methods: {
    cancel() {
      this.type = 1;
      this.money = "";
      (this.add_form = {
        name: "",
        accountNum: "",
        openingBank: "",
      }),
        this.$emit("cancel");
    },
    async addMerchantWithdraw() {
      if (Number(this.balance) < this.money || this.money == "") {
        this.$message.warning("账户余额不够");
        return;
      }
      if (!this.add_form.name) {
        this.$message.warning("姓名不能为空");
        return;
      }
      if (this.type == 1) {
        if (!this.money) {
          this.$message.warning("金额不能为空");
          return;
        }
        if (this.add_form.accountNum == "") {
          this.$message.warning("微信账号不能为空");
          return;
        }
      } else if (this.type == 2) {
        if (!this.add_form.accountNum) {
          this.$message.warning("支付宝账号不能为空");
          return;
        }
      } else if (this.type == 3) {
        if (!this.add_form.accountNum) {
          this.$message.warning("银行卡号不能为空");
          return;
        }
        if (!this.add_form.openingBank) {
          this.$message.warning("开户行不能为空");
          return;
        }
      }
      const { data } = await addMerchantWithdraw({
        type: this.type,
        accountContent: this.add_form,
        money: this.money,
        orderNum: 1,
        merchantId: this.merchantId,
      });
      this.$message.success("添加成功");
      this.$emit("confirm");
    },
    change() {
      if (this.type === 1) {
        this.typeName = "微信账号";
      }
      if (this.type === 2) {
        this.typeName = "支付宝账号";
      }
      if (this.type === 3) {
        this.typeName = "银行卡号";
      }
    },
  },
};
</script>

<style scoped></style>
