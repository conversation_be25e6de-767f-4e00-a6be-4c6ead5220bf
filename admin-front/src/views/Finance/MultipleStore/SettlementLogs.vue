<template>
  <ContainerQuery>
    <!--    <div slot="left">-->
    <!--      <el-button type="primary" @click="getAllMerchantSettlement(1)">-->
    <!--        导出-->
    <!--      </el-button>-->
    <!--    </div>-->
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <el-date-picker
          v-model="time"
          clearable
          type="daterange"
          value-format="timestamp"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="timeChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="search_form.keyword"
          clearable
          style="width: 220px"
          placeholder="请输入商品名称"
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
    </el-form>
    <vxe-table :data="tableData" border="inner">
      <vxe-table-column field="id" title="ID"></vxe-table-column>
      <vxe-table-column field="orderNo" title="订单号"></vxe-table-column>
      <vxe-table-column field="goodsName" title="商品名称"></vxe-table-column>
      <vxe-table-column field="goodsPrice" title="商品单价">
        <template v-slot="{ row }">
          {{ $_common.formattedNumber(row.goodsPrice) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="goodsNum" title="商品数量">
        <template v-slot="{ row }">
          {{ $_common.formatNub(row.goodsNum) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="goodsMoney" title="商品金额">
        <template v-slot="{ row }">
          {{ $_common.formattedNumber(row.goodsMoney) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="settlementStatus" title="订单状态">
        <template v-slot="{ row }">
          <!--          {{ row.settlementStatus === 4 ? "未结算" : "已结算" }}-->
          <span v-if="row.settlementStatus === 4" class="warning-status"> 未结算 </span>
          <span v-if="row.settlementStatus === 5" class="success-status"> 已结算 </span>
          <!--          <span>-->
          <!--            <el-button-->
          <!--              type="text"-->
          <!--              size="small"-->
          <!--              :disabled="row.settlementStatus == 4 ? false : true"-->
          <!--              @click="settle(row)"-->
          <!--            >-->
          <!--              审核-->
          <!--            </el-button>-->
          <!--          </span>-->
        </template>
      </vxe-table-column>
      <vxe-table-column field="createTime" title="订单创建时间">
        <template v-slot="{ row }">
          {{ $_common.formatDate(row.createTime) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="outStockTime" title="出库时间">
        <template v-slot="{ row }">
          {{ $_common.formatDate(row.updateTime) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="finishTime" title="完成时间">
        <template v-slot="{ row }">
          <span v-if="row.finishTime != null">
            {{ $_common.formatDate(row.finishTime) }}
          </span>
          <span v-else></span>
        </template>
      </vxe-table-column>
    </vxe-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import { mapGetters } from "vuex";

import { getAllMerchantSettlement, updateMerchantSettlement, exportgetAllMerchantSettlement } from "@/api/Merchants.js";
export default {
  name: "SettlementLogs",
  data() {
    return {
      tableData: [],
      time: [],
      search_form: {
        start: "",
        end: "",
        keyword: "",
      },
      total: 0,
      page: 1,
      pageSize: 10,
      start: "",
      end: "",
      settlementStatus: 5, //4已结算5未结算
      merchantId: "",
    };
  },
  computed: {
    ...mapGetters({
      storeData: "MUser/storeData",
    }),
  },
  created() {
    if (this.systemType === 3) {
      this.merchantId = this.storeData.merchantData.id;
    }
    this.getAllMerchantSettlement();
  },
  methods: {
    async getAllMerchantSettlement(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        merchantId: this.merchantId,
        goodsName: this.search_form.keyword,
        // settlementStatus: this.settlementStatus,
        start: this.search_form.start,
        end: this.search_form.end,
      };
      if (exports) {
        params.export = exports;
        const target = await exportgetAllMerchantSettlement({
          ...params,
        });
      } else {
        const data = await getAllMerchantSettlement({
          ...params,
        });
        this.total = data.pageTotal;
        this.tableData = data.data;
      }
      // const res = await getAllMerchantSettlement({
      //   ...params,
      // });
      // exportgetAllMerchantSettlement
      // this.tableData = res.data;
      // this.total = res.pageTotal;
    },
    timeChange(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.getAllMerchantSettlement();
    },
    // 切页
    async pageChange(val) {
      this.page = val;
      const res = await getAllMerchantSettlement({
        page: this.page,
        pageSize: this.pageSize,
        goodsName: this.search_form.keyword,
        merchantId: this.merchantId,
      });
      this.tableData = res.data;
      this.total = res.pageTotal;
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
