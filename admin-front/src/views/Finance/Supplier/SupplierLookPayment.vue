<template>
  <ContainerTit class="LookPayment">
    <div class="page-tip-div" style="margin: 0">
      温馨提示： 1、退款时在金额输入框输入负数即可！ 2、付款时，在金额输入框输入正数即可！
      3、商家预付货款时，在新增选择单据类型为采购预付后不用选择原单据号即可创建！
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="付款单详情" name="one">
        <el-row style="padding-bottom: 13px">
          <el-col :span="24">
            <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">付款单信息</p>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">付款单位</span>
            <span class="form_right">{{ form.supplierName }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">所属店铺</span>
            <span class="form_right">{{ form.shopName }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">业务类型</span>
            <span class="form_right">{{ form.financeType }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">单据日期</span>
            <span class="form_right">
              {{ $_common.formatDate(form.receiptTime) }}
            </span>
          </el-col>
          <el-col class="form" :span="6" style="padding-left: 54px">
            <span class="form_left">付款人</span>
            <span class="form_right">
              {{ form.currentAccountName }}
            </span>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <div class="order_bottom">
      <p class="text">付款单明细</p>
      <el-table :data="form.accountList">
        <el-table-column prop="accountName" label="结算账户" min-width="160"></el-table-column>
        <el-table-column prop="money" label="付款金额" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.money) }}
          </template>
        </el-table-column>
        <el-table-column prop="discountMoney" label="优惠金额" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.discountMoney) }}
          </template>
        </el-table-column>
        <el-table-column label="实际付款金额" min-width="120">
          <template slot-scope="scope">
            {{ $NP.minus(scope.row.money || 0, scope.row.discountMoney || 0) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200"></el-table-column>
      </el-table>
    </div>
    <div class="order_bottom">
      <p class="text">核销明细</p>
      <div class="Enunciate">
        <div class="Enunciate_cont clearfix">
          <div class="float_left">
            <span> 未核销金额：{{ $_common.formattedNumber(offSetNotTotal) }} </span>
            <span style="margin: 0 20px"> 本次核销金额：{{ $_common.formattedNumber(offSetTotal) }} </span>
            <span> 核销差额：{{ $_common.formattedNumber(offSetTotal - moneyTotal) }} </span>
          </div>
        </div>
      </div>
      <el-table ref="multipleTable" :data="tableData">
        <el-table-column prop="financeType" label="单据类型" min-width="100">
          <template slot-scope="scope">
            <span @click="scope">应付单</span>
          </template>
        </el-table-column>
        <el-table-column prop="no" label="单据编号" min-width="140"></el-table-column>
        <el-table-column prop="address" label="单据日期" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="address" label="金额" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.money) }}
          </template>
        </el-table-column>
        <el-table-column prop="address" label="未核销金额" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.notOffsetMoney) }}
          </template>
        </el-table-column>
        <el-table-column prop="offsetMoney" label="本次核销金额" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.offsetMoney) }}
          </template>
        </el-table-column>
        <el-table-column prop="purchaseNo" label="来源单据" min-width="100">
          <template slot-scope="scope">
            <span class="click-div" @click="lookData(scope.row)">
              {{ scope.row.purchaseNo }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="supplierName" label="往来单位名称" min-width="140"></el-table-column>
      </el-table>
    </div>
  </ContainerTit>
</template>
<script>
import { getAllPaidOffset, getSupplierPaidInfo } from "@/api/Finance";
export default {
  name: "SupplierLookPayment",
  data() {
    return {
      activeName: "one",
      paid: "",
      form: {},
      tableData: [],
    };
  },
  computed: {
    // 付款总金额
    moneyTotal() {
      if (!this.form.accountList.length) {
        return 0;
      } else if (this.form.accountList.length === 1) {
        return this.$NP.minus(Number(this.form.accountList[0].money), Number(this.form.accountList[0].discountMoney));
      } else {
        let sum = 0;
        this.form.accountList.forEach((item) => {
          const money = this.$NP.minus(Number(item.money), Number(item.discountMoney));
          sum = this.$NP.plus(sum, money);
        });
        return sum;
      }
    },
    // 核销总金额
    offSetTotal() {
      if (!this.choose_data.length) {
        return 0;
      } else if (this.choose_data.length === 1) {
        return Number(this.choose_data[0].offsetMoney);
      } else {
        let sum = 0;
        this.choose_data.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.offsetMoney) || 0);
        });
        return sum;
      }
    },
    // 未核销总金额
    offSetNotTotal() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return Number(this.tableData[0].payMoney);
      } else {
        let sum = 0;
        this.tableData.forEach((item) => {
          sum = this.$NP.plus(sum, Number(item.payMoney) || 0);
        });
        return sum;
      }
    },
  },
  async created() {
    if (this.$route.params.createTime) {
      this.form.createTime = this.$route.params.createTime;
    }
    if (this.$route.params.id) {
      // 编辑页面
      this.paid = this.$route.params.id;
      // 付款单详情
      await this.getPaidInfo();
      // 付款单核销记录
      await this.getAllPaidOffset();
    }
  },
  methods: {
    //  详情
    async getPaidInfo() {
      const { data } = await getSupplierPaidInfo({
        id: this.paid,
        createTime: this.form.createTime,
      });

      this.form = {
        ...data,
        receiptTime: data.receiptTime * 1000,
        accountList: data.accountList,
      };
      // if (data.offsetDate) {
      //   this.tableData = data.offsetDate;
      //   this.choose_data = data.offsetDate;
      //   setTimeout(() => {
      //     this.tableData.forEach((item) => {
      //       if (item.offsetMoney > 0) {
      //         this.$refs.multipleTable.toggleRowSelection(item);
      //       }
      //     });
      //   }, 200);
      // }
    },
    async getAllPaidOffset() {
      const { data } = await getAllPaidOffset({
        paidId: this.paid,
      });
      this.tableData = data;
      this.choose_data = data;
    },
    // 查看
    lookData(row) {
      this.$router.push(`/Purchase/ManageP/PurchaseOrderLook/${row.purchaseId}`);
    },
  },
};
</script>
<style lang="scss" scoped>
.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;
  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}
.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}
.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;
  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }
  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}
.Enunciate {
  width: 100%;
  height: 72px;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  padding: 16px 24px;
  border-top: 1px solid #dee2ee;
  .Enunciate_cont {
    background-color: #fa6400;
    border-radius: 3px;
    padding: 0 24px;
  }
}
</style>
<style>
.LookPayment {
  background-color: #fff;
}
.LookPayment .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}
.LookPayment .is-active {
  font-weight: 700;
  color: #000;
}
.LookPayment .el-tabs__nav {
  margin-left: 24px;
}
</style>
