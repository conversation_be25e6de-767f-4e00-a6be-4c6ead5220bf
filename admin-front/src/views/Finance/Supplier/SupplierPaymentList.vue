<template>
  <ContainerQuery>
    <!--    <div slot="tip" class="page-tip-div">-->
    <!--      <i class="el-icon-info"></i>-->
    <!--      温馨提示：1、退款单也是在付款单新增页面进行新增，金额输入负数即可！-->
    <!--      2、付款时，在金额输入框输入正数即可！-->
    <!--    </div>-->
    <div slot="left">
      <el-dropdown
        v-if="$accessCheck($Access.PaymentListAddPaid)"
        split-button
        size="small"
        type="primary"
        @click="$router.push('/Finance/Handle/AddPayment')"
      >
        新增付款单
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <div class="dropdown-div" @click="getAllPaid(1)">导出</div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!--      <el-button-->
      <!--        v-if="$accessCheck($Access.PaymentListAddPaid)"-->
      <!--        slot="left"-->
      <!--        size="small"-->
      <!--        type="primary"-->
      <!--        @click="$router.push('/Finance/Handle/AddPayment')"-->
      <!--      >-->
      <!--        新增付款单-->
      <!--      </el-button>-->
    </div>

    <div slot="more">
      <el-form size="small" :inline="true" :model="searchDate">
        <el-form-item>
          <el-input
            v-model="searchDate.keyword"
            clearable
            style="width: 220px"
            placeholder="单据编号"
            @clear="pageChange(1)"
            @keyup.enter.native="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="searchDate.time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="orderDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchDate.status"
            style="width: 150px"
            clearable
            placeholder="订单状态"
            @change="pageChange(1)"
          >
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchDate.financeTypeId"
            style="width: 150px"
            clearable
            placeholder="付款类型"
            @change="pageChange(1)"
          >
            <el-option v-for="item in pay_type_list" :key="item.value" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="payment_list" @row-dblclick="goDetail">
      <el-table-column prop="id" align="left" label="ID" fixed="left" width="60"></el-table-column>
      <el-table-column prop="no" label="单据号" align="left" min-width="200" fixed="left">
        <template slot-scope="scope">
          <span class="click-div" @click="goDetail(scope.row)">
            {{ scope.row.no }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="sourceNo" label="源单据号" align="left" min-width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.sourceNo }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="receiptTime" label="单据日期" align="left" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.receiptTime ? $_common.formatDate(scope.row.receiptTime, "yyyy-MM-dd") : "暂无数据" }}
        </template>
      </el-table-column>
      <el-table-column prop="financeType" align="left" label="付款类型" min-width="100"></el-table-column>
      <el-table-column prop="totalMoney" label="金额" align="left" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="totalDiscountMoney" label="优惠金额" align="left" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalDiscountMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="totalFinalMoney" label="实付金额" align="left" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalFinalMoney) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="shopName"
        label="相关店铺"
        align="left"
        min-width="160"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column prop="auditStatus" show-overflow-tooltip label="审核状态" align="left" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 1" class="info-status"> 待审核 </span>
          <span v-else class="success-status">已审核</span>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import { exportsGetSupplierAllPaid, getAllFinanceTypeNoPage, getSupplierAllPaid } from "@/api/Finance";

export default {
  name: "SupplierPaymentList",
  data() {
    return {
      pay_type_list: [],
      mobile: "",
      searchDate: {
        financeTypeId: "",
        keyword: "",
        status: "",
        time: [],
        start: "",
        end: "",
      },

      payment_list: [],
      statusList: [
        { value: 1, label: "待审核" },
        { value: 2, label: "已审核" },
      ],
      region_options: [],
      total: 0,
      page: 1,
      pageSize: 10,
      checkList: ["付款类型", "金额", "优惠金额", "实际付款金额", "相关店铺", "审核状态"],
      columns: [
        {
          label: "付款类型",
        },
        {
          label: "金额",
        },
        {
          label: "优惠金额",
        },
        {
          label: "实际付款金额",
        },
        {
          label: "相关店铺",
        },
        {
          label: "审核状态",
        },
      ],
    };
  },
  created() {
    this.getAllFinanceType();
    this.getAllPaid();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    goDetail(row) {
      this.$router.push(`/Finance/Supplier/SupplierLookPayment/${row.id}/${row.createTime}`);
    },
    //  获取财务类型
    async getAllFinanceType() {
      const { data } = await getAllFinanceTypeNoPage(2);

      this.pay_type_list = data;
    },
    staffSel() {},
    //  获取列表
    async getAllPaid(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        financeTypeId: this.searchDate.financeTypeId,
        start: this.searchDate.start,
        end: this.searchDate.end,
        auditStatus: this.searchDate.status,
        no: this.searchDate.keyword,
      };
      if (exports) {
        params.export = 1;
        const target = await exportsGetSupplierAllPaid({
          ...params,
        });
      } else {
        const { data, pageTotal } = await getSupplierAllPaid({
          ...params,
        });
        this.payment_list = data;
        this.total = pageTotal;
      }
    },
    getData() {
      this.getAllPaid();
    },
    // 选择客户
    selUnitSupplier(val) {
      this.pageChange(1);
    },
    //  订单时间
    orderDate(val) {
      if (val && val.length) {
        this.searchDate.start = val[0] / 1000;
        this.searchDate.end = val[1] / 1000 + 86399;
      } else {
        this.searchDate.start = "";
        this.searchDate.end = "";
      }
      this.pageChange(1);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
  },
};
</script>
<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
