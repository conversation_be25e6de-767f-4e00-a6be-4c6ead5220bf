<template>
  <div>
    <el-form label-width="120px" size="small">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <div class="detail-tab-item" style="height: 470px">
              <p class="detail-tab-title">商户信息</p>
              <div class="detail-tab-main">
                <el-form-item label="商户名称:">
                  {{ merchantsInfo.name || "--" }}
                </el-form-item>
                <el-form-item v-if="false" label="商户LOGO:">
                  <img :src="merchantsInfo.logo || '--'" alt="" style="width: 50px; height: 50px" />
                </el-form-item>
                <el-form-item label="主营类目:">
                  {{ merchantsInfo.category || "--" }}
                </el-form-item>
                <el-form-item label="商户简介:">
                  {{ merchantsInfo.desc || "--" }}
                </el-form-item>
                <el-form-item v-if="merchantsInfo.area" label="商户地区:">
                  {{ merchantsInfo.area.provinceName }}- {{ merchantsInfo.area.cityName }}-
                  {{ merchantsInfo.area.districtName }}
                </el-form-item>
                <el-form-item label="详细地址:">
                  {{ merchantsInfo.address || "--" }}
                </el-form-item>
                <el-form-item label="入驻时间:">
                  {{ merchantsInfo.createTime ? $_common.formatDate(merchantsInfo.createTime) : "--" }}
                </el-form-item>
                <el-form-item label="到期时间:">
                  {{ merchantsInfo.expireTime ? $_common.formatDate(merchantsInfo.expireTime) : "永久" }}
                  <i class="el-icon-edit" @click="openEdit('expireTime')"></i>
                </el-form-item>
                <el-form-item label="商户状态:">
                  {{ merchantsInfo.enabledStatus === 5 ? "启用" : "禁用" }}
                  <i class="el-icon-edit" @click="openEdit('enabledStatus')"></i>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <div class="detail-tab-item" style="height: 470px">
              <p class="detail-tab-title">其他信息</p>
              <div class="detail-tab-main">
                <el-form-item label="申请会员信息:">
                  {{ merchantsInfo.userCenterId || "--" }}
                </el-form-item>
                <el-form-item label="账号:">
                  {{ merchantsInfo.mobile || "--" }}
                  <i class="el-icon-edit" @click="openEdit('mobile')"></i>
                </el-form-item>
                <!--                <el-form-item label="密码:"></el-form-item>-->
                <el-form-item label="登录地址:" style="cursor: pointer; color: #1881f7">
                  <span @click="openUrl">{{ merchantsInfo.url }}</span>
                  <el-button type="text" style="margin-left: 10px" @click="openUrl"> 点击打开 </el-button>
                </el-form-item>
                <el-form-item label="联系人姓名:">
                  {{ merchantsInfo.contactName || "--" }}
                </el-form-item>
                <el-form-item label="联系方式:">
                  {{ merchantsInfo.contactMobile || "--" }}
                </el-form-item>
                <!--el-form-item label="备注:">
                  {{ merchantsInfo.note || "无" }}
                </el-form-item-->
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <div v-if="false" class="detail-tab-item">
        <p class="detail-tab-title">交易信息</p>
        <div class="detail-tab-main">
          <div class="merchinfo">
            <div class="merchinfo_item">
              <div>
                <span>支付金额</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="子商户支付订单金额总和，不剔除去退款金额"
                  placement="top"
                >
                  <span><i class="el-icon-question"></i></span>
                </el-tooltip>
              </div>
              <div style="font-weight: 500; font-size: 24px">0</div>
            </div>
            <div class="merchinfo_item">
              <div>
                <span>可结算金额</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="子商户可申请结算订单金额总和（已完结订单）"
                  placement="top"
                >
                  <span><i class="el-icon-question"></i></span>
                </el-tooltip>
              </div>
              <div style="font-weight: 500; font-size: 24px">0</div>
            </div>
            <div class="merchinfo_item">
              <div>
                <span>结算中金额</span>
                <el-tooltip class="item" effect="dark" content="子商户待审核和待打款结算订单金额总和" placement="top">
                  <span><i class="el-icon-question"></i></span>
                </el-tooltip>
              </div>
              <div style="font-weight: 500; font-size: 24px">0</div>
            </div>
            <div class="merchinfo_item">
              <div>
                <span>已打款金额</span>
                <el-tooltip class="item" effect="dark" content="子商户完成结算实际打款金额总和" placement="top">
                  <span><i class="el-icon-question"></i></span>
                </el-tooltip>
              </div>
              <div style="font-weight: 500; font-size: 24px">0</div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="false" class="detail-tab-item">
        <p class="detail-tab-title">商品信息</p>
        <div class="detail-tab-main shop_info">
          <div class="shop_info_left">
            <div class="shop_info_sec">
              <div class="shop_info_item">
                <p>待审核</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>审核未通过</p>
                <p class="tit">0</p>
              </div>
            </div>
          </div>
          <div class="shop_info_right">
            <div class="shop_info_sec">
              <div class="shop_info_item">
                <p>全部商品</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>出售中</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>已售罄</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>仓库中</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>回收站</p>
                <p class="tit">0</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="false" class="detail-tab-item">
        <p class="detail-tab-title">订单信息</p>
        <div class="detail-tab-main shop_info">
          <div class="shop_info_left">
            <div class="shop_info_sec">
              <div class="shop_info_item">
                <p>维权中</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>维权完成</p>
                <p class="tit">0</p>
              </div>
            </div>
          </div>
          <div class="shop_info_right">
            <div class="shop_info_sec">
              <div class="shop_info_item">
                <p>全部订单</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>待付款</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>待发货</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>待收货</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>已完成</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>已关闭</p>
                <p class="tit">0</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="商户修改"
      :visible.sync="is_edit"
      width="30%"
    >
      <el-form size="small" label-width="80px">
        <el-form-item v-if="edit_tag === 'expireTime'" label="到期时间">
          <el-date-picker v-model="edit_form.expireTime" type="date" placeholder="选择日期"></el-date-picker>
          <span style="margin-left: 10px">
            <el-checkbox v-model="edit_form.notime">不限制</el-checkbox>
          </span>
        </el-form-item>
        <el-form-item v-if="edit_tag === 'enabledStatus'" label="商户状态">
          <el-radio-group v-model="edit_form.enabledStatus">
            <el-radio :label="4">禁用</el-radio>
            <el-radio :label="5">启用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="edit_tag === 'mobile'" label="账号">
          <el-input v-model="edit_form.mobile"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="is_edit = false">取 消</el-button>
        <el-button size="small" type="primary" @click="is_edit = false"> 确 定 </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "CommodityInfo",
  props: {
    merchantsInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      url: "",
      edit_tag: "",
      shopId: 1,
      is_edit: false,
      edit_form: {
        notime: false,
        expireTime: "",
        enabledStatus: 5,
        mobile: "",
      },
    };
  },
  created() {},
  methods: {
    openUrl() {
      let routeData = this.$router.resolve({
        path: "/MerchantsLogin?shopId=" + this.merchantsInfo.id,
      });
      window.open(routeData.href, "_blank");
    },
    openEdit(prop) {
      this.edit_tag = prop;
      this.is_edit = true;
      this.edit_form[prop] = this.merchantsInfo[prop];
      if (prop === "expireTime" && !this.merchantsInfo[prop]) {
        this.edit_form.notime = this;
      }
    },
  },
};
</script>

<style scoped>
.merchinfo {
  display: flex;
  -webkit-justify-content: center;
}
.merchinfo_item {
  flex: 1;
  height: 140px;
  text-align: center;
  padding-top: 50px;
}
.shop_info {
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.shop_info_sec {
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding-right: 50px;
}
.shop_info_left {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  position: relative;
  margin-left: 10px;
  margin-right: 50px;
  border-right: 1px solid #e2e2e2;
}
.shop_info_right {
  -webkit-box-flex: 2;
  -webkit-flex: 2;
  flex: 2;
  position: relative;
  margin-left: 10px;
}
.shop_info_item {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 140px;
  text-align: center;
  padding-top: 50px;
}
.tit {
  font-weight: 500;
  font-size: 30px;
}
.el-icon-edit {
  color: #999999;
  cursor: pointer;
}
</style>
