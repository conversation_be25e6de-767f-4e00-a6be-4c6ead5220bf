<template>
  <el-dialog
    :title="!isEdit ? '新建分组' : '编辑分组'"
    :visible.sync="isShow"
    width="30%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form ref="form" size="small" :model="add_form" label-width="80px">
      <el-form-item label="分组名称:">
        <el-input v-model="add_form.name" placeholder="请输入分组名称" maxlength="10" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="分组说明:">
        <el-input
          v-model="add_form.desc"
          placeholder="请输入分组说明"
          maxlength="140"
          type="textarea"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="状态:">
        <el-radio-group v-model="add_form.status">
          <el-radio :label="5">显示</el-radio>
          <el-radio :label="4">隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "AddGroup",
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      add_form: {
        name: "",
        status: 4,
        desc: "",
      },
    };
  },
  methods: {
    cancel() {
      this.$emit("cancel");
    },
    confirm() {
      this.cancel();
      this.$emit("confirm");
    },
  },
};
</script>

<style scoped></style>
