<template>
  <div>
    <el-form label-width="120px" :model="add_form">
      <div class="detail-tab-item">
        <p class="detail-tab-title">结算方式</p>
        <div class="detail-tab-main">
          <el-form-item label="结算方式：" style="width: 80%">
            <span>主商城收款</span>
            <span style="margin-left: 30px"> 买家购买子商户商品由主商城统一收款 </span>
            <p class="form-tip">主商城需要设置抽成比例。子商户不需要配置支付，需要和主商城申请结算提现</p>
            <el-form-item label="抽成比例" style="width: 40%" size="small" label-width="70px">
              <el-input v-model="add_form.take_price" placeholder="请输入内容">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
            <el-form-item label="结算设置" style="width: 40%" size="small" label-width="70px">
              <!--              <el-checkbox v-model="add_form.settlement.settlement_one">-->
              <!--                子商户结算金额 扣除分销佣金金额 - -->
              <!--                如：订单金额100元，分销佣金10元。可结算金额为90元-->
              <!--              </el-checkbox>-->
              <!--              <p class="form-tip" style="width: 400px">-->
              <!--                可在下方“营销/应用设置”中设置子商户是否可设置商品分销佣金金额-->
              <!--              </p>-->
              <el-checkbox v-model="add_form.settlement.settlement_two">
                子商户结算金额 扣除后台确认付款订单金额 -
                如：订单金额100元，买家并未支付，子商户在后台确认付款。可结算金额为0元
              </el-checkbox>
              <el-checkbox v-model="add_form.settlement.settlement_three">
                子商户结算金额 扣除货到付款订单金额 -
                如：订单金额100元，买家并未支付，支付方式为货到付款。可结算金额为0元
              </el-checkbox>
              <p class="form-tip" style="width: 400px">可在下方“商户设置”中设置子商户商品是否支持货到付款</p>
              <!--              <el-checkbox v-model="add_form.settlement.settlement_four">-->
              <!--                子商户结算金额 添加积分抵扣金额 - -->
              <!--                如：积分抵扣10元，订单金额100元。可结算金额110元-->
              <!--              </el-checkbox>-->
              <!--              <p class="form-tip" style="width: 400px">-->
              <!--                可在“积分抵扣”活动中设置子商户商品是否可参与积分抵扣-->
              <!--              </p>-->
              <!--              <el-checkbox v-model="add_form.settlement.settlement_five">-->
              <!--                子商户结算金额 包含积分兑换金额 - -->
              <!--                如：买家购买积分商城商品，兑换积分10元，支付金额100元。可结算金额110元-->
              <!--              </el-checkbox>-->
              <!--              <p class="form-tip" style="width: 400px">-->
              <!--                可在“积分商城”应用中设置子商户商品的积分兑换设置-->
              <!--              </p>-->
              <!--              <el-checkbox v-model="add_form.settlement.settlement_six">-->
              <!--                子商户结算金额 包含余额支付金额 - -->
              <!--                如：订单金额100元，余额支付10元。可结算金额100元-->
              <!--              </el-checkbox>-->
              <!--              <p class="form-tip" style="width: 400px">-->
              <!--                可在下方“商户设置”中设置子商户商品是否可使用余额支付-->
              <!--              </p>-->
            </el-form-item>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">审核设置</p>
        <div class="detail-tab-main">
          <el-form-item label="审核设置：">
            <el-radio-group v-model="add_form.auditType">
              <el-radio :label="0">系统默认</el-radio>
              <el-radio :label="1">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">结算设置</p>
        <div class="detail-tab-main">
          <el-form-item label="结算设置：">
            <el-radio-group v-model="add_form.settleType">
              <el-radio :label="0">系统默认</el-radio>
              <el-radio :label="1">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">商户设置</p>
        <div class="detail-tab-main">
          <el-form-item label="商户设置：">
            <el-radio-group v-model="add_form.settingType">
              <el-radio :label="0">系统默认</el-radio>
              <el-radio :label="1">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">营销/应用设置</p>
        <div class="detail-tab-main">
          <el-form-item label=" 营销/应用设置：">
            <el-radio-group v-model="add_form.application">
              <el-radio :label="3">系统默认</el-radio>
              <el-radio :label="6">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <!--      <div class="detail-tab-item">-->
      <!--        <p class="detail-tab-title">-->
      <!--          其他设置-->
      <!--        </p>-->
      <!--        <div class="detail-tab-main">-->
      <!--          <el-form-item label=" 商户分组：">-->
      <!--            <el-select-->
      <!--              v-model="add_form.other"-->
      <!--              placeholder="请选择"-->
      <!--              size="small"-->
      <!--            >-->
      <!--              <el-option-->
      <!--                v-for="item in add_form.other_options"-->
      <!--                :key="item.value"-->
      <!--                :label="item.label"-->
      <!--                :value="item.value"-->
      <!--              >-->
      <!--              </el-option>-->
      <!--            </el-select>-->
      <!--          </el-form-item>-->
      <!--        </div>-->
      <!--      </div>-->
    </el-form>
  </div>
</template>

<script>
export default {
  name: "CommoditySet",
  props: {
    merchantsInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      add_form: {
        take_price: "",
        auditType: 0,
        settleType: 0,
        settingType: 0,
        application: 3,
        other: "",
        settlement: {
          settlement_one: true,
          settlement_two: true,
          settlement_three: false,
          settlement_four: true,
          settlement_five: false,
          settlement_six: false,
        },
        other_options: [],
      },
    };
  },
  created() {},
  methods: {},
};
</script>

<style scoped></style>
