<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" @click="setMerchant">保存</el-button>
    </div>
    <el-form ref="base_form" :model="base_form" label-width="160px" :rules="base_rules" size="small">
      <div class="clearfix">
        <div class="float_left" style="width: calc(100% - 395px)">
          <div class="detail-tab-item">
            <p class="detail-tab-title">页面设置</p>
            <div class="detail-tab-main">
              <el-form-item label="页面名称：" prop="name" style="width: 50%">
                <el-input v-model="base_form.name"></el-input>
              </el-form-item>
              <el-form-item label="Banner图片：" prop="images">
                <UploadQiniu
                  :is-check="true"
                  :limit="6"
                  :file-list="img_list"
                  @uploadSuccess="uploadSuccess"
                  @handleRemove="uploadRemove"
                />
              </el-form-item>
              <el-form-item label="用户定位：" style="width: 80%">
                <el-radio-group v-model="base_form.user_origin">
                  <el-radio :label="1">关闭</el-radio>
                  <el-radio :label="2">当前位置</el-radio>
                  <el-radio :label="3">城市定位</el-radio>
                </el-radio-group>
                <p style="font-size: 12px">
                  当前位置：根据用户当前位置定位，由近到远显示所有商户信息（用户可调整当前位置）
                  未获取用户当前位置时，根据商户权重显示所有商户信息
                </p>
                <p style="font-size: 12px">
                  城市定位：根据用户当前城市定位，仅显示用户定位城市所在商户信息（用户可调整所在城市）
                  未获取用户当前城市或当前城市无任何入驻商户时，不显示商户信息
                </p>
              </el-form-item>
              <!--          <el-form-item label="商户分组：">-->
              <!--            <el-radio-group v-model="base_form.user_group">-->
              <!--              <el-radio :label="5">开启</el-radio>-->
              <!--              <el-radio :label="4">关闭</el-radio>-->
              <!--            </el-radio-group>-->
              <!--            <p style="font-size: 12px">-->
              <!--              开启后，可根据商户分组显示商户-->
              <!--              <span-->
              <!--                style="-->
              <!--                  font-size: 12px;-->
              <!--                  color: rgb(64, 158, 255);-->
              <!--                  cursor: pointer;-->
              <!--                  margin-left: 6px;-->
              <!--                "-->
              <!--                @click="$router.push('/Merchants/MerchantsGroup')"-->
              <!--              >-->
              <!--                设置分组-->
              <!--              </span>-->
              <!--            </p>-->
              <!--          </el-form-item>-->
              <el-form-item label="商户信息：">
                <el-checkbox-group v-model="base_form.user_info">
                  <el-checkbox label="商户LOGO"></el-checkbox>
                  <el-checkbox label="商户名称" disabled></el-checkbox>
                  <el-checkbox label="商户简介"></el-checkbox>
                  <el-checkbox label="商户地址"></el-checkbox>
                  <el-checkbox label="距离" :disabled="base_form.user_origin === 1"></el-checkbox>
                  <!--                  <el-checkbox label="查看地图"></el-checkbox>-->
                </el-checkbox-group>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="float_right" style="width: 375px">
          <div class="detail-tab-item">
            <p class="detail-tab-title">页面示例</p>
            <div class="detail-tab-main" style="background-color: #f7f7f7; padding: 0">
              <div class="head_in" style="background-color: #fff">
                <p class="tit">入驻商户</p>
              </div>
              <div style="height: 54vh; overflow-y: auto">
                <div style="background-color: #fff; padding: 0 12px; margin-bottom: 10px">
                  <div v-if="base_form.images.length" style="margin-bottom: 6px">
                    <img :src="base_form.images[0]" alt="" style="width: 100%; height: 80px" />
                  </div>
                  <div v-if="base_form.user_origin === 2" style="height: 50px">
                    <p style="color: #999ca7; font-size: 13px">当前位置</p>
                    <div style="margin-top: 6px" class="clearfix">
                      <div class="float_left">
                        <i class="el-icon-delete-location"></i>
                        <span style="margin-left: 4px">大明宫万达</span>
                      </div>
                      <div class="float_right" style="color: red">修改位置</div>
                    </div>
                  </div>
                  <div v-if="base_form.user_origin === 3" style="height: 40px; padding: 0 6px">
                    <div>
                      <span style="font-weight: 560; margin: 6px 6px 0 0"> 西安市 </span>
                      <i class="el-icon-arrow-down"></i>
                      <el-input
                        placeholder="请输入店铺名称"
                        prefix-icon="el-icon-search"
                        size="mini"
                        style="width: 74%; border-radius: 10px"
                        class="float_right"
                      ></el-input>
                    </div>
                  </div>
                </div>

                <div style="background-color: #fff; padding: 0 12px">
                  <div
                    v-for="(item, index) in shop_list"
                    :key="index"
                    class="clearfix"
                    style="border-bottom: 1px solid #e2e2e2; padding: 10px 0"
                  >
                    <div v-if="base_form.user_info.includes('商户LOGO')" class="float_left">
                      <img :src="require('../../assets/img/login-bg.jpg')" alt="" style="width: 100px; height: 100px" />
                    </div>
                    <div class="float_left" style="margin-left: 14px">
                      <p v-if="base_form.user_info.includes('商户名称')" style="font-weight: 560">
                        {{ item.name }}
                      </p>
                      <p v-if="base_form.user_info.includes('商户简介')" style="margin-top: 6px; color: #999ca7">
                        {{ item.shopSpec }}
                      </p>
                      <p v-if="base_form.user_info.includes('商户地址')" style="margin-top: 6px">
                        {{ item.region }}
                      </p>
                      <div style="margin-top: 6px">
                        <span v-if="base_form.user_info.includes('距离')">
                          <i class="el-icon-delete-location"></i>
                          <span>{{ item.shop_num }}</span>
                        </span>

                        <span v-if="base_form.user_info.includes('查看地图')" style="margin-left: 4px; color: red">
                          查看地图 >
                        </span>
                      </div>
                    </div>
                    <div class="float_right" style="line-height: 100px">
                      <el-button size="mini" type="danger" round> 进店 </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>
  </ContainerTit>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu.vue";
import { getSettingMerchant, saveSettingMerchant } from "@/api/Merchants";
export default {
  name: "MerchantsListSet",
  components: {
    UploadQiniu,
  },
  data() {
    return {
      base_form: {
        name: "入驻商户",
        images: [],
        user_origin: 2,
        user_group: 4,
        user_info: ["商户名称", "商户LOGO", "商户地址", "商户简介", "距离"],
      },
      img_list: [],
      base_rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
      },
      shop_list: [
        {
          img: "../../assets/img/pageComponents/preview_head.png",
          name: "我的店铺",
          shopSpec: "舜津科技",
          region: "西安市未央区大明宫万达",
          shop_num: "223m",
        },
        {
          img: "../../assets/img/pageComponents/preview_head.png",
          name: "我的店铺",
          shopSpec: "舜津科技",
          region: "西安市未央区大明宫万达",
          shop_num: "223m",
        },
        {
          img: "../../assets/img/pageComponents/preview_head.png",
          name: "我的店铺",
          shopSpec: "舜津科技",
          region: "西安市未央区大明宫万达",
          shop_num: "223m",
        },
        {
          img: "../../assets/img/pageComponents/preview_head.png",
          name: "我的店铺",
          shopSpec: "舜津科技",
          region: "西安市未央区大明宫万达",
          shop_num: "223m",
        },
        {
          img: "../../assets/img/pageComponents/preview_head.png",
          name: "我的店铺",
          shopSpec: "舜津科技",
          region: "西安市未央区大明宫万达",
          shop_num: "223m",
        },
        {
          img: "../../assets/img/pageComponents/preview_head.png",
          name: "我的店铺",
          shopSpec: "舜津科技",
          region: "西安市未央区大明宫万达",
          shop_num: "223m",
        },
      ],
    };
  },
  created() {
    this.getSettingMerchant();
  },
  methods: {
    uploadSuccess(val, res, file, fileList) {
      const imgArr = fileList.map((item) => {
        return item.content;
      });

      if (this.base_form.images.length) {
        this.base_form.images = this.base_form.images.concat(imgArr);
      } else {
        this.base_form.images = imgArr;
      }
      this.img_list = this.base_form.images.map((item) => {
        return {
          name: "",
          content: item,
        };
      });
    },
    uploadRemove(file, fileList) {
      this.base_form.images = "";
    },
    // 详情
    async getSettingMerchant() {
      const data = await getSettingMerchant({
        type: 6,
      });
      if (JSON.stringify(data.data) !== "{}") {
        this.base_form = data.data;
      }
      if (this.base_form.images) {
        this.img_list = this.base_form.images.map((item) => {
          return {
            name: "",
            url: item,
          };
        });
      } else {
        this.img_list = [];
      }
    },
    // 保存
    async setMerchant() {
      const data = await saveSettingMerchant({
        data: this.base_form,
        type: 6,
      });
      this.$message.success("保存成功");
      this.getSettingMerchant();
    },
  },
};
</script>

<style scoped>
.head_in {
  position: relative;
  width: 100%;
  height: 74px;
  background-image: url("../../assets/img/pageComponents/preview_head.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
.tit {
  position: absolute;
  font-size: 14px;
  text-align: center;
  width: 100%;
  bottom: 14px;
  left: 0;
  font-weight: 520;
}
.el-icon-delete-location {
  color: red;
}
</style>
