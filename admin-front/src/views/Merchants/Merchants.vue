<template>
  <Container>
    <div slot="left">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="keyword"
            placeholder="商户名称"
            prefix-icon="el-icon-search"
            size="small"
            clearable
            style="width: 220px"
            @clear="pageChange(1)"
            @keyup.enter.native="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="start_time"
            type="daterange"
            range-separator="-"
            start-placeholder="申请开始日期"
            end-placeholder="申请结束日期"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="全部" name="first"></el-tab-pane>
      <el-tab-pane label="待审核" name="second"></el-tab-pane>
      <el-tab-pane label="已入驻" name="fourth"></el-tab-pane>
      <el-tab-pane label="已拒绝" name="third"></el-tab-pane>
    </el-tabs>
    <!--    @selection-change="handleSelectionChange"-->
    <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark">
      <!--      <el-table-column type="selection" width="55"></el-table-column>-->
      <el-table-column prop="userCenterId" label="申请会员信息" min-width="120px"></el-table-column>
      <el-table-column prop="name" label="商户名称" min-width="120px"></el-table-column>
      <el-table-column v-if="mainFlag" prop="category" label="主营类目" min-width="120px"></el-table-column>
      <el-table-column v-if="nameFlag" prop="contactName" label="联系人" min-width="80px"></el-table-column>
      <el-table-column v-if="phoneFlag" prop="contactMobile" label="联系方式" min-width="120px"></el-table-column>
      <el-table-column
        v-if="siteFlag"
        prop="address"
        label="商户地址"
        show-overflow-tooltip
        min-width="160px"
      ></el-table-column>
      <el-table-column v-if="timeFlag" prop="address" label="申请时间" min-width="140px">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column v-if="stateFlag" prop="auditStatus" label="审核状态" min-width="80px">
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.auditStatus === 3"
            effect="dark"
            :content="scope.row.auditFailReason"
            placement="top"
          >
            <span class="danger-status">已拒绝</span>
          </el-tooltip>
          <span
            :class="[
              scope.row.auditStatus === 1
                ? 'warning-status'
                : scope.row.auditStatus === 2
                ? 'success-status'
                : 'danger-status',
            ]"
          >
            {{ scope.row.auditStatus === 1 ? "待审核" : scope.row.auditStatus === 2 ? "已入驻" : "已拒绝" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" prop="address" min-width="140px">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button type="text" @click="merchantsInfo(scope.row)"> 查看 </el-button>
          <el-button :disabled="scope.row.auditStatus !== 1" type="text" @click="auditApply(scope.row.id)">
            通过
          </el-button>
          <el-button :disabled="scope.row.auditStatus !== 1" type="text" @click="rejectAudit(scope.row.id)">
            拒绝
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <!--    拒绝入驻-->
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="拒绝入驻"
      :visible.sync="is_reject"
      width="30%"
    >
      <el-input v-model="reject_reason" placeholder="请输入拒绝入驻的原因" maxlength="20" autocomplete="off"></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="is_reject = false">取 消</el-button>
        <el-button type="primary" @click="rejectConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <!--   申请详情 -->
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="申请详情"
      :visible.sync="add_data"
      width="30%"
    >
      <el-form ref="add_form" :model="add_form" size="small" label-width="120px">
        <el-form-item label="申请会员信息：">
          <el-input v-model="add_form.userCenterId"></el-input>
        </el-form-item>
        <el-form-item label="商户名称：">
          <el-input v-model="add_form.name"></el-input>
        </el-form-item>
        <el-form-item label="申请时间：">
          {{ $_common.formatDate(add_form.createTime) }}
        </el-form-item>
        <el-form-item label="联系人姓名：">
          <el-input v-model="add_form.contactName"></el-input>
        </el-form-item>
        <el-form-item label="联系方式：">
          <el-input v-model="add_form.contactMobile"></el-input>
        </el-form-item>
        <el-form-item label="主营类目：">
          <el-input v-model="add_form.category"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="add_data = false">取 消</el-button>
        <el-button type="primary" @click="add_data = false">确 定</el-button>
      </span>
    </el-dialog>
  </Container>
</template>

<script>
import { getAllMerchant, auditApply } from "@/api/Merchants";
export default {
  name: "Merchants",
  data() {
    return {
      search_form: {
        // status: 0,
        start: "",
        end: "",
      },
      is_reject: false, //拒绝
      reject_reason: "", //拒绝原因
      shop_id: "", // 商铺id
      add_data: false,
      keyword: "",
      vip_info: "",
      start_time: [],
      add_form: {
        userCenterId: "",
        name: "",
        contactName: "",
        contactMobile: "",
        category: "",
      },
      examine_options: [
        {
          value: 0,
          label: "全部",
        },
        {
          value: 1,
          label: "待审核",
        },
        {
          value: 2,
          label: "审核通过",
        },
        {
          value: 3,
          label: "审核拒绝",
        },
      ],
      total: 0,
      page: 1,
      pageSize: 10,
      activeName: "first",
      tableData: [],
      multipleSelection: [],
      auditStatus: 0,
      checkList: ["主营类目", "联系人姓名", "联系方式", "商户地址", "申请时间", "审核状态"],
      columns: [
        {
          label: "主营类目",
        },
        {
          label: "联系人姓名",
        },
        {
          label: "联系方式",
        },
        {
          label: "商户地址",
        },
        {
          label: "申请时间",
        },
        {
          label: "审核状态",
        },
      ],
      mainFlag: true,
      nameFlag: true,
      phoneFlag: true,
      siteFlag: true,
      timeFlag: true,
      stateFlag: true,
    };
  },
  created() {
    this.getAllMerchant();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllMerchant();
  },
  methods: {
    timeChange(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    handleClick(tab, event) {
      if (this.activeName === "second") {
        this.auditStatus = 1;
      } else if (this.activeName === "third") {
        this.auditStatus = 3;
      } else {
        this.auditStatus = 2;
      }
      this.pageChange(1);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    pageChange(val) {
      this.page = val;
      this.getAllMerchant();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 入驻列表
    async getAllMerchant() {
      const data = await getAllMerchant({
        auditStatus: this.auditStatus,
        search: this.keyword,
        page: this.page,
        pageSize: this.pageSize,
        starCreateTime: this.search_form.start,
        endCreateTime: this.search_form.end,
      });
      this.tableData = data.data;
      this.total = data.pageTotal;
    },
    // 入驻审核
    async auditApply(id) {
      this.$confirm("确认通过审核吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await auditApply(id, {
          auditStatus: 2,
        });
        this.$message({
          type: "success",
          message: "审核成功",
        });
        this.getAllMerchant();
      });
    },
    // 列表详情
    merchantsInfo(row) {
      this.add_data = true;
      this.add_form = row;
    },
    //拒绝审核
    rejectAudit(id) {
      this.shop_id = id;
      this.is_reject = true;
    },
    rejectConfirm() {
      this.is_reject = false;
      this.$confirm("确认拒绝该商户入驻吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await auditApply(this.shop_id, {
          auditStatus: 3,
          auditFailReason: this.reject_reason,
        });
        this.$message({
          type: "success",
          message: "已拒绝入驻",
        });
        this.getAllMerchant();
      });
    },
    change() {
      this.mainFlag = this.checkList.some((item) => item === "主营类目");
      this.nameFlag = this.checkList.some((item) => item === "联系人姓名");
      this.phoneFlag = this.checkList.some((item) => item === "联系方式");
      this.siteFlag = this.checkList.some((item) => item === "商户地址");
      this.timeFlag = this.checkList.some((item) => item === "申请时间");
      this.stateFlag = this.checkList.some((item) => item === "审核状态");
    },
  },
};
</script>

<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
