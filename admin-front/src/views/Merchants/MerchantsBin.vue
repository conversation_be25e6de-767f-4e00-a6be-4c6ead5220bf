<!--多商户回收站-->
<template>
  <Container>
    <div slot="left">
      <el-form :inline="true">
        <el-form-item>
          <el-input
            v-model="search_form.crux"
            placeholder="商户名称"
            size="small"
            clearable
            style="width: 220px"
            @clear="pageChange(1)"
            @keyup.enter.native="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="-"
            start-placeholder="入驻开始日期"
            end-placeholder="入驻结束日期"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="del_time"
            type="daterange"
            range-separator="-"
            start-placeholder="删除开始日期"
            end-placeholder="删除结束日期"
            @change="delTimeChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="clear_data">
      <el-table-column prop="date" label="商户信息" min-width="180">
        <template slot-scope="scope">
          <p>{{ scope.row.name }}</p>
          <p>{{ scope.row.contactMobile }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="" label="订单数" min-width="120"></el-table-column>
      <el-table-column prop="" label="支付金额" min-width="120"></el-table-column>
      <el-table-column prop="name" label="入驻时间" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="删除时间" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.deleteTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="contactName" label="操作人" min-width="120"></el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { getAllMerchant } from "@/api/Merchants";
export default {
  name: "MerchantsBin",
  data() {
    return {
      pageSize: 10,
      page: 1,
      total: 0,
      clear_data: [],
      time: [],
      del_time: [],
      search_form: {
        crux: "",
        start: "",
        end: "",
        delStart: "",
        delEnd: "",
      },
    };
  },
  created() {
    this.getAllMerchant();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllMerchant();
  },
  methods: {
    //  时间
    timeChange(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    //  删除时间
    delTimeChange(val) {
      if (val && val.length) {
        this.search_form.delStart = val[0] / 1000;
        this.search_form.delEnd = val[1] / 1000 + 86399;
      } else {
        this.search_form.delStart = "";
        this.search_form.delEnd = "";
      }
      this.pageChange(1);
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getAllMerchant();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    async getAllMerchant() {
      const data = await getAllMerchant({
        page: this.page,
        pageSize: this.pageSize,
        deleteStatus: 4,
        search: this.search_form.crux,
        starCreateTime: this.search_form.start,
        endCreateTime: this.search_form.end,
        starDeleteTime: this.search_form.delStart,
        endDeleteTime: this.search_form.delEnd,
      });
      this.clear_data = data.data;
      this.total = data.pageTotal;
    },
  },
};
</script>

<style scoped></style>
