<template>
  <ContainerTit>
    <div class="detail-tab-item">
      <p class="detail-tab-title">商户数量</p>
      <div style="font-size: 14px; margin: 20px 0 30px 30px">
        <p style="margin-bottom: 14px">
          <span style="font-size: 20px; font-weight: 500"> {{ merchants_length }}个 </span>
          <span>/100个</span>
          <span>({{ merchantsNum }}%)</span>
        </p>
        <el-progress :percentage="merchantsNum" style="width: 50%"></el-progress>
        <p style="margin-top: 14px">100个 = 100个基础数量 + 0个增购数量</p>
      </div>
    </div>
    <div class="detail-tab-item">
      <p class="detail-tab-title">购买记录</p>
      <div class="detail-tab-main">
        <el-table :data="tableData" style="width: 100%" border size="small">
          <el-table-column prop="date" label="购买数量"></el-table-column>
          <el-table-column prop="name" label="购买时间"></el-table-column>
          <el-table-column prop="address" label="购买金额"></el-table-column>
          <el-table-column prop="address" label="操作"></el-table-column>
        </el-table>
        <FooterPage
          :page-size="pageSize"
          :total-page.sync="total"
          :current-page.sync="page"
          @pageChange="pageChange"
          @sizeChange="sizeChange"
        ></FooterPage>
      </div>
    </div>
  </ContainerTit>
</template>

<script>
export default {
  name: "AddMerchantsInfo",
  data() {
    return {
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      merchants_length: "",
    };
  },
  computed: {
    merchantsNum() {
      const num = (this.merchants_length / 100) * 100;
      return num;
    },
  },
  created() {
    this.merchants_length = this.$route.params.length;
  },
  methods: {
    pageChange(val) {
      this.page = val;
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
