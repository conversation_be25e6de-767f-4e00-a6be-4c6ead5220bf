<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" size="small" @click="$router.push('/Merchants/Merchants')"> 返回 </el-button>
    </div>
    <el-form ref="add_form" :model="add_form" size="small" label-width="120px">
      <el-form-item label="申请会员信息：">
        <el-input v-model="add_form.userCenterId"></el-input>
      </el-form-item>
      <el-form-item label="商户名称：">
        <el-input v-model="add_form.name"></el-input>
      </el-form-item>
      <el-form-item label="申请时间：">
        {{ $_common.formatDate(add_form.createTime) }}
      </el-form-item>
      <el-form-item label="联系人姓名：">
        <el-input v-model="add_form.contactName"></el-input>
      </el-form-item>
      <el-form-item label="联系方式：">
        <el-input v-model="add_form.contactMobile"></el-input>
      </el-form-item>
      <el-form-item label="主营类目：">
        <el-input v-model="add_form.category"></el-input>
      </el-form-item>
    </el-form>
  </ContainerTit>
</template>

<script>
export default {
  name: "ApplyMerchantsInfo",
  data() {
    return {
      target_id: "",
      add_form: {
        userCenterId: "",
        name: "",
        contactName: "",
        contactMobile: "",
        category: "",
      },
    };
  },
  created() {
    this.target_id = this.$route.params.id;
    this.getInfoApply();
  },
  methods: {
    async getInfoApply() {
      this.$loading.start();
      const { err, data } = await this.$service.getInfoApply(this.target_id);
      this.$loading.done();
      if (err) return;
      this.add_form = data.data;
    },
  },
};
</script>

<style scoped>
.el-form-item {
  width: 30%;
}
</style>
