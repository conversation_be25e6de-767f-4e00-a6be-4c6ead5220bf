<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" :loading="loading_btn" @click="addMerchant"> 保存 </el-button>
    </div>
    <el-form ref="add_form" :model="add_form" label-width="120px" :rules="base_rules" size="small">
      <div class="detail-tab-item">
        <div class="detail-tab-title">
          <span>入驻商户信息</span>
        </div>
        <div class="detail-tab-main">
          <el-form-item label="商户名称:" prop="name">
            <el-input v-model="add_form.name" placeholder="请输入商户名称"></el-input>
          </el-form-item>
          <el-form-item label="商户简介:">
            <el-input v-model="add_form.desc" placeholder="请输入商户简介" type="textarea" :rows="2"></el-input>
          </el-form-item>
          <el-form-item label="商户类目:" prop="category">
            <el-input v-model="add_form.category" placeholder="请输入商户类目"></el-input>
          </el-form-item>
          <el-form-item label="商户地区:">
            <div v-if="!!shop_id && add_form.region.length > 0">
              <RegionSelect v-model="add_form.region" size="small" style="width: 100%" @change="regionChange" />
            </div>
            <div v-else>
              <RegionSelect v-model="add_form.region" size="small" style="width: 100%" @change="regionChange" />
            </div>
          </el-form-item>
          <el-form-item label="详细地址:">
            <el-input v-model="add_form.address" placeholder="请输入详细地址"></el-input>
          </el-form-item>
          <el-form-item label="联系人姓名:" prop="contactName">
            <el-input v-model="add_form.contactName" placeholder="请输入联系人姓名"></el-input>
          </el-form-item>
          <el-form-item label="联系方式:" prop="mobile">
            <el-input v-model="add_form.mobile" placeholder="请输入联系方式"></el-input>
          </el-form-item>
          <!--          <el-form-item-->
          <!--            label="备注:"-->
          <!--          >-->
          <!--            <el-input-->
          <!--              placeholder="请输入备注"-->
          <!--              type="textarea"-->
          <!--              :rows="2"-->
          <!--              v-model="add_form.remark"-->
          <!--            >-->
          <!--            </el-input>-->
          <!--          </el-form-item>-->
        </div>
      </div>
      <!--      <div class="detail-tab-item">-->
      <!--        <p class="detail-tab-title">绑定仓库</p>-->
      <!--        <div class="detail-tab-main">-->
      <!--          <el-button type="primary" @click="warehouse_show = true">-->
      <!--            选择仓库-->
      <!--          </el-button>-->
      <!--          <div style="display: inline-block; margin-left: 10px">-->
      <!--            <el-tag-->
      <!--              v-for="(item, index) in useWarehouseNameArr"-->
      <!--              :key="index"-->
      <!--              closable-->
      <!--              @close="closeTag(index)"-->
      <!--            >-->
      <!--              {{ item.warehouseName || "未设置" }}-->
      <!--            </el-tag>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
      <div class="detail-tab-item">
        <p class="detail-tab-title">账号设置</p>
        <div class="detail-tab-main">
          <el-form-item label="账号:" prop="userCenterMobile">
            <el-input v-model="add_form.userCenterMobile" :disabled="!!shop_id" placeholder="请输入账号"></el-input>
            <p class="form-tip" style="width: 400px">用于入驻商户登录系统管理后台使用的账号，商户可以修改账号</p>
          </el-form-item>
          <!--          <el-form-item-->
          <!--            label="登录密码:"-->
          <!--            prop="password"-->
          <!--          >-->
          <!--            <el-input-->
          <!--              placeholder="请输入登录密码"-->
          <!--              v-model="add_form.password"-->
          <!--            >-->
          <!--            </el-input>-->
          <!--            <p-->
          <!--              class="form-tip"-->
          <!--              style="width: 400px"-->
          <!--            >-->
          <!--              用于入驻商户登录系统管理后台的初始密码，商户可以修改密码-->
          <!--            </p>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item-->
          <!--            label="到期时间:"-->
          <!--            prop="end_time"-->
          <!--            style="position: relative"-->
          <!--          >-->
          <!--            <el-date-picker-->
          <!--              v-model="add_form.expireTime"-->
          <!--              style="float: left"-->
          <!--              type="date"-->
          <!--              placeholder="选择日期"-->
          <!--              :disabled="add_form.checked"-->
          <!--              value-format="timestamp"-->
          <!--              :clearable="false"-->
          <!--            ></el-date-picker>-->
          <!--            <el-checkbox-->
          <!--              v-model="add_form.checked"-->
          <!--              style="position: absolute; top: 0; margin-left: 10px; float: left"-->
          <!--            >-->
          <!--              不限制-->
          <!--            </el-checkbox>-->

          <!--            <div class="form-tip" style="width: 400px; float: left">-->
          <!--              子商户有效期限，到期后无法登录管理后台，商品将自动下架-->
          <!--            </div>-->
          <!--          </el-form-item>-->
        </div>
      </div>
      <div v-if="false" class="detail-tab-item">
        <p class="detail-tab-title">结算方式</p>
        <div class="detail-tab-main">
          <el-form-item label="结算方式：" style="width: 80%">
            <span>主商城收款</span>
            <span style="margin-left: 30px"> 买家购买子商户商品由主商城统一收款 </span>
            <p v-if="false" class="form-tip">主商城需要设置抽成比例。子商户不需要配置支付，需要和主商城申请结算提现</p>
            <el-form-item v-if="false" label="抽成比例" style="width: 40%" size="small" label-width="70px">
              <el-input v-model="add_form.take_price" placeholder="请输入内容">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
            <el-form-item v-if="false" label="结算设置" style="width: 40%" size="small" label-width="70px">
              <el-checkbox v-model="add_form.settlement.settlement_one">
                子商户结算金额 扣除分销佣金金额 - 如：订单金额100元，分销佣金10元。可结算金额为90元
              </el-checkbox>
              <p class="form-tip" style="width: 400px">可在下方“营销/应用设置”中设置子商户是否可设置商品分销佣金金额</p>
              <el-checkbox v-model="add_form.settlement.settlement_two">
                子商户结算金额 扣除后台确认付款订单金额 -
                如：订单金额100元，买家并未支付，子商户在后台确认付款。可结算金额为0元
              </el-checkbox>
              <el-checkbox v-model="add_form.settlement.settlement_three">
                子商户结算金额 扣除货到付款订单金额 -
                如：订单金额100元，买家并未支付，支付方式为货到付款。可结算金额为0元
              </el-checkbox>
              <p class="form-tip" style="width: 400px">可在下方“商户设置”中设置子商户商品是否支持货到付款</p>
              <el-checkbox v-model="add_form.settlement.settlement_four">
                子商户结算金额 添加积分抵扣金额 - 如：积分抵扣10元，订单金额100元。可结算金额110元
              </el-checkbox>
              <p class="form-tip" style="width: 400px">可在“积分抵扣”活动中设置子商户商品是否可参与积分抵扣</p>
              <el-checkbox v-model="add_form.settlement.settlement_five">
                子商户结算金额 包含积分兑换金额 - 如：买家购买积分商城商品，兑换积分10元，支付金额100元。可结算金额110元
              </el-checkbox>
              <p class="form-tip" style="width: 400px">可在“积分商城”应用中设置子商户商品的积分兑换设置</p>
              <el-checkbox v-model="add_form.settlement.settlement_six">
                子商户结算金额 包含余额支付金额 - 如：订单金额100元，余额支付10元。可结算金额100元
              </el-checkbox>
              <p class="form-tip" style="width: 400px">可在下方“商户设置”中设置子商户商品是否可使用余额支付</p>
            </el-form-item>
          </el-form-item>
        </div>
      </div>
      <div v-if="false" class="detail-tab-item">
        <p class="detail-tab-title">审核设置</p>
        <div class="detail-tab-main">
          <el-form-item label="审核设置：">
            <el-radio-group v-model="add_form.examine">
              <el-radio :label="3">系统默认</el-radio>
              <el-radio :label="6">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <div v-if="false" class="detail-tab-item">
        <p class="detail-tab-title">结算设置</p>
        <div class="detail-tab-main">
          <el-form-item label="结算设置：">
            <el-radio-group v-model="add_form.settlement_price">
              <el-radio :label="3">系统默认</el-radio>
              <el-radio :label="6">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <div v-if="false" class="detail-tab-item">
        <p class="detail-tab-title">商户设置</p>
        <div class="detail-tab-main">
          <el-form-item label="商户设置：">
            <el-radio-group v-model="add_form.merchants">
              <el-radio :label="3">系统默认</el-radio>
              <el-radio :label="6">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <!--    &lt;!&ndash;    选择仓库&ndash;&gt;-->
    <!--    <WarehouseModel-->
    <!--      v-if="warehouse_show"-->
    <!--      :is-show="warehouse_show"-->
    <!--      @confirm="selWarehouse"-->
    <!--      @cancel="warehouse_show = false"-->
    <!--    />-->
  </ContainerTit>
</template>

<script>
import RegionSelect from "@/component/common/RegionSelectJSON";
import { addMerchant, getInfoMerchant, updateMerchant } from "@/api/Merchants";
// import WarehouseModel from "@/component/common/WarehouseModel.vue";
export default {
  name: "AddMerchants",
  components: {
    RegionSelect,
    // WarehouseModel,
  },
  data() {
    const validatePhone = (rule, value, callback) => {
      const re = /^1[3456789]\d{9}$/; // 正则表达式
      if (value === "") {
        callback(new Error("请输入账号!"));
      } else if (!re.test(value)) {
        callback(new Error("手机号格式有误，请重新输入!"));
      } else {
        callback();
      }
    };
    return {
      loading_btn: false,
      warehouse_show: false,
      add_form: {
        warehouseId: "",
        name: "",
        desc: "",
        category: "",
        region: [],
        address: "",
        contactName: "",
        mobile: "",
        // remark: '',
        userCenterMobile: "",
        // password: '',
        // expireTime: "",
        checked: true,
        take_price: "",
        examine: 3,
        settlement_price: 6,
        merchants: 3,
        settlement: {
          settlement_one: true,
          settlement_two: true,
          settlement_three: false,
          settlement_four: true,
          settlement_five: false,
          settlement_six: false,
        },
      },
      // endTime: "",
      useWarehouseNameArr: [],
      base_rules: {
        name: [{ required: true, message: "请输入商户名称", trigger: "blur" }],
        contactName: [{ required: true, message: "请输入联系人姓名", trigger: "blur" }],
        mobile: [{ required: true, message: "请输入联系方式", trigger: "blur" }],
        category: [{ required: true, message: "请输入商户类目", trigger: "blur" }],
        userCenterMobile: [
          { required: true, message: "请输入账号", trigger: "blur" },
          { validator: validatePhone, trigger: "blur" },
        ],
        // password: [
        //   { required: true, message: '请输入登录密码', trigger: 'blur' }
        // ],
        // end_time: [
        //   { required: true, message: '请选择到期时间', trigger: 'blur' }
        // ]
      },
    };
  },
  created() {
    if (this.$route.query.id) {
      this.shop_id = this.$route.query.id;
      this.getInfoMerchant();
    }
  },
  methods: {
    // 详情
    async getInfoMerchant() {
      const { data } = await getInfoMerchant(this.shop_id);
      this.add_form = {
        name: data.name,
        desc: data.desc || "",
        mobile: data.contactMobile,
        category: data.category,
        contactName: data.contactName,
        userCenterMobile: data.mobile,
        provinceCode: data.provinceCode,
        cityCode: data.cityCode,
        districtCode: data.districtCode,
        address: data.address,
        latitude: "",
        longitude: "",
        // checked: !data.expireTime,
        // expireTime: data.expireTime ? data.expireTime * 1000 : "",
        region: data.provinceCode
          ? [parseInt(data.provinceCode), parseInt(data.cityCode), parseInt(data.districtCode)]
          : [],
      };
      // this.endTime = data.expireTime;

      let useWarehouseNameArr = [];
      for (let i in data.warehouseData) {
        useWarehouseNameArr.push({
          ...data.warehouseData[i],
          id: parseInt(data.warehouseData[i].warehouseId),
        });
      }
      this.useWarehouseNameArr = useWarehouseNameArr;
    },
    //  选择仓库
    // selWarehouse(row) {
    //   if (this.useWarehouseNameArr.length) {
    //     this.useWarehouseNameArr = this.$_common.unique(
    //       this.useWarehouseNameArr.concat(row),
    //       ["id"]
    //     );
    //   } else {
    //     this.useWarehouseNameArr = row;
    //   }
    //   this.useWarehouseName = this.useWarehouseNameArr.map((item) => {
    //     return item.warehouseName;
    //   });
    // },
    //  关闭标签
    // closeTag(index) {
    //   this.useWarehouseNameArr.splice(index, 1);
    // },

    async addMerchant() {
      this.$refs.add_form.validate(async (valid) => {
        if (valid) {
          // const warehouseId = this.useWarehouseNameArr.map((item) => {
          //   return item.id;
          // });
          // if (!warehouseId.length) {
          //   this.$message.warning("请绑定仓库");
          //   return;
          // }
          const params = {
            name: this.add_form.name,
            category: this.add_form.category,
            desc: this.add_form.desc,
            contactName: this.add_form.contactName,
            contactMobile: this.add_form.mobile,
            userCenterMobile: this.add_form.userCenterMobile,
            // expireTime: parseInt(this.add_form.expireTime / 1000),
            provinceCode: this.add_form.region[0] || "",
            cityCode: this.add_form.region[1] || "",
            districtCode: this.add_form.region[2] || "",
            address: this.add_form.address,
            // warehouseId: warehouseId,
            latitude: "",
            longitude: "",
          };
          this.loading_btn = true;
          if (this.shop_id) {
            try {
              const { data } = await updateMerchant(this.shop_id, params);
              this.$message.success("修改成功");
              this.loading_btn = false;
              this.$closeCurrentGoEdit("/Merchants/MerchartsList");
            } finally {
              this.loading_btn = false;
            }
          } else {
            this.$confirm("您确定要创建当前子商户吗?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(async () => {
              try {
                const { data } = await addMerchant(params);
                this.$message.success(data);
                this.loading_btn = false;
                this.$closeCurrentGoEdit("/Merchants/MerchartsList");
              } finally {
                this.loading_btn = false;
              }
            });
          }
        }
      });
    },
    regionChange(row) {
      console.log(row);
      this.add_form.region = row;
    },
  },
};
</script>

<style scoped>
.el-form-item {
  width: 30%;
}
</style>
