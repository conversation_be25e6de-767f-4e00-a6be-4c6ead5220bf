<template>
  <ContainerQuery>
    <div slot="more">
      <el-form slot="right" ref="add_form" :inline="true" size="small" :model="add_form">
        <el-form-item>
          <el-input
            v-model="add_form.order_select_input"
            :placeholder="add_form.order_select === 1 ? '请输入订单编号' : '请输入物流单号'"
            class="input-with-select"
          >
            <el-select slot="prepend" v-model="add_form.order_select" placeholder="订单编号" style="width: 100px">
              <el-option label="订单编号" :value="1"></el-option>
              <el-option label="物流单号" :value="2"></el-option>
            </el-select>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="add_form.personal_info_input"
            :placeholder="
              add_form.personal_info === 1
                ? '请输入收货人姓名'
                : add_form.personal_info === 2
                ? '请输入会员昵称'
                : '请输入收货人手机号'
            "
            class="input-with-select"
          >
            <el-select slot="prepend" v-model="add_form.personal_info" placeholder="请选择" style="width: 120px">
              <el-option label="收货人姓名" :value="1"></el-option>
              <el-option label="会员昵称" :value="2"></el-option>
              <el-option label="收货人手机号" :value="3"></el-option>
            </el-select>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="add_form.order_time"
            placeholder="时间类型"
            style="width: 100px"
            class="input-with-select"
          >
            <el-option label="创建时间" :value="1"></el-option>
            <el-option label="支付时间" :value="2"></el-option>
            <el-option label="发货时间" :value="3"></el-option>
            <el-option label="完成时间" :value="4"></el-option>
          </el-select>
          <el-date-picker
            v-model="add_form.order_time_picker"
            style="width: 150px"
            type="date"
            placeholder="请选择时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select v-model="add_form.merchants_name" style="width: 150px" placeholder="商户名称">
            <el-option
              v-for="item in add_form.merchants_name_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="add_form.shop_name"
            style="width: 150px"
            placeholder="商品名称"
            prefix-icon="el-icon-search"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="add_form.distribution_mode" style="width: 150px" placeholder="配送方式">
            <el-option
              v-for="item in add_form.distribution_mode_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="add_form.payment_type" style="width: 150px" placeholder="支付方式">
            <el-option
              v-for="item in add_form.payment_type_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="add_form.order_status_all" style="width: 150px" placeholder="订单状态">
            <el-option
              v-for="item in add_form.order_status_all_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="add_form.order_source" style="width: 150px" placeholder="订单来源">
            <el-option
              v-for="item in add_form.order_source_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="add_form.order_region" style="width: 150px" placeholder="订单区域">
            <el-option
              v-for="item in add_form.order_region_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="add_form.order_shop" style="width: 150px" placeholder="下单门店">
            <el-option
              v-for="item in add_form.order_shop_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="商品信息" width="120"></el-table-column>
      <el-table-column v-if="quantityFlag" prop="name" label="单价/数量" width="120"></el-table-column>
      <el-table-column v-if="protectRightsFlag" prop="address" label="维权"></el-table-column>
      <el-table-column v-if="paymentFlag" prop="address" label="实付款"></el-table-column>
      <el-table-column v-if="sourceFlag" prop="address" label="订单来源"></el-table-column>
      <el-table-column v-if="buyerFlag" prop="address" label="买家"></el-table-column>
      <el-table-column v-if="outletFlag" prop="address" label="下单门店"></el-table-column>
      <el-table-column v-if="timeFlag" prop="address" label="下单时间"></el-table-column>
      <el-table-column v-if="modeOfPaymentFlag" prop="address" label="支付方式"></el-table-column>
      <el-table-column v-if="distributionFlag" prop="address" label="配送方式"></el-table-column>
      <el-table-column prop="address" label="商户名称"></el-table-column>
      <el-table-column prop="address">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
export default {
  name: "SafeguardingOrder",
  data() {
    return {
      add_form: {
        order_select: 1,
        order_select_input: "",
        merchants_name: "",
        shop_name: "",
        personal_info: 1,
        personal_info_input: "",
        order_time: 1,
        order_time_picker: "",
        order_type: 1,
        shop_type: 1,
        distribution_mode: 1,
        order_status: 1,
        payment_type: 1,
        order_status_all: 1,
        order_source: 1,
        order_region: 1,
        order_shop: 1,
        merchants_name_options: [
          {
            value: 1,
            label: "全部",
          },
        ],
        order_type_options: [
          {
            value: 1,
            label: "全部",
          },
          {
            value: 2,
            label: "普通订单",
          },
          {
            value: 3,
            label: "秒杀订单",
          },
        ],
        shop_type_options: [
          {
            value: 1,
            label: "全部",
          },
          {
            value: 2,
            label: "实体商品",
          },
          {
            value: 3,
            label: "虚拟商品",
          },
        ],
        distribution_mode_options: [
          {
            value: 1,
            label: "全部",
          },
          {
            value: 2,
            label: "快递",
          },
          {
            value: 3,
            label: "自提",
          },
          {
            value: 4,
            label: "同城配送",
          },
        ],
        order_status_options: [
          {
            value: 1,
            label: "全部",
          },
          {
            value: 2,
            label: "未维权",
          },
          {
            value: 3,
            label: "维权中",
          },
          {
            value: 4,
            label: "维权完成",
          },
        ],
        payment_type_options: [
          {
            value: 1,
            label: "全部",
          },
          {
            value: 2,
            label: "余额支付",
          },
          {
            value: 3,
            label: "货到付款",
          },
          {
            value: 4,
            label: "微信支付",
          },
          {
            value: 5,
            label: "支付宝支付",
          },
        ],
        order_status_all_options: [
          {
            value: 1,
            label: "全部",
          },
          {
            value: 2,
            label: "待付款",
          },
          {
            value: 3,
            label: "待发货",
          },
          {
            value: 4,
            label: "待收货",
          },
          {
            value: 5,
            label: "已完成",
          },
          {
            value: 6,
            label: "已关闭",
          },
        ],
        order_source_options: [
          {
            value: 1,
            label: "全部",
          },
          {
            value: 2,
            label: "公众号",
          },
          {
            value: 3,
            label: "小程序",
          },
          {
            value: 4,
            label: "app",
          },
        ],
        order_region_options: [
          {
            value: 1,
            label: "全部",
          },
          {
            value: 2,
            label: "国内",
          },
          {
            value: 3,
            label: "海外",
          },
        ],
        order_shop_options: [
          {
            value: 1,
            label: "全部",
          },
        ],
      },
      tableData: [],
      multipleSelection: [],
      total: 0,
      page: 1,
      pageSize: 10,
      checkList: ["单价/数量", "维权", "实付款", "订单来源", "买家", "下单门店", "下单时间", "支付方式", "配送方式"],
      columns: [
        {
          label: "单价/数量",
        },
        {
          label: "维权",
        },
        {
          label: "实付款",
        },
        {
          label: "订单来源",
        },
        {
          label: "买家",
        },
        {
          label: "下单门店",
        },
        {
          label: "下单时间",
        },
        {
          label: "支付方式",
        },
        {
          label: "配送方式",
        },
      ],
      quantityFlag: true,
      protectRightsFlag: true,
      sourceFlag: true,
      paymentFlag: true,
      buyerFlag: true,
      outletFlag: true,
      timeFlag: true,
      modeOfPaymentFlag: true,
      distributionFlag: true,
    };
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    pageChange(val) {
      this.page = val;
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    change() {
      this.quantityFlag = this.checkList.some((item) => item === "单价/数量");
      this.protectRightsFlag = this.checkList.some((item) => item === "维权");
      this.sourceFlag = this.checkList.some((item) => item === "订单来源");
      this.paymentFlag = this.checkList.some((item) => item === "实付款");
      this.buyerFlag = this.checkList.some((item) => item === "买家");
      this.outletFlag = this.checkList.some((item) => item === "下单门店");
      this.timeFlag = this.checkList.some((item) => item === "下单时间");
      this.modeOfPaymentFlag = this.checkList.some((item) => item === "支付方式");
      this.distributionFlag = this.checkList.some((item) => item === "配送方式");
    },
  },
};
</script>

<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
