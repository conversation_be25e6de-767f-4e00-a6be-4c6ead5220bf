<template>
  <Container>
    <div slot="left">
      <el-form :inline="true" size="small">
        <!--<el-form-item>
          <el-select
            v-model="search_form.shopId"
            style="width: 150px"
            placeholder="商户名称"
          >
            <el-option
              v-for="item in search_form.merchants_name_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>-->
        <el-form-item>
          <el-input
            v-model="search_form.keyword"
            style="width: 220px"
            placeholder="结算单号"
            size="small"
            clearable
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_form.price_type"
            style="width: 150px"
            placeholder="结算方式"
            clearable
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in search_form.price_type_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="search_form.time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始申请日期"
            end-placeholder="结束申请日期"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <el-tabs v-model="activeName" type="card" @tab-click="pageChange(1)">
      <el-tab-pane label="全部" name="0"></el-tab-pane>
      <el-tab-pane label="待审核" name="1"></el-tab-pane>
      <el-tab-pane label="待打款" name="4"></el-tab-pane>
      <el-tab-pane label="已打款" name="2"></el-tab-pane>
      <el-tab-pane label="拒绝" name="3"></el-tab-pane>
    </el-tabs>
    <el-table :data="settlement_list">
      <el-table-column prop="no" label="结算单号" min-width="160"></el-table-column>
      <el-table-column prop="no" label="商户信息" min-width="160">
        <template slot-scope="scope">
          <p>{{ scope.row.merchantName }}-子商户</p>
          <p>联系人姓名-{{ scope.row.accountContent.name }}</p>
        </template>
      </el-table-column>
      <el-table-column v-if="closeAnAccountFlag" prop="type" label="结算方式" min-width="80">
        <template slot-scope="scope">
          <el-popover placement="right" width="400" trigger="click">
            <span slot="reference" class="click-div">
              {{
                scope.row.type === 1
                  ? "微信钱包"
                  : scope.row.type === 2
                  ? "支付宝"
                  : scope.row.type === 3
                  ? "银行卡"
                  : "其他"
              }}
            </span>
            <div>
              <p>姓名：{{ scope.row.accountContent.name }}</p>
              <p v-if="scope.row.type === 1">微信号：{{ scope.row.accountContent.accountNum }}</p>
              <p v-if="scope.row.type === 2">支付宝账号：{{ scope.row.accountContent.accountNum }}</p>
              <p v-if="scope.row.type === 3">银行：{{ scope.row.accountContent.openingBank }}</p>
              <p v-if="scope.row.type === 3">银行账号：{{ scope.row.accountContent.accountNum }}</p>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column v-if="appliedAmountFlag" prop="money" label="申请金额" min-width="100">
        <template slot-scope="scope">
          <p>金额：{{ scope.row.money }}</p>
          <p>订单：{{ scope.row.orderNum }}</p>
        </template>
      </el-table-column>
      <el-table-column v-if="auditFlag" prop="money" label="审核通过" min-width="100">
        <template slot-scope="scope">
          <p>金额：{{ scope.row.passMoney }}</p>
          <p>订单：{{ scope.row.passOrderNum }}</p>
        </template>
      </el-table-column>
      <el-table-column v-if="commissionFlag" prop="rate" label="抽成" min-width="120">
        <template slot-scope="scope">
          <p>金额：{{ scope.row.fee }}</p>
          <p>比例：{{ scope.row.rate }}%</p>
        </template>
      </el-table-column>
      <el-table-column v-if="remitFlag" prop="nowMoney" label="打款金额" min-width="80"></el-table-column>
      <el-table-column v-if="timeFlag" prop="createTime" label="申请时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column v-if="settlementStatusFlag" prop="auditStatus" label="结算状态" min-width="100">
        <template slot-scope="scope">
          <span
            :class="[
              scope.row.auditStatus === 3
                ? 'info-status'
                : scope.row.auditStatus === 1
                ? 'warning-status'
                : 'success-status',
            ]"
          >
            {{
              scope.row.auditStatus === 1
                ? "待审核"
                : scope.row.auditStatus === 2
                ? "已结算"
                : scope.row.auditStatus === 3
                ? "已拒绝"
                : scope.row.auditStatus === 4
                ? "待打款"
                : "其他"
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column min-width="140">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.MerchantsSettlementUpdateAuditStatusCash)"
            type="text"
            :disabled="scope.row.auditStatus !== 4"
            @click="MerchantUpdateAuditStatus(scope.row.id, 2)"
          >
            打款
          </el-button>
          <el-button
            v-if="$accessCheck($Access.MerchantsSettlementUpdateAuditStatusConfirm)"
            type="text"
            :disabled="![1, 3].includes(scope.row.auditStatus) || scope.row.auditStatus === 3"
            @click="MerchantUpdateAuditStatus(scope.row.id, 4)"
          >
            审核
          </el-button>
          <el-button
            v-if="$accessCheck($Access.MerchantsSettlementUpdateAuditStatusRefuse)"
            type="text"
            :disabled="scope.row.auditStatus !== 1"
            @click="MerchantUpdateAuditStatus(scope.row.id, 3)"
          >
            拒绝
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { MerchantWithdrawGetAll, MerchantUpdateAuditStatus } from "@/api/Merchants.js";
export default {
  name: "MerchartsShop",
  data() {
    return {
      search_form: {
        shopId: "",
        price_type: "",
        keyword: "",
        start: "",
        end: "",
        time: [],
        price_type_options: [
          {
            value: 3,
            label: "银行卡",
          },
          {
            value: 1,
            label: "微信钱包",
          },
          {
            value: 2,
            label: "支付宝",
          },
        ],
        merchants_name_options: [
          {
            value: 1,
            label: "全部",
          },
        ],
      },
      activeName: "0",
      settlement_list: [],
      multipleSelection: [],
      total: 0,
      page: 1,
      pageSize: 10,
      checkList: ["结算方式", "申请金额", "审核通过", "抽成", "打款金额", "申请时间", "结算状态"],
      columns: [
        {
          label: "结算方式",
        },
        {
          label: "申请金额",
        },
        {
          label: "审核通过",
        },
        {
          label: "抽成",
        },
        {
          label: "打款金额",
        },
        {
          label: "申请时间",
        },
        {
          label: "结算状态",
        },
      ],
      closeAnAccountFlag: true,
      appliedAmountFlag: true,
      auditFlag: true,
      commissionFlag: true,
      remitFlag: true,
      timeFlag: true,
      settlementStatusFlag: true,
    };
  },
  created() {
    this.MerchantWithdrawGetAll();
  },
  methods: {
    //  订单时间
    timeChange(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    async MerchantWithdrawGetAll() {
      const { data, pageTotal } = await MerchantWithdrawGetAll({
        page: this.page,
        pageSize: this.pageSize,
        auditStatus: this.activeName === "0" ? "" : parseInt(this.activeName),
        startTime: this.search_form.start,
        endTime: this.search_form.end,
        type: this.search_form.price_type,
        shopId: this.search_form.shopId,
        no: this.search_form.keyword,
      });
      this.settlement_list = data;
      this.total = pageTotal;
    },
    pageChange(val) {
      this.page = val;
      this.MerchantWithdrawGetAll();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    MerchantUpdateAuditStatus(id, status) {
      this.$confirm(
        `确定要${status === 2 ? "打款" : status === 4 ? "审核通过" : status === 3 ? "拒绝申请" : ""}吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(async () => {
        const data = await MerchantUpdateAuditStatus(id, { status: status });
        this.$message.success("操作成功");
        this.MerchantWithdrawGetAll();
      });
    },
    change() {
      this.closeAnAccountFlag = this.checkList.some((item) => item === "结算方式");
      this.appliedAmountFlag = this.checkList.some((item) => item === "申请金额");
      this.auditFlag = this.checkList.some((item) => item === "审核通过");
      this.commissionFlag = this.checkList.some((item) => item === "抽成");
      this.remitFlag = this.checkList.some((item) => item === "打款金额");
      this.timeFlag = this.checkList.some((item) => item === "申请时间");
      this.settlementStatusFlag = this.checkList.some((item) => item === "结算状态");
    },
  },
};
</script>

<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
