<template>
  <ContainerQuery>
    <div slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="keyword"
            style="width: 220px"
            placeholder="商品名称/编码/条码"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" @click="pageChange(1)">
              <i class="el-icon-search"></i>
            </el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <GoodsCategory
            v-model="form.categoryPath"
            :width="150"
            check-strictly
            placeholder="商品分类"
            clearable
            size="small"
            @change="goodsChane"
          />
        </el-form-item>
        <el-form-item>
          <el-input v-model="form.brand" style="width: 150px" placeholder="商品品牌" clearable @clear="delBrand">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="sel_brand = true"></i>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-select
            v-model="enableStatus"
            placeholder="销售状态"
            style="width: 150px"
            clearable
            @change="pageChange(1)"
          >
            <el-option label="上架" :value="5"></el-option>
            <el-option label="下架" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <SelectShop v-model="shopId" width="150" placeholder="销售店铺" @change="shopConfirm" @clear="delShop" />
        </el-form-item>
      </el-form>
    </div>
    <div slot="left">
      <el-dropdown
        v-if="$accessCheck($Access.MerchartsGoodsAddMerchartsGoods)"
        split-button
        type="primary"
        @click="openAddGoods"
      >
        发布商品
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <div class="dropdown-div" @click="getData(1)">导出</div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button v-else size="small" type="primary" plain @click="getData(1)"> 导出 </el-button>
    </div>
    <el-tabs v-model="activeName" type="card" @tab-click="tabChange">
      <el-tab-pane label="全部" name="all"></el-tab-pane>
      <el-tab-pane label="销售中" name="GoodsSale"></el-tab-pane>
      <el-tab-pane label="已售罄" name="SoldOut"></el-tab-pane>
      <el-tab-pane label="仓库中" name="InWarehouse"></el-tab-pane>
    </el-tabs>
    <el-table ref="goodsTable" :data="goods_data" @selection-change="selectionChange">
      <el-table-column type="selection" width="55" fixed="left"></el-table-column>
      <el-table-column prop="materialName" label="商品" fixed="left" min-width="220">
        <template slot-scope="scope">
          <div class="clearfix">
            <div class="float_left">
              <el-image fit="cover" :src="scope.row.images[0]"></el-image>
            </div>
            <div class="float_left goods-name-view" style="margin-left: 10px">
              <div class="goods-title">
                {{ scope.row.title }}
              </div>
              <div class="goods-no">
                {{ scope.row.code }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="classifyFlag"
        prop="categoryName"
        label="商品分类"
        align="left"
        min-width="120"
      ></el-table-column>
      <el-table-column v-if="specificationFlag" label="规格" align="left" min-width="100">
        <template slot-scope="scope">
          <el-popover placement="right" width="1000" trigger="hover" @show="showSpec(scope.$index)">
            <el-table v-loading="spec_loading" :height="500" :data="scope.row.goods_sku_list" size="small" border>
              <el-table-column property="unitName" label="单位" min-width="60px"></el-table-column>
              <el-table-column property="specValueName" label="属性" min-width="120px"></el-table-column>
              <el-table-column property="enabledLadder" label="阶梯价" min-width="70px">
                <template slot-scope="props">
                  <el-tag v-if="props.row.enabledLadder === 1" type="success"> 是 </el-tag>
                  <el-tag v-else type="info">否</el-tag>
                </template>
              </el-table-column>
              <el-table-column property="salePrice" label="销售价(元)" min-width="160px">
                <template slot-scope="props">
                  <span v-if="props.row.enabledLadder === 0" style="color: #ff4040"> ¥{{ props.row.salePrice }} </span>
                  <div v-else>
                    <p v-for="(item, index) in props.row.ladderPrice" :key="index">
                      <span>
                        数量：
                        <span style="color: #ff4040">
                          {{ item.from }}-{{ index === props.row.ladderPrice.length - 1 ? "∞" : item.to }}
                        </span>
                        ，
                      </span>
                      <span>
                        价格：
                        <span style="color: #ff4040">¥{{ item.price }}</span>
                        ；
                      </span>
                    </p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column property="marketPrice" label="市场价(元)" min-width="100px"></el-table-column>
              <el-table-column property="setNum" label="起订数量" min-width="100px"></el-table-column>
              <el-table-column property="inventory" label="可用库存" min-width="100px">
                <template slot-scope="props">
                  {{ props.row.inventory - 0 }}
                </template>
              </el-table-column>
              <el-table-column property="salesNum" label="销量" min-width="100px"></el-table-column>
              <el-table-column property="barCode" label="条形码" min-width="100px"></el-table-column>
            </el-table>
            <el-button slot="reference" size="mini" plain type="primary">
              {{ scope.row.specTotal }}
              种规格
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        v-if="unitFlag"
        prop="unitNameMaster"
        label="基本单位"
        align="left"
        min-width="80"
      ></el-table-column>
      <el-table-column v-if="inventoryFlag" prop="masterInventory" label="总库存" align="left" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.inventorTotal) }}
        </template>
      </el-table-column>
      <!--<el-table-column
        v-if="virtualFlag"
        prop="virtualSalesNum"
        label="虚拟销量"
        align="left"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-input-number
            v-if="scope.row.sale_false_visible"
            v-model="virtual_sales_num"
            style="width: 100%"
            :controls="false"
            size="small"
            @keyup.enter.native="setSalesNum(scope.$index)"
            @blur="setSalesNum(scope.$index)"
          ></el-input-number>
          <div v-else class="click-div" @click="showSetSaleNum(scope.$index)">
            {{ scope.row.virtualSalesNum }}
            <el-button
              v-if="$accessCheck($Access.PublishGoodssetSalesNum)"
              type="text"
              icon="el-icon-edit"
            ></el-button>
          </div>
        </template>
      </el-table-column>-->
      <el-table-column v-if="trueFlag" prop="salesNum" label="真实销量" align="left" min-width="100"></el-table-column>
      <el-table-column v-if="currentStateFlag" prop="enableStatus" label="当前状态" align="left" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.MerchartsGoodsUpMerchartsGoods)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="上架"
            inactive-text="下架"
            @change="changeGoodsStatus($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 上架 </span>
            <span v-else class="info-status">下架</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="retailStoresFlag"
        prop="shopName"
        label="销售店铺"
        align="left"
        min-width="100"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column v-if="timeFlag" prop="createTime" label="创建时间" align="left" width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column align="left" fixed="right" width="180">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.MerchartsGoodsEditMerchartsGoods)"
            type="text"
            @click="openPriceModel(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.MerchartsGoodsDelMerchartsGoods)"
            type="text"
            @click="delData(scope.row.id)"
          >
            删除
          </el-button>
          <el-button
            v-if="$accessCheck($Access.MerchartsGoodsTopMerchartsGoods)"
            type="text"
            @click="setTop(scope.row.id)"
          >
            {{ scope.row.topTime !== 0 ? "取消置顶" : "置顶" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-drawer class="edit-drawer" :title="goods_name" :visible.sync="is_price" direction="rtl" size="50%">
      <EditGoods v-if="is_price" :goods-id="price_goods_detail.id" @subData="editsubData" />
    </el-drawer>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div slot="btn-div" class="foot-btn-div">
        <el-checkbox v-model="checkedAll" @change="checkAllChange"></el-checkbox>
        <el-dropdown v-if="$accessCheck($Access.PublishGoodsUpdateEnableStatus)">
          <el-button size="mini">
            批量上下架
            <i class="el-icon-caret-top"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <div class="dropdown-div" @click="BatchUnloading(5)">批量上架</div>
            </el-dropdown-item>
            <el-dropdown-item>
              <div class="dropdown-div" @click="BatchUnloading(4)">批量下架</div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown
          v-if="
            $accessCheck($Access.PublishGoodssetSalesNumBatch) || $accessCheck($Access.PublishGoodsbatchGoodsExpress)
          "
        >
          <el-button size="mini">
            批量设置
            <i class="el-icon-caret-top"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <!--<el-dropdown-item
              v-if="$accessCheck($Access.PublishGoodssetSalesNumBatch)"
            >
              <div class="dropdown-div" @click="openSet('虚拟销量')">
                设置虚拟销量
              </div>
            </el-dropdown-item>-->
            <el-dropdown-item v-if="$accessCheck($Access.PublishGoodsbatchGoodsExpress)">
              <div class="dropdown-div" @click="openSet('物流设置')">批量设置物流</div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown
          v-if="$accessCheck($Access.BaseDataListUpdateCategory) || $accessCheck($Access.BaseDataListSetBrand)"
        >
          <el-button size="mini">
            批量转移
            <i class="el-icon-caret-top"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <div
                v-if="$accessCheck($Access.BaseDataListUpdateCategory)"
                class="dropdown-div"
                @click="openSet('转移分类')"
              >
                转移分类
              </div>
            </el-dropdown-item>
            <el-dropdown-item>
              <div v-if="$accessCheck($Access.BaseDataListSetBrand)" class="dropdown-div" @click="openSet('设置品牌')">
                转移品牌
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </FooterPage>
    <BrandSelModel
      v-if="sel_brand"
      :is-check="false"
      :dialog-visible="sel_brand"
      @close="sel_brand = false"
      @confirm="brandConfirm"
    />
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :title="set_tit"
      :visible.sync="is_set"
      width="30%"
    >
      <el-form ref="set_form" :model="set_form" label-width="100px">
        <el-form-item v-if="set_tit === '虚拟销量'" label="销量区间：">
          <el-input-number v-model="sale_num_form.minVal" :controls="false"></el-input-number>
          -
          <el-input-number v-model="sale_num_form.maxVal" :controls="false"></el-input-number>
          <p class="form-tip">虚拟总销量等于销量区间内产生的随机数</p>
        </el-form-item>
        <el-form-item v-if="set_tit === '转移分类'" label="转移分类：">
          <GoodsCategory v-model="set_form.category" check-strictly />
        </el-form-item>
        <el-form-item v-if="set_tit === '设置品牌'" label="设置品牌：">
          <el-input v-model="set_form.brandName" readonly style="width: 240px" placeholder="请选择品牌">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="sel_brand = true"></i>
          </el-input>
        </el-form-item>
        <div v-if="set_tit === '物流设置'">
          <el-form-item v-if="false" label="物流支持：" prop="deliverySupIds">
            <el-checkbox-group v-model="set_form.deliverySupIds">
              <el-checkbox label="1">快递</el-checkbox>
              <el-checkbox label="2">自提</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <br />
          <el-form-item label="快递运费：">
            <el-radio-group v-model="set_form.expressType">
              <el-radio :label="1">包邮</el-radio>
              <el-radio :label="2">运费模版</el-radio>
              <el-radio :label="3">统一运费</el-radio>
            </el-radio-group>
          </el-form-item>
          <br />
          <div v-if="set_form.expressType === 2">
            <el-form-item label="运费模板：" prop="ruleId">
              <el-select v-model="set_form.ruleId" placeholder="请选择">
                <el-option
                  v-for="(item, index) in express_list"
                  :key="index"
                  :label="item.title"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div v-if="set_form.expressType === 3">
            <el-form-item label="统一运费：" prop="expressFee">
              <el-input-number v-model="set_form.expressFee" :controls="false"></el-input-number>
              <span>元</span>
            </el-form-item>
          </div>
          <el-form-item v-if="false" label="是否展示快递：">
            <el-radio-group v-model="set_form.showExpress">
              <el-radio :label="4">不展示</el-radio>
              <el-radio :label="5">展示</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_set = false">取 消</el-button>
        <el-button :loading="more_sub_btn" type="primary" @click="moreSubData"> 确 定 </el-button>
      </span>
    </el-dialog>
  </ContainerQuery>
</template>

<script>
import GoodsCategory from "@/component/common/GoodsCategory.vue";
import BrandSelModel from "@/component/goods/BrandSelModel.vue";
import EditGoods from "@/views/goods/sale/AddGoods";
import SelectShop from "@/component/goods/SelectShop.vue";

import {
  setSalesNum,
  getGoodsInfo,
  BatchUnloading,
  getAllGoods,
  exportGetAllGoods,
  searchGood,
  exportSearchGood,
  delGoods,
  setTop,
  GoodsUpdateEnableStatus,
  batchGoodsExpress,
  setSalesNumBatch,
  setBrand,
  updateCategory,
} from "@/api/goods";
import { getAllExpressRule } from "@/api/System";
export default {
  name: "PublishGoods",
  components: {
    GoodsCategory,
    EditGoods,
    BrandSelModel,
    SelectShop,
  },
  data() {
    return {
      virtual_sales_num: 0,
      set_goods_id: 0,
      sale_num_form: {
        ids: [],
        minVal: "",
        maxVal: "",
      },
      activeName: "all",
      goods_sku_list: [],
      checkedAll: false,
      is_price: false,
      sku_visible: false,
      sku_goods_name: "",
      goods_name: "",
      keyword: "",
      brandId: "",
      categoryId: "",
      enableStatus: "",
      shopId: "",
      sel_brand: false,
      total: 0,
      page: 1,
      pageSize: 10,
      goods_data: [], // table 数据
      choose_data: [],
      form: {
        categoryPath: [],
        search_key: "",
        brand: "",
        shop: "",
        enableStatus: "",
      },
      price_goods_detail: {},
      inSales: 0,
      inStock: 0,
      spec_loading: false,
      set_form: {
        masterUnitId: "",
        shopName: "",
        brandName: "",
        category: [],
        branchUnit: [],
        deliverySupIds: ["1", "2"], // 物流支持 固定值 1 快递 2 自提 传参格式：1,2
        expressType: 1, // 快递运费 1 包邮 2 运费模版 3 固定费用
        expressFee: "", // 固定费用
        showExpress: 5, // 是否展示快递 5展示 4不展示
        ruleId: "", // 运费模版id
      },
      express_list: [],
      assistForm: {},
      more_sub_btn: false,
      unit_show: false,
      is_set: false,
      set_tit: "",
      checkList: [
        "商品分类",
        "规格",
        "基本单位",
        "总库存",
        // "虚拟销量",
        "真实销量",
        "当前状态",
        "销售店铺",
        "创建时间",
      ],
      columns: [
        {
          label: "商品分类",
        },
        {
          label: "规格",
        },
        {
          label: "基本单位",
        },
        {
          label: "总库存",
        },
        // {
        //   label: "虚拟销量",
        // },
        {
          label: "真实销量",
        },
        {
          label: "当前状态",
        },
        {
          label: "销售店铺",
        },
        {
          label: "创建时间",
        },
      ],
      classifyFlag: true,
      specificationFlag: true,
      unitFlag: true,
      inventoryFlag: true,
      virtualFlag: true,
      trueFlag: true,
      currentStateFlag: true,
      retailStoresFlag: true,
      timeFlag: true,
    };
  },
  created() {
    if (this.$route.name === "GoodsSale") {
      this.inSales = 5;
    } else if (this.$route.name === "SoldOut") {
      this.inStock = 4;
    } else if (this.$route.name === "InWarehouse") {
      this.inStock = 5;
    }
    this.getAllGoods();
    this.getAllExpressRule();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    // 获取运费模版
    async getAllExpressRule() {
      const { data } = await getAllExpressRule();
      this.express_list = data;
    },
    async setSalesNum(index) {
      if (this.virtual_sales_num <= 0) {
        this.$message.warning("请输入大于0的值");
        return;
      }
      const data = await setSalesNum({
        id: this.goods_data[index].id,
        val: this.virtual_sales_num,
      });

      this.goods_data[index].sale_false_visible = false;
      this.getData();
    },
    showSetSaleNum(index) {
      this.virtual_sales_num = this.goods_data[index].virtualSalesNum;
      this.goods_data[index].sale_false_visible = true;
    },
    openAddGoods() {
      if (parseInt(this.$store.getters["MUser/enterpriseScope"]) === 4) {
        this.$router.push("/goods/sale/AddGoodsOneStore");
      } else {
        this.$router.push("/goods/sale/AddGoods");
      }
    },
    tabChange() {
      switch (this.activeName) {
        case "all":
          this.inSales = 0;
          this.inStock = 0;
          break;
        case "GoodsSale":
          this.inSales = 5;
          this.inStock = 0;
          break;
        case "SoldOut":
          this.inSales = 0;
          this.inStock = 4;
          break;
        case "InWarehouse":
          this.inSales = 0;
          this.inStock = 5;
          break;
      }
      this.pageChange(1);
    },
    async showSpec(index) {
      let target = this.$_common.deepClone(this.goods_data);
      if (!target[index].goods_sku_list) {
        this.spec_loading = true;
        const { data } = await getGoodsInfo(target[index].id);
        this.spec_loading = false;

        if (data.specType === 2) {
          this.goods_data[index].goods_sku_list = data.specMultiple.map((item) => {
            const specValueName = item.specGroup
              .map((itemS) => {
                return itemS.specValueName;
              })
              .join("_");
            return {
              ...item,
              specValueName: specValueName,
            };
          });
        } else if (data.specType === 1) {
          this.goods_data[index].goods_sku_list = data.specMultiple.map((item) => {
            return {
              ...item,
              specValueName: "无",
            };
          });
        }
      }
      // this.goods_data = target
    },
    delBrand() {
      this.form.brand = "";
      this.brandId = "";
      this.pageChange(1);
    },
    delShop() {
      this.form.shop = "";
      this.shopId = "";
      this.pageChange(1);
    },
    // 批量选择
    selectionChange(val) {
      this.checkedAll = val.length === this.goods_data.length;
      this.choose_data = val;
    },
    // 编辑完成回调
    editsubData() {
      this.getData();
    },
    // 调整价格 编辑查看
    openPriceModel(row) {
      if (parseInt(this.$store.getters["MUser/enterpriseScope"]) === 5) {
        this.is_price = true;
        this.goods_name = row.title;
        this.price_goods_detail = row;
      } else {
        this.$router.push(`/goods/sale/EditGoodsOneStore/${row.id}`);
      }
    },

    //  批量上下架
    async BatchUnloading(enableStatus) {
      let title = enableStatus === 4 ? "确定要批量下架这些商品吗？" : "确定要批量上架这些商品吗？";
      if (!this.choose_data.length) {
        this.$message.warning("请选择要操作的商品");
        return;
      }
      this.$confirm(title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        if (!this.choose_data.length) {
          this.$message.warning("请选择要操作的商品");
          return;
        }
        const idData = this.choose_data.map((item) => {
          return item.id;
        });
        const data = await BatchUnloading({
          id: idData,
          enableStatus: enableStatus,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.getData();
      });
    },
    // 获取列表
    async getAllGoods(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
      };
      if (this.inSales) {
        params.inSales = this.inSales;
      }
      if (this.inStock) {
        params.inStock = this.inStock;
      }
      if (exports) {
        const data = await exportGetAllGoods({
          ...params,
          export: 1,
        });
      } else {
        const data = await getAllGoods(params);

        this.goods_data = data.data.map((item) => {
          return {
            ...item,
            sale_false_visible: false,
          };
        });
        this.total = data.pageTotal;
      }
    },
    //  搜索商品 searchGood
    async searchGood(exports) {
      let params = {
        keyword: this.keyword,
        brandId: this.brandId,
        categoryPath: this.form.categoryPath.join(","),
        enableStatus: this.enableStatus,
        shopId: this.shopId,
        page: this.page,
        pageSize: this.pageSize,
      };
      if (this.inSales) {
        params.inSales = this.inSales;
      }
      if (this.inStock) {
        params.inStock = this.inStock;
      }
      if (exports) {
        const data = await exportSearchGood({
          ...params,
          export: 1,
        });
      } else {
        const data = await searchGood(params);

        this.goods_data = data.data.map((item) => {
          return {
            ...item,
            sale_false_visible: false,
          };
        });
        this.total = data.pageTotal;
      }
    },
    // 判断当前使用方法为列表接口还是搜索引擎接口 获取列表数据
    getData(exports) {
      // 搜索参数规整
      const obj = {
        keyword: this.keyword,
        brandId: this.brandId,
        categoryPath: this.form.categoryPath.join(","),
        enableStatus: this.enableStatus,
        shopId: this.shopId,
      };
      const isKey = this.$_common.isSerch(obj);
      if (isKey) {
        this.searchGood(exports);
      } else {
        this.getAllGoods(exports);
      }
    },
    // 分类搜索
    goodsChane(val) {
      this.pageChange(1);
    },
    // 品牌搜索
    brandConfirm(row) {
      this.form.brand = row[0].title;
      this.brandId = row[0].id;
      this.pageChange(1);
    },
    // 商铺搜索
    shopConfirm(val, row) {
      this.shopId = row[0].id;
      this.form.shop = row[0].name;
      this.pageChange(1);
    },
    // 删除商品
    async delData(id) {
      this.$confirm("确定要删除该条商品吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delGoods(id);

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.getData();
      });
    },
    // 删除商品
    async setTop(id) {
      this.$confirm("是否要将该商品置顶?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await setTop(id);

        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.getData();
      });
    },
    // 商品上下架
    async changeGoodsStatus(val, row) {
      try {
        const data = await GoodsUpdateEnableStatus({
          id: row.id,
          enableStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        this.getData();
      }
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    checkAllChange() {
      this.$refs.goodsTable.toggleAllSelection();
    },
    // 打开批量设置弹窗
    openSet(tit) {
      this.is_set = true;
      this.set_tit = tit;
    },
    // 批量提交
    moreSubData() {
      if (this.set_tit === "转移分类") {
        this.updateCategory();
      }
      if (this.set_tit === "设置品牌") {
        this.setBrand();
      }
      if (this.set_tit === "虚拟销量") {
        this.setSalesNumBatch();
      }
      if (this.set_tit === "物流设置") {
        this.batchGoodsExpress();
      }
    },
    // 批量设置运费
    async batchGoodsExpress() {
      if (!this.choose_data.length) {
        this.$message.warning("请选择要操作的商品");
        return;
      }
      const idData = this.choose_data.map((item) => {
        return item.id;
      });
      this.more_sub_btn = true;
      const data = await batchGoodsExpress({
        expressType: this.set_form.expressType,
        ruleId: this.set_form.ruleId,
        expressFee: this.set_form.expressFee,
        ids: idData,
      });
      this.more_sub_btn = false;

      this.is_set = false;
      this.$message.success("操作成功");
      this.getData();
    },
    // 设置虚拟销量
    async setSalesNumBatch() {
      if (!this.choose_data.length) {
        this.$message.warning("请选择要操作的商品");
        return;
      }
      if (!this.sale_num_form.minVal) {
        this.$message.warning("虚拟销量区间最小值不能为0");
        return;
      }
      if (!this.sale_num_form.maxVal) {
        this.$message.warning("虚拟销量区间最大值不能为0");
        return;
      }
      if (this.sale_num_form.maxVal <= this.sale_num_form.minVal) {
        this.$message.warning("虚拟销量区间最大值必须大于最小值");
        return;
      }
      const idData = this.choose_data.map((item) => {
        return item.id;
      });
      this.more_sub_btn = true;
      const data = await setSalesNumBatch({
        ...this.sale_num_form,
        ids: idData,
      });
      this.more_sub_btn = false;

      this.is_set = false;
      this.$message.success("操作成功");
      this.getData();
    },
    //  设置品牌
    async setBrand() {
      if (!this.choose_data.length) {
        this.$message.warning("请选择要操作的商品");
        return;
      }
      if (!this.brandId) {
        this.$message.warning("请选择要操作的品牌");
        return;
      }
      const idData = this.choose_data.map((item) => {
        return item.basicGoodsId;
      });
      this.more_sub_btn = true;
      const data = await setBrand({
        id: idData,
        brandId: this.brandId,
      });
      this.more_sub_btn = false;

      this.is_set = false;
      this.$message.success("操作成功");
      this.getData();
    },

    // 转移分类
    async updateCategory() {
      if (!this.choose_data.length) {
        this.$message.warning("请选择要操作的商品");
        return;
      }
      if (!this.set_form.category) {
        this.$message.warning("请选择要转移的分类");
        return;
      }
      const idData = this.choose_data.map((item) => {
        return item.basicGoodsId;
      });
      this.more_sub_btn = true;
      const data = await updateCategory({
        id: idData, // 要移动的基础商品id
        categoryId: this.set_form.category[this.set_form.category.length - 1], // 移动至商品分类id
        categoryPath: this.set_form.category.join(","), // 新的商品分类路径
      });
      this.more_sub_btn = false;

      this.is_set = false;
      this.$message.success("操作成功");
      this.getData();
    },
    change() {
      this.classifyFlag = this.checkList.some((item) => item === "商品分类");
      this.specificationFlag = this.checkList.some((item) => item === "规格");
      this.unitFlag = this.checkList.some((item) => item === "基本单位");
      this.inventoryFlag = this.checkList.some((item) => item === "总库存");
      // this.virtualFlag = this.checkList.some((item) => item === "虚拟销量");
      this.trueFlag = this.checkList.some((item) => item === "真实销量");
      this.currentStateFlag = this.checkList.some((item) => item === "当前状态");
      this.retailStoresFlag = this.checkList.some((item) => item === "销售店铺");
      this.timeFlag = this.checkList.some((item) => item === "创建时间");
    },
  },
};
</script>
<style scoped lang="scss">
.open-span,
.disabled-span {
  cursor: pointer;
}
.open-span:hover,
.disabled-span:hover {
  color: #1c8fef;
}
.goods-name-view {
  width: calc(100% - 76px);
}
.goods-title {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
<style>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>
