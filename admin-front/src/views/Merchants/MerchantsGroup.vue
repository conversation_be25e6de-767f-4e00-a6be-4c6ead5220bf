<!--商户分组-->
<template>
  <Container>
    <div slot="left">
      <el-button size="small" type="primary" @click="openAddGroup()"> 新建分组 </el-button>
    </div>
    <el-table
      ref="groupTabel"
      :data="group_list"
      border
      size="small"
      height="calc( 100vh - 200px )"
      @selection-change="selectionChange"
    >
      <el-table-column fixed="left" type="selection" align="center" width="55"></el-table-column>
      <el-table-column prop="date" label="权重" min-width="60"></el-table-column>
      <el-table-column prop="name" label="分组名称" min-width="120"></el-table-column>
      <el-table-column prop="name" label="分组说明" show-overflow-tooltip min-width="200"></el-table-column>
      <el-table-column prop="name" label="创建时间" min-width="160"></el-table-column>
      <el-table-column prop="name" label="商户数" min-width="100"></el-table-column>
      <el-table-column prop="name" label="状态" min-width="100"></el-table-column>
      <el-table-column prop="name" label="操作" min-width="120">
        <template slot-scope="scope">
          <el-button-group class="table-btn-group">
            <el-button type="text" @click="openAddGroup(scope.row.id)"> 编辑 </el-button>
            <el-button type="text">显示</el-button>
            <el-button type="text" @click="delData(scope.row.id)"> 删除 </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div slot="btn-div" class="foot-btn-div">
        <el-checkbox v-model="checkedAll" @change="checkAllChange"></el-checkbox>
        <el-button size="mini">显示</el-button>
        <el-button size="mini">隐藏</el-button>
        <el-button size="mini">删除</el-button>
      </div>
    </FooterPage>
    <AddGroup :is-edit="is_edit" :is-show="add_show" @cancel="add_show = false" @confirm="addConfirm" />
  </Container>
</template>

<script>
import AddGroup from "./components/AddGroup";
export default {
  name: "MerchantsGroup",
  components: {
    AddGroup,
  },
  data() {
    return {
      add_show: false,
      is_edit: false,
      checkedAll: false,
      choose_data: [],
      pageSize: 10,
      page: 1,
      total: 0,
      group_list: [{ id: 1 }],
    };
  },
  created() {},
  activated() {
    if (this.$_isInit()) return;
  },
  methods: {
    openAddGroup(id) {
      this.add_show = true;
      this.is_edit = !!id;
    },
    delData(id) {},
    addConfirm() {},
    // 改变页数
    pageChange(val) {
      this.page = val;
      // this.getList()
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    checkAllChange() {
      this.$refs.groupTabel.toggleAllSelection();
    },
    // 批量选择
    selectionChange(val) {
      this.checkedAll = val.length === this.group_list.length;
      this.choose_data = val;
    },
  },
};
</script>

<style scoped></style>
