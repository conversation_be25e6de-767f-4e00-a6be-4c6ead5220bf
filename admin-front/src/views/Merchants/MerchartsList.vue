<template>
  <ContainerQuery>
    <!--    <div slot="tip" style="padding-bottom: 10px">-->
    <!--      <p style="display: inline-block; font-size: 14px">商户数量</p>-->
    <!--      <p style="width: 30%; display: inline-block; margin-left: 10px">-->
    <!--        <el-progress :percentage="(tableData.length / 100) * 100"></el-progress>-->
    <!--      </p>-->
    <!--      <p style="display: inline-block; font-size: 12px">-->
    <!--        已使用：-->
    <!--        <span style="color: rgb(64, 158, 255); font-size: 14px">-->
    <!--          {{ tableData.length }}/100-->
    <!--        </span>-->
    <!--        个-->
    <!--        &lt;!&ndash;<el-button-->
    <!--          type="text"-->
    <!--          size="small"-->
    <!--          @click="-->
    <!--            $router.push('/Merchants/AddMerchantsInfo/' + tableData.length)-->
    <!--          "-->
    <!--        >-->
    <!--          查看详情-->
    <!--        </el-button>&ndash;&gt;-->
    <!--      </p>-->
    <!--    </div>-->
    <div v-if="$accessCheck($Access.MerchartsListAddMercharts)" slot="left">
      <el-button size="small" type="primary" @click="addMercharts()"> 添加商户 </el-button>
    </div>
    <div slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="search_form.crux"
            style="width: 220px"
            placeholder="商户名称"
            prefix-icon="el-icon-search"
            size="small"
            clearable
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_form.enabledStatus"
            style="width: 150px"
            placeholder="商户状态"
            @change="pageChange(1)"
          >
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="search_form.service" style="width: 150px" placeholder="服务期">
            <el-option
              v-for="item in service_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="-"
            start-placeholder="入驻开始日期"
            end-placeholder="入驻结束日期"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="time_end"
            type="daterange"
            range-separator="-"
            start-placeholder="到期开始日期"
            end-placeholder="到期结束日期"
            @change="delTimeChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <!--          @selection-change="handleSelectionChange"
-->
    <el-table ref="goodsTable" :data="tableData" tooltip-effect="dark">
      <!--      <el-table-column-->
      <!--        align="center"-->
      <!--        type="selection"-->
      <!--        width="55"-->
      <!--      ></el-table-column>-->
      <!--      <el-table-column prop="weight" label="权重" min-width="60">-->
      <!--        <template slot-scope="scope">-->
      <!--          {{ scope.row.weights || 0 }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column prop="name" label="商户信息" min-width="140"></el-table-column>
      <el-table-column label="联系人" prop="contactName" min-width="80"></el-table-column>
      <el-table-column label="联系方式" prop="contactMobile" min-width="120"></el-table-column>
      <!--      <el-table-column-->
      <!--        v-if="commodityFlag"-->
      <!--        label="商品数"-->
      <!--        min-width="80"-->
      <!--        prop="materielNum"-->
      <!--      ></el-table-column>-->
      <!--      <el-table-column-->
      <!--        v-if="indentFlag"-->
      <!--        label="订单数"-->
      <!--        min-width="80"-->
      <!--        prop="orderNum"-->
      <!--      ></el-table-column>-->
      <!--      <el-table-column-->
      <!--        v-if="paymentFlag"-->
      <!--        label="支付金额"-->
      <!--        min-width="80"-->
      <!--        prop="subPayMoney"-->
      <!--      ></el-table-column>-->
      <el-table-column v-if="enterTimeFlag" prop="createTime" label="入驻时间" min-width="140">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <!--      <el-table-column-->
      <!--        v-if="timeFlag"-->
      <!--        prop="address"-->
      <!--        label="到期时间"-->
      <!--        min-width="140"-->
      <!--      >-->
      <!--        <template slot-scope="scope">-->
      <!--          {{-->
      <!--            scope.row.expireTime-->
      <!--              ? $_common.formatDate(scope.row.expireTime)-->
      <!--              : "永久"-->
      <!--          }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column v-if="commercialTenantFlag" prop="address" label="商户状态" min-width="80">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.MerchartsListUpStatus)"
            v-model="scope.row.enabledStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="enabledMerchant($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="info-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" prop="address" min-width="120">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.MerchartsListDetailMercharts)"
            type="text"
            @click="merchantsInfo(scope.row.id)"
          >
            查看
          </el-button>
          <el-button
            v-if="$accessCheck($Access.MerchartsListEditMercharts)"
            type="text"
            @click="addMercharts(scope.row.id)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import { getAllMerchant, enabledMerchant } from "@/api/Merchants";
export default {
  name: "MerchartsList",
  data() {
    return {
      checkedAll: false,
      tableData: [],
      multipleSelection: [],
      total: 0,
      page: 1,
      pageSize: 10,
      enabledStatus: 5,
      time: [],
      time_end: [],
      search_form: {
        crux: "",
        enabledStatus: "",
        service: "",
        start: "",
        end: "",
        delStart: "",
        delEnd: "",
      },
      options: [
        {
          value: 5,
          label: "启用",
        },
        {
          value: 4,
          label: "禁用",
        },
      ],
      service_options: [
        {
          value: 2,
          label: "正常",
        },
        {
          value: 3,
          label: "即将过期",
        },
        {
          value: 4,
          label: "已过期",
        },
      ],
      checkList: [
        // "商品数",
        // "订单数",
        // "支付金额",
        "入驻时间",
        // "到期时间",
        "商户状态",
      ],
      columns: [
        // {
        //   label: "商品数",
        // },
        // {
        // label: "订单数",
        // },
        // {
        //   label: "支付金额",
        // },
        {
          label: "入驻时间",
        },
        // {
        //   label: "到期时间",
        // },
        {
          label: "商户状态",
        },
      ],
      // commodityFlag: true,
      // indentFlag: true,
      // paymentFlag: true,
      // timeFlag: true,
      commercialTenantFlag: true,
      enterTimeFlag: true,
    };
  },
  created() {
    this.getAllMerchant();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllMerchant();
  },
  methods: {
    async enabledMerchant(val, row) {
      try {
        const data = await enabledMerchant(row.id, {
          enabledStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        await this.getAllMerchant();
      }
    },
    timeChange(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    //  到期时间
    delTimeChange(val) {
      if (val && val.length) {
        this.search_form.delStart = val[0] / 1000;
        this.search_form.delEnd = val[1] / 1000 + 86399;
      } else {
        this.search_form.delStart = "";
        this.search_form.delEnd = "";
      }
      this.pageChange(1);
    },
    pageChange(val) {
      this.page = val;
      this.getAllMerchant();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    addMercharts(id) {
      if (id) {
        this.$router.push("/Merchants/EditMerchants?id=" + id);
      } else {
        this.$router.push("/Merchants/AddMerchants");
      }
    },
    handleSelectionChange(val) {
      this.checkedAll = val.length === this.tableData.length;
      this.multipleSelection = val;
    },
    merchantsInfo(id) {
      this.$router.push("/Merchants/MerchantsInfo/" + id);
    },
    async getAllMerchant() {
      const data = await getAllMerchant({
        auditStatus: 2,
        search: this.search_form.crux,
        page: this.page,
        pageSize: this.pageSize,
        starAuditTime: this.search_form.start,
        endAuditTime: this.search_form.end,
        starExpireTime: this.search_form.delStart,
        endExpireTime: this.search_form.delEnd,
        enabledStatus: this.search_form.enabledStatus,
      });
      this.tableData = data.data;
      this.total = data.pageTotal;
    },
    checkAllChange() {
      this.$refs.goodsTable.toggleAllSelection();
    },
    change() {
      // this.commodityFlag = this.checkList.some((item) => item === "商品数");
      // this.indentFlag = this.checkList.some((item) => item === "订单数");
      // this.paymentFlag = this.checkList.some((item) => item === "支付金额");
      this.enterTimeFlag = this.checkList.some((item) => item === "入驻时间");
      // this.timeFlag = this.checkList.some((item) => item === "到期时间");
      this.commercialTenantFlag = this.checkList.some((item) => item === "商户状态");
    },
  },
};
</script>

<style scoped lang="scss">
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
