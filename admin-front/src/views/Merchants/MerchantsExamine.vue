<template>
  <Container>
    <el-form slot="left" ref="add_form" :inline="true" size="small" :model="add_form">
      <el-form-item>
        <el-input
          v-model="keyword"
          style="width: 220px"
          placeholder="商品名称/编码/条码"
          clearable
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" @click="pageChange(1)">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <SelectShop v-model="shopId" placeholder="选择销售店铺" @change="shopConfirm" @clear="delShop" />
      </el-form-item>
    </el-form>
    <el-table ref="goodsTable" :data="goods_data">
      <el-table-column prop="materialName" label="商品" fixed="left" min-width="220">
        <template slot-scope="scope">
          <div class="clearfix">
            <div class="float_left">
              <el-image fit="cover" :src="scope.row.images[0]"></el-image>
            </div>
            <div class="float_left goods-name-view" style="margin-left: 10px">
              <div class="goods-title">
                {{ scope.row.title }}
              </div>
              <div class="goods-no">
                {{ scope.row.code }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="classifyFlag"
        prop="categoryName"
        label="商品分类"
        align="left"
        min-width="120"
      ></el-table-column>
      <el-table-column v-if="specificationFlag" label="规格" align="left" min-width="100">
        <template slot-scope="scope">
          <el-popover placement="right" width="1000" trigger="hover" @show="showSpec(scope.$index)">
            <el-table v-loading="spec_loading" :height="500" :data="scope.row.goods_sku_list" size="small" border>
              <el-table-column property="unitName" label="单位" min-width="60px"></el-table-column>
              <el-table-column property="specValueName" label="属性" min-width="120px"></el-table-column>
              <el-table-column property="enabledLadder" label="阶梯价" min-width="70px">
                <template slot-scope="props">
                  <el-tag v-if="props.row.enabledLadder === 1" type="success"> 是 </el-tag>
                  <el-tag v-else type="info">否</el-tag>
                </template>
              </el-table-column>
              <el-table-column property="salePrice" label="销售价(元)" min-width="160px">
                <template slot-scope="props">
                  <span v-if="props.row.enabledLadder === 0" style="color: #ff4040"> ¥{{ props.row.salePrice }} </span>
                  <div v-else>
                    <p v-for="(item, index) in props.row.ladderPrice" :key="index">
                      <span>
                        数量：
                        <span style="color: #ff4040">
                          {{ item.from }}-{{ index === props.row.ladderPrice.length - 1 ? "∞" : item.to }}
                        </span>
                        ，
                      </span>
                      <span>
                        价格：
                        <span style="color: #ff4040">¥{{ item.price }}</span>
                        ；
                      </span>
                    </p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column property="marketPrice" label="市场价(元)" min-width="100px"></el-table-column>
              <el-table-column property="setNum" label="起订数量" min-width="100px"></el-table-column>
              <el-table-column property="inventory" label="可用库存" min-width="100px">
                <template slot-scope="props">
                  {{ props.row.inventory - 0 }}
                </template>
              </el-table-column>
              <el-table-column property="salesNum" label="销量" min-width="100px"></el-table-column>
              <el-table-column property="barCode" label="条形码" min-width="100px"></el-table-column>
            </el-table>
            <el-button slot="reference" size="mini" plain type="primary">
              {{ scope.row.specTotal }}
              种规格
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        v-if="unitFlag"
        prop="unitNameMaster"
        label="基本单位"
        align="left"
        min-width="80"
      ></el-table-column>
      <el-table-column v-if="inventoryFlag" prop="masterInventory" label="总库存" align="left" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.inventorTotal) }}
        </template>
      </el-table-column>
      <el-table-column v-if="virtualFlag" prop="virtualSalesNum" label="虚拟销量" align="left" min-width="100">
        <template slot-scope="scope">
          <el-input-number
            v-if="scope.row.sale_false_visible"
            v-model="virtual_sales_num"
            style="width: 100%"
            :controls="false"
            size="small"
            @keyup.enter.native="setSalesNum(scope.$index)"
            @blur="setSalesNum(scope.$index)"
          ></el-input-number>
          <div v-else class="click-div" @click="showSetSaleNum(scope.$index)">
            {{ scope.row.virtualSalesNum }}
            <el-button v-if="$accessCheck($Access.PublishGoodssetSalesNum)" type="text" icon="el-icon-edit"></el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="trueFlag" prop="salesNum" label="真实销量" align="left" min-width="100"></el-table-column>
      <el-table-column v-if="currentStateFlag" prop="enableStatus" label="当前状态" align="left" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.enableStatus === 5" class="success-status"> 上架 </span>
          <span v-else class="danger-status">下架</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="retailStoresFlag"
        prop="shopName"
        label="销售店铺"
        align="left"
        min-width="100"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column v-if="timeFlag" prop="createTime" label="创建时间" align="left" width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column align="left" fixed="right" width="140">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button-group class="table-btn-group">
            <el-button type="text" @click="auditGoods(scope.row.id)"> 审核 </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import SelectShop from "@/component/goods/SelectShop.vue";
import {
  auditGoods,
  exportGetAllGoods,
  exportSearchGood,
  getAllGoods,
  getGoodsInfo,
  searchGood,
  setSalesNum,
} from "@/api/goods";

export default {
  name: "MerchantsExamine",
  components: {
    SelectShop,
  },
  data() {
    return {
      add_form: {
        shop_name: "",
        merchants_name: 1,
        shop_type: 1,
        create_time: "",
        merchants_name_options: [
          {
            value: 1,
            label: "全部",
          },
        ],
        shop_type_options: [
          {
            value: 1,
            label: "全部",
          },
          {
            value: 2,
            label: "实体商品",
          },
          {
            value: 3,
            label: "虚拟商品",
          },
          {
            value: 4,
            label: "电子卡密商品",
          },
        ],
      },
      goods_data: [],
      virtual_sales_num: 0,
      set_goods_id: 0,
      sale_num_form: {
        ids: [],
        minVal: "",
        maxVal: "",
      },
      checkList: ["商品分类", "规格", "基本单位", "总库存", "虚拟销量", "真实销量", "当前状态", "销售店铺", "创建时间"],
      columns: [
        {
          label: "商品分类",
        },
        {
          label: "规格",
        },
        {
          label: "基本单位",
        },
        {
          label: "总库存",
        },
        {
          label: "虚拟销量",
        },
        {
          label: "真实销量",
        },
        {
          label: "当前状态",
        },
        {
          label: "销售店铺",
        },
        {
          label: "创建时间",
        },
      ],
      classifyFlag: true,
      specificationFlag: true,
      unitFlag: true,
      inventoryFlag: true,
      virtualFlag: true,
      trueFlag: true,
      currentStateFlag: true,
      retailStoresFlag: true,
      timeFlag: true,
      multipleSelection: [],
      total: 0,
      page: 1,
      pageSize: 10,
      goods_sku_list: [],
      checkedAll: false,
      is_price: false,
      sku_visible: false,
      sku_goods_name: "",
      goods_name: "",
      keyword: "",
      brandId: "",
      categoryId: "",
      enableStatus: "",
      shopId: "",
      sel_brand: false,
      choose_data: [],
      form: {
        categoryPath: [],
        search_key: "",
        brand: "",
        shop: "",
        enableStatus: "",
      },
      price_goods_detail: {},
      spec_loading: false,
      set_form: {
        masterUnitId: "",
        shopName: "",
        brandName: "",
        category: [],
        branchUnit: [],
        deliverySupIds: ["1", "2"], // 物流支持 固定值 1 快递 2 自提 传参格式：1,2
        expressType: 1, // 快递运费 1 包邮 2 运费模版 3 固定费用
        expressFee: "", // 固定费用
        showExpress: 5, // 是否展示快递 5展示 4不展示
        ruleId: "", // 运费模版id
      },
      express_list: [],
      assistForm: {},
      more_sub_btn: false,
      unit_show: false,
      is_set: false,
      set_tit: "",
    };
  },
  created() {
    this.getAllGoods();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    async setSalesNum(index) {
      if (this.virtual_sales_num <= 0) {
        this.$message.warning("请输入大于0的值");
        return;
      }
      const data = await setSalesNum({
        id: this.goods_data[index].id,
        val: this.virtual_sales_num,
      });

      this.goods_data[index].sale_false_visible = false;
      this.getData();
    },
    showSetSaleNum(index) {
      this.virtual_sales_num = this.goods_data[index].virtualSalesNum;
      this.goods_data[index].sale_false_visible = true;
    },
    async showSpec(index) {
      let target = this.$_common.deepClone(this.goods_data);
      if (!target[index].goods_sku_list) {
        this.spec_loading = true;
        const { data } = await getGoodsInfo(target[index].id);
        this.spec_loading = false;

        if (data.specType === 2) {
          this.goods_data[index].goods_sku_list = data.specMultiple.map((item) => {
            const specValueName = item.specGroup
              .map((itemS) => {
                return itemS.specValueName;
              })
              .join("_");
            return {
              ...item,
              specValueName: specValueName,
            };
          });
        } else if (data.specType === 1) {
          this.goods_data[index].goods_sku_list = data.specMultiple.map((item) => {
            return {
              ...item,
              specValueName: "无",
            };
          });
        }
      }
      // this.goods_data = target
    },
    // 获取列表
    async getAllGoods(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        auditStatus: 1,
      };

      if (exports) {
        const data = await exportGetAllGoods({
          ...params,
          export: 1,
        });
      } else {
        const data = await getAllGoods(params);

        this.goods_data = data.data.map((item) => {
          return {
            ...item,
            sale_false_visible: false,
          };
        });
        this.total = data.pageTotal;
      }
    },
    //  搜索商品 searchGood
    async searchGood(exports) {
      let params = {
        keyword: this.keyword,
        brandId: this.brandId,
        categoryPath: this.form.categoryPath.join(","),
        enableStatus: this.enableStatus,
        shopId: this.shopId,
        page: this.page,
        pageSize: this.pageSize,
      };
      if (exports) {
        const data = await exportSearchGood({
          ...params,
          export: 1,
        });
      } else {
        const data = await searchGood(params);

        this.goods_data = data.data.map((item) => {
          return {
            ...item,
            sale_false_visible: false,
          };
        });
        this.total = data.pageTotal;
      }
    },
    // 判断当前使用方法为列表接口还是搜索引擎接口 获取列表数据
    getData(exports) {
      // 搜索参数规整
      const obj = {
        keyword: this.keyword,
        brandId: this.brandId,
        categoryPath: this.form.categoryPath.join(","),
        enableStatus: this.enableStatus,
        shopId: this.shopId,
      };
      const isKey = this.$_common.isSerch(obj);
      if (isKey) {
        this.searchGood(exports);
      } else {
        this.getAllGoods(exports);
      }
    },
    // 商铺搜索
    shopConfirm(val, row) {
      this.shopId = row[0].id;
      this.form.shop = row[0].name;
      this.pageChange(1);
    },
    delShop() {
      this.form.shop = "";
      this.shopId = "";
      this.pageChange(1);
    },
    change() {
      this.classifyFlag = this.checkList.some((item) => item === "商品分类");
      this.specificationFlag = this.checkList.some((item) => item === "规格");
      this.unitFlag = this.checkList.some((item) => item === "基本单位");
      this.inventoryFlag = this.checkList.some((item) => item === "总库存");
      this.virtualFlag = this.checkList.some((item) => item === "虚拟销量");
      this.trueFlag = this.checkList.some((item) => item === "真实销量");
      this.currentStateFlag = this.checkList.some((item) => item === "当前状态");
      this.retailStoresFlag = this.checkList.some((item) => item === "销售店铺");
      this.timeFlag = this.checkList.some((item) => item === "创建时间");
    },
    async auditGoods(id) {
      this.$confirm("确定要审核该条商品吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await auditGoods(id);

        this.$message({
          type: "success",
          message: "审核成功!",
        });
        this.getData();
      });
    },
  },
};
</script>
<style scoped lang="scss">
.open-span,
.disabled-span {
  cursor: pointer;
}
.open-span:hover,
.disabled-span:hover {
  color: #1c8fef;
}
.goods-name-view {
  width: calc(100% - 76px);
}
.goods-title {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
<style>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>
