<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" @click="setMerchant">保存</el-button>
    </div>
    <el-form ref="base_form" :model="base_form" label-width="130px" size="small">
      <div class="detail-tab-item">
        <p class="detail-tab-title">商户后台</p>
        <div class="detail-tab-main">
          <el-form-item label="子商户后台地址：" style="cursor: pointer; color: #1881f7">
            <span @click="openUrl">{{ base_form.url }}</span>
            <el-button type="text" style="margin-left: 10px" @click="openUrl"> 点击打开 </el-button>
          </el-form-item>
          <!--          <el-form-item label="系统LOGO:" prop="logo">-->
          <!--            <UploadQiniu-->
          <!--              :limit="1"-->
          <!--              :file-list="img_list"-->
          <!--              @uploadSuccess="uploadSuccess"-->
          <!--              @handleRemove="uploadRemove"-->
          <!--            />-->
          <!--          </el-form-item>-->
          <el-form-item label="客服电话：" style="width: 30%">
            <el-input v-model="base_form.mobile" placeholder="默认获取商城客服电话号码"></el-input>
          </el-form-item>
        </div>
      </div>
      <div v-if="false" class="detail-tab-item">
        <p class="detail-tab-title">结算方式</p>
        <div class="detail-tab-main">
          <div class="page-tip-div" style="margin-top: 0">
            <span>
              注：结算方式为“主商城收款”或“微信分账”时，商城全部商品分销佣金（包括子商户商品）由主商城打款可以通过设置
              “子商户商品不支持分销”， “子商户结算金额扣除分销佣金金额”或 “提高抽成比例”控制主商城支出
            </span>
          </div>
          <el-form-item label="结算方式：" style="width: 80%">
            <el-radio-group v-model="base_form.price_type" style="margin-top: 10px">
              <el-radio :label="1">
                <span>主商城收款</span>
                <span style="margin-left: 30px"> 买家购买子商户商品由主商城统一收款 </span>
                <div style="margin-left: 60px">
                  <p class="form-tip">主商城需要设置抽成比例。子商户不需要配置支付，需要和主商城申请结算提现</p>
                  <el-form-item label="抽成比例" style="width: 20%" size="small" label-width="70px">
                    <el-input v-model="base_form.take_price" placeholder="请输入内容">
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="结算设置" style="width: 40%" size="small" label-width="70px">
                    <!--                  <el-checkbox v-model="base_form.settlement.settlement_one">-->
                    <!--                    子商户结算金额 扣除分销佣金金额 - -->
                    <!--                    如：订单金额100元，分销佣金10元。可结算金额为90元-->
                    <!--                  </el-checkbox>-->
                    <!--                  <p class="form-tip" style="width: 400px">-->
                    <!--                    可在下方“营销/应用设置”中设置子商户是否可设置商品分销佣金金额-->
                    <!--                  </p>-->
                    <el-checkbox v-model="base_form.settlement.settlement_two">
                      子商户结算金额 扣除后台确认付款订单金额 -
                      如：订单金额100元，买家并未支付，子商户在后台确认付款。可结算金额为0元
                    </el-checkbox>
                    <br />
                    <el-checkbox v-model="base_form.settlement.settlement_three">
                      子商户结算金额 扣除货到付款订单金额 -
                      如：订单金额100元，买家并未支付，支付方式为货到付款。可结算金额为0元
                    </el-checkbox>
                    <p class="form-tip" style="width: 400px">可在下方“商户设置”中设置子商户商品是否支持货到付款</p>
                    <!--                  <el-checkbox v-model="base_form.settlement.settlement_four">-->
                    <!--                    子商户结算金额 添加积分抵扣金额 - -->
                    <!--                    如：积分抵扣10元，订单金额100元。可结算金额110元-->
                    <!--                  </el-checkbox>-->
                    <!--                  <p class="form-tip" style="width: 400px">-->
                    <!--                    可在“积分抵扣”活动中设置子商户商品是否可参与积分抵扣-->
                    <!--                  </p>-->
                    <!--                  <el-checkbox v-model="base_form.settlement.settlement_five">-->
                    <!--                    子商户结算金额 包含积分兑换金额 - -->
                    <!--                    如：买家购买积分商城商品，兑换积分10元，支付金额100元。可结算金额110元-->
                    <!--                  </el-checkbox>-->
                    <!--                  <p class="form-tip" style="width: 400px">-->
                    <!--                    可在“积分商城”应用中设置子商户商品的积分兑换设置-->
                    <!--                  </p>-->
                    <!--                  <el-checkbox v-model="base_form.settlement.settlement_six">-->
                    <!--                    子商户结算金额 包含余额支付金额 - -->
                    <!--                    如：订单金额100元，余额支付10元。可结算金额100元-->
                    <!--                  </el-checkbox>-->
                    <!--                  <p class="form-tip" style="width: 400px">-->
                    <!--                    可在下方“商户设置”中设置子商户商品是否可使用余额支付-->
                    <!--                  </p>-->
                  </el-form-item>
                </div>
              </el-radio>
              <el-radio :label="2">
                <span>微信分账</span>
                <span style="margin-left: 30px">
                  买家购买子商户商品根据抽成比例自动分账（余额支付、积分抵扣h，积分兑换仍需要子商户申请结算）
                </span>
                <div style="margin-left: 60px">
                  <p class="form-tip">
                    商城支付仅支持微信支付和余额支付，其他支付方式将自动关闭（除余额支付），购买子商户商品产生的分销佣金由主商城支付
                  </p>
                  <el-form-item label="抽成比例" style="width: 40%" size="small" label-width="70px">
                    <el-input v-model="base_form.take_price_wechart" placeholder="请输入内容">
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                  <p style="font-size: 12px; margin-bottom: 10px">
                    <span>主商城支付</span>
                    <span> 微信分账仅支持V3支付接口，请前往 支付设置 配置微信支付 </span>
                  </p>
                  <p style="font-size: 12px">
                    <span>子商城支付</span>
                    <span>需要在具体子商户设置中单独配置</span>
                  </p>
                  <el-form-item label="结算设置" style="width: 40%" size="small" label-width="70px">
                    <el-checkbox v-model="base_form.settlement_wechart.settlement_one">
                      子商户结算金额 添加积分抵扣金额 - 如：积分抵扣10元，订单金额100元。可结算金额110元
                    </el-checkbox>
                    <p class="form-tip" style="width: 400px">可在“积分抵扣”活动中设置子商户商品是否可参与积分抵扣</p>
                    <el-checkbox v-model="base_form.settlement_wechart.settlement_two">
                      子商户结算金额 包含积分兑换金额 -
                      如：买家购买积分商城商品，兑换积分10元，支付金额100元。可结算金额110元
                    </el-checkbox>
                    <p class="form-tip" style="width: 400px">可在“积分商城”应用中设置子商户商品的积分兑换设置</p>
                    <el-checkbox v-model="base_form.settlement_wechart.settlement_three">
                      子商户结算金额 包含余额支付金额 - 如：订单金额100元，余额支付10元。可结算金额100元
                    </el-checkbox>
                    <p class="form-tip" style="width: 400px">可在下方“商户设置”中设置子商户商品是否可使用余额支付</p>
                  </el-form-item>
                </div>
              </el-radio>
              <el-radio :label="3">
                <span>子商户收款</span>
                <span style="margin-left: 30px">
                  买家购买子商户商品由子商户收款（余额支付、积分抵扣，积分兑换仍需要子商户申请结算）
                </span>
                <div style="margin-left: 60px">
                  <p style="font-size: 12px; margin: 10px 0">
                    不同商户商品不能同时下单，购买子商户商品产生的分销佣金由子商户支付
                  </p>
                  <p class="form-tip">
                    注：使用“子商户收款”方式时，主商城的微信支付方式商户类型必须使用“普通商户”类型，子商户微信支付方式商户类型必须使用“子商户”类型
                  </p>
                  <el-form-item label="结算设置" style="width: 40%" size="small" label-width="70px">
                    <el-checkbox v-model="base_form.settlement_price.settlement_one">
                      子商户结算金额 添加积分抵扣金额 - 如：积分抵扣10元，订单金额100元。可结算金额110元
                    </el-checkbox>
                    <p class="form-tip" style="width: 400px">可在“积分抵扣”活动中设置子商户商品是否可参与积分抵扣</p>
                    <el-checkbox v-model="base_form.settlement_price.settlement_two">
                      子商户结算金额 包含积分兑换金额 -
                      如：买家购买积分商城商品，兑换积分10元，支付金额100元。可结算金额110元
                    </el-checkbox>
                    <p class="form-tip" style="width: 400px">可在“积分商城”应用中设置子商户商品的积分兑换设置</p>
                    <el-checkbox v-model="base_form.settlement_price.settlement_three">
                      子商户结算金额 包含余额支付金额 - 如：订单金额100元，余额支付10元。可结算金额100元
                    </el-checkbox>
                    <p class="form-tip" style="width: 400px">可在下方“商户设置”中设置子商户商品是否可使用余额支付</p>
                  </el-form-item>
                </div>
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">审核设置</p>
        <div class="detail-tab-main">
          <!--<el-form-item label="商品审核：">
            <el-radio-group v-model="base_form.shop_examine">
              <el-radio :label="5">开启</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
            <p style="font-size: 12px">
              开启后，商户创建商品需要主商城审核，推荐开启
            </p>
          </el-form-item>-->
          <el-form-item label="结算审核：">
            <el-radio-group v-model="base_form.price_examine">
              <el-radio :label="5">开启</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
            <p style="font-size: 12px">开启后，商户提交结算申请需要主商城审核，推荐开启</p>
          </el-form-item>
        </div>
      </div>
      <div v-if="false" class="detail-tab-item">
        <p class="detail-tab-title">结算设置</p>
        <div class="detail-tab-main">
          <el-form-item label="最低结算额度：" style="width: 30%">
            <el-input v-model="base_form.shop_price_low" placeholder="请输入内容">
              <template slot="append">元</template>
            </el-input>
            <p style="font-size: 12px; width: 400px">子商户申请结算时，预计打款金额小于最低结算额度时不可申请提现</p>
          </el-form-item>
          <el-form-item label="提现方式：">
            <el-checkbox-group v-model="base_form.shop_price_examine">
              <el-checkbox :label="1">微信钱包</el-checkbox>
              <el-checkbox :label="2">支付宝</el-checkbox>
              <el-checkbox :label="3">银行卡</el-checkbox>
            </el-checkbox-group>
            <p style="font-size: 12px">支付宝和银行卡结算时，需要主商城手动打款</p>
          </el-form-item>
          <el-form-item v-if="base_form.shop_price_examine.includes(3)" label="支持银行卡：">
            <div v-for="(item, index) in base_form.bank_card" :key="index" style="margin-top: 6px">
              <el-input v-model="item.card_name" size="small" style="width: 30%"></el-input>
              <el-button type="text" size="mini" style="margin-left: 6px" @click="delCard(index)"> 删除 </el-button>
            </div>
            <el-button type="text" size="mini" @click="addCard"> 添加 + </el-button>
          </el-form-item>
        </div>
      </div>
      <div v-if="false" class="detail-tab-item">
        <p class="detail-tab-title">商户设置</p>
        <div class="detail-tab-main">
          <el-form-item label="商品限制：">
            <p style="font-size: 12px; display: inline-block">最多可创建</p>
            <p style="display: inline-block; margin: 0 10px">
              <el-input
                v-model="base_form.shop_limit"
                placeholder="请输入内容"
                :disabled="base_form.shop_limit_checked === false"
                size="small"
              ></el-input>
            </p>

            <p style="font-size: 12px; display: inline-block">个商品</p>
            <p style="display: inline-block; margin-left: 10px">
              <el-checkbox v-model="base_form.shop_limit_checked"> 不限制 </el-checkbox>
            </p>
          </el-form-item>
          <el-form-item label="员工限制：">
            <p style="font-size: 12px; display: inline-block">最多可创建</p>
            <p style="display: inline-block; margin: 0 10px">
              <el-input
                v-model="base_form.staff_limit"
                placeholder="请输入内容"
                :disabled="base_form.staff_limit_checked === false"
                size="small"
              ></el-input>
            </p>

            <p style="font-size: 12px; display: inline-block">个员工账号</p>
            <p style="display: inline-block; margin-left: 10px">
              <el-checkbox v-model="base_form.staff_limit_checked"> 不限制 </el-checkbox>
            </p>
          </el-form-item>
          <el-form-item label="容量限制：">
            <p style="font-size: 12px; display: inline-block">最多可创建</p>
            <p style="display: inline-block; margin: 0 10px">
              <el-input
                v-model="base_form.capacity_limit"
                placeholder="请输入内容"
                :disabled="base_form.capacity_limit_checked === false"
                size="small"
              ></el-input>
            </p>

            <p style="font-size: 12px; display: inline-block">G素材</p>
            <p style="display: inline-block; margin-left: 10px">
              <el-checkbox v-model="base_form.capacity_limit_checked"> 不限制 </el-checkbox>
            </p>
          </el-form-item>
          <el-form-item label="自提点限制：">
            <p style="font-size: 12px; display: inline-block">最多可创建</p>
            <p style="display: inline-block; margin: 0 10px">
              <el-input
                v-model="base_form.self_limit"
                placeholder="请输入内容"
                :disabled="base_form.self_limit_checked === false"
                size="small"
              ></el-input>
            </p>

            <p style="font-size: 12px; display: inline-block">个自提点</p>
            <p style="display: inline-block; margin-left: 10px">
              <el-checkbox v-model="base_form.self_limit_checked"> 不限制 </el-checkbox>
            </p>
          </el-form-item>
          <el-form-item label="手动确认付款：">
            <el-radio-group v-model="base_form.price_manual">
              <el-radio :label="5">开启</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
            <p style="font-size: 12px">开启后，子商户可以在后台操作手动确认付款</p>
          </el-form-item>
          <el-form-item label="货到付款：">
            <el-radio-group v-model="base_form.cash_on_delivery">
              <el-radio :label="5">开启</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
            <p style="font-size: 12px">开启后，子商户可以使用货到付款</p>
          </el-form-item>
          <el-form-item label="余额支付：">
            <el-radio-group v-model="base_form.balance_payment">
              <el-radio :label="5">开启</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
            <p style="font-size: 12px">开启后，子商户商品支持余额支付</p>
          </el-form-item>
          <el-form-item label="优惠券：">
            <el-radio-group v-model="base_form.coupon">
              <el-radio :label="5">开启</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
            <p style="font-size: 12px">开启后，子商户可创建优惠券，仅子商户商品可使用</p>
          </el-form-item>
          <el-form-item label="手动退款：">
            <el-radio-group v-model="base_form.refund">
              <el-radio :label="5">开启</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
            <p style="font-size: 12px">开启后，子商户可以在后台操作退款（待发货订单退款，待自提订单退款并关闭）</p>
          </el-form-item>
        </div>
      </div>
      <!--      <div class="detail-tab-item">-->
      <!--        <p class="detail-tab-title">营销/应用设置</p>-->
      <!--        <div class="detail-tab-main">-->
      <!--          <el-form-item label="营销活动：">-->
      <!--            <el-checkbox-group v-model="base_form.market_checkList">-->
      <!--              <el-checkbox label="砍价"></el-checkbox>-->
      <!--              <el-checkbox label="积分抵扣"></el-checkbox>-->
      <!--              <el-checkbox label="折扣"></el-checkbox>-->
      <!--              <el-checkbox label="包邮"></el-checkbox>-->
      <!--              <el-checkbox label="满额立减"></el-checkbox>-->
      <!--              <el-checkbox label="拼团"></el-checkbox>-->
      <!--              <el-checkbox label="秒杀"></el-checkbox>-->
      <!--            </el-checkbox-group>-->
      <!--          </el-form-item>-->
      <!--          <el-form-item label="应用工具：">-->
      <!--            <el-checkbox-group v-model="base_form.application_checkList">-->
      <!--              <el-checkbox-->
      <!--                label="分销 （子商户可自定义子商户商品的佣金金额，可修改分销订单佣金金额）"-->
      <!--              ></el-checkbox>-->
      <!--              <br />-->
      <!--              <el-checkbox label="消息通知"></el-checkbox>-->
      <!--              <el-checkbox label="快递助手"></el-checkbox>-->
      <!--              <el-checkbox label="小票打印"></el-checkbox>-->
      <!--              <el-checkbox label="店铺助手小程序"></el-checkbox>-->
      <!--              <el-checkbox label="商品助手"></el-checkbox>-->
      <!--              <el-checkbox label="电子卡密"></el-checkbox>-->
      <!--              <el-checkbox label="积分商城"></el-checkbox>-->
      <!--              <el-checkbox label="快速购买"></el-checkbox>-->
      <!--              <el-checkbox label="直播带货"></el-checkbox>-->
      <!--            </el-checkbox-group>-->
      <!--          </el-form-item>-->
      <!--        </div>-->
      <!--      </div>-->
    </el-form>
  </ContainerTit>
</template>

<script>
import { saveSettingMerchant, getSettingMerchant } from "@/api/Merchants";
import UploadQiniu from "@/component/common/UploadQiniu.vue";
import { mapActions, mapGetters } from "vuex";
export default {
  name: "BasicsSet",
  // components: {
  //   UploadQiniu,
  // },
  data() {
    return {
      base_form: {
        url: "",
        mobile: 123456,
        logo: [],
        take_price: 0,
        price_type: 1,
        take_price_wechart: 0,
        // shop_examine: 5,
        price_examine: 5,
        shop_price_low: 0,
        shop_limit: 0,
        staff_limit: 0,
        capacity_limit: 0,
        self_limit: 0,
        price_manual: 5,
        cash_on_delivery: 4,
        balance_payment: 5,
        coupon: 5,
        refund: 4,
        shop_price_examine: [1, 2, 3],
        bank_card: [
          {
            card_name: "农行",
          },
          {
            card_name: "招商",
          },
        ],
        market_checkList: [],
        application_checkList: [],
        shop_limit_checked: true,
        staff_limit_checked: true,
        capacity_limit_checked: true,
        self_limit_checked: true,
        settlement_wechart: {
          settlement_one: true,
          settlement_two: true,
          settlement_three: false,
        },
        settlement_price: {
          settlement_one: true,
          settlement_two: true,
          settlement_three: false,
        },
        settlement: {
          settlement_one: true,
          settlement_two: true,
          settlement_three: false,
          settlement_four: true,
          settlement_five: false,
          settlement_six: false,
        },
      },
      img_list: [],
      data: {},
    };
  },
  computed: {
    ...mapGetters({
      storeData: "MUser/storeData",
    }),
  },
  created() {
    this.base_form.url = window.location.origin + "/#/MerchantsLogin";
    this.getSettingMerchant();
    this.data = this.base_form;
  },
  activated() {
    if (this.$_isInit()) return;
    this.getSettingMerchant();
  },
  methods: {
    ...mapActions({
      changeStoreData: "MUser/changeStoreData",
    }),
    openUrl() {
      let routeData = this.$router.resolve({
        path: "/MerchantsLogin?shopId=" + this.storeData.id,
      });
      window.open(routeData.href, "_blank");
    },
    uploadSuccess(val, res, file, fileList) {
      this.base_form.logo = val;
    },
    uploadRemove(file, fileList) {
      this.base_form.logo = "";
    },
    // 获取详情
    async getSettingMerchant() {
      const data = await getSettingMerchant({
        type: 4,
      });
      if (JSON.stringify(data.data) !== "{}") {
        this.base_form = data.data;
      } else {
        this.base_form.logo = data.data.logo;
      }
      if (this.base_form.logo) {
        this.img_list = [
          {
            name: "",
            url: this.base_form.logo,
          },
        ];
      } else {
        this.img_list = [];
      }
    },
    // 保存
    async setMerchant() {
      const data = await saveSettingMerchant({
        data: this.base_form,
        type: 4,
      });
      this.$message.success("保存成功");
      this.getSettingMerchant();
    },
    addCard() {
      this.base_form.bank_card.push({
        card_name: "",
      });
    },
    delCard(index) {
      this.base_form.bank_card.splice(index, 1);
    },
  },
};
</script>

<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
</style>
