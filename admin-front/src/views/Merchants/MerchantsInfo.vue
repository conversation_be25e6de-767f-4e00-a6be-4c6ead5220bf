<template>
  <ContainerTit>
    <div slot="headr">
      <el-button v-if="activeName === 'first'" type="primary" @click="delMerchant"> 删除商户 </el-button>
      <el-button v-else type="primary">保存</el-button>
    </div>
    <div class="merchantsInfo">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="商户信息" name="first">
          <CommodityInfo :merchants-info="base_form"></CommodityInfo>
        </el-tab-pane>
        <!--        <el-tab-pane label="商户设置" name="second">-->
        <!--          <CommoditySet :merchants-info="base_form"></CommoditySet>-->
        <!--        </el-tab-pane>-->
      </el-tabs>
    </div>
  </ContainerTit>
</template>

<script>
import CommoditySet from "./components/CommoditySet.vue";
import CommodityInfo from "./components/CommodityInfo.vue";
import { delMerchant, getInfoMerchant } from "@/api/Merchants";
export default {
  name: "MerchantsInfo",
  components: {
    // CommoditySet,
    CommodityInfo,
  },
  data() {
    return {
      activeName: "first",
      target_id: "",
      base_form: {},
    };
  },
  created() {
    this.target_id = this.$route.params.id;
    this.getInfoMerchant();
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    // 详情
    async getInfoMerchant() {
      const { data } = await getInfoMerchant(this.target_id);
      this.base_form = {
        ...data,
        url: window.location.origin + "/#/MerchantsLogin",
      };
    },
    // 删除商户
    async delMerchant() {
      this.$confirm("您确定要删除这个子商户吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delMerchant(this.target_id);
        this.$router.push("/Merchants/MerchartsList");
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
  },
};
</script>
<style>
.merchantsInfo .el-tabs__nav-scroll {
  background-color: #fff;
  padding: 10px;
}
</style>
<style scoped></style>
