<template>
  <div>
    <el-form ref="base_form" :model="base_form" label-width="120px" size="small">
      <div class="detail-tab-item">
        <p class="detail-tab-title">审核信息</p>
        <div class="detail-tab-main shop_info">
          <div class="shop_info_left">
            <div class="shop_info_sec">
              <p style="font-size: 12px; font-weight: 580">入驻审核</p>
              <div class="shop_info_item">
                <p>待审核</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>审核中</p>
                <p class="tit">0</p>
              </div>
            </div>
          </div>
          <div class="shop_info_left">
            <div class="shop_info_sec">
              <p style="font-size: 12px; font-weight: 580">提现审核</p>
              <div class="shop_info_item">
                <p>待审核</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>待打款</p>
                <p class="tit">0</p>
              </div>
            </div>
          </div>
          <div class="shop_info_left" style="border-right: none">
            <div class="shop_info_sec">
              <p style="font-size: 12px; font-weight: 580">商品审核</p>
              <div class="shop_info_item">
                <p>待审核</p>
                <p class="tit">0</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">商户信息</p>
        <div class="detail-tab-main shop_info">
          <div class="shop_info_left">
            <div class="shop_info_sec">
              <div class="shop_info_item">
                <p>商户数量</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>启用</p>
                <p class="tit">0</p>
                <p style="font-size: 12px; cursor: pointer" @click="$router.push('/Merchants/MerchartsList')">
                  禁用：
                  <span>0</span>
                </p>
              </div>
              <div class="shop_info_item">
                <p>即将过期</p>
                <p class="tit">0</p>
                <p style="font-size: 12px; cursor: pointer" @click="$router.push('/Merchants/MerchartsList')">
                  已过期：
                  <span>0</span>
                </p>
              </div>
            </div>
          </div>
          <div class="shop_info_left" style="border-right: none">
            <div class="shop_info_sec">
              <div class="shop_info_item">
                <p>全部商品</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>出售中</p>
                <p class="tit">0</p>
                <p style="font-size: 12px; cursor: pointer" @click="$router.push('/Merchants/MerchartsShop')">
                  已售罄：
                  <span>0</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">交易信息</p>
        <div class="detail-tab-main">
          <div class="merchinfo">
            <div class="merchinfo_item">
              <div>
                <span>支付金额</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="子商户支付订单金额总和，不剔除去退款金额"
                  placement="top"
                >
                  <span><i class="el-icon-question"></i></span>
                </el-tooltip>
              </div>
              <div style="font-weight: 500; font-size: 24px">0</div>
            </div>
            <div class="merchinfo_item">
              <div>
                <span>可结算金额</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="子商户可申请结算订单金额总和（已完结订单）"
                  placement="top"
                >
                  <span><i class="el-icon-question"></i></span>
                </el-tooltip>
              </div>
              <div style="font-weight: 500; font-size: 24px">0</div>
            </div>
            <div class="merchinfo_item">
              <div>
                <span>结算中金额</span>
                <el-tooltip class="item" effect="dark" content="子商户待审核和待打款结算订单金额总和" placement="top">
                  <span><i class="el-icon-question"></i></span>
                </el-tooltip>
              </div>
              <div style="font-weight: 500; font-size: 24px">0</div>
            </div>
            <div class="merchinfo_item">
              <div>
                <span>已打款金额</span>
                <el-tooltip class="item" effect="dark" content="子商户完成结算实际打款金额总和" placement="top">
                  <span><i class="el-icon-question"></i></span>
                </el-tooltip>
              </div>
              <div style="font-weight: 500; font-size: 24px">0</div>
            </div>
          </div>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">订单信息</p>
        <div class="detail-tab-main shop_info">
          <div class="shop_info_left">
            <div class="shop_info_sec">
              <div class="shop_info_item">
                <p>维权订单</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>维权中</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>维权完成</p>
                <p class="tit">0</p>
              </div>
            </div>
          </div>
          <div class="shop_info_left" style="border-right: none">
            <div class="shop_info_sec">
              <div class="shop_info_item">
                <p>全部订单</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>待付款</p>
                <p class="tit">0</p>
              </div>
              <div class="shop_info_item">
                <p>待发货</p>
                <p class="tit">0</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "MerchantsOverview",
  data() {
    return {
      base_form: {},
    };
  },
  methods: {},
};
</script>

<style scoped>
.merchinfo {
  display: flex;
  -webkit-justify-content: center;
}
.merchinfo_item {
  flex: 1;
  height: 140px;
  text-align: center;
  padding-top: 50px;
}
.shop_info {
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.shop_info_sec {
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding-right: 50px;
}
.shop_info_left {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  position: relative;
  margin-left: 10px;
  margin-right: 50px;
  border-right: 1px solid #e2e2e2;
}
.shop_info_right {
  -webkit-box-flex: 2;
  -webkit-flex: 2;
  flex: 2;
  position: relative;
  margin-left: 10px;
}
.shop_info_item {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 140px;
  text-align: center;
  padding-top: 50px;
}
.tit {
  font-weight: 500;
  font-size: 30px;
}
</style>
