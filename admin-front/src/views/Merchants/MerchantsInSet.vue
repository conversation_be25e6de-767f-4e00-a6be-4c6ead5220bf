<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" @click="setMerchant">保存</el-button>
    </div>
    <el-form ref="base_form" :model="base_form" label-width="160px" :rules="base_rules" size="small">
      <div class="detail-tab-item">
        <p class="detail-tab-title">商户后台</p>
        <div class="detail-tab-main">
          <el-form-item label="入驻申请页标题：" style="width: 30%">
            <el-input v-model="base_form.apply_title"></el-input>
          </el-form-item>
          <el-form-item label="Banner图片：">
            <UploadQiniu :limit="1" :file-list="img_list" @uploadSuccess="uploadSuccess" @handleRemove="uploadRemove" />
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">申请资料</p>
        <div class="detail-tab-main">
          <el-table :data="base_form.applyProp" style="width: 100%" border size="small">
            <el-table-column prop="name" label="资料项名称">
              <template slot-scope="scope">
                {{ scope.row.name }}
                <span v-if="scope.row.type === 1" style="margin-left: 20px">
                  系统字段-{{ scope.row.origin_name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="data_type" label="数据类型">
              <template slot-scope="scope">
                {{
                  scope.row.data_type === 1
                    ? "单行文本"
                    : scope.row.data_type === 2
                    ? "多行文本"
                    : scope.row.data_type === 3
                    ? "地区"
                    : scope.row.data_type === 4
                    ? "图片"
                    : "选择位置"
                }}
              </template>
            </el-table-column>
            <el-table-column prop="is_enable" label="是否启用">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.is_enable" :disabled="scope.row.disable"></el-switch>
              </template>
            </el-table-column>
            <el-table-column prop="is_required" label="是否必填">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.is_required" :disabled="scope.row.disable"></el-switch>
              </template>
            </el-table-column>
            <el-table-column prop="address" label="操作">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="editData(scope.$index, scope.row)"> 编辑 </el-button>
                <el-button v-if="scope.row.type !== 1" size="mini" type="text" @click="deleteMerchant(scope.$index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div
            style="text-align: center; font-size: 12px; margin-top: 20px; color: rgb(64, 158, 255); cursor: pointer"
            @click="addDataFn"
          >
            <i class="el-icon-plus"></i>
            <span>添加资料申请项</span>
            <span>{{ base_form.applyProp.length }}</span>
            <span>/20</span>
          </div>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">入驻协议</p>
        <div class="detail-tab-main">
          <el-form-item label="入驻协议：">
            <el-radio-group v-model="base_form.settlement">
              <el-radio :label="5">开启</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
            <p style="font-size: 12px">申请入驻多商户时，显示入驻协议</p>
          </el-form-item>
          <el-form-item v-if="base_form.settlement === 5" label="入驻申请协议：" style="width: 30%">
            <el-input v-model="base_form.settlement_set"></el-input>
          </el-form-item>
          <el-form-item v-if="base_form.settlement === 5" label="入驻申请页内容：">
            <el-input
              v-model="base_form.settlement_content"
              type="textarea"
              style="width: 30%"
              :rows="2"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :title="target_id ? '编辑资料项' : '添加资料项'"
      :visible.sync="add_data"
      width="30%"
    >
      <el-form ref="add_form" :model="add_form" :rules="base_rules" size="small" label-width="120px">
        <el-form-item label="资料项名称：" prop="name" style="margin-bottom: 20px">
          <el-input
            v-model="add_form.name"
            type="text"
            placeholder="请输入内容"
            maxlength="10"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="数据类型:" prop="data_type">
          <el-select v-model="add_form.data_type" placeholder="请选择" size="small" :disabled="add_form.type === 1">
            <el-option
              v-for="item in data_type_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="add_data = false">取 消</el-button>
        <el-button type="primary" @click="addData">确 定</el-button>
      </span>
    </el-dialog>
  </ContainerTit>
</template>

<script>
import { saveSettingMerchant, getSettingMerchant } from "@/api/Merchants";
import UploadQiniu from "@/component/common/UploadQiniu.vue";
export default {
  name: "SettlementSet",
  components: {
    UploadQiniu,
  },
  data() {
    return {
      img_list: [],
      base_form: {
        apply_title: "商户入驻申请", //页面标题
        settlement: 4, //是否启用入驻申请
        settlement_set: "入驻申请协议", //入驻申请协议标题
        settlement_content: "", //入驻申请协议内容
        banner_img: "", // 申请页面图片
        applyProp: [
          {
            origin_name: "联系人姓名",
            name: "联系人姓名",
            prop: "contactName",
            is_enable: true,
            is_required: true,
            data_type: 1,
            type: 1,
            disable: true,
          },
          {
            origin_name: "联系方式",
            name: "联系方式",
            prop: "contactMobile",
            is_enable: true,
            is_required: true,
            data_type: 1,
            disable: true,
            type: 1,
          },
          {
            origin_name: "商户名称",
            name: "商户名称",
            prop: "name",
            is_enable: true,
            is_required: true,
            data_type: 1,
            disable: true,
            type: 1,
          },
          {
            origin_name: "主营类目",
            name: "主营类目",
            prop: "category",
            is_enable: true,
            is_required: true,
            data_type: 1,
            type: 1,
          },
          {
            origin_name: "商户地区",
            name: "商户地区",
            prop: "regionName",
            is_enable: true,
            is_required: true,
            data_type: 3,
            type: 1,
          },
          {
            origin_name: "详细地址",
            name: "详细地址",
            prop: "address",
            is_enable: true,
            is_required: true,
            data_type: 5,
            type: 1,
          },
          {
            origin_name: "商户简介",
            name: "商户简介",
            prop: "desc",
            is_enable: true,
            is_required: true,
            data_type: 2,
            type: 1,
          },
          {
            origin_name: "营业执照",
            name: "营业执照",
            prop: "license",
            is_enable: true,
            is_required: true,
            data_type: 4,
            type: 1,
          },
        ],
      },
      base_rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        data_type: [{ required: true, message: "请输入数据类型", trigger: "blur" }],
      },
      add_form: {
        name: "",
        is_enable: true,
        is_required: true,
        data_type: "",
        prop: "value",
      },
      data_type_options: [
        {
          value: 1,
          label: "单行文本",
        },
        {
          value: 2,
          label: "多行文本",
        },
        {
          value: 3,
          label: "地区",
        },
        {
          value: 4,
          label: "图片",
        },
        {
          value: 5,
          label: "选择位置",
        },
      ],
      dialogVisible: false,
      add_data: false,
      data: {},
      target_id: "",
      originName: "",
    };
  },
  created() {
    this.getSettingMerchant();
    this.data = this.base_form;
  },
  activated() {
    if (this.$_isInit()) return;
    this.getSettingMerchant();
  },
  methods: {
    uploadSuccess(val, res, file, fileList) {
      this.base_form.banner_img = val;
    },
    uploadRemove(file, fileList) {
      this.base_form.banner_img = "";
    },
    addData() {
      if (this.target_id) {
        this.base_form.applyProp[this.target_id - 1] = this.add_form;
        this.add_data = false;
      } else {
        this.base_form.applyProp.push(this.$_common.deepClone(this.add_form));
        this.add_data = false;
      }
    },
    editData(index, row) {
      this.add_data = true;
      this.add_form = row;
      this.target_id = index + 1;
    },
    addDataFn() {
      this.target_id = "";
      this.add_form = {
        name: "",
        is_enable: true,
        is_required: true,
        data_type: "",
        prop: "value",
      };
      this.add_data = true;
    },
    // 详情
    async getSettingMerchant() {
      const data = await getSettingMerchant({
        type: 5,
      });
      if (JSON.stringify(data.data) !== "{}") {
        this.base_form = data.data;
      }
      if (this.base_form.banner_img) {
        this.img_list = [
          {
            name: "",
            url: this.base_form.banner_img,
          },
        ];
      } else {
        this.img_list = [];
      }
    },
    // 保存
    async setMerchant() {
      const data = await saveSettingMerchant({
        data: this.base_form,
        type: 5,
      });
      this.$message.success("保存成功");
      this.getSettingMerchant();
    },
    deleteMerchant(index) {
      this.base_form.applyProp.splice(index, 1);
    },
  },
};
</script>

<style scoped></style>
