<template>
  <Container>
    <el-form slot="left" :inline="true" size="small" style="margin-bottom: 0">
      <el-form-item label="分销商：">
        {{ businessman_name }}
      </el-form-item>
      <el-form-item label="下线总数：">
        {{ subTotal }}
      </el-form-item>
      <el-form-item label="分销商总数：">
        {{ businessmanTotal }}
      </el-form-item>
      <!--      <el-form-item-->
      <!--        label="时间"-->
      <!--      >-->
      <!--       -->
      <!--      </el-form-item>-->
    </el-form>
    <el-tabs v-if="level > 0" v-model="tab_name" type="card" @tab-click="onTabChange">
      <el-tab-pane label="一级" name="1"></el-tab-pane>
      <el-tab-pane v-if="level > 1" label="二级" name="2"></el-tab-pane>
      <el-tab-pane v-if="level > 2" label="三级" name="3"></el-tab-pane>
    </el-tabs>
    <el-table :data="businessman_list">
      <el-table-column prop="name" label="用户" min-width="100">
        <template slot-scope="scope">
          <img
            :src="scope.row.avatar"
            style="width: 40px; height: 40px; object-fit: cover; vertical-align: middle; margin-right: 10px"
          />
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column prop="isCommission" label="用户类型" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.isCommission === 4 ? "普通用户" : scope.row.isCommission === 5 ? "分销商" : "--" }}
        </template>
      </el-table-column>
      <el-table-column prop="totalMoney" label="消费金额" min-width="100"></el-table-column>
      <el-table-column prop="orderTotal" min-width="100" label="订单总数"></el-table-column>
      <el-table-column prop="createTime" min-width="160" label="时间">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column min-width="160" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="editRelationshipChange(scope.row)"> 修改上级 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <vxe-modal
      v-model="relationship_show"
      title="修改上级"
      width="500"
      min-height="400"
      @close="closeRelationship('form')"
    >
      <template #default>
        <el-form ref="form" :model="form" label-width="130px" :rules="rules">
          <el-form-item label="上级分销商名称" prop="distributorName">
            <el-input
              v-model="form.distributorName"
              readonly
              style="width: 300px"
              size="small"
              placeholder="选择分销商"
            >
              <i slot="suffix" class="el-input__icon el-icon-search" @click="DistributorList_show = true"></i>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="relationship_show = false">取 消</el-button>
            <el-button type="primary" @click="editRelationship"> 确 定 </el-button>
          </el-form-item>
        </el-form>
      </template>
    </vxe-modal>
    <DistributorList
      v-if="DistributorList_show"
      :is-show="DistributorList_show"
      @cancel="DistributorList_show = false"
      @confirm="accountSel"
    ></DistributorList>
  </Container>
</template>

<script>
import { getSetting, delBusinessman, getAllSub, getAllGrade, editRelationship } from "@/api/Commission";
import DistributorList from "@/component/common/DistributorList";
export default {
  name: "DistributorNext",
  components: {
    DistributorList,
  },
  data() {
    return {
      tab_name: "1",
      search_form: {
        auditStatus: 2,
        auditStar: "",
        auditEnd: "",
        grade: "",
        search: "",
      },
      auditTime: [],
      businessman_list: [],
      grade_list: [],
      total: 0,
      page: 1,
      pageSize: 10,
      subTotal: "",
      businessman_name: "",
      businessmanTotal: "",
      businessman_id: "",
      level: "",
      relationship_show: false,
      form: {
        id: "",
        distributorName: "",
        distributorId: "",
      },
      DistributorList_show: false,
      rules: {
        distributorName: [{ required: true, message: "请选择分销商", trigger: "blur" }],
      },
    };
  },
  async created() {
    this.businessman_id = this.$route.query.id;
    this.businessman_name = this.$route.query.name;
    await this.getSetting();
    await this.getAllBusinessman();
  },
  async activated() {
    if (this.$_isInit()) return;
    this.businessman_id = this.$route.query.id;
    this.businessman_name = this.$route.query.name;
    await this.getSetting();
    await this.getAllBusinessman();
  },
  methods: {
    // 分销商级别切换
    onTabChange(name) {
      this.page = 1;
      this.getAllBusinessman();
    },
    // 获取分销商设置
    async getSetting() {
      const { data } = await getSetting();

      this.level = data.level;
    },
    // 删除分销商
    delBusinessman(id) {
      this.$confirm("确定删除该分销商吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delBusinessman(id);

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        await this.getAllBusinessman();
      });
    },
    async getAllBusinessman() {
      const data = await getAllSub({
        page: this.page,
        pageSize: this.pageSize,
        id: parseInt(this.businessman_id), // 分销商id
        level: parseInt(this.tab_name), // 层级
        // ...this.search_form
      });

      this.businessman_list = data.data;
      this.total = data.pageTotal;
      this.subTotal = data.subTotal;
      this.businessmanTotal = data.businessmanTotal;
    },
    async getAllGrade() {
      const { data } = await getAllGrade();

      this.grade_list = data;
    },
    pageChange(val) {
      this.page = val;
      this.getAllBusinessman();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getAllBusinessman();
    },
    auditTimeChange(val) {
      if (val && val.length) {
        this.search_form.auditStar = val[0] / 1000;
        this.search_form.auditEnd = val[1] / 1000 + 86399;
      } else {
        this.search_form.auditStar = "";
        this.search_form.auditEnd = "";
      }
      this.pageChange(1);
    },
    editRelationshipChange(row) {
      this.form.id = row.userCenterId;
      this.relationship_show = true;
    },
    accountSel(val) {
      this.form.distributorName = val[0].name;
      this.form.distributorId = val[0].userCenterId;
    },
    async editRelationship() {
      if (!this.form.distributorId) {
        this.$message.warning("请选择分销商");
        return;
      }
      const { data } = await editRelationship({
        id: this.form.id,
        superiorId: this.form.distributorId,
      });
      this.$message.success("修改上级成功");
      this.relationship_show = false;
      await this.getAllBusinessman();
    },
    closeRelationship(formData) {
      this.relationship_show = false;
      this.form = {
        id: "",
        distributorName: "",
        distributorId: "",
      };
      this.$refs[formData].resetFields();
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
/*.commodity{*/
/*  width: 30%;*/
/*}*/
.clear span {
  color: red;
  cursor: pointer;
  margin-left: 20px;
  font-size: 12px;
}
</style>
