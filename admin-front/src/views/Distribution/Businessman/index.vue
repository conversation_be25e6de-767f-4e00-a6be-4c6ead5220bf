<template>
  <keep-alive v-if="onKeepAlive" :include="cachedRoutes">
    <router-view :key="$route.path" />
  </keep-alive>
  <router-view v-else :key="$route.path" />
</template>
<script>
import { mapGetters } from "vuex";

export default {
  name: "Businessman",
  computed: {
    ...mapGetters({
      visitedRoutes: "tagsBar/visitedRoutes",
    }),
    cachedRoutes() {
      return this.visitedRoutes.filter((item) => !item.meta.noKeepAlive).flatMap((item) => item.matched);
    },
  },
};
</script>
