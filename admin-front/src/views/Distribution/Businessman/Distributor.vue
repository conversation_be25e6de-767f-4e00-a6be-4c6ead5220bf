<template>
  <Container>
    <el-form slot="left" :inline="true" style="margin-bottom: 0" size="small">
      <el-form-item>
        <el-input
          v-model="search_form.search"
          placeholder="手机号/姓名"
          class="input-with-select"
          style="width: 200px"
          clearable
          @clear="pageChange(1)"
          @keyup.enter.native="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="search_form.grade"
          placeholder="等级"
          clearable
          style="width: 150px"
          @change="pageChange(1)"
        >
          <el-option
            v-for="(item, index) in grade_list"
            :key="index"
            :value="item.grade"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="auditTime"
          type="daterange"
          range-separator="-"
          start-placeholder="成为分销商开始时间"
          end-placeholder="成为分销商结束时间"
          value-format="timestamp"
          @change="auditTimeChange"
        ></el-date-picker>
      </el-form-item>
      <el-button size="small" type="primary" @click="getAllBusinessman(1)"> 导出 </el-button>
    </el-form>
    <el-table :data="businessman_list">
      <el-table-column prop="name" label="姓名" min-width="130" show-overflow-tooltip></el-table-column>
      <el-table-column prop="mobile" label="手机号" min-width="120"></el-table-column>
      <el-table-column prop="gradeName" label="等级" min-width="100"></el-table-column>
      <el-table-column prop="totalMoney" min-width="100" label="累计佣金">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="waitMoney" min-width="100" label="提现佣金">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.waitMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="成为分销商时间" min-width="160px">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="subTotal" label="下线总数" min-width="100px">
        <template slot-scope="scope">
          {{ scope.row.statistics ? scope.row.statistics.sub || 0 : 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="subBusinessman" label="下级分销商总数" min-width="120px">
        <template slot-scope="scope">
          {{ scope.row.statistics ? scope.row.statistics.subBusinessman || 0 : 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="operation" label="操作" min-width="140px">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.BusinessmangetdelBusinessman)"
            type="text"
            size="small"
            @click="delBusinessman(scope.row.userCenterId)"
          >
            删除
          </el-button>
          <el-button
            v-if="scope.row.statistics && scope.row.statistics.sub > 0 && $accessCheck($Access.BusinessmangetgetAllSub)"
            type="text"
            size="small"
            @click="
              $router.push('/Distribution/Businessman/DistributorNext?id=' + scope.row.id + '&name=' + scope.row.name)
            "
          >
            查看下级
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { delBusinessman, getAllBusinessman, getAllGrade, exportGetAllBusinessman } from "@/api/Commission";
export default {
  name: "Distributor",
  data() {
    return {
      search_form: {
        auditStatus: 2,
        auditStar: "",
        auditEnd: "",
        grade: "",
        search: "",
      },
      auditTime: [],
      businessman_list: [],
      grade_list: [],
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  created() {
    this.getAllBusinessman();
    this.getAllGrade();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllBusinessman();
  },
  methods: {
    delBusinessman(userCenterId) {
      this.$confirm("确定删除该分销商吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delBusinessman({
          userCenterId: userCenterId,
        });

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        await this.getAllBusinessman();
      });
    },
    async getAllBusinessman(exports) {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        ...this.search_form,
      };
      if (exports) {
        params.export = exports;
        params.distribution = 1;
        const target = await exportGetAllBusinessman({
          ...params,
        });
      } else {
        const { data, pageTotal } = await getAllBusinessman({
          ...params,
        });
        this.businessman_list = data;
        this.total = pageTotal;
      }
    },
    async getAllGrade() {
      const { data } = await getAllGrade();

      this.grade_list = data;
    },
    pageChange(val) {
      this.page = val;
      this.getAllBusinessman();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getAllBusinessman();
    },
    auditTimeChange(val) {
      if (val && val.length) {
        this.search_form.auditStar = val[0] / 1000;
        this.search_form.auditEnd = val[1] / 1000 + 86399;
      } else {
        this.search_form.auditStar = "";
        this.search_form.auditEnd = "";
      }
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
/*.commodity{*/
/*  width: 30%;*/
/*}*/
.clear span {
  color: red;
  cursor: pointer;
  margin-left: 20px;
  font-size: 12px;
}
</style>
