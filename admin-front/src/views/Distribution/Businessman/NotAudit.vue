<template>
  <Container>
    <el-form slot="left" :inline="true" size="small" style="margin-bottom: 0">
      <el-form-item>
        <el-input
          v-model="search_form.search"
          placeholder="手机号/姓名"
          class="input-with-select"
          style="width: 200px"
          @keyup.enter.native="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search_form.grade" style="width: 150px" placeholder="等级" @change="pageChange(1)">
          <el-option
            v-for="(item, index) in grade_list"
            :key="index"
            :value="item.grade"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="applicationTime"
          type="daterange"
          range-separator="-"
          start-placeholder="申请开始日期"
          end-placeholder="申请结束日期"
          value-format="timestamp"
          @change="applicationTimeChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="createTime"
          type="daterange"
          range-separator="-"
          start-placeholder="注册开始日期"
          end-placeholder="注册结束日期"
          value-format="timestamp"
          @change="createTimeChange"
        ></el-date-picker>
      </el-form-item>
      <el-button size="small" type="primary" @click="getAllBusinessman(1)"> 导出 </el-button>
    </el-form>
    <el-table :data="businessman_list">
      <el-table-column prop="name" label="姓名" min-width="100"></el-table-column>
      <el-table-column prop="mobile" label="手机号" min-width="120"></el-table-column>
      <el-table-column prop="gradeName" label="等级" min-width="100"></el-table-column>
      <el-table-column prop="auditOrderTotal" label="累计消费次数" min-width="140">
        <template slot-scope="scope">
          {{ scope.row.statistics ? scope.row.statistics.auditOrderTotal || 0 : 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="auditTotalMoney" label="累计消费金额" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.statistics ? $_common.formattedNumber(scope.row.statistics.auditTotalMoney) : "0.00" }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="注册时间" min-width="150">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.customerCreateTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="applicationComplete" label="申请状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.applicationComplete === 4" class="warning-status"> 未满足 </span>
          <span v-else class="success-status">满足</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="申请时间" min-width="150">
        <template slot-scope="scope">
          {{ scope.row.createTime ? $_common.formatDate(scope.row.createTime) : "--" }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="$accessCheck($Access.BusinessmangetAuditBusinessman)"
        label="操作"
        align="center"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="audit(scope.row.userCenterId)"> 审核 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { getAllBusinessman, getAllGrade, auditBusinessman, exportGetAllBusinessman } from "@/api/Commission";
import { exportGetAllSaleOut } from "@/api/Stock";
export default {
  name: "NotAudit",
  data() {
    return {
      search_form: {
        auditStatus: 1,
        applicationStar: "",
        applicationEnd: "",
        createStar: "",
        createEnd: "",
        grade: "",
        search: "",
      },
      applicationTime: [],
      createTime: [],
      businessman_list: [],
      total: 0,
      page: 1,
      pageSize: 10,
      grade_list: [],
    };
  },
  created() {
    this.getAllBusinessman();
    this.getAllGrade();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllBusinessman();
  },
  methods: {
    async getAllBusinessman(exports) {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        ...this.search_form,
      };
      if (exports) {
        params.export = exports;
        const target = await exportGetAllBusinessman({
          ...params,
        });
      } else {
        const { data, pageTotal } = await getAllBusinessman({
          ...params,
        });
        this.businessman_list = data;
        this.total = pageTotal;
      }
    },
    async getAllGrade() {
      const { data } = await getAllGrade();

      this.grade_list = data;
    },
    pageChange(val) {
      this.page = val;
      this.getAllBusinessman();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getAllBusinessman();
    },
    applicationTimeChange(val) {
      if (val && val.length) {
        this.search_form.applicationStar = val[0] / 1000;
        this.search_form.applicationEnd = val[1] / 1000 + 86399;
      } else {
        this.search_form.applicationStar = "";
        this.search_form.applicationEnd = "";
      }
      this.pageChange(1);
    },
    createTimeChange(val) {
      if (val && val.length) {
        this.search_form.createStar = val[0] / 1000;
        this.search_form.createEnd = val[1] / 1000 + 86399;
      } else {
        this.search_form.createStar = "";
        this.search_form.createEnd = "";
      }
      this.pageChange(1);
    },
    audit(userCenterId) {
      this.$confirm("确定要通过审核吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await auditBusinessman({
          userCenterId: userCenterId,
        });

        this.$message.success("操作成功");
        this.getAllBusinessman();
      });
    },
  },
};
</script>

<style scoped>
.clear span {
  color: red;
  cursor: pointer;
  font-size: 12px;
}
</style>
