<template>
  <ContainerTit>
    <div v-if="$accessCheck($Access.TextSetsaveTxtSetting)" slot="headr">
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
    <div class="page-div">
      <el-form :model="base_form" label-width="130px" size="small">
        <el-form-item label="分销商" prop="activityType" class="commodity">
          <el-input v-model="base_form.name" placeholder="分销商"></el-input>
        </el-form-item>
        <el-form-item label="分销中心" prop="activityType" class="commodity">
          <el-input v-model="base_form.distribution" placeholder="分销中心"></el-input>
        </el-form-item>
        <el-form-item label="成为分销商" prop="activityType" class="commodity">
          <el-input v-model="base_form.distributor" placeholder="成为分销商"></el-input>
        </el-form-item>
        <el-form-item label="提现" prop="activityType" class="commodity">
          <el-input v-model="base_form.withdrawal" placeholder="提现"></el-input>
        </el-form-item>
        <el-form-item label="佣金" prop="activityType" class="commodity">
          <el-input v-model="base_form.commission" placeholder="佣金"></el-input>
        </el-form-item>
        <el-form-item label="可提现佣金" prop="activityType" class="commodity">
          <el-input v-model="base_form.commission_w" placeholder="可提现佣金"></el-input>
        </el-form-item>
        <el-form-item label="累计佣金" prop="activityType" class="commodity">
          <el-input v-model="base_form.commission_c" placeholder="累计佣金"></el-input>
        </el-form-item>
        <el-form-item label="分销订单" prop="activityType" class="commodity">
          <el-input v-model="base_form.distribution_order" placeholder="分销订单"></el-input>
        </el-form-item>
        <el-form-item label="我的团队" prop="activityType" class="commodity">
          <el-input v-model="base_form.self" placeholder="我的团队"></el-input>
        </el-form-item>
        <el-form-item label="下线" prop="activityType" class="commodity">
          <el-input v-model="base_form.Offline" placeholder="下线"></el-input>
        </el-form-item>
        <el-form-item label="提现明细" prop="activityType" class="commodity">
          <el-input v-model="base_form.withdrawal_m" placeholder="提现明细"></el-input>
        </el-form-item>
        <el-form-item label="佣金排名" prop="activityType" class="commodity">
          <el-input v-model="base_form.commission_p" placeholder="佣金排名"></el-input>
        </el-form-item>
        <el-form-item label="待审核佣金" prop="activityType" class="commodity">
          <el-input v-model="base_form.commission_s" placeholder="待审核佣金"></el-input>
        </el-form-item>
        <el-form-item label="待打款佣金" prop="activityType" class="commodity">
          <el-input v-model="base_form.commission_d" placeholder="待打款佣金"></el-input>
        </el-form-item>
        <el-form-item label="待入账佣金" prop="activityType" class="commodity">
          <el-input v-model="base_form.commission_r" placeholder="待入账佣金"></el-input>
        </el-form-item>
        <el-form-item label="级别名称" prop="activityType">
          <el-input v-model="base_form.level_one" placeholder="一级" class="level"></el-input>
          <el-input v-model="base_form.level_two" placeholder="二级" class="level"></el-input>
          <el-input v-model="base_form.level_three" placeholder="三级" class="level"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </ContainerTit>
</template>

<script>
import { getTxtSetting, saveTxtSetting } from "@/api/Commission";
export default {
  name: "TextSet",
  data() {
    return {
      base_form: {
        name: "分销商",
        distribution: "分销中心",
        distributor: "成为分销商",
        withdrawal: "提现",
        commission: "佣金",
        commission_w: "可提现佣金",
        commission_c: "累计佣金",
        distribution_order: "分销订单",
        self: "我的团队",
        Offline: "下线",
        withdrawal_m: "提现明细",
        commission_p: "佣金排名",
        commission_s: "待审核佣金",
        commission_d: "待打款佣金",
        commission_r: "待入账佣金",
        level_one: "一级",
        level_two: "二级",
        level_three: "三级",
      },
    };
  },
  created() {
    this.getTxtSetting();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getTxtSetting();
  },
  methods: {
    async getTxtSetting() {
      if (!this.$accessCheck(this.$Access.TextSetsaveTxtSetting)) return;
      const { data } = await getTxtSetting();
      if (data.base_form) {
        this.base_form = data.base_form;
      }
    },
    async submit() {
      const data = await saveTxtSetting({
        base_form: this.base_form,
      });

      this.$message.success("提交成功");
    },
  },
};
</script>

<style scoped>
.commodity {
  width: 30%;
}
.level {
  width: 100px;
}
</style>
