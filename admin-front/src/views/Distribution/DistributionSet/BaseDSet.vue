<template>
  <!--  基础设置-->
  <ContainerTit>
    <div v-if="$accessCheck($Access.BaseDSetsaveSetting)" slot="headr">
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
    <div class="page-div">
      <el-form ref="add_form" :model="add_form" label-width="200px" :rules="base_rules" size="small">
        <div class="detail-tab-item">
          <p class="detail-tab-title">分销设置</p>
          <div class="detail-tab-main">
            <el-form-item label="分销层级:">
              <el-radio-group v-model="add_form.level">
                <el-radio :label="0">关闭</el-radio>
                <el-radio :label="1">一级分销</el-radio>
                <el-radio :label="2">二级分销</el-radio>
                <el-radio :label="3">三级分销</el-radio>
              </el-radio-group>
              <p class="form-tip">
                默认佣金比例请到
                <el-button type="text" size="mini" @click="$router.push('/Distribution/DistributionList')">
                  分销商等级
                </el-button>
                进行设置
              </p>
            </el-form-item>
            <div v-if="add_form.level > 0">
              <el-form-item label="分销自购:">
                <el-radio-group v-model="add_form.self">
                  <el-radio :label="4">关闭</el-radio>
                  <el-radio :label="5">自购返佣</el-radio>
                </el-radio-group>
                <!--<p class="form-tip">
                自购优惠开启后，分销商自己购买商品时，可以直接优惠下单，不再返佣。
              </p>-->
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="detail-tab-item">
          <p class="detail-tab-title">分销商资格</p>
          <div class="detail-tab-main">
            <el-form-item label="成为分销商条件:">
              <el-radio-group v-model="add_form.businessmanCondition">
                <el-radio :label="0">无条件</el-radio>
                <el-radio :label="1">资料申请</el-radio>
                <el-radio :label="2">消费次数</el-radio>
                <el-radio :label="3">消费金额</el-radio>
                <el-radio :label="4">购买商品</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="add_form.businessmanCondition > 0">
              <div v-if="add_form.businessmanCondition === 4">
                <el-form-item label="指定商品:">
                  <el-tabs v-model="levelName" type="card" @tab-click="levelNameClick">
                    <el-tab-pane
                      v-for="(item, index) in grade_list"
                      :key="index"
                      :label="item.gradeName"
                      :name="item.gradeName"
                    >
                      <el-table border :data="item.goodsData">
                        <el-table-column prop="goodsName" label="商品名称"></el-table-column>
                        <el-table-column prop="skuName" label="规格">
                          <template slot-scope="scope">
                            {{ scope.row.unitName }}
                            <span v-for="(itemS, indexS) in scope.row.specGroup" :key="indexS">
                              _{{ itemS.specValueName }}
                            </span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="salePrice" label="销售价"></el-table-column>
                        <el-table-column prop="inventory" label="库存">
                          <template slot-scope="scope">
                            {{ scope.row.inventory - 0 }}
                          </template>
                        </el-table-column>
                        <el-table-column label="操作">
                          <template slot-scope="scope">
                            <el-button type="text" size="mini" @click="delGoods(index, scope.$index)"> 删除 </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                      <div style="text-align: center; padding-top: 10px" class="clearfix" @click="goods_show = true">
                        <el-button type="text" icon="el-icon-circle-plus"> 选择商品 </el-button>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </el-form-item>
                <!--              <el-form-item label="商品样式:">-->
                <!--                <el-radio-group v-model="base_form.goodsStyle">-->
                <!--                  <el-radio :label="1">-->
                <!--                    单列商品-->
                <!--                  </el-radio>-->
                <!--                  <el-radio :label="2">-->
                <!--                    双列商品-->
                <!--                  </el-radio>-->
                <!--                </el-radio-group>-->
                <!--              </el-form-item>-->
              </div>
              <el-form-item v-if="add_form.businessmanCondition === 2" label="累计消费次数:">
                <el-input v-model="add_form.buyNum" style="width: 300px">
                  <i slot="append">次</i>
                </el-input>
              </el-form-item>
              <el-form-item v-if="add_form.businessmanCondition === 3" label="累计消费金额:">
                <el-input v-model="add_form.buyAmount" style="width: 300px">
                  <i slot="append">元</i>
                </el-input>
              </el-form-item>
              <el-form-item v-if="add_form.businessmanCondition > 2" label="消费条件:">
                <el-radio-group v-model="add_form.buyCondition">
                  <el-radio :label="4">付款后</el-radio>
                  <el-radio :label="5">订单完成</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="申请页面顶部图片:">
                <UploadQiniu
                  :width="100"
                  is-btn="picture"
                  :file-list="img_list"
                  @uploadSuccess="uploadSuccess"
                  @handleRemove="uploadRemove"
                />
                <p class="form-tip">
                  建议上传图片尺寸为
                  <span style="color: #e6a23c">750px*322px</span>
                </p>
              </el-form-item>
              <el-form-item label="是否需要审核:">
                <el-radio-group v-model="add_form.audit">
                  <el-radio :label="5">需要</el-radio>
                  <el-radio :label="4">不需要</el-radio>
                </el-radio-group>
              </el-form-item>

              <div v-if="add_form.businessmanCondition === 1">
                <el-form-item label="显示申请协议:">
                  <el-radio-group v-model="add_form.descStatus">
                    <el-radio :label="5">显示</el-radio>
                    <el-radio :label="4">隐藏</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="add_form.descStatus === 5" label="申请协议:">
                  {{ add_form.title }}
                  <el-button type="text" size="mini" @click="show_agree = true"> 设置 </el-button>
                </el-form-item>
                <el-form-item label="完善资料:">
                  <el-radio-group v-model="add_form.infoStatus">
                    <el-radio :label="5">需要</el-radio>
                    <el-radio :label="4">不需要</el-radio>
                  </el-radio-group>
                  <p class="form-tip">申请成为分销商是否必须完善资料</p>
                </el-form-item>
                <el-form-item label="资料内容:">
                  <div v-for="(item, index) in add_form.info" :key="index">
                    <div style="padding-bottom: 10px">
                      <span class="index-view">{{ index + 1 }}</span>
                      <el-input v-model="item.prop" style="width: 300px" maxlength="10" show-word-limit></el-input>
                      <el-button
                        size="mini"
                        :disabled="add_form.info.length <= 1"
                        type="text"
                        style="color: #f56c6c"
                        icon="el-icon-delete"
                        @click="delInfo(index)"
                      ></el-button>
                    </div>
                  </div>
                  <div>
                    <el-button
                      type="text"
                      :disabled="add_form.info.length >= 5"
                      size="mini"
                      icon="el-icon-plus"
                      @click="addInfo"
                    >
                      添加{{ add_form.info.length }}/5
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </div>
          </div>
        </div>
        <div class="detail-tab-item">
          <p class="detail-tab-title">上下线关系</p>
          <div class="detail-tab-main">
            <el-form-item label="成为下线条件:">
              <el-radio-group v-model="add_form.subConditions">
                <el-radio :label="1">首次点击分享链接</el-radio>
                <el-radio :label="2">首次下单</el-radio>
                <el-radio :label="3">首次付款</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
        <div v-if="add_form.level > 0" class="detail-tab-item">
          <p class="detail-tab-title">分销商品</p>
          <div class="detail-tab-main">
            <el-form-item label="商品默认参与分销:">
              <el-radio-group v-model="add_form.goods">
                <el-radio :label="5">是</el-radio>
                <el-radio :label="4">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
        <div class="detail-tab-item">
          <p class="detail-tab-title">排行榜设置</p>
          <div class="detail-tab-main">
            <el-form-item label="排行榜状态:">
              <el-radio-group v-model="add_form.leaderboard">
                <el-radio :label="4">开启</el-radio>
                <el-radio :label="5">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="申请协议设置"
      :visible.sync="show_agree"
      width="60%"
    >
      <ul>
        <li style="padding-bottom: 10px" class="clearfix">
          <span class="f-label">协议名称</span>
          <el-input
            v-model="add_form.title"
            size="small"
            style="float: left; width: calc(100% - 120px)"
            placeholder="请输入协议名称"
          ></el-input>
        </li>
        <li class="clearfix">
          <span class="f-label">协议内容</span>
          <div style="float: left; width: calc(100% - 120px)">
            <Tinymce v-model="add_form.desc" :height="300" />
          </div>
        </li>
      </ul>
      <span slot="footer" class="dialog-footer">
        <el-button @click="show_agree = false">取 消</el-button>
        <el-button type="primary" @click="show_agree = false">确 定</el-button>
      </span>
    </el-dialog>
    <SaleGoodsSel
      v-if="goods_show"
      :is-show="goods_show"
      @cancel="goods_show = false"
      @confirm="selGoods"
    ></SaleGoodsSel>
  </ContainerTit>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu.vue";
import Tinymce from "@/component/Tinymce";
import SaleGoodsSel from "@/component/goods/SaleGoodsSel";
import { getAllGrade, getSetting, saveSetting } from "@/api/Commission";
export default {
  name: "BaseDSet",
  components: {
    UploadQiniu,
    Tinymce,
    SaleGoodsSel,
  },
  data() {
    return {
      goods_show: false,
      buyGoods: [],
      add_form: {
        level: 0,
        self: 4,
        audit: 5,
        businessmanCondition: 1,
        buyNum: 0,
        buyAmount: 0,
        buyCondition: 5,
        image: "",
        infoStatus: 5,
        info: [
          {
            prop: "姓名",
            value: "",
          },
          {
            prop: "微信号",
            value: "",
          },
          {
            prop: "手机号",
            value: "",
          },
        ],
        buyGoods: [],
        descStatus: 5,
        desc: "",
        title: "",
        subConditions: 1,
        goods: 4,
        mode: 4,
        quota: 10,
        fee: 5,
        freeStart: 0,
        freeEnd: 0,
        day: 0,
        withdrawAudit: 5,
        autoPay: 5,
        payType: [4, 5],
        withdrawType: [4, 5],
        leaderboard: 5,
        withdrawGrade: "",
        withdrawAmount: 0,
        withdrawInfo: [
          {
            cardNum: "",
          },
        ],
      },
      show_more_cont: false,
      show_agree: false,
      base_rules: {},
      levelName: "one",
      img_list: [],
      grade_list: [],
    };
  },
  async created() {
    await this.getSetting();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getSetting();
  },
  methods: {
    async getAllGrade() {
      const { data } = await getAllGrade();

      this.levelName = data[0].name;
      this.grade_list = data.map((item) => {
        return {
          ...item,
          gradeName: item.name,
          goodsData: [],
        };
      });
    },
    async getSetting() {
      if (!this.$accessCheck(this.$Access.BaseDSetgetSetting)) {
        return;
      }
      const { data } = await getSetting();

      this.add_form = {
        ...data,
        info:
          data.info.length > 0
            ? data.info
            : [
                {
                  prop: "姓名",
                  value: "",
                },
                {
                  prop: "微信号",
                  value: "",
                },
                {
                  prop: "手机号",
                  value: "",
                },
              ],
        withdrawInfo: data.withdrawInfo.length > 0 ? data.withdrawInfo : [{ cardNum: "" }],
      };
      if (data.image) {
        this.img_list = [
          {
            name: "",
            url: data.image,
          },
        ];
      }

      if (data.buyGoods && data.buyGoods.length) {
        if (data.buyGoods[0].gradeName) {
          this.levelName = data.buyGoods[0].gradeName;
        }
        this.grade_list = data.buyGoods;

        // data.buyGoods.forEach(item => {
        //   const index = this.grade_list.findIndex(itemF => item.grade === itemF.grade)
        //   if (index > -1) {
        //     this.grade_list[index].goodsData = item.goodsData
        //   }
        // })
      }
    },
    uploadSuccess(val) {
      this.add_form.image = val;
      this.img_list = [
        {
          name: "",
          url: val,
        },
      ];
    },
    async submit() {
      let buyGoods = {};
      if (this.add_form.businessmanCondition === 4) {
        this.grade_list.forEach((item) => {
          buyGoods[item.grade] = [];
          item.goodsData.forEach((itemG) => {
            if (itemG.skuId && itemG.goodsId) {
              buyGoods[item.grade].push({
                skuId: itemG.skuId,
                goodsId: itemG.goodsId,
              });
            }
          });
        });
        this.add_form.buyGoods = buyGoods;
      }
      // 当条件不为购买商品时，不传购买商品字段
      const params = this.$_common.deepClone(this.add_form);
      if (this.add_form.businessmanCondition !== 4) {
        delete params.buyGoods;
      }

      const data = await saveSetting(params);

      this.$message.success("提交成功");
      this.getSetting();
    },
    uploadRemove() {},
    delGoods(index, sindex) {
      const target = this.$_common.deepClone(this.grade_list);
      target[index].goodsData.splice(sindex, 1);
      this.grade_list = target;
    },
    selGoods(val) {
      const goodsData = val.map((item) => {
        return {
          skuId: item.skuId,
          unitName: item.unitName,
          specGroup: item.specGroup,
          goodsId: item.id,
          goodsName: item.title,
          salePrice: item.salePrice,
          inventory: item.inventory,
        };
      });
      const target = this.$_common.deepClone(this.grade_list);
      const index = this.grade_list.findIndex((item) => item.gradeName === this.levelName);
      if (target[index].goodsData.length) {
        target[index].goodsData = this.$_common.unique(target[index].goodsData.concat(goodsData), ["goodsId", "skuId"]);
      } else {
        target[index].goodsData = goodsData;
      }
      this.grade_list = target;
    },
    levelNameClick() {},
    addInfo() {
      if (this.add_form.info.length === 5) {
        return;
      }
      this.add_form.info.push({ prop: "", value: "" });
    },
    delInfo(index) {
      this.add_form.info.splice(index, 1);
    },
  },
};
</script>

<style scoped>
.index-view {
  display: inline-block;
  font-size: 12px;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border: 1px solid #ddd;
  border-radius: 100%;
  text-align: center;
  margin-right: 10px;
  color: #666;
}
.f-label {
  width: 90px;
  text-align: right;
  float: left;
  display: inline-block;
  vertical-align: middle;
  padding-right: 10px;
}
</style>
