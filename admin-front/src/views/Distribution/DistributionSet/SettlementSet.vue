<template>
  <ContainerTit>
    <div v-if="$accessCheck($Access.SettlementSetsaveSetting)" slot="headr">
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
    <el-form ref="add_form" :model="add_form" label-width="130px" :rules="base_rules" size="small">
      <div class="detail-tab-item">
        <p class="detail-tab-title">分销设置</p>
        <div class="detail-tab-main">
          <el-form-item label="佣金计算方式:">
            <el-radio-group v-model="add_form.mode">
              <el-radio :label="4">商品折扣价</el-radio>
              <el-radio :label="5">实际支付</el-radio>
            </el-radio-group>
            <p class="form-tip">
              商品折扣价：按商品参加优惠活动后金额计算，不包含优惠券优惠金额。（不含运费）
              <br />
              实际支付：按订单最后实际支付金额计算。（不含运费）
            </p>
          </el-form-item>
          <el-form-item label="最低提现额度:">
            <el-input v-model="add_form.quota" style="width: 300px">
              <i slot="append">元</i>
            </el-input>
          </el-form-item>
          <el-form-item label="佣金提现手续费:">
            <el-input v-model="add_form.fee" style="width: 300px">
              <i slot="append">%</i>
            </el-input>
          </el-form-item>
          <el-form-item label="免手续费区间:">
            <el-input v-model="add_form.freeStart" style="width: 140px">
              <i slot="append">元</i>
            </el-input>
            至
            <el-input v-model="add_form.freeEnd" style="width: 140px">
              <i slot="append">元</i>
            </el-input>
            <p class="form-tip">当提现手续费金额在此区间时，不扣除提现手续费</p>
          </el-form-item>
          <el-form-item label="结算天数:">
            订单确认收货后
            <el-input v-model="add_form.day" style="width: 130px">
              <i slot="append">天</i>
            </el-input>
            能申请提现
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">提现方式</p>
        <div class="detail-tab-main">
          <el-form-item label="提现审核:">
            <el-radio-group v-model="add_form.withdrawAudit">
              <el-radio :label="5">手动审核</el-radio>
              <el-radio :label="4">自动审核</el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-if="add_form.withdrawAudit === 4">
            <el-form-item label="分销商等级:">
              <el-select v-model="add_form.withdrawGrade" style="width: 300px" placeholder="请选择">
                <el-option v-for="item in grade_list" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
              <p class="form-tip">该等级以上分销商提现时，自动审核通过</p>
            </el-form-item>
            <el-form-item label="提现金额:">
              <el-input v-model="add_form.withdrawAmount" style="width: 300px">
                <i slot="append">元</i>
              </el-input>
              <p class="form-tip">申请提现金额小于该设置时，自动审核通过</p>
            </el-form-item>
          </div>
          <el-form-item label="自动打款:">
            <el-radio-group v-model="add_form.autoPay">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
            <p class="form-tip">提现审核通过将自动打款（支付宝和银行卡仍需要手动打款）</p>
          </el-form-item>
          <el-form-item label="提现方式:">
            <el-checkbox-group v-model="add_form.payType">
              <el-checkbox v-for="(item, index) in cashFn" :key="index" :label="item.value">
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div v-if="add_form.payType.includes(5)">
            <el-form-item label="手动提现方式:">
              <el-checkbox-group v-model="add_form.withdrawType">
                <el-checkbox :label="4">提现到支付宝</el-checkbox>
                <el-checkbox :label="5">提现到银行卡</el-checkbox>
              </el-checkbox-group>
              <p class="form-tip">选择支付宝或银行卡提现时，需要商户手动打款</p>
            </el-form-item>
            <el-form-item v-if="add_form.withdrawType.includes(5)" label="支持银行卡:">
              <p v-for="(item, index) in add_form.withdrawInfo" :key="index">
                <el-input v-model="item.cardNum" style="width: 300px" placeholder="请填写银行名称"></el-input>
                <el-button type="text" size="mini" @click="delCard(index)"> 删除 </el-button>
              </p>
              <el-button type="text" size="mini" icon="el-icon-plus" @click="addBankCard"> 添加 </el-button>
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
  </ContainerTit>
</template>

<script>
import { getAllGrade, getSetting, saveSetting } from "@/api/Commission";

export default {
  name: "SettlementSet",
  data() {
    return {
      add_form: {
        level: 3,
        self: 4,
        audit: 5,
        businessmanCondition: 1,
        buyNum: 0,
        buyAmount: 0,
        buyCondition: 5,
        image: "",
        infoStatus: 5,
        info: [],
        buyGoods: [],
        descStatus: 5,
        desc: "",
        title: "",
        subConditions: 1,
        goods: 4,
        mode: 4,
        quota: 10,
        fee: 5,
        freeStart: 0,
        freeEnd: 0,
        day: 0,
        withdrawAudit: 5,
        autoPay: 5,
        payType: [4, 5],
        withdrawType: [4, 5],
        leaderboard: 5,
        withdrawGrade: "",
        withdrawAmount: 0,
        withdrawInfo: [
          {
            cardNum: "",
          },
        ],
      },
      base_rules: {},
      cashFn: [
        {
          label: "提现到微信钱包",
          value: 4,
        },
        {
          label: "手动提现",
          value: 5,
        },
      ],
      grade_list: [],
    };
  },
  async created() {
    await this.getAllGrade();
    await this.getSetting();
  },
  async activated() {
    if (this.$_isInit()) return;
    await this.getAllGrade();
    await this.getSetting();
  },
  methods: {
    async getAllGrade() {
      const { data } = await getAllGrade();

      this.grade_list = data;
    },
    async getSetting() {
      if (!this.$accessCheck(this.$Access.BaseDSetgetSetting)) {
        return;
      }
      const { data } = await getSetting();

      this.add_form = {
        ...data,
        info:
          data.info.length > 0
            ? data.info
            : [
                {
                  prop: "姓名",
                  value: "",
                },
                {
                  prop: "微信号",
                  value: "",
                },
                {
                  prop: "手机号",
                  value: "",
                },
              ],
        withdrawInfo: data.withdrawInfo.length > 0 ? data.withdrawInfo : [{ cardNum: "" }],
      };
    },
    addBankCard() {
      this.add_form.withdrawInfo.push({
        cardNum: "",
      });
    },
    delCard(index) {
      if (this.add_form.withdrawInfo.length === 1) {
        this.$message.warning("至少填写一张银行卡号");
        return;
      }
      this.add_form.withdrawInfo.splice(index, 1);
    },
    async submit() {
      const data = await saveSetting({
        ...this.add_form,
      });

      this.$message.success("提交成功");
      this.getSetting();
    },
  },
};
</script>
