<template>
  <Container>
    <el-form slot="left" :inline="true" size="small" style="margin-bottom: 0">
      <el-form-item>
        <el-input
          v-model="search_form.keyword"
          style="width: 194px"
          placeholder="请输入商品名称"
          class="input-with-select"
          @keyup.enter.native="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <GoodsCategory
          v-model="categoryPath"
          check-strictly
          clearable
          size="small"
          placeholder="商品分类"
          @change="categoryChange"
        />
      </el-form-item>
      <el-form-item>
        <el-select v-model="search_form.enableStatus" placeholder="商品状态" @change="pageChange(1)">
          <el-option
            v-for="(item, index) in enableStatus_options"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="search_form.isJoinCommission"
          placeholder="分销状态"
          style="width: 150px"
          @change="pageChange(1)"
        >
          <el-option
            v-for="(item, index) in join_options"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table
      ref="goodsTable"
      :data="goods_list"
      @selection-change="handleSelectionChange"
      @expand-change="goodsDetail"
    >
      <el-table-column align="center" type="selection" width="55"></el-table-column>
      <el-table-column prop="title" label="商品" min-width="280">
        <template slot-scope="scope">
          <div class="clearfix">
            <div class="float_left">
              <el-image :src="scope.row.images[0]" fit="cover"></el-image>
            </div>
            <div class="float_left goods-name-view" style="margin-left: 10px">
              {{ scope.row.title }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="specificationFlag" label="规格明细" width="100" type="expand">
        <template slot-scope="scope">
          <ul class="sku-ul">
            <li v-for="(item, index) in scope.row.specMultiple" :key="index" class="sku-li">
              <div class="clearfix">
                <div class="float_left">
                  <el-image class="sku-img" fit="cover" :src="item.specImage || scope.row.images[0]" />
                </div>
                <div class="sku-info float_left">
                  <p>
                    <span class="label">规格:</span>
                    {{ item.unitName }};
                    <span v-for="(items, indexs) in item.specData" :key="indexs"> {{ items.specValueName }}; </span>
                  </p>
                  <p>
                    <span class="label">销售价:</span>
                    {{ $_common.formattedNumber(item.price) }}
                  </p>
                </div>
              </div>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column v-if="commissionFlag" prop="commission" label="佣金" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.commission) }}
        </template>
      </el-table-column>
      <el-table-column v-if="inventoryFlag" prop="inventoryTotal" label="库存" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.inventoryTotal) }}
        </template>
      </el-table-column>
      <el-table-column v-if="salesVolumeFlag" prop="salesCount" label="销量" min-width="120"></el-table-column>
      <el-table-column v-if="commodityFlag" prop="enableStatus" label="商品状态" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.enableStatus === 5" class="success-status"> 上架 </span>
          <span v-else class="danger-status">下架</span>
        </template>
      </el-table-column>
      <el-table-column v-if="distributionFlag" prop="isJoinCommission" label="分销状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.isJoinCommission === 5" class="success-status"> 参与 </span>
          <span v-else class="danger-status">不参与</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$accessCheck($Access.DGoodsListsetCommission) || $accessCheck($Access.DGoodsListupdateIsJoin)"
        prop="operation"
        label="操作"
        min-width="160"
      >
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.DGoodsListsetCommission)"
            type="text"
            @click="$router.push(`/Distribution/SetCommission/${scope.row.id}`)"
          >
            设置佣金
          </el-button>
          <el-button
            v-if="$accessCheck($Access.DGoodsListupdateIsJoin)"
            type="text"
            @click="updateIsJoin(scope.row, scope.row.isJoinCommission === 5 ? 4 : 5)"
          >
            {{ scope.row.isJoinCommission === 5 ? "不参与" : "参与" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="$accessCheck($Access.DGoodsListupdateIsJoin)" slot="btn-div" class="foot-btn-div">
        <span class="batch-checkbox">
          <el-checkbox v-model="checkedAll" @change="checkAllChange"></el-checkbox>
        </span>
        <el-dropdown>
          <el-button size="mini">
            批量设置分销
            <i class="el-icon-caret-top"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <div class="dropdown-div" @click="updateIsJoin(choose_data, 5)">参与</div>
            </el-dropdown-item>
            <el-dropdown-item>
              <div class="dropdown-div" @click="updateIsJoin(choose_data, 4)">不参与</div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <!--      <el-button-->
        <!--        size="mini"-->
        <!--        plain-->
        <!--        type="primary"-->
        <!--        @click="show_set = true"-->
        <!--      >-->
        <!--        设置佣金-->
        <!--      </el-button>-->
      </div>
    </FooterPage>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="设置佣金"
      :visible.sync="show_set"
      width="30%"
    >
      <div>
        <span style="margin-right: 10px">批量设置佣金:</span>
        <el-input v-model="set_form.ratio" style="width: 150px" size="small">
          <span slot="append">%</span>
        </el-input>
        <el-input v-model="set_form.money" size="small" style="width: 150px">
          <span slot="append">元</span>
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="show_set = false">取 消</el-button>
        <el-button type="primary" @click="setCommission">确 定</el-button>
      </span>
    </el-dialog>
  </Container>
</template>

<script>
import GoodsCategory from "@/component/common/GoodsCategory.vue";
import { CommissionGoodsGetInfo, CommissionGoodsGetAll, updateIsJoin } from "@/api/Commission";
export default {
  name: "DGoodsList",
  components: {
    GoodsCategory,
  },
  data() {
    return {
      show_set: false,
      checkedAll: false,
      set_form: {
        ratio: "",
        money: "",
      },
      categoryPath: [],
      search_form: {
        keyword: "",
        categoryId: "",
        enableStatus: "", // 上下架状态
        isJoinCommission: "", // 是否参与分销 5是 4否
      },
      enableStatus_options: [
        {
          value: 5,
          label: "上架",
        },
        {
          value: 4,
          label: "下架",
        },
      ],
      join_options: [
        {
          value: 5,
          label: "参与",
        },
        {
          value: 4,
          label: "不参与",
        },
      ],
      total: 0,
      page: 1,
      pageSize: 10,
      goods_list: [],
      choose_data: [],
      specMultiple: [],
      specMultipleLoad: false,
      checkList: ["规格明细", "佣金", "库存", "销量", "商品状态", "分销状态"],
      columns: [
        {
          label: "规格明细",
        },
        {
          label: "佣金",
        },
        {
          label: "库存",
        },
        {
          label: "销量",
        },
        {
          label: "商品状态",
        },
        {
          label: "分销状态",
        },
      ],
      specificationFlag: true,
      commissionFlag: true,
      inventoryFlag: true,
      salesVolumeFlag: true,
      commodityFlag: true,
      distributionFlag: true,
    };
  },
  created() {
    this.CommissionGoodsGetAll();
  },
  activated() {
    if (this.$_isInit()) return;
    this.CommissionGoodsGetAll();
  },
  methods: {
    async goodsDetail(row) {
      if (!row.specMultiple.length) {
        const index = this.goods_list.findIndex((item) => item.id === row.id);
        const { data } = await CommissionGoodsGetInfo(row.id);
        this.goods_list[index].specMultiple = data.specMultiple;
      }
    },
    categoryChange(val) {
      if (val.length) {
        this.search_form.categoryId = val[val.length - 1];
      } else {
        this.search_form.categoryId = [];
      }
      this.pageChange(1);
    },
    async CommissionGoodsGetAll() {
      const { data, pageTotal } = await CommissionGoodsGetAll({
        page: this.page,
        pageSize: this.pageSize,
        ...this.search_form,
      });

      this.goods_list = data.map((item) => {
        return {
          ...item,
          specMultiple: [],
        };
      });
      this.total = pageTotal;
      for (let i = 0; i < this.goods_list.length; i++) {
        const isTrue = this.choose_data.find((itemF) => {
          return itemF.id === this.goods_list[i].id;
        });
        if (isTrue) {
          this.toggleRowSelection([this.goods_list[i]]);
        }
      }
    },
    // 批量选择
    handleSelectionChange(val) {
      if (val.length) {
        if (!this.choose_data.length) {
          this.choose_data = val;
        } else {
          this.choose_data = this.$_common.unique(this.choose_data.concat(val), ["id"]);
        }
      } else {
        for (let i = 0; i < this.goods_list.length; i++) {
          const index = this.choose_data.findIndex((itemF) => {
            return itemF.id === this.goods_list[i].id;
          });
          if (index > -1) {
            this.choose_data.splice(index, 1);
          }
        }
      }
      this.checkedAll = val.length >= this.goods_list.length;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.CommissionGoodsGetAll();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.CommissionGoodsGetAll();
    },
    updateIsJoin(rows, isJoinCommission) {
      let ids = [];
      if (Array.isArray(rows)) {
        ids = rows.map((item) => {
          return item.id;
        });
      } else {
        ids = [rows.id];
      }
      if (!ids.length) {
        this.$message.warning("至少要选择一个商品");
        return;
      }
      this.$confirm("是否设置商品参与分销?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateIsJoin({
          id: ids,
          isJoinCommission: isJoinCommission,
        });

        this.$message.success("操作成功");
        this.CommissionGoodsGetAll();
      });
    },
    async setCommission() {
      this.show_set = false;
    },
    toggleRowSelection(rows) {
      this.$nextTick(() => {
        rows.forEach((row) => {
          this.$refs.goodsTable.toggleRowSelection(row, true);
        });
      });
    },
    checkAllChange() {
      this.$refs.goodsTable.toggleAllSelection();
    },
    change() {
      this.specificationFlag = this.checkList.some((item) => item === "规格明细");
      this.commissionFlag = this.checkList.some((item) => item === "佣金");
      this.inventoryFlag = this.checkList.some((item) => item === "库存");
      this.salesVolumeFlag = this.checkList.some((item) => item === "销量");
      this.commodityFlag = this.checkList.some((item) => item === "商品状态");
      this.distributionFlag = this.checkList.some((item) => item === "分销状态");
    },
  },
};
</script>

<style scoped lang="scss">
.goods-name-view {
  width: calc(100% - 76px);
  margin-left: 10px;
}
.goods-title {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.all-handel-label {
  color: #666;
  font-size: 12px;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
.sku-ul {
  .sku-li {
    display: inline-block;
    margin-right: 10px;
    border: 1px solid #ebeef5;
    padding: 10px;
    width: 294px;
    vertical-align: middle;
    .sku-img {
      width: 50px;
      height: 50px;
      border-radius: 2px;
      margin-right: 8px;
    }
    .sku-info {
      line-height: 23px;
      color: #111111;
      .label {
        display: inline-block;
        width: 50px;
        color: #666666;
        text-align: right;
      }
    }
  }
}
</style>
