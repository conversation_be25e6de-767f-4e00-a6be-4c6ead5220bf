<template>
  <Container>
    <el-form slot="left" :inline="true" size="small">
      <el-form-item>
        <el-input
          v-model="keyword"
          style="width: 300px"
          :placeholder="input_select === 'no' ? '订单编号' : '商品名称'"
          @keyup.enter.native="pageChange(1)"
        >
          <el-select slot="prepend" v-model="input_select" placeholder="订单编号" style="width: 100px">
            <el-option label="订单编号" value="no"></el-option>
            <el-option label="商品名称" value="goodsName"></el-option>
          </el-select>
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <SelectCustomer v-model="customer_name" @clear="customerClear" @change="customerSel" />
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="orderTime"
          type="daterange"
          range-separator="-"
          start-placeholder="下单开始日期"
          end-placeholder="下单结束日期"
          value-format="timestamp"
          @change="orderTimeChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search_form.isAccount" style="width: 150px" placeholder="佣金状态" @change="pageChange(1)">
          <el-option :value="4" label="待入账"></el-option>
          <el-option :value="5" label="已入账"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-tabs v-model="search_form.state" type="card" @tab-click="pageChange(1)">
      <el-tab-pane v-for="item in order_status" :key="item.value" :label="item.label" :name="item.value"></el-tab-pane>
    </el-tabs>
    <el-table :data="goodsList">
      <el-table-column prop="shop" label="商品信息" min-width="140px">
        <template slot-scope="scope">
          <div class="clearfix">
            <div class="float_left">
              <el-image :src="scope.row.goodsImages" fit="cover"></el-image>
            </div>
            <div class="float_left" style="margin-left: 10px">
              {{ scope.row.goodsName }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="unitPriceFlag" prop="goodsPrice" label="单价" min-width="80px"></el-table-column>
      <el-table-column v-if="quantityFlag" prop="buyNum" label="数量" min-width="80px"></el-table-column>
      <el-table-column v-if="actualPaymentFlag" prop="goodsPrice" label="实付款" min-width="80px"></el-table-column>
      <el-table-column v-if="sourceFlag" prop="orderNo" label="订单来源" min-width="160px"></el-table-column>
      <el-table-column
        v-if="buyerFlag"
        prop="customerName"
        label="买家"
        show-overflow-tooltip
        min-width="120px"
      ></el-table-column>
      <el-table-column v-if="orderStatusFlag" prop="orderMsg" label="订单状态" min-width="120px">
        <template slot-scope="scope">
          <span
            :class="[
              scope.row.orderMsg === '待审核'
                ? 'warning-status'
                : scope.row.orderMsg === '已关闭'
                ? 'info-status'
                : scope.row.orderMsg === '已出库'
                ? 'primary-status'
                : scope.row.orderMsg === '已完成'
                ? 'success-status'
                : scope.row.orderMsg === '待出库'
                ? 'danger-status'
                : 'primary-status',
            ]"
          >
            {{ scope.row.orderMsg }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="timeFlag" prop="createTime" label="时间" min-width="140px">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column v-if="distributorFlag" prop="retCustomerName" label="分销商" min-width="120px"></el-table-column>
      <el-table-column v-if="commissionFlag" prop="retMoney" label="佣金" min-width="100px">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.retMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="retMsg" min-width="100px">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>佣金状态</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <span class="primary-status">{{ scope.row.retMsg }}</span>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import SelectCustomer from "@/component/common/SelectCustomer";
import { CommissionOrderGetAll } from "@/api/Commission";
export default {
  name: "Distributor",
  components: {
    SelectCustomer,
  },
  data() {
    return {
      goodsList: [],
      orderTime: [],
      total: 0,
      page: 1,
      pageSize: 10,
      customer_name: "",
      input_select: "no",
      keyword: "",
      search_form: {
        no: "",
        startTime: "",
        state: "all",
        endTime: "",
        isAccount: "", // 佣金状态 isAccount 3取消 4等待入账 5已入账
        customerId: "",
        goodsName: "",
      },
      order_status: [
        {
          label: "全部",
          value: "all",
        },
        {
          label: "待审核", // 新订单】
          value: "waitAudit",
        },
        {
          label: "待发货", // 等待出库
          value: "waitOutStock",
        },
        {
          label: "待收货", // 已出库
          value: "hasOutStock",
        },
        {
          label: "已完成", // 已收货
          value: "finish",
        },
        {
          label: "已关闭", // 已取消
          value: "close",
        },
      ],
      checkList: ["单价", "数量", "实付款", "订单来源", "买家", "订单状态", "时间", "分销商", "佣金"],
      columns: [
        {
          label: "单价",
        },
        {
          label: "数量",
        },
        {
          label: "实付款",
        },
        {
          label: "订单来源",
        },
        {
          label: "买家",
        },
        {
          label: "订单状态",
        },
        {
          label: "时间",
        },
        {
          label: "分销商",
        },
        {
          label: "佣金",
        },
      ],
      unitPriceFlag: true,
      quantityFlag: true,
      actualPaymentFlag: true,
      sourceFlag: true,
      buyerFlag: true,
      orderStatusFlag: true,
      timeFlag: true,
      distributorFlag: true,
      commissionFlag: true,
    };
  },
  created() {
    this.CommissionOrderGetAll();
  },
  activated() {
    if (this.$_isInit()) return;
    this.CommissionOrderGetAll();
  },
  methods: {
    async CommissionOrderGetAll() {
      if (this.input_select === "no") {
        this.search_form.no = this.keyword;
        this.search_form.goodsName = "";
      } else {
        this.search_form.goodsName = this.keyword;
        this.search_form.no = "";
      }
      const { data, pageTotal } = await CommissionOrderGetAll({
        page: this.page,
        pageSize: this.pageSize,
        ...this.search_form,
      });

      this.goodsList = data;
      this.total = pageTotal;
    },
    //  订单时间
    orderTimeChange(val) {
      if (val && val.length) {
        this.search_form.startTime = val[0] / 1000;
        this.search_form.endTime = val[1] / 1000 + 86399;
      } else {
        this.search_form.startTime = "";
        this.search_form.endTime = "";
      }
      this.pageChange(1);
    },
    // 选择客户
    customerSel(val, list) {
      this.search_form.customerId = list[0].id;
      this.pageChange(1);
    },
    customerClear() {
      this.search_form.customerId = "";
      this.customer_name = "";
      this.pageChange(1);
    },
    pageChange(val) {
      this.page = val;
      this.CommissionOrderGetAll();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.CommissionOrderGetAll();
    },
    change() {
      this.unitPriceFlag = this.checkList.some((item) => item === "单价");
      this.quantityFlag = this.checkList.some((item) => item === "数量");
      this.actualPaymentFlag = this.checkList.some((item) => item === "实付款");
      this.sourceFlag = this.checkList.some((item) => item === "订单来源");
      this.buyerFlag = this.checkList.some((item) => item === "买家");
      this.orderStatusFlag = this.checkList.some((item) => item === "订单状态");
      this.timeFlag = this.checkList.some((item) => item === "时间");
      this.distributorFlag = this.checkList.some((item) => item === "分销商");
      this.commissionFlag = this.checkList.some((item) => item === "佣金");
    },
  },
};
</script>

<style scoped lang="scss">
.clear span {
  color: red;
  cursor: pointer;
  margin-left: 20px;
  font-size: 12px;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
