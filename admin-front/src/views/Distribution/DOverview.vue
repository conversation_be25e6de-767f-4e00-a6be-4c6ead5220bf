<template>
  <div class="Overview">
    <div v-if="$accessCheck($Access.DOverviewstatisticsCommissionOne)" class="module-li" style="margin: 0 0 20px 0">
      <div class="clearfix tit-view">
        <div class="float_left module-name">佣金概览</div>
      </div>
      <ul class="nav-btn-ul">
        <li class="nav-btn-li clearfix">
          <div class="float_left">
            <span
              style="background: linear-gradient(315deg, rgb(18, 186, 123) 0%, #1aecae 100%)"
              class="iconfont icon-dingjiatiaojia num-item-icon"
            ></span>
          </div>
          <div class="float_left">
            <p class="price">提现待审核(元)</p>
            <p class="num">
              {{ topTotal.withdrawNoAudit || 0 }}
            </p>
          </div>
        </li>
        <li class="nav-btn-li clearfix">
          <div class="float_left">
            <span
              style="background: linear-gradient(315deg, #3d9dfe 0%, #77cdff 100%)"
              class="iconfont icon-yingshou num-item-icon"
            ></span>
          </div>
          <div class="float_left">
            <p class="price">提现待打款(元)</p>
            <p class="num">
              {{ topTotal.withdrawNoPay || 0 }}
            </p>
          </div>
        </li>
        <li class="nav-btn-li clearfix">
          <div class="float_left">
            <span
              style="background: linear-gradient(315deg, #fe6b58 0%, #fbb162 100%)"
              class="iconfont icon-jine num-item-icon"
            ></span>
          </div>
          <div class="float_left">
            <p class="price">累计佣金</p>
            <p class="num">
              {{ topTotal.totalMoney || 0 }}
            </p>
          </div>
        </li>
        <li class="nav-btn-li clearfix">
          <div class="float_left">
            <span
              style="background: linear-gradient(315deg, #fb2c95 0%, #ff7d7d 100%)"
              class="iconfont icon-shoukuan num-item-icon"
            ></span>
          </div>
          <div class="float_left">
            <p class="price">提现佣金</p>
            <p class="num">
              {{ topTotal.withdraw || 0 }}
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div v-if="$accessCheck($Access.DOverviewstatisticsCommissionTwo)" class="module-li" style="margin-top: 0">
      <div class="clearfix tit-view">
        <div class="float_left module-name">分销商概览</div>
      </div>
      <el-row style="padding: 20px">
        <el-col :span="6">
          <div
            class="grid-content bg-purple"
            style="background: linear-gradient(-125deg, #ff7d7d, #fb2c95); color: #fff"
          >
            <div class="float_left" style="padding-left: 40px">
              <p class="price-label">待审核(人)</p>
              <p class="num">
                {{ topTotal.auditBusinessman || 0 }}
              </p>
            </div>
            <div class="card-num float_right" style="padding-right: 20px">
              <span class="iconfont icon-kehu card-icon"></span>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div
            class="grid-content bg-purple"
            style="background: linear-gradient(-113deg, #c543d8, #925cc3); color: #fff"
          >
            <div class="float_left" style="padding-left: 40px">
              <p class="price-label">分销商(人)</p>
              <p class="num">
                {{ topTotal.businessman || 0 }}
              </p>
            </div>
            <div class="card-num float_right" style="padding-right: 20px">
              <span class="iconfont icon-kehu1 card-icon"></span>
            </div>
          </div>
        </el-col>
        <el-col :span="6" style="height: 180px; overflow: hidden">
          <div style="margin-right: 20px">
            <div
              class="clearfix grid-content bg-purple"
              style="
                background: linear-gradient(-125deg, rgb(169 231 249), rgb(31 132 230));
                color: #ffffff;
                position: relative;
                width: 100%;
              "
            >
              <div class="float_left" style="padding-left: 40px">
                <span class="price-label">会员数</span>
                <br />
                <span class="num">
                  {{ topTotal.customer || 0 }}
                </span>
              </div>
              <div style="width: 300px; height: 200px; transform: translateY(-50px)" class="float_right">
                <vab-chart
                  style="width: 100%; height: 100%"
                  :autoresize="true"
                  :options="num_option"
                  theme="vab-echarts-theme"
                />
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6" style="height: 180px; overflow: hidden">
          <div style="margin-right: 20px">
            <div
              class="clearfix grid-content bg-purple"
              style="
                background: linear-gradient(-125deg, rgb(169 231 249), rgb(31 132 230));
                color: #ffffff;
                position: relative;
                width: 100%;
              "
            >
              <div class="float_left" style="padding-left: 40px">
                <span class="price-label">分销等级</span>
              </div>
              <div style="width: 300px; height: 200px; transform: translateY(-50px)" class="float_right">
                <vab-chart
                  style="width: 100%; height: 100%"
                  :autoresize="true"
                  :options="grade_option"
                  theme="vab-echarts-theme"
                />
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-row v-if="$accessCheck($Access.DOverviewstatisticsCommissionThree)" :gutter="24" style="margin: 20px 0 0 0">
      <el-col :span="12" style="padding-left: 0">
        <div class="col_one">
          <div class="tit-view">新增分销商数</div>
          <div style="padding: 20px">
            <vab-chart style="width: 100%" :autoresize="true" :options="chartData" theme="vab-echarts-theme" />
          </div>
        </div>
      </el-col>
      <el-col :span="12" style="padding: 0">
        <div class="col_one">
          <div class="tit-view">分销商排行top10</div>
          <div class="tab-moni">
            <div class="tab-btn">
              <div
                class="tab-btn-li"
                :class="[ranking_on === 'moneyTop' ? 'tab-btn-on' : '']"
                @click="handleClick('moneyTop')"
              >
                累计佣金
              </div>
              <div
                :class="[ranking_on === 'subTop' ? 'tab-btn-on' : '']"
                class="tab-btn-li"
                @click="handleClick('subTop')"
              >
                下级会员
              </div>
            </div>
            <div class="ranking-li">
              <el-row class="ranking-th">
                <el-col :span="4">排名</el-col>
                <el-col :span="10">姓名</el-col>
                <el-col :span="10">
                  {{ ranking_on === "moneyTop" ? "佣金(元)" : "数量(人)" }}
                </el-col>
              </el-row>
              <div v-if="ranking_on === 'moneyTop'">
                <el-row v-for="(item, index) in top_list.moneyTop" :key="index" class="ranking-tr">
                  <el-col :span="4">
                    <span v-if="index === 0" class="ranking-sort ranking-sort-o">
                      {{ index + 1 }}
                    </span>
                    <span v-else-if="index === 1" class="ranking-sort ranking-sort-t">
                      {{ index + 1 }}
                    </span>
                    <span v-else-if="index === 2" class="ranking-sort ranking-sort-three">
                      {{ index + 1 }}
                    </span>
                    <span v-else class="ranking-sort">{{ index + 1 }}</span>
                  </el-col>
                  <el-col :span="10">
                    {{ item.name }}
                  </el-col>
                  <el-col :span="10">
                    {{ item.money }}
                  </el-col>
                </el-row>
                <div v-if="!top_list.moneyTop.length" class="empty-view">暂无数据</div>
              </div>
              <div v-else>
                <el-row v-for="(item, index) in top_list.subTop" :key="index" class="ranking-tr">
                  <el-col :span="4">
                    <span v-if="index === 0" class="ranking-sort ranking-sort-o">
                      {{ index + 1 }}
                    </span>
                    <span v-else-if="index === 1" class="ranking-sort ranking-sort-t">
                      {{ index + 1 }}
                    </span>
                    <span v-else-if="index === 2" class="ranking-sort ranking-sort-three">
                      {{ index + 1 }}
                    </span>
                    <span v-else class="ranking-sort">{{ index + 1 }}</span>
                  </el-col>
                  <el-col :span="10">
                    {{ item.name }}
                  </el-col>
                  <el-col :span="10">
                    {{ item.sub }}
                  </el-col>
                </el-row>
                <div v-if="!top_list.subTop.length" class="empty-view">暂无数据</div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { statisticsCommissionThree, statisticsCommissionOne, statisticsCommissionTwo } from "@/api/Commission";
import VabChart from "@/extra/vabCharts";
export default {
  name: "Overview",
  components: {
    VabChart,
  },
  data() {
    return {
      ranking_on: "moneyTop",
      topTotal: [],
      chartData: {
        tooltip: {
          trigger: "axis",
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          data: [],
        },
        yAxis: {
          type: "value",
        },
        legend: {
          data: ["新增分销商数"],
        },
        series: [
          {
            data: [],
            name: "新增分销商数",
            type: "line",
            smooth: true,
            areaStyle: {},
          },
        ],
      },
      num_option: {},
      grade_option: {},
      top_list: [],
    };
  },
  mounted() {
    // this.drawLine_one()
    this.statisticsCommissionThree();
    this.statisticsCommissionOne();
    this.statisticsCommissionTwo();
  },
  activated() {
    if (this.$_isInit()) return;
    this.statisticsCommissionThree();
    this.statisticsCommissionOne();
    this.statisticsCommissionTwo();
  },
  methods: {
    handleClick(tab) {
      this.ranking_on = tab;
    },
    async statisticsCommissionThree() {
      if (!this.$accessCheck(this.$Access.DOverviewstatisticsCommissionThree)) {
        return;
      }
      const data = await statisticsCommissionThree();

      this.top_list = data.data;
    },
    async statisticsCommissionOne() {
      if (!this.$accessCheck(this.$Access.DOverviewstatisticsCommissionOne)) {
        return;
      }
      const data = await statisticsCommissionOne();

      this.topTotal = data.data;
      this.loadingChart(this.topTotal.businessman);
      this.loadingChart_one(this.topTotal.gradeBusinessman);
    },
    async statisticsCommissionTwo() {
      if (!this.$accessCheck(this.$Access.DOverviewstatisticsCommissionTwo)) {
        return;
      }
      const { data } = await statisticsCommissionTwo();

      data.forEach((item) => {
        this.chartData.xAxis.data.push(item.createData);
        this.chartData.series[0].data.push(item.count);
      });
    },
    loadingChart(val) {
      this.num_option = {
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
        },
        legend: {
          orient: "vertical",
          left: 10,
          data: [],
          textStyle: {
            color: "#fff",
          },
        },
        series: [
          {
            name: "分销商人数",
            type: "pie",
            radius: ["52%", "70%"],
            center: [180, 90],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "12",
              },
            },
            labelLine: {
              show: false,
            },
            color: ["#12ba7b", "#ffffff"],
            data: [
              { value: val, name: "分销商人数" },
              { value: val, name: "分销商占比" },
            ],
          },
        ],
      };
    },
    loadingChart_one(val) {
      let arr = [];
      for (let item in val) {
        arr.push(val[item]);
      }
      this.grade_option = {
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
        },
        legend: {
          orient: "vertical",
          left: 10,
          data: ["默认等级", "1级", "2级", "3级", "4级", "5级", "6级"],
          textStyle: {
            color: "#fff",
          },
        },
        series: [
          {
            name: "等级",
            type: "pie",
            radius: ["60%", "82%"],
            center: [180, 84],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "12",
              },
            },
            labelLine: {
              show: false,
            },
            color: ["#B8B8B8", "#3399FF", "#FF9900", "#009966", "#666699", "#99CCFF", "#66CC99"],
            data: [
              { value: arr[0].businessmanNum, name: arr[0].gradeName },
              { value: arr[1].businessmanNum, name: arr[1].gradeName },
              { value: arr[2].businessmanNum, name: arr[2].gradeName },
              { value: arr[3].businessmanNum, name: arr[3].gradeName },
              { value: arr[4].businessmanNum, name: arr[4].gradeName },
              { value: arr[5].businessmanNum, name: arr[5].gradeName },
              { value: arr[6].businessmanNum, name: arr[6].gradeName },
            ],
          },
        ],
      };
    },
  },
};
</script>

<style scoped>
.Overview {
  background: #f5f8fa;
}
.header span {
  color: white;
  font-weight: bold;
  margin: 20px 0 20px 20px;
  display: inline-block;
}
.module-li {
  background: #fff;
  margin-top: 20px;
}
.card-num {
  font-size: 42px;
  line-height: 46px;
  margin: 8px 0;
}
.card-num .card-icon {
  font-size: 70px;
  opacity: 0.2;
}
.module-name {
  font-size: 15px;
  color: #333333;
  transform: translateY(-2px);
}
.tit-view {
  padding: 0 20px;
  line-height: 50px;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #f2f2f2;
}
.price {
  color: #999999;
  font-size: 14px;
}
.nav-btn-ul {
  padding: 20px;
  display: flex;
}
.nav-btn-li {
  flex: 4;
  font-size: 14px;
}
.num-item-icon {
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 10px;
  color: #fff;
  text-align: center;
  margin-right: 20px;
  display: inline-block;
  font-size: 30px;
  background: linear-gradient(315deg, rgba(86, 171, 47, 1) 0%, rgba(168, 224, 99, 1) 100%);
}
.bg-purple {
  background: #3e4651;
  text-align: center;
}
.num {
  font-size: 34px;
}
.grid-content {
  border-radius: 4px;
  height: 180px;
  padding-top: 50px;
  margin-right: 20px;
}
.col_one {
  background-color: white;
  border-radius: 4px;
  height: 450px;
  min-width: 500px;
}
.price-label {
  font-size: 14px;
  opacity: 0.8;
  padding-bottom: 10px;
}
.tab-moni {
  padding: 20px;
}
.tab-btn {
  padding-bottom: 20px;
}
.tab-btn-li {
  line-height: 28px;
  padding: 0 12px;
  color: #666666;
  border: 1px solid #ddd;
  font-size: 12px;
  display: inline-block;
  margin-right: 20px;
  border-radius: 30px;
  cursor: pointer;
}
.tab-btn-on {
  background: linear-gradient(to right, #5f74fb, #5384f5);
  color: #ffffff;
  border-color: #4a7af7;
}
.ranking-li {
  font-size: 13px;
  color: #333333;
  height: 280px;
  overflow: auto;
}
.ranking-th {
  font-weight: 600;
  padding-bottom: 10px;
}
.ranking-tr {
  line-height: 44px;
}
.ranking-sort {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 100%;
  text-align: center;
}
.ranking-sort-o {
  color: #f788a3;
  font-size: 12px;
  background: #faeceb;
}
.ranking-sort-t {
  color: #fabb79;
  font-size: 12px;
  background: #fff6e3;
}
.ranking-sort-three {
  color: #4ec1cd;
  font-size: 12px;
  background: #e0f9f1;
}
.empty-view {
  line-height: 100px;
  text-align: center;
}
</style>
