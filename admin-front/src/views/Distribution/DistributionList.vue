<template>
  <Container>
    <div slot="tip" class="page-tip-div" style="margin-top: 0">
      <i class="el-icon-info"></i>
      温馨提示：没有设置等级的经销商将按默认等级计算佣金,商品指定了佣金金额就按商品的佣金金额来算，不受等级影响
    </div>
    <div v-if="$accessCheck($Access.DistributionListupdateGrade)" slot="left">
      <el-button type="primary" size="small" @click="go()">添加等级</el-button>
    </div>
    <div>
      <el-table ref="singleTable" :data="grade_list">
        <el-table-column property="grade" label="等级" width="120"></el-table-column>
        <el-table-column property="name" label="等级名称" width="120"></el-table-column>
        <el-table-column property="oneRate" label="一级佣金比例">
          <template slot-scope="scope">{{ scope.row.oneRate }}%</template>
        </el-table-column>
        <el-table-column property="twoRate" label="二级佣金比例">
          <template slot-scope="scope">{{ scope.row.twoRate }}%</template>
        </el-table-column>
        <el-table-column property="threeRate" label="三级佣金比例">
          <template slot-scope="scope">{{ scope.row.threeRate }}%</template>
        </el-table-column>
        <el-table-column property="upgrade" label="升级条件" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="scope.row.upgradeConditionInfo && scope.row.upgradeConditionInfo.length > 0">
              <span v-for="(item, index) in scope.row.upgradeConditionInfo" :key="index">
                {{ item.name }}达到{{ item.value || "--" }}
                <span v-if="item.name.indexOf('额') > -1">元</span>
                <span v-else-if="item.name.indexOf('人') > -1">人</span>
                <span v-else>个</span>
                ;
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              v-if="$accessCheck($Access.DistributionListupdateGrade)"
              type="text"
              @click="$router.push('/Distribution/EditDistributionList/' + scope.row.id)"
            >
              编辑
            </el-button>
            <!--<el-button
                type="text"
                v-if="scope.row.enableStatus===5"
              >
                禁用
              </el-button>-->
            <el-button
              v-if="scope.row.grade > 0 && $accessCheck($Access.DistributionListdelGrade)"
              type="text"
              @click="delData(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </Container>
</template>

<script>
import { getAllGrade, delGrade } from "@/api/Commission";
export default {
  name: "DistributionList",
  data() {
    return {
      grade_list: [],
      currentRow: null,
    };
  },
  created() {
    this.getAllGrade();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllGrade();
  },
  methods: {
    go() {
      this.$router.push("/Distribution/AddDistributionList");
    },
    async getAllGrade() {
      const { data } = await getAllGrade();

      this.grade_list = data;
    },
    delData(id) {
      this.$confirm("确定要删除该分销商等级吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delGrade(id);

        this.$message.success("删除成功");
        this.getAllGrade();
      });
    },
  },
};
</script>

<style scoped></style>
