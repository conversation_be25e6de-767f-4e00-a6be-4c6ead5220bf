<template>
  <!--  设置佣金-->
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" @click="submit">保存</el-button>
    </div>

    <el-card class="box-card" shadow="never">
      <div slot="header">
        <span>商品信息</span>
      </div>
      <el-form ref="add_form" :model="add_form" label-width="120px">
        <el-form-item label="商品图片:" prop="activityType">
          <el-image style="width: 50px; height: 50px" :src="goods_detail.images[0]" fit="contain"></el-image>
        </el-form-item>
        <el-form-item label="商品名称:">
          <span>{{ goods_detail.title }}</span>
        </el-form-item>
        <el-form-item label="商品价格/销量:" class="table_title">
          <table>
            <tr v-for="(item, index) in goods_detail.specMultiple" :key="index">
              <td>
                <span>{{ item.unitName }}</span>
                <span v-for="(items, indexs) in item.specData" :key="indexs"> _{{ items.specValueName }} </span>
              </td>
              <td class="table_one">￥{{ item.price }}</td>
              <td>销量: {{ item.saleNum }}</td>
            </tr>
          </table>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card" shadow="never">
      <div slot="header">
        <span>佣金设置</span>
      </div>
      <el-form ref="add_form" :model="add_form" label-width="100px">
        <el-form-item label="参与分销:">
          <el-radio-group v-model="add_form.isJoinCommission">
            <el-radio :label="5">参与</el-radio>
            <el-radio :label="4">不参与</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="add_form.isJoinCommission === 5" label="佣金规则:">
          <el-radio-group v-model="add_form.isDefine">
            <el-radio :label="4">默认规则</el-radio>
            <el-radio :label="5">单独设置</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="add_form.isDefine === 5" label="返佣方式:">
          <el-switch
            v-model="add_form.retType"
            :active-value="5"
            :inactive-value="4"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="比例返佣"
            inactive-text="金额返佣"
          ></el-switch>
          <el-tooltip placement="right">
            <div slot="content">
              填写佣金规则,如果是数字(只能是纯数字).则是以固定金额给佣金
              <br />
              例如1就是按照卖—件,给分销商1元
              <br />
              如果上百分号
              <br />
              例如1%则是以支付商品金额的百分比给佣金
              <br />
              如果比例为空，则使用固定规则，如果都为空则无分销佣金
            </div>
            <i
              style="position: absolute; top: 30%; left: 190px; color: rgb(192, 197, 207)"
              class="el-icon-question"
            ></i>
          </el-tooltip>
        </el-form-item>
        <div v-if="add_form.isJoinCommission === 5" style="padding-left: 104px">
          <!--          默认规则设置-->
          <el-table v-show="add_form.isDefine === 4" size="mini" border :data="default_rule">
            <el-table-column prop="name" label="分销等级名称" min-width="240">
              <template slot-scope="scope">
                {{ scope.row.name }}
                <el-tag v-if="scope.row.tag === 1" type="danger">
                  {{ scope.row.name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column v-if="level >= 1" prop="oneRate" label="一级佣金比例" min-width="240">
              <template slot-scope="scope">
                {{ scope.row.rule.oneRate }}
              </template>
            </el-table-column>
            <el-table-column v-if="level >= 2" prop="twoRate" label="二级佣金比例" min-width="240">
              <template slot-scope="scope">
                {{ scope.row.rule.twoRate }}
              </template>
            </el-table-column>
            <el-table-column v-if="level >= 3" prop="threeRate" label="三级佣金比例" min-width="240">
              <template slot-scope="scope">
                {{ scope.row.rule.threeRate }}
              </template>
            </el-table-column>
          </el-table>
          <el-table v-show="add_form.isDefine === 5" :data="commission_rule" style="width: 100%" size="mini" border>
            <el-table-column label="商品规格" min-width="140">
              <template slot-scope="scope">
                <p v-for="(item, index) in scope.row.rule" :key="index">
                  <span>{{ item.unitName }}</span>
                  <span v-for="(items, indexs) in item.specData" :key="indexs"> _{{ items.specValueName }} </span>
                </p>
              </template>
            </el-table-column>
            <el-table-column label="价格" min-width="100">
              <template slot-scope="scope">
                <p v-for="(item, index) in scope.row.rule" :key="index">
                  <span>{{ $_common.formattedNumber(item.price) }}</span>
                </p>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="分销商等级名称" min-width="160">
              <template slot-scope="scope">
                {{ scope.row.name }}
                <el-tag v-if="scope.row.tag === 1" type="danger">
                  {{ scope.row.name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column v-if="level >= 1" label="一级佣金" min-width="280">
              <template slot-scope="scope">
                <div v-for="(item, index) in scope.row.rule" :key="index">
                  <div class="QN-input-view clearfix">
                    <input v-model="item.oneRate" class="QN-input" type="number" :min="0" />
                    <span v-if="add_form.retType === 5" class="qn-append"> % </span>
                    <span v-else class="qn-append">元</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="level >= 2" label="二级佣金" min-width="280">
              <template slot-scope="scope">
                <div v-for="(item, index) in scope.row.rule" :key="index">
                  <div class="QN-input-view clearfix">
                    <input v-model="item.twoRate" class="QN-input" type="number" :min="0" />
                    <span v-if="add_form.retType === 5" class="qn-append"> % </span>
                    <span v-else class="qn-append">元</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="level >= 3" label="三级佣金" min-width="280">
              <template slot-scope="scope">
                <div v-for="(item, index) in scope.row.rule" :key="index">
                  <div class="QN-input-view clearfix">
                    <input v-model="item.threeRate" class="QN-input" type="number" :min="0" />
                    <span v-if="add_form.retType === 5" class="qn-append"> % </span>
                    <span v-else class="qn-append">元</span>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </el-card>
  </ContainerTit>
</template>

<script>
import { setCommission, CommissionGoodsGetInfo, getSetting } from "@/api/Commission";
export default {
  name: "Commission",
  data() {
    return {
      add_form: {
        id: 0,
        isDefine: 5,
        isJoinCommission: 5,
        commission_rule: [],
        retType: 4,
      },
      type: 5,
      commission_rule: [],
      default_rule: [],
      goods_id: 0,
      goods_detail: {
        images: [],
      },
      specMultiple: [],
      level: 0,
    };
  },
  created() {
    this.goods_id = this.$route.params.id;
    this.getInfo();
    this.getSetting();
  },
  methods: {
    async getSetting() {
      const { data } = await getSetting();
      this.level = data.level || 0;
      console.log(this.level);
    },
    async submit() {
      let isReturn = false;
      let commissionRule = [];
      if (this.add_form.isDefine === 4) {
        // commissionRule = this.default_rule
      } else {
        let rule = this.specMultiple.map((itemC) => {
          return {
            skuId: itemC.skuId,
            rule: {},
          };
        });
        for (let i in this.commission_rule) {
          let item = this.commission_rule[i];
          for (let r in item.rule) {
            let itemR = item.rule[r];
            if (itemR.oneRate > 0 && itemR.twoRate > 0 && itemR.threeRate > 0) {
              const index = rule.findIndex((itemU) => itemU.skuId === itemR.skuId);
              if (index > -1) {
                rule[index].rule[item.grade] = {
                  oneRate: itemR.oneRate,
                  twoRate: itemR.twoRate,
                  threeRate: itemR.threeRate,
                };
              }
            } else {
              this.$message.warning(item.name + "佣金比例不能为0");
              isReturn = true;
              break;
            }
          }
        }
        commissionRule = rule;
      }
      if (isReturn) {
        return;
      }

      // return

      const { data } = await setCommission({
        ...this.add_form,
        commission_rule: commissionRule,
      });

      this.$message.success("操作成功");
      this.$closeCurrentGoEdit("/Distribution/DGoodsList");
    },
    async getInfo() {
      const { data } = await CommissionGoodsGetInfo(this.goods_id);

      this.goods_detail = data;
      this.add_form = {
        id: data.id,
        isDefine: data.isDefine,
        isJoinCommission: data.isJoinCommission,
        commission_rule: data.commission_rule,
        retType: data.retType,
      };
      this.commission_rule = data.commission_rule;
      this.default_rule = data.default_rule;
      this.specMultiple = data.specMultiple;
    },
  },
};
</script>

<style scoped>
.table_one {
  color: red;
  padding: 0 50px 0 100px;
}
.tableOne th {
  width: 200px;
  background-color: #ebeef5;
}
.tableTwo {
  width: 200px;
  height: 20px;
  margin-right: 10px;
}
.QN-input-view {
  display: inline-block;
  border: 1px solid #eeeeee;
}
.QN-input {
  width: 200px;
  height: 28px;
  line-height: 28px;
  font-size: 12px;
  float: left;
  padding: 0 15px;
}
.qn-append {
  float: left;
  background-color: #f5f7fa;
  line-height: 28px;
  padding: 0 10px;
  color: #909399;
  border-left: 1px solid #eee;
}
</style>
