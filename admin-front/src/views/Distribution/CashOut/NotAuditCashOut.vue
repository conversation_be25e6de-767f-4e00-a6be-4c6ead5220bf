<template>
  <Container>
    <el-form slot="left" style="margin-bottom: 0" :inline="true" size="small">
      <el-form-item>
        <el-input
          v-model="search_form.keywords"
          placeholder="手机号/微信昵称/姓名"
          class="input-with-select"
          style="width: 200px"
          @keyup.enter.native="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @change="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search_form.grade" style="width: 150px" placeholder="分销商等级" @change="pageChange(1)">
          <el-option
            v-for="(item, index) in grade_list"
            :key="index"
            :label="item.name"
            :value="item.grade"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="search_form.type_options"
          style="width: 150px"
          placeholder="提现方式"
          clearable
          @change="pageChange(1)"
        >
          <el-option
            v-for="(item, index) in type_options"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="search_form.time"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="timestamp"
          @change="timeChange"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <el-table :data="withdrawals_data">
      <el-table-column prop="customer" label="分销商名称" show-overflow-tooltip min-width="100"></el-table-column>
      <el-table-column v-if="gradeFlag" prop="gradeName" label="分销商等级" min-width="100"></el-table-column>
      <el-table-column v-if="withdrawalWayFlag" prop="type" label="提现方式" min-width="100">
        <template slot-scope="scope">
          {{
            scope.row.type === 1 ? "微信钱包" : scope.row.type === 2 ? "支付宝" : scope.row.type === 3 ? "银行卡" : ""
          }}
        </template>
      </el-table-column>
      <el-table-column v-if="commissionFlag" prop="money" min-width="120" label="申请佣金">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.money) }}
        </template>
      </el-table-column>
      <el-table-column v-if="practicalFlag" prop="nowMoney" label="实际佣金" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.nowMoney) }}
        </template>
      </el-table-column>
      <el-table-column v-if="serviceChargeFlag" prop="fee" label="提现手续费" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.fee) }}
        </template>
      </el-table-column>
      <el-table-column v-if="timeFlag" prop="createTime" label="申请时间" min-width="140">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="['NotAuditCashOut', 'NotCashOut'].includes($route.name) && $accessCheck($Access.CashOutupdateAuditStatus)"
        align="center"
        min-width="140"
      >
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span>操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$route.name === 'NotAuditCashOut'"
            type="text"
            size="mini"
            @click="auditChange(scope.row.id, 4)"
          >
            审核
          </el-button>
          <el-button v-if="$route.name === 'NotCashOut'" type="text" size="mini" @click="auditChange(scope.row.id, 2)">
            打款
          </el-button>
          <el-button
            v-if="$route.name === 'NotAuditCashOut'"
            type="text"
            size="mini"
            @click="auditChange(scope.row.id, 3)"
          >
            拒绝
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { CommissionWithdrawalsGetAll, getAllGrade, WithdrawalsUpdateAuditStatus } from "@/api/Commission";
export default {
  name: "NotDistributor",
  data() {
    return {
      type_options: [
        {
          value: 1,
          label: "微信",
        },
        {
          value: 2,
          label: "支付宝",
        },
        {
          value: 3,
          label: "银行卡",
        },
      ],
      search_form: {
        keywords: "",
        grade: 0,
        auditStatus: "",
        startTime: 0,
        endTime: 0,
        time: [],
        type_options: "",
      },
      withdrawals_data: [],
      value: "",
      value1: "",
      input3: "",
      total: 0,
      page: 1,
      pageSize: 10,
      grade_list: [],
      checkList: ["分销商等级", "提现方式", "申请佣金", "实际佣金", "提现手续费", "申请时间"],
      columns: [
        {
          label: "分销商等级",
        },
        {
          label: "提现方式",
        },
        {
          label: "申请佣金",
        },
        {
          label: "实际佣金",
        },
        {
          label: "提现手续费",
        },
        {
          label: "申请时间",
        },
      ],
      gradeFlag: true,
      withdrawalWayFlag: true,
      commissionFlag: true,
      practicalFlag: true,
      serviceChargeFlag: true,
      timeFlag: true,
    };
  },
  created() {
    // '审核状态',  1待审核 4待打款  2 打款成功  3失效
    switch (this.$route.name) {
      case "NotAuditCashOut":
        // 1待审核
        this.search_form.auditStatus = 1;
        break;
      case "CashOut":
        // 2打款成功
        this.search_form.auditStatus = 2;
        break;
      case "Invalid":
        // 3失效
        this.search_form.auditStatus = 3;
        break;

      case "NotCashOut":
        // 4待打款
        this.search_form.auditStatus = 4;
        break;
    }
    this.CommissionWithdrawalsGetAll();
    this.getAllGrade();
  },
  activated() {
    if (this.$_isInit()) return;
    this.CommissionWithdrawalsGetAll();
  },
  methods: {
    async CommissionWithdrawalsGetAll() {
      const data = await CommissionWithdrawalsGetAll({
        page: this.page,
        pageSize: this.pageSize,
        auditStatus: this.search_form.auditStatus,
        keyword: this.search_form.keyword,
        grade: this.search_form.grade,
        startTime: this.search_form.startTime,
        endTime: this.search_form.endTime,
        type: this.search_form.type_options,
      });

      this.withdrawals_data = data.data;
      this.total = data.pageTotal;
    },
    async getAllGrade() {
      const { data } = await getAllGrade();
      this.grade_list = data;
    },
    pageChange(val) {
      this.page = val;
      this.CommissionWithdrawalsGetAll();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.CommissionWithdrawalsGetAll();
    },
    timeChange(val) {
      if (val && val.length) {
        this.search_form.startTime = val[0] / 1000;
        this.search_form.endTime = val[1] / 1000 + 86399;
      } else {
        this.search_form.startTime = 0;
        this.search_form.endTime = 0;
      }
      this.pageChange(1);
    },
    async auditChange(id, status) {
      this.$confirm("确定要审核通过该提现申请吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await WithdrawalsUpdateAuditStatus(id, {
          status: status,
        });

        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.CommissionWithdrawalsGetAll();
      });
    },
    change() {
      this.gradeFlag = this.checkList.some((item) => item === "分销商等级");
      this.withdrawalWayFlag = this.checkList.some((item) => item === "提现方式");
      this.commissionFlag = this.checkList.some((item) => item === "申请佣金");
      this.practicalFlag = this.checkList.some((item) => item === "实际佣金");
      this.serviceChargeFlag = this.checkList.some((item) => item === "提现手续费");
      this.timeFlag = this.checkList.some((item) => item === "申请时间");
    },
  },
};
</script>

<style scoped lang="scss">
.commodity {
  width: 30%;
}
.clear span {
  color: red;
  cursor: pointer;
  margin-left: 20px;
  font-size: 12px;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
