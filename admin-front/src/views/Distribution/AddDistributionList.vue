<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
    <el-form ref="add_form" :model="add_form" size="small" :rules="add_rules" label-width="120px">
      <div class="detail-tab-item">
        <div class="detail-tab-title">
          <span>等级佣金比例</span>
        </div>
        <div class="detail-tab-main">
          <el-form-item v-if="add_form.grade > 0" label="等级权重" prop="activityType">
            <el-button
              v-for="(item, index) in grade_list"
              :key="index"
              :type="item.value === add_form.grade ? 'primary' : ''"
              :disabled="item.disabled"
              @click="changeGrade(item.value)"
            >
              {{ item.label }}
            </el-button>
          </el-form-item>
          <el-form-item label="等级名称" prop="name">
            <el-input
              v-model="add_form.name"
              style="width: 300px"
              placeholder="请输入等级名称"
              class="width60"
              maxlength="10"
              show-word-limit
              type="text"
            ></el-input>
          </el-form-item>
          <el-form-item prop="oneRate" label="一级佣金比例">
            <el-input v-model="add_form.oneRate" style="width: 300px" placeholder="请输入内容">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
          <el-form-item prop="twoRate" label="二级佣金比例">
            <el-input v-model="add_form.twoRate" style="width: 300px" placeholder="请输入内容">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
          <el-form-item prop="threeRate" label="三级佣金比例">
            <el-input v-model="add_form.threeRate" style="width: 300px" placeholder="请输入内容">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </div>
      </div>
      <div v-if="add_form.grade > 0" class="detail-tab-item">
        <div class="detail-tab-title">
          <span>添加升级条件</span>
        </div>
        <div class="detail-tab-main">
          <el-form-item label="升级方式" prop="activityType">
            <el-radio-group v-model="add_form.upgradeMode">
              <el-radio :label="4">满足以下任意条件</el-radio>
              <el-radio :label="5">满足以下全部条件</el-radio>
            </el-radio-group>
          </el-form-item>
          <p>
            <el-button
              v-for="(item, index) in button_list"
              :key="index"
              size="small"
              style="margin-bottom: 10px"
              :type="upgradeCondition.findIndex((itemU) => itemU.id === item.id) > -1 ? 'primary' : ''"
              @click="priceFn(item)"
            >
              {{ item.title }}
            </el-button>
          </p>
        </div>
      </div>
      <div v-if="add_form.grade > 0" class="detail-tab-item">
        <div class="detail-tab-title">
          <span>升级条件限制</span>
        </div>
        <div class="detail-tab-main">
          <el-form-item v-for="(item, index) in upgradeCondition" :key="index" label-width="1">
            <div style="margin-right: 10px; display: inline-block; width: 120px; text-align: right">
              <span style="color: #f56c6c">*</span>
              {{ item.title }}
            </div>
            <el-input v-model="item.desc" style="width: 300px">
              <el-button slot="append">
                <span v-if="item.title.indexOf('额') > -1">元</span>
                <span v-else-if="item.title.indexOf('人') > -1">人</span>
                <span v-else>个</span>
              </el-button>
            </el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </ContainerTit>
</template>

<script>
import { getAllGrade, updateGrade, addGrade, getAllGradeCondition, getInfoGrade } from "@/api/Commission";
export default {
  name: "AddDistributionList",
  data() {
    return {
      goods_show: false,
      radio: "1",
      time: "",
      price_list: [],
      button_list: [],
      add_form: {
        name: "",
        grade: "",
        oneRate: 0,
        twoRate: 0,
        threeRate: 0,
        upgradeMode: 5,
        upgradeCondition: [],
      },
      upgradeCondition: [],
      grade_list: [
        {
          label: "一级",
          value: 1,
          disabled: false,
        },
        {
          label: "二级",
          value: 2,
          disabled: false,
        },
        {
          label: "三级",
          value: 3,
          disabled: false,
        },
        {
          label: "四级",
          value: 4,
          disabled: false,
        },
        {
          label: "五级",
          value: 5,
          disabled: false,
        },
        {
          label: "六级",
          value: 6,
          disabled: false,
        },
        {
          label: "七级",
          value: 7,
          disabled: false,
        },
        {
          label: "八级",
          value: 8,
          disabled: false,
        },
        {
          label: "九级",
          value: 9,
          disabled: false,
        },
        {
          label: "十级",
          value: 10,
          disabled: false,
        },
      ],
      grade_id: 0,
      grade_data: [],
      add_rules: {
        name: [{ required: true, message: "请输入等级名称", trigger: "blur" }],
        oneRate: [{ required: true, message: "请输入一级佣金比例", trigger: "blur" }],
        twoRate: [{ required: true, message: "请输入二级佣金比例", trigger: "blur" }],
        threeRate: [{ required: true, message: "请输入三级佣金比例", trigger: "blur" }],
      },
    };
  },
  async created() {
    await this.getAllGradeCondition();
    await this.getAllGrade();
    this.grade_id = this.$route.params.id || 0;
    if (this.grade_id) {
      await this.getInfoGrade();
    }
  },
  methods: {
    async getAllGrade() {
      const { data } = await getAllGrade();

      // this.grade_data = data
      this.grade_list = this.grade_list.map((item) => {
        return {
          ...item,
          disabled: !!data.find((itemF) => itemF.grade === item.value),
        };
      });
      const findGrade = this.grade_list.find((item) => !item.disabled);
      this.add_form.grade = findGrade ? findGrade.value : "";
    },
    async submit() {
      this.$refs.add_form.validate(async (valid) => {
        if (valid) {
          let upgradeCondition = {};
          this.upgradeCondition.forEach((item) => {
            upgradeCondition[item.id] = item.desc;
          });

          let target = {};
          if (this.grade_id) {
            target = await updateGrade(this.grade_id, {
              ...this.add_form,
              upgradeCondition: upgradeCondition,
            });
          } else {
            target = await addGrade({
              ...this.add_form,
              upgradeCondition: upgradeCondition,
            });
          }
          const data = target;

          this.$message.success("提交成功");
          this.$closeCurrentGoEdit("/Distribution/DistributionList");
        }
      });
    },
    changeGrade(val) {
      this.add_form.grade = val;
    },
    async getAllGradeCondition() {
      const data = await getAllGradeCondition();

      this.button_list = data.data;
      this.upgradeCondition.push(data.data[0], data.data[1]);
    },
    async getInfoGrade() {
      const { data } = await getInfoGrade(this.grade_id);

      this.add_form = {
        name: data.name,
        grade: data.grade,
        oneRate: data.oneRate,
        twoRate: data.twoRate,
        threeRate: data.threeRate,
        upgradeMode: data.upgradeMode,
        upgradeCondition: data.upgradeCondition,
      };
      this.upgradeCondition = data.upgradeConditionInfo.map((item) => {
        return {
          ...item,
          desc: item.value,
          title: item.name,
        };
      });
    },
    priceFn(item) {
      const index = this.upgradeCondition.findIndex((itemU) => itemU.id === item.id);
      if (index === -1) {
        this.upgradeCondition.push(item);
      } else {
        this.upgradeCondition.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped></style>
