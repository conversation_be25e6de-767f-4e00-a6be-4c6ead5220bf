<template>
  <ContainerQuery>
    <div v-if="accessSearch" slot="right">
      <el-form size="small" :inline="true">
        <el-form-item label="关键词">
          <el-input
            v-model="searchDate.keyword"
            clearable
            style="width: 220px"
            :placeholder="'订单编号/收货人/商品' + ($route.name === 'selfOrder' ? '/自提码' : '')"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="客户">
          <SelectCustomer v-model="customer_name" @clear="customerClear" @change="customerSel" />
        </el-form-item>
      </el-form>
    </div>
    <div v-if="accessSearch" slot="more">
      <el-form size="small" :inline="true">
        <el-form-item label="关键词">
          <el-input
            v-model="searchDate.keyword"
            clearable
            style="width: 300px"
            :placeholder="'订单编号、收货人、商品名称' + ($route.name === 'selfOrder' ? '、自提码' : '')"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="下单客户">
          <SelectCustomer v-model="customer_name" @clear="customerClear" @change="customerSel" />
        </el-form-item>
        <el-form-item label="下单日期">
          <el-date-picker
            v-model="search_form.time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="orderDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="所属区域">
          <RegionSelect v-model="search_form.region" width="194" size="small" clearable @change="regionChange" />
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select
            v-model="searchDate.payType"
            clearable
            placeholder="支付方式"
            @visible-change="paytypevisibleChange"
            @change="pageChange(1)"
          >
            <el-option
              v-for="(item, index) in pay_type_list"
              :key="index"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户类型">
          <el-select v-model="searchDate.customerType" clearable placeholder="客户类型" @change="customerTypes">
            <el-option
              v-for="(item, index) in customerType"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单来源">
          <el-select v-model="searchDate.source" clearable placeholder="订单来源" @change="pageChange(1)">
            <el-option
              v-for="(item, index) in order_from"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属员工">
          <el-input
            v-model="search_form.staff"
            clearable
            placeholder="请选择业务员"
            @clear="staffClear"
            @blur="search_form.staff = ''"
          >
            <i slot="suffix" class="el-input__icon el-icon-search" @click="saleFn(true)"></i>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="left">
      <el-button v-if="accessAdd" type="primary" size="small" @click="goAddOrder"> 新建订单 </el-button>
      <el-button type="primary" plain size="small" @click="getData(1)"> 导出 </el-button>
      <el-button v-if="parseInt(printTag) === 5" type="primary" plain size="small" @click="resetPage">
        刷新页面
      </el-button>
    </div>
    <el-table
      ref="orderList"
      :data="order_list"
      :row-class-name="tableRowClassName"
      @row-dblclick="goDetail"
      @selection-change="selectOrderChange"
    >
      <el-table-column
        v-if="accessAudit && $route.name === 'NewOrderList'"
        type="selection"
        width="55"
      ></el-table-column>
      <el-table-column prop="id" label="ID" fixed="left" width="80"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" fixed="left" min-width="150">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="客户" min-width="180">
        <template slot-scope="scope">
          <el-popover v-if="scope.row.deliveryType !== 2" placement="right" width="400" trigger="hover">
            <div>
              <p class="order-info-p">
                <span class="order-info-label">收货人：</span>
                {{ scope.row.receiveData.realName }}
              </p>
              <p class="order-info-p">
                <span class="order-info-label">联系电话：</span>
                {{ scope.row.receiveData.mobile }}
              </p>
              <p v-if="scope.row.receiveData.area" class="order-info-p">
                <span class="order-info-label">联系地址：</span>
                {{ scope.row.receiveData.area.provinceName }}{{ scope.row.receiveData.area.cityName
                }}{{ scope.row.receiveData.area.districtName }}{{ scope.row.receiveData.address }}
              </p>
            </div>
            <span
              slot="reference"
              class="click-div"
              @click="$router.push(`/Customer/CustomerAdmin/CustomerDetail/${scope.row.customerId}`)"
            >
              {{ scope.row.customerName }}
            </span>
          </el-popover>
          <span
            v-else
            class="click-div"
            @click="$router.push(`/Customer/CustomerAdmin/CustomerDetail/${scope.row.customerId}`)"
          >
            {{ scope.row.customerName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="payAmount" label="订单金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.payAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="payAmount" label="商品" min-width="90">
        <template slot-scope="scope">
          <span class="click-div" @click="showGoods(scope.row)">商品明细</span>
        </template>
      </el-table-column>
      <el-table-column prop="orderMsg" label="订单状态" min-width="120">
        <template slot-scope="scope">
          <span
            :class="[
              scope.row.orderMsg === '待审核'
                ? 'warning-status'
                : scope.row.orderMsg === '已关闭'
                ? 'info-status'
                : scope.row.orderMsg === '已出库'
                ? 'primary-status'
                : scope.row.orderMsg === '已完成'
                ? 'success-status'
                : scope.row.orderMsg === '待出库'
                ? 'danger-status'
                : 'primary-status',
            ]"
          >
            {{ scope.row.orderMsg }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="orderMsg" label="付款状态" min-width="80">
        <template slot-scope="scope">
          <span
            :class="[
              parseInt(scope.row.payStatus) === 4
                ? 'danger-status'
                : parseInt(scope.row.payStatus) === 5
                ? 'success-status'
                : 'warning-status',
            ]"
          >
            {{
              parseInt(scope.row.payStatus) === 4
                ? "未支付"
                : parseInt(scope.row.payStatus) === 5
                ? "已支付"
                : "部分支付"
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="auditStatus" label="审核状态" min-width="80">
        <template slot-scope="scope">
          <span
            :class="[
              parseInt(scope.row.auditStatus) === 1
                ? 'warning-status'
                : parseInt(scope.row.auditStatus) === 2
                ? 'success-status'
                : parseInt(scope.row.auditStatus) === 3
                ? 'danger-status'
                : parseInt(scope.row.auditStatus) === 4
                ? 'warning-status'
                : 'warning-status',
            ]"
          >
            {{
              parseInt(scope.row.auditStatus) === 1
                ? "待审核"
                : parseInt(scope.row.auditStatus) === 2
                ? "已审核"
                : parseInt(scope.row.auditStatus) === 3
                ? "已驳回"
                : parseInt(scope.row.auditStatus) === 4
                ? "审核中"
                : "其他"
            }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="deliveryType" label="配送方式" min-width="90">
        <template slot-scope="scope">
          {{
            parseInt(scope.row.deliveryType) === 1
              ? "商品配送"
              : parseInt(scope.row.deliveryType) === 3
              ? "物流"
              : parseInt(scope.row.deliveryType) === 2
              ? "上门自提"
              : ""
          }}
          <p style="font-weight: bold; color: #67c23a">
            {{ scope.row.verifyCode }}
          </p>
        </template>
      </el-table-column>
      <el-table-column prop="payType" label="支付方式" min-width="90">
        <template slot-scope="scope">
          {{
            parseInt(scope.row.payType) === 3
              ? "货到付款"
              : parseInt(scope.row.payType) === 2
              ? "支付宝"
              : parseInt(scope.row.payType) === 1
              ? "微信支付"
              : parseInt(scope.row.payType) === 5
              ? "现金"
              : parseInt(scope.row.payType) === 6
              ? "其他"
              : parseInt(scope.row.payType) === 4
              ? "上门自提"
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column prop="salesman" label="所属员工" min-width="110">
        <template slot-scope="scope">
          {{ scope.row.salesManName || "未分配" }}

          <el-button
            v-if="!scope.row.salesManName"
            type="text"
            icon="el-icon-edit"
            @click="saleFn(false, scope.row.id)"
          ></el-button>
        </template>
      </el-table-column>
      <el-table-column prop="source" label="订单来源" min-width="110">
        <template slot-scope="scope">
          {{
            parseInt(scope.row.source) === 1
              ? "ios"
              : parseInt(scope.row.source) === 2
              ? "安卓"
              : parseInt(scope.row.source) === 3
              ? "微信小程序"
              : parseInt(scope.row.source) === 4
              ? "后台创建"
              : parseInt(scope.row.source) === 5
              ? "H5页面"
              : parseInt(scope.row.source) === 8
              ? "字节跳动小程序"
              : parseInt(scope.row.source) === 6
              ? "pc页面"
              : ""
          }}
        </template>
      </el-table-column>

      <el-table-column prop="no" label="订单号" min-width="180">
        <template slot-scope="scope">
          <span v-if="accessDetail" class="click-div" @click="goDetail(scope.row)">
            {{ scope.row.no }}
          </span>
          <span v-else>{{ scope.row.no }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="parseInt(printTag) === 5"
        prop="printingNum"
        label="打印次数"
        min-width="80"
      ></el-table-column>

      <el-table-column label="操作" fixed="right" :min-width="$route.name === 'OrderListWaitAudit' ? 280 : 140">
        <template slot-scope="scope">
          <el-popconfirm
            v-if="accessDetail"
            title="请选择要打印的单据"
            confirm-button-text="小票打印机"
            cancel-button-text="网页打印"
            @onConfirm="toPrint(scope.row)"
            @onCancel="openUrl(scope.row)"
          >
            <el-button slot="reference" type="text">打印</el-button>
          </el-popconfirm>
          <el-button
            v-if="accessDetail && $route.name === 'OrderListWaitAudit'"
            type="text"
            @click="$router.push(`/order/manageO/OrderEdit/${scope.row.userCenterId}/${scope.row.id}`)"
          >
            编辑
          </el-button>
          <el-button
            v-if="accessAudit && $route.name === 'OrderListWaitAudit'"
            :disabled="parseInt(scope.row.payStatus) === 4 && [1, 2].includes(scope.row.payType)"
            type="text"
            @click="updateAuditStatus(scope.row)"
          >
            审核
          </el-button>
          <el-button
            v-if="accessCancel && $route.name === 'OrderListWaitAudit'"
            type="text"
            :disabled="[2, 6].includes(parseInt(scope.row.auditStatus) === 2)"
            @click="updateOrderStatus(scope.row)"
          >
            取消订单
          </el-button>
          <el-button
            v-if="$accessCheck($Access.ReturnWarehousingOrderAddOrderReturn) && $route.name === 'OrderListHasOutStock'"
            type="text"
            @click="returnOrder(scope.row)"
          >
            退单
          </el-button>
          <el-button v-if="accessDetail" type="text" @click="goDetail(scope.row)"> 查看 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="accessAudit && $route.name === 'NewOrderList'" slot="btn-div" class="foot-btn-div">
        <span class="batch-checkbox">
          <el-checkbox v-model="checkedAll" @change="checkAllChange"></el-checkbox>
        </span>

        <el-button size="mini" @click="batchUpdateAuditStatus"> 批量审核 </el-button>
      </div>
    </FooterPage>
    <staffListModal
      v-if="staff_show"
      :is-show="staff_show"
      :is-check="false"
      :isserch="isserch"
      @cancel="staff_show = false"
      @confirm="staffSel"
    />
    <el-dialog v-if="is_show_goods" title="商品明细" :visible.sync="is_show_goods">
      <el-table border :data="goodsDataD">
        <el-table-column property="goodsName" label="商品名称" show-overflow-tooltip min-width="140"></el-table-column>
        <el-table-column property="goodsCode" label="商品编码" min-width="140"></el-table-column>
        <el-table-column property="unitName" label="规格" min-width="140">
          <template slot-scope="prop">
            {{ prop.row.unitName }}；
            <span v-for="(item, index) in prop.row.specGroup" :key="index"> {{ item.specValueName }}； </span>
          </template>
        </el-table-column>

        <el-table-column property="price" label="单价" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.price) }}
          </template>
        </el-table-column>
        <el-table-column property="buyNum" label="购买数量" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.buyNum) }}
          </template>
        </el-table-column>
        <el-table-column property="totalMoney" label="商品总价" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.totalMoney) }}
          </template>
        </el-table-column>
        <el-table-column property="deliverNum" label="发货数量" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.deliverNum) }}
          </template>
        </el-table-column>
        <el-table-column property="barCode" label="商品条码" min-width="120"></el-table-column>
        <el-table-column property="storage" label="货架编码" min-width="120"></el-table-column>
      </el-table>
    </el-dialog>
  </ContainerQuery>
</template>

<script>
import RegionSelect from "@/component/common/RegionSelectJSON";
import staffListModal from "@/component/common/staffListModal";
import { mapGetters } from "vuex";
import {
  getOrderInfoById,
  getAllOrder,
  exportGetAllOrder,
  searchOrder,
  exportSearchOrder,
  updateOrderStatus,
  updateAuditStatus,
  setSalesMan,
} from "@/api/Order";
import { getAllCustomerSource, getAllPayment } from "@/api/System";
import { toPrint } from "@/api/common";
import SelectCustomer from "@/component/common/SelectCustomer.vue";

export default {
  name: "OrderList",
  components: {
    RegionSelect,
    staffListModal,
    SelectCustomer,
  },

  data() {
    return {
      customerType: [],
      checkedAll: false,
      is_show_goods: false,
      selected_order: [],
      staff_show: false,
      order_list: [],
      goodsDataD: [],
      order_from: [
        {
          label: "ios",
          value: 1,
        },

        {
          label: "安卓",
          value: 2,
        },
        {
          label: "微信小程序",
          value: 3,
        },
        {
          label: "后台创建",
          value: 4,
        },
        {
          label: "H5页面",
          value: 5,
        },
        {
          label: "pc页面",
          value: 6,
        },
        {
          label: "字节跳动小程序",
          value: 8,
        },
      ],
      pay_type_list: [],
      region_options: [],
      total: 0,
      page: 1,
      pageSize: 10,
      search_form: {
        time: [],
        region: [],
        user: "",
        order_type: "",
        shopId: "",
        money_type: "",
        customType: "",
        staff: "",
      },
      customer_name: "",
      searchDate: {
        keyword: "",
        orderStatus: "",
        payType: "",
        provinceCode: "",
        cityCode: "",
        districtCode: "",
        start: "",
        end: "",
        customerId: "",
        customerType: "",
        salesManId: "",
        source: "",
        shopId: "",
      },
      salesManId: "",
      receiveData: [],
      deliveryType: "",
      auditStatus: "",
      // 权限
      accessAdd: true,
      accessAudit: true,
      accessCancel: true,
      accessSearch: true,
      accessDetail: true,
      isserch: true,
      order_id: "",
    };
  },
  computed: {
    ...mapGetters({
      printTag: "MUser/printTag",
      storeData: "MUser/storeData",
    }),
  },
  async mounted() {
    const pathArr = this.$route.path.split("/");
    this.searchDate.orderStatus = pathArr[pathArr.length - 1];
    this.searchDate.shopId = this.storeData.id;
    // 到期提示
    this.enterExpireTime();
    await this.getData();
    await this.getAllPayment();
    await this.getAllCustomerSource();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    async showGoods(row) {
      const { data } = await getOrderInfoById(row.userCenterId, {
        orderId: row.id,
      });

      this.is_show_goods = true;
      this.goodsDataD = data.goodsData;
    },
    returnOrder(row) {
      const allowReturn = row.allowReturn;
      const allowReturnDay = row.allowReturnDay;
      if (allowReturn === 4) {
        this.$confirm(`已经超过${allowReturnDay}天,建议不能退货, 是否确定强制退货?`, "提醒", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$message({
            type: "error",
            message: "你已确定强制退货!",
          });
        });
        this.$router.push(`/order/manageO/AddWareOrder?userCenterId=${row.userCenterId}&orderId=${row.id}`);
      } else {
        this.$router.push(`/order/manageO/AddWareOrder?userCenterId=${row.userCenterId}&orderId=${row.id}`);
      }
    },
    // 客户类型
    async getAllCustomerSource() {
      if (this.customerType.length) {
        return;
      }
      const data = await getAllCustomerSource({
        page: 1,
        pageSize: 20,
      });

      this.customerType = data.data;
    },
    customerTypes(val) {
      this.searchDate.customerType = val;
      this.pageChange(1);
    },
    // 获取列表
    async getAllOrder(exports) {
      let search = {};
      if (this.searchDate.orderStatus === "waitAudit") {
        search.auditStatus = 1;
      } else {
        search.auditStatus = 2;
      }
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        search: search,
        // shopId: this.searchDate.shopId,
      };
      if (exports) {
        params.export = 1;
        const target = await exportGetAllOrder({
          ...params,
          state: this.searchDate.orderStatus,
        });
      } else {
        const data = await getAllOrder({
          ...params,
          state: this.searchDate.orderStatus,
        });

        this.order_list = data.data;
        this.total = data.pageTotal;
      }
    },
    // 支付方式展开
    paytypevisibleChange(val) {
      if (val && !this.pay_type_list.length) {
        this.getAllPayment();
      }
    },
    // 获取支付方式
    async getAllPayment() {
      if (this.pay_type_list.length) {
        return;
      }
      const data = await getAllPayment({
        page: 1,
        pageSize: 20,
      });

      this.pay_type_list = data.data;
    },
    // 订单搜索searchOrder
    async searchOrder(exports) {
      const params = {
        deliveryType: this.deliveryType,
        keyword: this.searchDate.keyword,
        // 'auditStatus': this.searchDate.orderStatus,
        payType: this.searchDate.payType,
        provinceCode: this.searchDate.provinceCode,
        cityCode: this.searchDate.cityCode,
        districtCode: this.searchDate.districtCode,
        shopId: this.searchDate.shopId,
        start: this.searchDate.start,
        end: this.searchDate.end,
        customerId: this.searchDate.customerId,
        customerType: this.searchDate.customerType,
        salesManId: this.searchDate.salesManId,
        source: this.searchDate.source,
        page: this.page,
        pageSize: this.pageSize,
      };
      if (exports) {
        params.export = 1;
        const target = await exportSearchOrder({
          ...params,
          state: this.searchDate.orderStatus,
        });
      } else {
        const data = await searchOrder({
          ...params,
          state: this.searchDate.orderStatus,
        });

        this.order_list = data.data;
        this.total = data.pageTotal;
      }
    },
    //  判断
    getData(exports) {
      const obj = {
        keyword: this.searchDate.keyword,
        // 'orderStatus': this.searchDate.orderStatus,
        payType: this.searchDate.payType,
        provinceCode: this.searchDate.provinceCode,
        cityCode: this.searchDate.cityCode,
        districtCode: this.searchDate.districtCode,
        shopId: this.searchDate.shopId,
        start: this.searchDate.start,
        end: this.searchDate.end,
        customerId: this.searchDate.customerId,
        customerType: this.searchDate.customerType,
        salesManId: this.searchDate.salesManId,
        source: this.searchDate.source,
      };
      const isKey = this.$_common.isSerch(obj);
      if (isKey) {
        this.searchOrder(exports);
      } else {
        this.getAllOrder(exports);
      }
    },
    // 选择员工
    staffSel(val) {
      const row = val[0];
      if (this.isserch) {
        this.searchDate.salesManId = row.id;
        this.search_form.staff = row.staffName;
        this.pageChange(1);
      } else {
        this.salesManId = row.id;
        this.search_form.staff = row.staffName;
        this.setSalesMan();
      }
    },
    // 清除员工输入框
    staffClear() {
      this.searchDate.salesManId = "";
      this.search_form.staff = "";
      this.pageChange(1);
    },
    // 选择客户
    customerSel(val, list) {
      this.searchDate.customerId = list[0].id;
      this.pageChange(1);
    },
    customerClear() {
      this.searchDate.customerId = "";
      this.customer_name = "";
      this.pageChange(1);
    },
    //  选择区域region
    regionChange(val) {
      if (val && val.length) {
        this.searchDate.provinceCode = val[0];
        this.searchDate.cityCode = val[1];
        this.searchDate.districtCode = val[2];
      } else {
        this.searchDate.provinceCode = "";
        this.searchDate.cityCode = "";
        this.searchDate.districtCode = "";
      }
      this.pageChange(1);
    },
    //  订单时间
    orderDate(val) {
      if (val && val.length) {
        this.searchDate.start = val[0] / 1000;
        this.searchDate.end = val[1] / 1000 + 86399;
      } else {
        this.searchDate.start = "";
        this.searchDate.end = "";
      }
      this.pageChange(1);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    goAddOrder() {
      this.$router.push({
        path: "/SingleStore/order/StoreOrderAdd",
      });
    },
    goDetail(row) {
      if (!this.accessDetail) {
        return;
      }
      this.$router.push({
        path: `/order/manageO/OrderDetails/${row.userCenterId}/${row.id}`,
      });
    },
    openUrl(row) {
      // const params = { userCenterId: row.userCenterId, id: row.id }
      let routeData = this.$router.resolve({
        path: `/OrderPrinting/${row.userCenterId}/${row.id}`,
      });
      // console.log(routeData)
      window.open(routeData.href, "_blank");
    },
    // 单据小票打印
    async toPrint(row) {
      const data = await toPrint({
        objectId: row.id,
        objectType: 1, // 销售单
      });

      this.$message({
        type: "success",
        message: "操作成功",
      });
    },
    // 取消订单
    async updateOrderStatus(row) {
      this.$confirm("确定要取消该订单吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateOrderStatus(row.userCenterId, {
          orderId: row.id,
        });

        this.$message({
          type: "success",
          message: "操作成功",
        });
        this.getData();
      });
    },
    // 批量选择订单
    selectOrderChange(val) {
      this.checkedAll = val.length === this.order_list.length;
      this.selected_order = val;
    },
    // 批量审核订单
    batchUpdateAuditStatus() {
      if (!this.selected_order.length) {
        this.$message.warning("请选择要审核的订单");
        return;
      }
      this.$confirm("是否要批量审核订单吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const orderIds = this.selected_order.map((item) => {
          return item.id;
        });
        const data = await batchUpdateAuditStatus({
          orderIds: orderIds,
          audit: this.userName,
          auditStatus: 2,
        });

        this.$message.success("审核成功");
        this.getData();
      });
    },
    // 审核订单
    async updateAuditStatus(row) {
      this.$confirm("确定要审核通过该订单吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateAuditStatus(row.userCenterId, {
          orderId: row.id,
          auditStatus: 2,
          audit: this.userName,
        });

        this.getData();
        this.$confirm("订单审核成功，是否前去打印?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.openUrl(row);
        });
      });
    },
    checkAllChange() {
      this.$refs.orderList.toggleAllSelection();
    },
    async saleFn(isserch, id) {
      this.staff_show = true;
      this.isserch = isserch;
      if (id) {
        this.order_id = id;
      }
    },

    async setSalesMan() {
      const data = await setSalesMan({
        orderId: this.order_id,
        salesManName: this.search_form.staff,
        salesManId: this.salesManId,
      });

      this.getData();
    },
    tableRowClassName({ row, rowIndex }) {
      if (parseInt(this.printTag) === 5 && row.printingNum > 0) {
        return "print-row";
      }
      return "";
    },
    // 刷新页面
    resetPage() {
      this.getData();
    },
  },
};
</script>
<style scoped>
.padb-10 {
  padding-bottom: 0;
}
</style>
