<template>
  <ContainerQuery>
    <div slot="left">
      <!--<el-button
        v-if="$accessCheck($Access.ReturnWarehousingOrderAddOrderReturn)"
        size="small"
        type="primary"
        @click="$router.push(`/order/manageO/AddWareOrder`)"
      >
        新增退货
      </el-button>-->
      <el-button
        v-if="$accessCheck($Access.ReturnWarehousingOrderGetAllOrderReturn)"
        size="small"
        type="primary"
        plain
        @click="getData(1)"
      >
        导出
      </el-button>
    </div>
    <div v-if="$accessCheck($Access.ReturnWarehousingOrderSearchAllOrderReturn)" slot="right">
      <el-form :inline="true" size="small">
        <el-form-item label="关键词">
          <el-input
            v-model="search_form.keyWord"
            clearable
            style="width: 250px"
            placeholder="订单号/退货单号/商品名称"
            @keyup.enter.native="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select
            v-model="search_form.status"
            clearable
            style="width: 150px"
            placeholder="审核状态"
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in order_status"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="$accessCheck($Access.ReturnWarehousingOrderSearchAllOrderReturn)" slot="more">
      <el-form :inline="true" size="small">
        <el-form-item label="关键词">
          <el-input
            v-model="search_form.keyWord"
            clearable
            style="width: 250px"
            placeholder="订单号/退货单号/商品名称"
            @keyup.enter.native="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            v-model="search_form.time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="returWare"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select
            v-model="search_form.status"
            clearable
            style="width: 150px"
            placeholder="审核状态"
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in order_status"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单据状态">
          <el-select
            v-model="search_form.inStatus"
            clearable
            style="width: 150px"
            placeholder="单据状态"
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in single_status"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务员">
          <el-input v-model="search_form.staff" clearable placeholder="请选择业务员" @blur="search_form.staff = ''">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="staff_show = true"></i>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <el-table border :data="tableData" @row-dblclick="goDetail">
      <el-table-column prop="id" align="left" fixed="left" label="ID" min-width="50"></el-table-column>
      <el-table-column prop="no" fixed="left" align="left" label="退货单号" min-width="200">
        <template slot-scope="scope">
          <span
            v-if="$accessCheck($Access.ReturnWarehousingOrderGetOrderReturn)"
            class="click-div"
            @click="goDetail(scope.row)"
          >
            {{ scope.row.no }}
          </span>
          <span v-else>{{ scope.row.no }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="goodsCode" label="商品" min-width="160">
        <template slot-scope="scope">
          <p>{{ scope.row.goodsName }}</p>
          <p>{{ scope.row.goodsCode }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="unitName" label="单位" align="left" min-width="80"></el-table-column>
      <el-table-column prop="skuName" label="属性" align="left" min-width="130"></el-table-column>

      <el-table-column align="center" prop="goodsNum" label="数量" min-width="80">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.goodsNum, 2) }}
        </template>
      </el-table-column>
      <el-table-column prop="saleUnitPrice" align="left" label="销售单价" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.saleUnitPrice, 2) }}
        </template>
      </el-table-column>
      <el-table-column prop="saleTotalPrice" align="left" label="销售金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.saleTotalPrice, 2) }}
        </template>
      </el-table-column>
      <el-table-column prop="returnUnitPrice" align="left" label="退货单价" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.returnUnitPrice, 2) }}
        </template>
      </el-table-column>
      <el-table-column prop="returnTotalPrice" align="left" label="退货金额" min-width="100">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.returnTotalPrice, 2) }}
        </template>
      </el-table-column>
      <el-table-column prop="operatorName" align="left" label="操作人" min-width="120"></el-table-column>
      <el-table-column prop="originNo" align="left" label="源销售订单号" min-width="200"></el-table-column>
      <el-table-column prop="auditStatus" label="状态" align="left" min-width="140">
        <template slot-scope="scope">
          <p>
            入库：
            <el-tag size="mini" :type="scope.row.inStatus === 4 ? 'info' : 'success'">
              {{ scope.row.inStatus === 4 ? "未入库" : "已入库" }}
            </el-tag>
          </p>
          <p>
            审核：
            <el-tag size="mini" :type="scope.row.auditStatus === 1 ? 'info' : 'success'">
              {{ scope.row.auditStatus === 1 ? "待审核" : "已审核" }}
            </el-tag>
          </p>
        </template>
      </el-table-column>
      <el-table-column prop="auditTime" align="left" label="审核时间" min-width="160">
        <template slot-scope="scope">
          {{ scope.row.auditStatus === 2 ? $_common.formatDate(scope.row.auditTime) : "--" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" header-align="left" align="left" fixed="right" min-width="180">
        <template slot-scope="scope">
          <el-button-group class="table-btn-group">
            <el-button type="text" @click="openUrl(scope.row.id)"> 打印 </el-button>
            <el-button
              v-if="
                $accessCheck($Access.ReturnWarehousingOrderGetOrderReturn) &&
                $accessCheck($Access.ReturnWarehousingOrderAuditOrderReturn)
              "
              :disabled="~~scope.row.auditStatus === 2"
              type="text"
              @click="$router.push(`/order/manageO/EditWareOrder/${scope.row.id}`)"
            >
              编辑
            </el-button>
            <el-button
              v-if="$accessCheck($Access.ReturnWarehousingOrderUpdateOrderReturn)"
              :disabled="~~scope.row.auditStatus === 2"
              type="text"
              @click="updateAuditStatus(scope.row)"
            >
              审核
            </el-button>
            <el-button
              v-if="$accessCheck($Access.ReturnWarehousingOrderDeleteOrderReturn)"
              :disabled="~~scope.row.auditStatus === 2"
              type="text"
              @click="del(scope.row.id)"
            >
              删除
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <!--    业务员-->
    <staffListModal
      v-if="staff_show"
      :is-check="false"
      :is-show="staff_show"
      @cancel="staff_show = false"
      @confirm="staffSel"
    />
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import staffListModal from "@/component/common/staffListModal";
import { mapGetters } from "vuex";
import {
  searchAllOrderOut,
  exportSearchAllOrderOut,
  exportGetAllOrderOut,
  getAllOrderOut,
  auditOrderOut,
  deleteOrderOut,
} from "@/api/Order";

export default {
  name: "ReturnOrder",
  components: {
    staffListModal,
  },
  data() {
    return {
      tableData: [],
      staff_show: false,
      pageSize: 10,
      page: 1,
      total: 0,
      outWare: [],
      order_status: [
        { value: 5, label: "已审核" },
        { value: 4, label: "待审核" },
      ],
      single_status: [
        { value: 4, label: "未入库" },
        { value: 3, label: "已入库" },
      ],
      search_form: {
        shopId: "",
        shopName: "",
        wareStatus: "",
        inStatus: "",
        start: "",
        end: "",
        keyWord: "",
        customer: "",
        customer_id: "",
        operatorId: "",
        staff: "",
        status: "",
        categoryId: [],
      },
    };
  },
  computed: {
    ...mapGetters({
      storeData: "MUser/storeData",
    }),
  },
  created() {
    this.getAllOrderOut();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    // 打印
    openUrl(id) {
      let routeData = this.$router.resolve({
        path: `/ReturnOrderPrinting/${id}`,
      });
      window.open(routeData.href, "_blank");
    },
    //  退货单搜索
    async searchAllOrderOut(exports) {
      let params = {
        shopId: this.storeData.id,
        inStatus: this.search_form.inStatus,
        operatorId: this.search_form.operatorId,
        auditStatus: this.search_form.status,
        start: this.search_form.start,
        end: this.search_form.end,
        search: this.search_form.keyWord,
        page: this.page,
        pageSize: this.pageSize,
      };
      if (exports) {
        params.export = 1;
        const target = await exportSearchAllOrderOut(params);
      } else {
        const { data, pageTotal } = await searchAllOrderOut(params);

        this.tableData = data;
        this.total = pageTotal;
      }
    },
    //  请求退货单列表
    async getAllOrderOut(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
      };
      if (exports) {
        params.export = 1;
        const target = await exportGetAllOrderOut(params);
      } else {
        const { data, pageTotal } = await getAllOrderOut(params);

        this.tableData = data;
        this.total = pageTotal;
      }
    },
    getData(exports) {
      const isKey = this.$_common.isSerch(this.search_form);
      if (isKey) {
        this.searchAllOrderOut(exports);
      } else {
        this.getAllOrderOut(exports);
      }
    },
    //  审核
    async updateAuditStatus(row) {
      this.$confirm("确定要审核通过该单据吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await auditOrderOut(row.id, {
          auditName: this.userName,
        });

        this.$message({
          type: "success",
          message: "审核成功",
        });
        this.getData();
      });
    },
    //  删除销售退货单
    del(id) {
      this.$confirm("确定要删除该单据吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await deleteOrderOut(id);

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.getData();
      });
    },
    //  选择时间
    returWare(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    // 选择员工
    staffSel(val) {
      const row = val[0];
      this.search_form.operatorId = row.id;
      this.search_form.staff = row.staffName;
      this.pageChange(1);
    },
    //  选择商铺
    selShop(val, row) {
      // this.search_form.shopId = row[0].id
      this.search_form.shopName = row[0].name;
      this.pageChange(1);
    },
    clearShop() {
      this.search_form.shopId = "";
      this.search_form.shopName = "";
      this.pageChange(1);
    },
    pageChange(page) {
      this.page = page;
      this.getData();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    goDetail(row) {
      if (!this.$accessCheck(this.$Access.ReturnWarehousingOrderGetOrderReturn)) {
        return;
      }
      this.$router.push(`/order/manageO/LookWareOrder/${row.id}`);
    },
  },
};
</script>

<style scoped>
.num-ul > li {
  padding-right: 10px;
}
</style>
