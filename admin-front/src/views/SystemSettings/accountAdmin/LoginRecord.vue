<template>
  <div style="background-color: #ffffff">
    <Container></Container>
    <el-table :data="login_tabel">
      <el-table-column prop="source" label="来源"></el-table-column>
      <el-table-column prop="createTime" label="日期">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime, "yyyy-MM-dd hh:mm:ss") }}
        </template>
      </el-table-column>
      <el-table-column prop="mobile" label="手机号"></el-table-column>
      <el-table-column prop="actionType" label="操作类型"></el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </div>
</template>

<script>
import { getAllLog } from "@/api/common";
export default {
  name: "LoginRecord",
  data() {
    return {
      pageSize: 10,
      page: 1,
      total: 1,
      login_tabel: [],
    };
  },
  created() {
    this.getAllLog();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllLog();
  },
  methods: {
    async getAllLog() {
      const data = await getAllLog({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.login_tabel = data.data;
      this.total = data.pageTotal;
      // console.log(this.login_tabel)
    },
    pageChange(page) {
      this.page = page;
      this.getAllLog();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
