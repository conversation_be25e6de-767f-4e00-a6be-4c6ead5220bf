<template>
  <div class="page-div">
    <Container></Container>
    <el-row>
      <el-col :span="6" class="position-list">
        <div>
          <el-input
            v-model="keyword"
            style="margin-bottom: 12px"
            size="mini"
            clearable
            placeholder="输入关键字进行过滤"
            @change="searchRole"
            @blur="searchRole"
          ></el-input>
        </div>
        <div v-if="$accessCheck($Access.RoleAddRole)" style="padding-bottom: 10px">
          <el-button size="mini" @click="addGroup">新增角色</el-button>
        </div>
        <div>
          <el-tree
            ref="tree"
            class="filter-tree"
            :data="position_data"
            :props="defaultProps"
            default-expand-all
            :filter-node-method="filterNode"
            @node-click="selRole"
          >
            <div slot-scope="{ node, data }" class="custom-tree-node clearfix">
              <span class="float_left">{{ data.roleName }}</span>
              <span class="float_right">
                <el-button
                  v-if="$accessCheck($Access.RoleUpdateRole)"
                  type="text"
                  size="mini"
                  icon="el-icon-edit"
                  @click="editData(data)"
                ></el-button>
                <el-button
                  v-if="$accessCheck($Access.RoleDeleteRole)"
                  style="color: #f56c6c"
                  type="text"
                  size="mini"
                  icon="el-icon-delete"
                  @click="delData(data)"
                ></el-button>
              </span>
            </div>
          </el-tree>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="position-detail">
          <div class="div-tit">
            <span>{{ now_role.roleName }}</span>
            <el-button
              v-if="$accessCheck($Access.RoleUpdateRole)"
              type="text"
              size="mini"
              icon="el-icon-edit"
              @click="editRole"
            ></el-button>
          </div>
          <div style="padding-top: 10px">
            <el-table v-if="$accessCheck($Access.StaffGetAllStaff)" size="mini" :data="tableData">
              <el-table-column prop="staffCode" label="工号" min-width="160"></el-table-column>
              <el-table-column prop="staffName" label="姓名" min-width="120"></el-table-column>
              <el-table-column prop="departmentName" label="部门" min-width="160"></el-table-column>
            </el-table>
            <FooterPage
              :page-size="page_size"
              :total-page.sync="total"
              :current-page.sync="page"
              @pageChange="pageChange"
              @sizeChange="sizeChange"
            ></FooterPage>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-drawer
      class="edit-drawer"
      :size="add_position ? '40%' : '60%'"
      :visible.sync="is_drawer"
      direction="rtl"
      @close="drawerClose"
    >
      <AddPositionGroup
        v-if="is_drawer && add_group"
        :id="group_id"
        :is-edit="edit_group"
        @confirm="groupConfirm"
      ></AddPositionGroup>
      <AddPosition
        v-if="is_drawer && add_position"
        :id="position_id"
        :is-edit="edit_role"
        @confirm="groupConfirm"
      ></AddPosition>
    </el-drawer>
  </div>
</template>

<script>
import { deleteRole, getAllRole, getAllStaff } from "@/api/Department";
import AddPosition from "@/component/SystemSettings/AddPosition.vue";
import AddPositionGroup from "@/component/SystemSettings/AddPositionGroup.vue";
export default {
  name: "Role",
  components: {
    AddPositionGroup,
    AddPosition,
  },
  data() {
    return {
      keyword: "",
      position_id: "", // 角色id
      group_id: "", // 角色组id
      filterText: "",
      edit_group: false,
      is_drawer: false,
      total: 0,
      page: 1,
      page_size: 10,
      edit_role: false,
      add_group: false,
      add_position: false,
      tableData: [],
      position_data: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      now_role: {},
    };
  },
  async created() {
    await this.getAllRole();
    await this.getAllStaff();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllStaff();
  },
  methods: {
    drawerClose() {
      this.is_drawer = false;
      this.add_group = false;
      this.add_position = false;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllStaff();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 选择角色
    selRole(row) {
      //  返回当前点击的对象
      this.now_role = row;
      this.getAllStaff();
    },
    groupConfirm() {
      this.getAllRole();
      this.drawerClose();
    },
    //  获取职工列表
    async getAllStaff() {
      if (!this.$accessCheck(this.$Access.StaffGetAllStaff)) {
        return;
      }

      const data = await getAllStaff({
        page: this.page,
        pageSize: this.page_size,
        // 'departmentId': 1,
        roleId: this.now_role.id,
      });

      this.tableData = data.data;
      this.total = data.pageTotal;
      // console.log(this.tableData)
    },
    //  获取角色组列表
    async getAllRole() {
      const data = await getAllRole({
        page: this.page,
        pageSize: this.page_size,
        keyword: this.keyword,
      });

      this.position_data = data.data;
      this.now_role = data.data[0] || {};
    },
    //  搜索
    searchRole() {
      this.getAllRole();
    },
    // 树形控件 过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    //  新增角色组
    addGroup() {
      this.is_drawer = true;
      this.group_id = 0;
      this.position_id = 0;
      this.edit_group = false;
      this.add_group = true;
      this.getAllRole();
    },
    // 新增角色
    addPosition() {
      this.edit_role = false;
      this.add_position = true;
      this.is_drawer = true;
      this.getAllRole();
    },
    //  总经理旁边的编辑
    editRole() {
      this.editData(this.now_role);
    },
    //  编辑角色/组
    editData(data) {
      if (!data.pid) {
        //    编辑角色组
        this.group_id = data.id;
        this.is_drawer = true;
        this.add_group = true;
        this.edit_group = true;
        this.getAllRole();
      } else {
        //  编辑角色
        this.is_drawer = true;
        this.position_id = data.id;
        this.add_position = true;
        this.edit_role = true;
        this.getAllRole();
      }
    },

    //  删除角色
    delData(row) {
      // 判断是那个 如果有pid 则为角色删除
      this.$confirm("确定删除此角色", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await deleteRole(row.id);

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.getAllRole();
      });
    },
  },
};
</script>

<style scoped>
.position-list {
  padding: 24px 12px;
}
.position-detail {
  padding: 24px 12px;
  border-left: 1px solid #eee;
  height: calc(100vh - 165px);
}
.custom-tree-node {
  width: 100%;
  font-size: 14px;
}
</style>
