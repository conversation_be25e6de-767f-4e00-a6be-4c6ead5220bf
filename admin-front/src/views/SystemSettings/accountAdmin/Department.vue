<template>
  <div class="page-div">
    <Container></Container>
    <el-row>
      <el-col :span="6">
        <div class="branch-list">
          <el-input
            v-model="keyword"
            style="margin-bottom: 12px"
            size="mini"
            placeholder="输入关键字进行过滤"
            clearable
            @change="searchDeparment"
            @blur="searchDeparment"
          ></el-input>
          <div style="text-align: right; padding-bottom: 10px">
            <el-button v-if="$accessCheck($Access.DepartmentAddDepartment)" size="mini" @click="addDepartment">
              新增部门
            </el-button>
          </div>
          <el-tree
            ref="tree"
            class="filter-tree"
            :data="Department_data"
            :props="defaultProps"
            default-expand-all
            :filter-node-method="filterNode"
            @node-click="selDepartment"
          >
            <div slot-scope="{ node, data }" class="custom-tree-node clearfix">
              <span class="float_left">{{ data.departmentName }}</span>
              <span class="float_right">
                <el-button
                  v-if="$accessCheck($Access.DepartmentUpdateDepartment)"
                  type="text"
                  size="mini"
                  icon="el-icon-edit"
                  @click="editData(data.id)"
                ></el-button>
                <el-button
                  v-if="$accessCheck($Access.DepartmentDeleteDepartment)"
                  style="color: #f56c6c"
                  type="text"
                  size="mini"
                  icon="el-icon-delete"
                  @click="delData(data.id)"
                ></el-button>
              </span>
            </div>
          </el-tree>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="branch-detail">
          <div class="branch-name">
            <span style="padding-right: 12px">
              {{ now_department.departmentName }}
            </span>
            <el-button
              v-if="$accessCheck($Access.DepartmentUpdateDepartment)"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="editData(now_department.id)"
            ></el-button>
          </div>
          <!--  <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item>
              <span style="color: #1881f7">源点计划有限公司</span>
            </el-breadcrumb-item>
            <el-breadcrumb-item>研发中心</el-breadcrumb-item>
          </el-breadcrumb>-->
          <div class="last-branch">
            <div class="last-branch-tit">下级部门</div>
            <div class="branch-handel">
              <el-button size="mini" @click="addDepartment"> 新增子部门 </el-button>
            </div>
            <ul class="last-branch-ul">
              <li
                v-for="(item, index) in now_department.children"
                :key="index"
                class="last-branch-li clearfix"
                @click="selDepartment(item)"
              >
                <span class="float_left">{{ item.departmentName }}</span>
                <div class="float_right">
                  <i class="el-icon-arrow-right float_right"></i>
                </div>
              </li>
            </ul>
          </div>
          <div v-if="$accessCheck($Access.StaffGetAllStaff)" class="branch-staff">
            <div class="last-branch-tit">部门人员</div>
            <div class="branch-handel" style="margin-bottom: 12px">
              <el-button v-if="$accessCheck($Access.StaffAddStaff)" size="mini" plain type="primary" @click="openModel">
                新增成员
              </el-button>
              <el-button
                v-if="$accessCheck($Access.StaffDeleteStaff)"
                size="mini"
                plain
                type="danger"
                @click="deleteStaff"
              >
                批量删除
              </el-button>
            </div>
            <el-table ref="multipleTable" size="mini" :data="tableData" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="staffCode" label="工号" min-width="140"></el-table-column>
              <el-table-column prop="staffName" label="姓名" min-width="120"></el-table-column>

              <el-table-column prop="roleName" label="职位" min-width="120"></el-table-column>

              <el-table-column prop="mobile" label="手机" min-width="120"></el-table-column>
              <el-table-column prop="email" label="邮箱" show-overflow-tooltip min-width="160"></el-table-column>
              <el-table-column label="操作" width="130" fixed="right">
                <template slot-scope="scope">
                  <el-button
                    v-if="$accessCheck($Access.StaffUpdateStaff)"
                    type="text"
                    @click="openEditModel(scope.row.id)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-if="$accessCheck($Access.StaffDeleteStaff)"
                    type="text"
                    @click="deleteStaff(scope.row.id)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <FooterPage
              :page-size="staff_page_size"
              :total-page.sync="staff_total"
              :current-page.sync="staff_page"
              @pageChange="pageChange"
              @sizeChange="sizeChange"
            ></FooterPage>
          </div>
        </div>
      </el-col>
    </el-row>
    <AddDepartment
      v-if="add_department"
      :id="department_id"
      :is-edit="department_edit"
      :visible="add_department"
      @close="add_department = false"
      @confirm="departmentConfirm"
    ></AddDepartment>
    <AddStaff
      v-if="add_staff"
      :id="staff_id"
      :is-edit="staff_edit"
      :visible="add_staff"
      @close="add_staff = false"
    ></AddStaff>
    <!--    <FooterPage-->
    <!--      :page-size="page_size"-->
    <!--      :total-page="total_count"-->
    <!--      :current-page="page"-->
    <!--      @pageChange="pageChange"-->
    <!--      @sizeChange="sizeChange"-->
    <!--    />-->
  </div>
</template>

<script>
import AddDepartment from "@/component/SystemSettings/AddDepartment.vue";
import AddStaff from "@/component/SystemSettings/AddStaff.vue";
import { getAllDepartment, deleteDepartment, getAllStaff, deleteStaff } from "@/api/Department";
export default {
  components: {
    AddDepartment,
    AddStaff,
  },
  data() {
    return {
      total_count: 0,
      page: 1,
      page_size: 10,
      keyword: "",
      staff_page: 1,
      staff_page_size: 10,
      staff_total: 0,
      idArr: [],
      now_department: {},
      department_id: "", // 部门增删id
      staff_id: "", // 员工增删id
      department_edit: false,
      staff_edit: false,
      add_department: false,
      add_staff: false,
      filterText: "",
      tableData: [],
      Department_data: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  async created() {
    await this.getAllDepartment();
    await this.getAllStaff();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllStaff();
  },
  methods: {
    searchDeparment() {
      this.getAllDepartment();
    },
    // 选择部门
    selDepartment(val) {
      this.now_department = val;
      this.getAllStaff();
    },
    // 获取部门列表 getAllDepartment
    async getAllDepartment() {
      const data = await getAllDepartment({
        page: this.page,
        pageSize: this.page_size,
        keyword: this.keyword,
      });

      this.total_count = data.pageTotal;
      this.Department_data = data.data;
      this.now_department = data.data[0] || {};
    },
    delData(id) {
      this.$confirm("你确定删除此部门吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await deleteDepartment(id);

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.getAllDepartment();
      });
    },
    // 部门操作请求成功
    departmentConfirm() {
      this.getAllDepartment();
    },
    //  新增部门
    addDepartment() {
      this.department_id = "";
      this.department_edit = false;
      this.add_department = true;
    },
    // editData 编辑部门
    editData(id) {
      this.department_id = id;
      this.department_edit = true;
      this.add_department = true;
    },
    //  获取员工列表
    async getAllStaff() {
      if (!this.$accessCheck(this.$Access.StaffGetAllStaff)) {
        return;
      }
      const data = await getAllStaff({
        page: this.staff_page,
        pageSize: this.staff_page_size,
        departmentId: this.now_department.id,
      });

      this.tableData = data.data;
      this.staff_total = data.pageTotal;
    },
    // 删除员工
    deleteStaff(id) {
      this.$confirm("你确定删除此员工?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        if (id) {
          this.idArr = [id];
        }
        const data = await deleteStaff({
          id: this.idArr,
        });

        this.getAllStaff();
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    handleSelectionChange(val) {
      //  数组id
      const idArr = val.map((item) => {
        return item.id;
      });
      this.idArr = idArr;
    },
    // 编辑员工
    openEditModel(id) {
      this.staff_id = id;
      this.isEdit = true;
      this.add_staff = true;
    },
    //  新增成员addEditModel
    openModel() {
      this.staff_id = 0;
      this.isEdit = true;
      this.add_staff = true;
    },
    pageChange(page) {
      this.staff_page = page;
      this.getAllStaff();
    },
    sizeChange(size) {
      this.staff_page_size = size;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.branch-list {
  padding: 24px 12px;
}
.branch-detail {
  padding: 24px 12px;
  border-left: 1px solid #eee;
  height: calc(100vh - 150px);
}
.branch-name {
  padding-bottom: 12px;
}
.last-branch-li {
  font-size: 12px;
  line-height: 40px;
  border-bottom: 1px solid #eee;
  padding: 0 16px;
}
.last-branch-li .float_right {
  color: #9c9c9c;
  line-height: 40px;
  cursor: pointer;
}
.last-branch-tit {
  padding: 24px 0;
}
.branch-handel {
  background: #eceff4;
  padding: 8px 12px;
}
.custom-tree-node {
  width: 100%;
  font-size: 14px;
}
</style>
