<template>
  <div>
    <div style="padding: 12px; text-align: right">
      <el-button size="mini" type="primary" @click="add_group = true"> 新增岗位组 </el-button>
      <el-button size="mini" type="primary" @click="add_position = true"> 新增岗位 </el-button>
    </div>
    <el-table border size="mini" :data="tableData" style="width: 100%">
      <el-table-column prop="date" label="岗位编号" width="180" align="center"></el-table-column>
      <el-table-column prop="name" label="岗位名称" main-width="180" align="center"></el-table-column>
      <el-table-column prop="name" label="所属部门" main-width="180" align="center"></el-table-column>
      <el-table-column prop="name" label="人数统计" main-width="180" align="center"></el-table-column>

      <el-table-column prop="name" label="查看人员" main-width="180" align="center"></el-table-column>
      <el-table-column prop="name" label="岗位说明书" main-width="180" align="center">
        <template>
          <el-button size="mini" type="primary" @click="$router.push('/SystemSettings/accountAdmin/PositionExplain')">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <AddPositionGroup :visible="add_group" @close="add_group = false"></AddPositionGroup>
    <AddPosition :visible="add_position" @close="add_position = false"></AddPosition>
  </div>
</template>

<script>
import AddPositionGroup from "@/component/SystemSettings/AddPositionGroup.vue";
import AddPosition from "@/component/SystemSettings/AddPosition.vue";

export default {
  name: "Position",
  components: {
    AddPositionGroup,
    AddPosition,
  },
  data() {
    return {
      add_group: false,
      add_position: false,
      tableData: [
        {
          date: "12",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄",
        },
        {
          date: "12",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1517 弄",
        },
        {
          date: "12",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1519 弄",
        },
        {
          date: "12",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1516 弄",
        },
      ],
      data: [
        {
          label: "默认",
          children: [
            {
              label: "负责人",
            },
            {
              label: "主管",
            },
          ],
        },
        {
          label: "职务",
          children: [
            {
              label: "行政经理",
            },
            {
              label: "行政专家",
            },
          ],
        },
      ],
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
};
</script>

<style scoped>
.position-list {
  padding: 24px 12px;
}
.position-detail {
  padding: 24px 12px;
  border-left: 1px solid #eee;
  height: calc(100vh - 150px);
}
</style>
