<template>
  <ContainerQuery>
    <el-form slot="more" size="small" :inline="true">
      <el-form-item>
        <el-input
          v-model="search_key"
          placeholder="员工姓名"
          clearable
          style="width: 220px"
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <DepartmentSel v-model="departmentPidPath" width="150" clearable @change="departmentChange" />
      </el-form-item>
      <el-form-item>
        <RoleSelect v-model="rolePidPath" width="150" clearable @change="roleChange" />
      </el-form-item>
    </el-form>
    <el-button v-if="$accessCheck($Access.StaffAddStaff)" slot="left" size="small" type="primary" @click="openModel">
      新增成员
    </el-button>

    <el-table size="small" :data="tableData">
      <el-table-column prop="id" label="工号" min-width="100"></el-table-column>
      <el-table-column prop="staffCode" label="员工编码" min-width="120"></el-table-column>
      <el-table-column prop="staffName" label="姓名" min-width="180"></el-table-column>
      <el-table-column prop="mobile" label="电话" min-width="180"></el-table-column>
      <el-table-column prop="departmentName" label="所属部门" min-width="180"></el-table-column>
      <el-table-column prop="roleName" label="角色" min-width="180"></el-table-column>
      <el-table-column prop="staffType" label="员工类型" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.staffType === 1">普通员工</span>
          <span v-else-if="scope.row.staffType === 2">车载销售员工</span>
          <span v-else-if="scope.row.staffType === 3">车载销售合伙人</span>
          <span v-else>普通员工</span>
        </template>
      </el-table-column>
      <el-table-column prop="shopName" label="归属商铺" min-width="180"></el-table-column>
      <el-table-column label="操作" min-width="130" fixed="right" header->
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.DepartmentUpdateDepartment)"
            type="text"
            @click="openEditModel(scope.row.id)"
          >
            编辑
          </el-button>
          <el-button v-if="$accessCheck($Access.DepartmentDeleteDepartment)" type="text" @click="del(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="page_size"
      :total-page="total_count"
      :current-page="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    />
    <!--   新增员工-->
    <AddStaff
      v-if="add_staff"
      :id="staff_id"
      :is-edit="isEdit"
      :visible="add_staff"
      @close="add_staff = false"
      @confirm="staffConfirm"
    ></AddStaff>
  </ContainerQuery>
</template>

<script>
import { deleteStaff, getAllStaff } from "@/api/Department";
import AddStaff from "@/component/SystemSettings/AddStaff.vue";
import DepartmentSel from "@/component/common/DepartmentSel";
import RoleSelect from "../../../component/common/RoleSelect";
export default {
  name: "Department",
  components: {
    RoleSelect,
    DepartmentSel,
    AddStaff,
  },
  data() {
    return {
      roleId: "",
      search_key: "",
      departmentId: "",
      staff_id: 0,
      total_count: 0,
      page: 1,
      page_size: 10,
      pageTotal: 0,
      add_staff: false,
      id: "",
      isEdit: false,
      tableData: [],
      departmentPidPath: [],
      rolePidPath: [],
      searchForm: {
        region: "",
        role: "",
      },
    };
  },
  created() {
    this.getAllStaff();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllStaff();
  },
  methods: {
    //  选择角色
    roleChange(val) {
      this.roleId = val[val.length - 1];
      this.departmentPidPath = val.join(",");
      this.page = 1;
      this.getAllStaff();
    },
    // 选择部门
    departmentChange(val) {
      this.departmentId = val[val.length - 1];
      this.departmentPidPath = val.join(",");
      this.pageChange(1);
    },
    staffConfirm() {
      this.getAllStaff();
    },
    //  获取列表
    async getAllStaff() {
      const data = await getAllStaff({
        page: this.page,
        pageSize: this.page_size,
        departmentId: this.departmentId,
        keyword: this.search_key,
        roleId: this.roleId,
      });

      this.tableData = data.data;
      this.total_count = data.pageTotal;
    },
    // 删除用户
    del(id) {
      this.$confirm("你确定删除此员工吗?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await deleteStaff({ id: [id] });

        this.getAllStaff();
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },

    pageChange(page) {
      this.page = page;
      this.getAllStaff();
    },
    sizeChange(size) {
      this.page_size = size;
      this.pageChange(1);
    },
    //  编辑员工
    openEditModel(id) {
      this.staff_id = id;
      this.isEdit = true;
      this.add_staff = true;
    },
    openModel() {
      this.staff_id = 0;
      this.isEdit = false;
      this.add_staff = true;
    },
  },
};
</script>

<style scoped></style>
