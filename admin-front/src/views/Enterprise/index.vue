<template>
  <div class="out-box">
    <div class="top-view">
      <div class="main-content clearfix">
        <div class="logo-img float_left">
          <img src="../../assets/img/<EMAIL>" alt="logo" />
          <span class="line"></span>
          <span class="txt">企业中心</span>
        </div>
        <div class="float_right" style="color: #0a001f">
          <el-popover placement="bottom" width="200" trigger="hover">
            <div slot="reference">
              <img :src="avatar" alt="用户头像" class="user-avatar" />
              <span>{{ userName }}</span>
            </div>
            <p class="logo-out" @click="loginOut">退出登录</p>
          </el-popover>
        </div>
      </div>
    </div>
    <!--    <div class="tab">-->
    <div class="tab-list clearfix">
      <div class="tab-li" :class="[tab_index === 1 ? 'tab-on' : '']" @click="changeTab(false, 1)">企业中心</div>
      <div class="tab-li" :class="[tab_index === 2 ? 'tab-on' : '']" @click="changeTab(true, 2)">到期企业</div>
      <!-- <div class="add-btn float_right" @click="addModel">新建企业</div> -->
    </div>
    <!--    </div>-->
    <div class="e-main-div">
      <ul v-if="enterprise_list.length" class="enterprise-ul">
        <li v-for="(item, index) in enterprise_list" :key="index" class="enterprise-li">
          <div class="center-div clearfix">
            <div class="float_left">
              <img :src="item.logo" class="enter-logo" alt="" />
            </div>
            <div class="float_left enter-name">
              {{ item.enterpriseName }}
              <i class="iconfont icon-bianji" @click="editData(item)"></i>
            </div>
          </div>
          <div class="left-div">
            <div class="tag-li info-tag">
              {{ item.scope === 4 ? "单店铺" : "连锁店铺" }}
            </div>
            <div v-if="item.expireStatus" class="tag-li warning-tag">到期</div>
            <div class="tag-li success-tag">正常</div>
          </div>
          <div class="clearfix bottom-div">
            <div class="time-p float_left">
              <img src="@/assets/img/ic-yxtime.png" class="ic-yxtime" alt="" />
              {{ $_common.formatDate(item.expireTime) }}
            </div>
            <div class="float_right btn-ul" @click="goEnterprise(item)">进入企业</div>
          </div>
        </li>
      </ul>
      <div v-else class="no_enterprise-div">
        <img class="no_enterprise" src="../../assets/img/no_enterprise.png" alt="" />
        <p>
          您还没有企业，快去创建吧！
          <el-button type="text" @click="$router.push('/CreateStore')"> 立即创建 </el-button>
        </p>
      </div>
      <div class="bottom-slogin">
        ©2019- {{ fullYear }} {{ enterprise_title }} - 为传统企业量身打造的全渠道产业互联网线上线下一体化新零售营销系统!
      </div>
    </div>
    <el-dialog
      title="新建企业"
      :visible.sync="centerDialogVisible"
      width="40%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="centerDialogVisible = false"
    >
      <CreateStore
        v-if="centerDialogVisible"
        :enterprise-id="enterpriseId"
        @confirm="confirmAdd"
        @cancel="centerDialogVisible = false"
      ></CreateStore>
    </el-dialog>
  </div>
</template>

<script>
import { getAclList, getAllEnterprise, getStaffByToken } from "@/api/user";
import { recordRoute } from "@/config/settings";
import router from "@/router";
import { mapActions, mapGetters } from "vuex";
import CreateStore from "./CreateStore.vue";
export default {
  name: "SystemIndex",
  components: {
    CreateStore,
  },
  data() {
    return {
      enterpriseId: 0,
      centerDialogVisible: false,
      fullYear: new Date().getFullYear(),
      enterprise_list: [],
      enterprise_all: [],
      overdue_list: [],
      tab_index: 1,
      page: 1,
      page_size: 10,
      total_count: 0,
      user_info: "18888888888",
    };
  },
  computed: {
    ...mapGetters({
      avatar: "user/avatar",
      systemType: "MUser/systemType",
    }),
  },
  mounted() {
    this.getAllEnterprise();
  },
  methods: {
    addModel() {
      sessionStorage.removeItem("enToken");
      this.enterpriseId = 0;
      this.centerDialogVisible = true;
    },
    confirmAdd() {
      this.centerDialogVisible = false;
      this.getAllEnterprise();
    },
    ...mapActions({
      setAccessToken: "user/setAccessToken",
      setEnToken: "user/setEnToken",
      changeExpireTime: "MUser/changeExpireTime",
      changeUserName: "MUser/changeUserName",
      changeShelfLifeSetUp: "MUser/changeShelfLifeSetUp",
      changeUserCenterId: "MUser/changeUserCenterId",
      changeEnterpriseScope: "MUser/changeEnterpriseScope",
      changeEnterprise: "MUser/changeEnterprise",
    }),

    // 获取企业列表
    async getAllEnterprise() {
      const { data } = await getAllEnterprise({ source: this.systemType });
      if (data.length) {
        this.enterprise_list = data.filter((item) => {
          return !item.expireStatus;
        });
        this.overdue_list = data.filter((item) => {
          return item.expireStatus;
        });
        this.enterprise_all = data;
      }
    },
    changeTab(val, index) {
      this.enterprise_list = this.enterprise_all.filter((item) => {
        return item.expireStatus === val;
      });
      this.tab_index = index;
    },
    // 进入企业
    async goEnterprise(row) {
      const Loading = this.$baseColorfullLoading(1, "正在进入企业...");
      this.setEnToken(row.token);
      /*const { data } = await createToken({
          userCenterId: row.userCenterId,
        });
        // 重新生成token
        this.setAccessToken(data.token);*/
      // 保存店铺类型 区分单店和连锁
      this.changeEnterpriseScope(row.scope);
      this.changeEnterprise(row);
      this.changeExpireTime(row.expireTime);
      // 存储开启保质期设置状态
      this.changeShelfLifeSetUp(row.shelfLifeSetUp);

      sessionStorage.setItem("roleType", row.roleType);

      await this.$store.dispatch("user/getUserInfo", row.roleType);
      await this.getAclList(row.roleType);
      // await this.getStaffByToken(row.roleType);
      if (!this.$route.params.isCashier) {
        // await this.$router.push(`/`);
        this.$router.push(`/SelectStore`);
      } else {
        this.$router.push(`/CashierShop/CashierShop`);
      }
      Loading.close();
    },
    // 获取当前登录员工信息
    async getStaffByToken(roleType) {
      const { data } = await getStaffByToken(roleType);
      if (data.name) {
        this.changeUserName(data.name);
      }
      this.changeUserCenterId(data.userCenterId);
      if (!this.$route.params.isCashier) {
        // await this.$router.push(`/`);
        this.$router.push(`/SelectStore`);
      } else {
        this.$router.push(`/CashierShop/CashierShop`);
      }
    },
    // 获取员工权限
    async getAclList(roleType) {
      const { data } = await getAclList(roleType);
      sessionStorage.setItem("dataField", JSON.stringify(data.dataField));
      sessionStorage.setItem("isSuper", data.isAdministrator);
      sessionStorage.setItem("nodes", JSON.stringify(data.custom));

      // 进入企业设置，根据权限设置菜单
      let accessRoutes = await this.$store.dispatch("routes/setRoutes");
      router.addRoutes(accessRoutes);
    },
    editData(item) {
      sessionStorage.removeItem("enToken");
      setTimeout(() => {
        this.enterpriseId = item.enterpriseId;
        sessionStorage.setItem("enToken", item.token);
      }, 100);
      setTimeout(() => {
        this.centerDialogVisible = true;
      }, 500);
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      if (recordRoute) {
        const fullPath = this.$route.fullPath;
        this.$router.push(`/login?redirect=${fullPath}`);
      } else if (this.systemType === 3) {
        this.$router.push("/MerchantsLogin");
      } else {
        this.$router.push("/login");
      }
    },
    loginOut() {
      this.$confirm("确定要退出登录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        sessionStorage.clear();
        this.logout();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.out-box {
  background-color: #f8fafd;
  height: 100vh;
}
.top-view {
  height: 80px;
  line-height: 80px;
  position: relative;
  background-color: #ffffff;
  box-shadow: 0 2px 20px 0 rgba(202, 208, 215, 0.3);
  .main-content {
    width: 1200px;
    margin: 0 auto;
    .logo-img {
      width: 320px;
      font-size: 18px;
      img {
        height: 36px;
        display: inline-block;
        vertical-align: middle;
      }
      .line {
        margin: 0 20px;
        width: 1px;
        height: 24px;
        display: inline-block;
        background-color: #e6e6e6;
        vertical-align: middle;
        /*margin-left: 20px;*/
      }
      .txt {
        font-size: 18px;
        font-weight: 400;
        color: #000000;
        margin-top: 20px;
        vertical-align: center;
      }
    }
    .float_right {
      cursor: pointer;
      .user-avatar {
        width: 48px;
        height: 48px;
        border-radius: 100%;
        vertical-align: middle;
        margin-right: 8px;
      }
    }
  }
}

.right {
  position: relative;
  top: 10px;
  right: 10px;
}
.tab-list {
  font-weight: bold;
  margin: 36px auto 0;
  height: 64px;
  line-height: 64px;
  background-color: #ffffff;
  width: 1200px;
  padding-left: 28px;
  border-radius: 2px 2px 0px 0px;
  border-bottom: 1px solid #dee2ee;
  .add-btn {
    margin-top: 12px;
    margin-right: 20px;
    width: 103px;
    height: 40px;
    line-height: 40px;
    background: #2153d4;
    border-radius: 6px;
    color: #ffffff;
    text-align: center;
    cursor: pointer;
  }
  .tab-li {
    float: left;
    margin-right: 32px;
    font-size: 16px;
    cursor: pointer;
    color: #4f5e7b;
    font-weight: 400;
  }
  .tab-on {
    font-weight: bold;
    color: #2153d4;
    position: relative;
    &::after {
      display: block;
      content: "";
      width: 32px;
      height: 2px;
      background: #2153d4;
      border-radius: 1px;
      bottom: 0;
      left: 50%;
      transform: translate(50%, -2px);
    }
  }
}

.btn {
  float: right;
  margin-top: -7px;
}

.tab-li:hover {
  color: #2153d4;
}

.e-label {
  font-size: 24px;
  padding-top: 40px;
}
.e-main-div {
  width: 1200px;
  margin: 0 auto;
  padding-bottom: 50px;
}
.head-div {
  padding: 20px 0 40px;
  line-height: 50px;
}
.i {
  width: 24px;
  height: 26px;
  /*background: #4f5e7b;*/
}

.enterprise-ul {
  padding-top: 30px;
}
.enterprise-li {
  width: 390px;
  border-radius: 6px;
  margin-bottom: 16px;
  padding: 16px;
  margin-right: 15px;
  background-color: #ffffff;
  display: inline-block;
  &:nth-child(3n) {
    margin-right: 0;
  }
  .center-div {
    .enter-name {
      vertical-align: center;
      line-height: 40px;
      font-size: 20px;
      color: #333;
      font-weight: bold;
      .iconfont {
        font-size: 16px;
        margin-left: 16px;
        color: #6c6c6c;
        font-weight: normal;
        cursor: pointer;
        &:hover {
          color: #2153d4;
        }
      }
    }
    .enter-logo {
      width: 40px;
      height: 40px;
      object-fit: contain;
      border-radius: 100%;
      margin-right: 8px;
      border: 1px solid #ddd;
      overflow: hidden;
    }
  }

  .left-div {
    padding-left: 48px;
    padding-top: 6px;
    .tag-li {
      width: 80px;
      height: 28px;
      line-height: 28px;
      border: 1px solid #cad0d7;
      text-align: center;
      display: inline-block;
      border-radius: 3px;
      font-size: 14px;
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
      &.info-tag {
        color: #4f5e7b;
      }
      &.warning-tag {
        color: #f7bd1b;
        border-color: #f7bd1b;
        background: rgba(247, 189, 27, 0.2);
      }
      &.success-tag {
        color: #36b365;
        border-color: #36b365;
        background: rgba(54, 179, 101, 0.16);
      }
    }
  }

  .bottom-div {
    padding-top: 20px;
    .btn-ul {
      width: 103px;
      height: 36px;
      border-radius: 4px;
      text-align: center;
      border: 1px solid #2153d4;
      font-size: 14px;
      font-weight: 500;
      color: #2153d4;
      line-height: 36px;
      cursor: pointer;
      &:hover {
        background-color: #2153d4;
        color: #ffffff;
      }
    }
    .time-p {
      font-size: 16px;
      color: #2d405e;
      padding-top: 5px;
      line-height: 20px;
      font-family: DINCond-Medium, DINCond;
      .ic-yxtime {
        width: 20px;
        height: 20px;
        vertical-align: middle;
        margin-right: 6px;
        transform: translateY(-2px);
      }
    }
  }
}

.logo-out {
  text-align: center;
  cursor: pointer;
  line-height: 30px;
}
.logo-out:hover {
  background-color: #f4f4f4;
}
.bottom-slogin {
  height: 52px;
  line-height: 52px;
  font-size: 12px;
  color: #4f5e7b;
  text-align: center;
  position: fixed;
  left: 0;
  background-color: #ffffff;
  bottom: 0;
  width: 100%;
}
.no_enterprise {
  width: 200px;
  margin-bottom: 20px;
}
.no_enterprise-div {
  font-size: 14px;
  text-align: center;
  padding: 40px 0;
}
</style>
