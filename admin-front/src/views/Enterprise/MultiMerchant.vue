<template>
  <div>
    <div class="top-view">
      <div class="main-content">
        <div class="clearfix">
          <div class="float_left">
            <img src="../../assets/img/logo-white.png" class="logo-img" alt="" />
          </div>
          <div class="float_right">
            <el-popover placement="bottom" width="200" trigger="hover">
              <div slot="reference">
                <i class="el-icon-user-solid"></i>
                <span>{{ userName }}</span>
              </div>
              <p class="logo-out" @click="loginOut">退出登录</p>
            </el-popover>
          </div>
        </div>
      </div>
    </div>
    <div class="e-main-div">
      <div class="enterprise-ul">
        <div v-for="(item, index) in MerchantData" :key="index" class="enterprise-li clearfix">
          <div class="float_left left-div">
            <el-tag type="primary">商户</el-tag>
            <el-tag v-if="item.expireStatus" type="info">已打烊</el-tag>
            <el-tag v-else type="success">营业中</el-tag>
          </div>
          <div class="float_left center-div clearfix">
            <div class="float_left">
              <img :src="item.logo" class="enter-logo" alt="" />
            </div>
            <div class="float_left">
              <p class="enter-name">
                {{ item.merchantData.name }}
              </p>
            </div>
          </div>
          <!--          v-if="isSuper"-->
          <div class="float_right btn-ul">
            <el-button :loading="go_loading" type="primary" size="small" @click="changeStore(item)">
              进入后台
            </el-button>
          </div>
        </div>
      </div>

      <div class="bottom-slogin">
        ©2019- {{ fullYear }} {{ enterprise_title }} - 为传统企业量身打造的全渠道产业互联网线上线下一体化新零售营销系统!
      </div>
    </div>
  </div>
</template>

<script>
import { getStaffByToken, getAclList } from "@/api/user";
import { getShopByStaff, search } from "@/api/Shop";
import { mapActions, mapGetters } from "vuex";
import { isSuperAdmin } from "../../access/check";
import router from "@/router";
export default {
  name: "MultiMerchant",
  data() {
    return {
      fullYear: new Date().getFullYear(),
      go_loading: false,
    };
  },
  computed: {
    ...mapGetters({
      MerchantData: "MUser/MerchantData",
    }),
    isSuper() {
      return isSuperAdmin();
    },
  },
  methods: {
    ...mapActions({
      changeSystemType: "MUser/changeSystemType",
      changeStoreData: "MUser/changeStoreData",
      changeUserCenterId: "MUser/changeUserCenterId",
      changeUserName: "MUser/changeUserName",
      setEnToken: "user/setEnToken",
    }),
    // 获取当前登录员工信息
    async getStaffByToken(roleType) {
      try {
        const { data } = await getStaffByToken(roleType);
        if (data.name) {
          this.changeUserName(data.name);
        }
        this.changeUserCenterId(data.userCenterId || 0);
      } finally {
        this.go_loading = false;
      }
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      await this.$router.push("/MerchantsLogin");
    },
    loginOut() {
      this.$confirm("确定要退出登录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        sessionStorage.clear();
        this.logout();
      });
    },
    async changeStore(item) {
      this.go_loading = true;
      await this.setEnToken(item.token);
      await this.changeStoreData(item);
      await this.getStaffByToken(item.roleType);
      // 进入企业设置，根据权限设置菜单
      let accessRoutes = await this.$store.dispatch("routes/setRoutes");
      router.addRoutes(accessRoutes);
      await this.changeSystemType(3);
      await this.$router.push(`/MerchIndex`);
    },
  },
};
</script>

<style scoped lang="scss">
.top-view {
  height: 70px;
  line-height: 70px;
  position: relative;
  background: url("../../assets/img/enterprise_bg.png") no-repeat center;
}
.main-content {
  width: 1200px;
  margin: 0 auto;
  color: #ffffff;
}

.logo-img {
  vertical-align: middle;
  height: 32px;
  display: inline-block;
}
.e-label {
  font-size: 24px;
  padding-top: 40px;
}
.e-main-div {
  width: 1200px;
  margin: 0 auto;
  padding-bottom: 50px;
}
.head-div {
  padding: 20px 0 40px;
  line-height: 50px;
}
.add-btn {
  color: #fff;
  background-color: #105cfb;
  width: 200px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
}
.enterprise-ul {
  padding-top: 15px;
  margin-bottom: 20px;
}
.enterprise-li {
  border: 1px solid #f2f2f2;
  border-radius: 8px;
  margin-bottom: 20px;
  padding: 16px 26px;
  background-color: #ffffff;
}
.left-div {
  padding-top: 6px;
  padding-right: 26px;
}
.center-div {
  padding-left: 26px;
  border-left: 1px solid #ddd;
}
.enter-name {
  line-height: 43px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
}
.time-p {
  font-size: 12px;
  color: #999999;
  padding-top: 5px;
}
.enter-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 100%;
  margin-right: 10px;
  border: 1px solid #ddd;
}
.btn-ul {
  padding-top: 8px;
}

.logo-out {
  text-align: center;
  cursor: pointer;
  line-height: 30px;
}
.logo-out:hover {
  background-color: #f4f4f4;
}
.bottom-slogin {
  font-size: 12px;
  color: #666;
  text-align: center;
  position: fixed;
  left: 0;
  background-color: #ffffff;
  bottom: 0;
  padding-bottom: 20px;
  width: 100%;
  border-top: 1px solid #f2f2f2;
  padding-top: 20px;
}
</style>
