<template>
  <div>
    <div class="top-view">
      <div class="main-content">
        <div class="clearfix">
          <div class="logo-img">
            <img
              src="../../assets/img/<EMAIL>"
              style="vertical-align: middle; display: inline-block; height: 36px"
            />
            <span class="line"></span>
            <span class="txt">企业中心</span>
          </div>
          <div class="float_right" style="color: #0a001f">
            <el-popover placement="bottom" width="200" trigger="hover">
              <div slot="reference">
                <!--                <i class="el-icon-question i"></i>-->
                <!--                <i class="el-icon-refresh-right i"></i>-->
                <!--                <i class="el-icon-bell i"></i>-->
                <!--                <i class="el-icon-setting i"></i>-->
                <i class="el-icon-user-solid"></i>
                <span>{{ userName }}</span>
              </div>
              <p class="logo-out" @click="loginOut">退出登录</p>
            </el-popover>
          </div>
        </div>
      </div>
    </div>
    <div class="e-main-div">
      <div class="enterprise-ul">
        <div class="enterprise-li clearfix">
          <div class="float_left center-div clearfix">
            <div class="float_left">
              <img :src="enterprise.logo" class="enter-logo" alt="" />
            </div>
            <div class="float_left">
              <p class="enter-name">
                {{ enterprise.enterpriseName }}
              </p>
            </div>
            <div class="float_left left-div">
              <el-tag class="Chain">
                {{ enterprise.scope === 4 ? "单店铺" : "连锁店铺" }}
              </el-tag>
              <el-tag v-if="enterprise.expireStatus" type="info" style="width: 82px; height: 28px"> 已打烊 </el-tag>
              <el-tag v-else class="normal">正常</el-tag>
            </div>
          </div>
          <div class="time-p">
            <span style="margin-right: 4px">
              <img
                src="../../assets/img/ic-time.png"
                style="width: 20px; height: 20px; vertical-align: middle; transform: translateY(-2px)"
              />
            </span>
            {{ $_common.formatDate(enterprise.expireTime) }}
          </div>
          <!--          v-if="isSuper"-->
          <div class="float_right btn-ul">
            <el-button v-if="false" class="btn" size="small" @click="editData(enterprise)"> 编辑 </el-button>
            <el-button class="btn" :loading="go_loading" type="primary" size="small" @click="goEnterprise">
              进入企业后台
            </el-button>
          </div>
        </div>
        <div class="store-view">
          <div class="title clearfix">
            <span class="float_left" style="font-size: 16px">选择门店</span>
            <span class="float_right">
              <el-input
                v-model="keyword"
                style="width: 181px; border-radius: 3px"
                placeholder="搜索门店内容"
                prefix-icon="el-icon-search"
                @clear="pageChange(1)"
                @keyup.enter.native="pageChange(1)"
              ></el-input>
              <el-button type="primary" style="width: 88px; border-radius: 3px" @click="pageChange(1)">
                搜索
              </el-button>
            </span>
          </div>

          <ul v-if="store_list.length" class="store-sel-main">
            <li v-for="(item, index) in store_list" :key="index" class="store-li clearfix">
              <div class="float_left store-logo">
                <img :src="item.logo || enterprise.logo" alt="" style="border: 1px solid #979797" />
              </div>
              <div class="float_left store-info">
                <p class="store-name">
                  {{ item.name }}
                  <span v-if="item.isMaster === 5" style="color: #2153d4" type="primary" effect="dark" size="small">
                    【总店】
                  </span>
                </p>
                <p>
                  <el-tag
                    v-if="item.enableStatus === 5"
                    style="
                      background-color: rgba(54, 179, 101, 0.16);
                      border: 1px solid #36b365;
                      margin-left: 0;
                      color: #36b365;
                    "
                    class="day"
                    type="success"
                    size="small"
                    plain
                  >
                    营业中
                  </el-tag>
                  <el-tag v-else type="info" size="small" class="day"> 已打烊 </el-tag>

                  <el-tag v-if="item.merchantId" type="primary" size="small" class="day"> 入驻商户 </el-tag>
                  <el-tag
                    v-else
                    type="info"
                    size="small"
                    class="day"
                    style="background-color: #ffffff; border: 1px solid #cad0d7"
                  >
                    {{ item.shopType === 1 ? "直营商铺" : "联营商铺" }}
                  </el-tag>
                  <span v-if="item.openTime.isAllDay" class="store-time day" style="background-color: #ecf0f7">
                    全天
                  </span>
                  <span v-else class="store-time day"> {{ item.openTime.start }}-{{ item.openTime.end }} </span>
                </p>

                <!--                <p class="store-time">-->
                <!--                  &lt;!&ndash;                  <span style="padding-right: 10px">周一～周日</span>&ndash;&gt;-->
                <!--                  &lt;!&ndash;                  <span v-if="item.openTime.isAllDay">全天</span>&ndash;&gt;-->
                <!--                  &lt;!&ndash;                  <span v-else>&ndash;&gt;-->
                <!--                  &lt;!&ndash;                    {{ item.openTime.start }}-{{ item.openTime.end }}&ndash;&gt;-->
                <!--                  &lt;!&ndash;                  </span>&ndash;&gt;-->
                <!--                </p>-->
              </div>
              <div class="right">
                <div class="right_one">
                  <span v-if="item.area" style="margin-right: 50px">
                    {{ item.area.provinceName }}
                    {{ item.area.cityName }}
                    {{ item.area.districtName }}
                    {{ item.area.address }}
                  </span>
                  <span>
                    <el-button class="btn_door" @click="changeStore(item)"> 进入门店 </el-button>
                  </span>
                </div>
              </div>
            </li>
          </ul>
          <div v-else class="no_enterprise-div">
            <img class="no_enterprise" src="../../assets/img/no_enterprise.png" alt="" />
            <p>您还没有所属店铺！</p>
          </div>
          <FooterPage
            v-if="total > 10"
            :page-size="pageSize"
            :total-page.sync="total"
            :current-page.sync="page"
            @pageChange="pageChange"
            @sizeChange="sizeChange"
          ></FooterPage>
        </div>
      </div>

      <div class="bottom-slogin">
        ©2019- {{ fullYear }} {{ enterprise_title }} - 为传统企业量身打造的全渠道产业互联网线上线下一体化新零售营销系统!
      </div>
    </div>
  </div>
</template>

<script>
import { getStaffByToken, getAclList } from "@/api/user";
import { getShopByStaff, search } from "@/api/Shop";
//token失效回退到登录页时是否记录本次的路由
import { recordRoute } from "@/config/settings";
import { mapActions, mapGetters } from "vuex";
import { isSuperAdmin } from "../../access/check";
import router from "@/router";
export default {
  name: "SelectStore",
  data() {
    return {
      fullYear: new Date().getFullYear(),
      store_list: [], // 门店列表
      enterprise_list: [],
      enterprise_all: [],
      overdue_list: [],
      tab_index: 1,
      keyword: "",
      page: 1,
      pageSize: 10,
      total: 0,
      go_loading: false,
    };
  },
  computed: {
    ...mapGetters({
      enterprise: "MUser/enterprise",
    }),
    isSuper() {
      return isSuperAdmin();
    },
  },
  mounted() {
    this.getShopByStaff();
  },
  methods: {
    ...mapActions({
      changeSystemType: "MUser/changeSystemType",
      changeStoreData: "MUser/changeStoreData",
      changeUserCenterId: "MUser/changeUserCenterId",
      changeUserName: "MUser/changeUserName",
    }),

    // 进入企业
    async goEnterprise(row) {
      // await this.$router.push(`/`);
      this.go_loading = true;
      await this.getAclList(this.enterprise.roleType);
      await this.getStaffByToken();
      await this.$router.push(`/`);
    },
    // 获取当前登录员工信息
    async getStaffByToken() {
      try {
        const roleType = sessionStorage.getItem("roleType");
        const { data } = await getStaffByToken(roleType);
        if (data.name) {
          this.changeUserName(data.name);
        }
        this.changeUserCenterId(data.userCenterId || 0);
      } finally {
        this.go_loading = false;
      }
    },
    // 编辑企业
    editData(item) {
      sessionStorage.removeItem("enToken");
      setTimeout(() => {
        sessionStorage.setItem("enToken", item.token);
      }, 100);
      setTimeout(() => {
        this.$router.push(`/EditStore/${item.enterpriseId}`);
      }, 500);
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      if (recordRoute) {
        const fullPath = this.$route.fullPath;
        this.$router.push(`/login?redirect=${fullPath}`);
      } else if (this.systemType === 3) {
        this.$router.push("/MerchantsLogin");
      } else {
        this.$router.push("/login");
      }
    },
    loginOut() {
      this.$confirm("确定要退出登录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        sessionStorage.clear();
        this.logout();
      });
    },
    //  门店

    //  获取列表
    async getShopByStaff() {
      const data = await getShopByStaff({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.store_list = data.data;
      this.total = data.pageTotal;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getShopByStaff();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    async changeStore(item) {
      await this.getStaffByToken();
      // 进入企业设置，根据权限设置菜单
      let accessRoutes = await this.$store.dispatch("routes/setRoutes");
      router.addRoutes(accessRoutes);
      if (item.merchantId) {
        item.merchantData = {
          name: item.name,
          id: item.merchantId,
        };
        await this.changeStoreData(item);
        await this.changeSystemType(3);
        await this.$router.push(`/MerchIndex`);
      } else {
        await this.changeStoreData(item);
        await this.changeSystemType(2);
        await this.$router.push("/SingleStore/goods/GoodsAdministration");
      }
    },
    // 获取员工权限
    async getAclList(roleType) {
      try {
        const { data } = await getAclList(roleType);
        sessionStorage.setItem("dataField", JSON.stringify(data.dataField));
        sessionStorage.setItem("isSuper", data.isAdministrator);
        sessionStorage.setItem("nodes", JSON.stringify(data.custom));

        // 进入企业设置，根据权限设置菜单
        let accessRoutes = await this.$store.dispatch("routes/setRoutes");
        router.addRoutes(accessRoutes);
      } finally {
        this.go_loading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.Chain {
  color: #ffffff;
  width: 82px;
  height: 28px;
  background-color: #f5762c;
  margin-left: 15px;
  border: 0;
  font-size: 14px;
  display: inline-block;
  line-height: 28px;
  text-align: center;
  font-family: SourceHanSansCN-Normal, SourceHanSansCN;
}
.normal {
  font-family: SourceHanSansCN-Normal, SourceHanSansCN;
  width: 82px;
  height: 28px;
  background-color: #36b365;
  border: 0;
  font-size: 14px;
  color: #ffffff;
  display: inline-block;
  line-height: 28px;
  text-align: center;
}
.btn_door {
  width: 103px;
  height: 36px;
  border-radius: 4px;
  border: 1px solid #2153d4;
  font-size: 14px;
  color: #2153d4;
  font-weight: 500;
  text-align: center;
}
.btn_door:hover {
  background: #2153d4;
  color: #ffffff;
}
.right {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  .right_one {
    font-size: 14px;
    font-weight: 400;
    color: #2d405e;
    line-height: 21px;
  }
}
.day {
  text-align: center;
  display: inline-block;
  min-width: 80px;
  height: 28px;
  line-height: 26px;
  color: #4f5e7b;
  margin-left: 10px;
  background: #ecf0f7;
  border-radius: 3px;
  padding: 0 12px;
  font-size: 14px;
}
.top-view {
  height: 80px;
  line-height: 80px;
  position: relative;
  background-color: #ffffff;
  //background: url("../../assets/img/enterprise_bg.png") no-repeat center;
  /*background-size: 100% 100%;*/
  box-shadow: 0px 2px 20px 0px rgba(202, 208, 215, 0.3);
}
.main-content {
  width: 1200px;
  margin: 0 auto;
}
.line {
  margin: 0 20px;
  width: 1px;
  height: 24px;
  display: inline-block;
  background-color: #e6e6e6;
  vertical-align: middle;
  /*margin-left: 20px;*/
}
.txt {
  position: absolute;
  top: -20px;
  left: 218px;
  width: 72px;
  font-size: 18px;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN;
  font-weight: 400;
  color: #000000;
  /*line-height: 27px;*/
  margin-top: 20px;
  vertical-align: center;
  display: inline-block;
}
.tab-list {
  position: absolute;
  bottom: 10px;
  left: 50%;
  padding: 0 10px;
  color: #fff;
  width: 1200px;
  transform: translateX(-50%);
}
.tab-li {
  float: left;
  margin-right: 20px;
  font-size: 14px;
  cursor: pointer;
  padding-bottom: 5px;
}
.tab-on {
  border-bottom: 2px solid #ffffff;
}
.tab-li:hover {
  border-bottom: 2px solid #ffffff;
}
.logo-img {
  display: inline-block;
  width: 320px;
  font-size: 18px;
  position: relative;
}
.e-label {
  font-size: 24px;
  padding-top: 40px;
}
.e-main-div {
  width: 1200px;
  margin: 0 auto;
  padding-bottom: 50px;
}
.head-div {
  padding: 20px 0 40px;
  line-height: 50px;
}
.add-btn {
  color: #fff;
  background-color: #105cfb;
  width: 200px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
}
.enterprise-ul {
  padding-top: 15px;
}
.enterprise-li {
  //border: 1px solid #f2f2f2;
  //border-radius: 8px;
  //margin-bottom: 20px;
  //padding: 16px 26px;
  //background-color: #ffffff;
  position: relative;
  width: 1200px;
  height: 120px;
  background: #2153d4;
  border-radius: 6px;
  padding: 20px;
}
.left-div {
  padding-top: 6px;
  padding-right: 26px;
}
.enter-name {
  margin-top: 3px;
  font-size: 20px;
  color: #ffffff;
  font-weight: bold;
  margin-right: 10px;
  display: inline-block;
  line-height: 30px;
}
.time-p {
  position: absolute;
  bottom: 16px;
  left: 20px;
  //width: 90px;
  height: 18px;
  font-size: 16px;
  font-family: DINCond-Medium, DINCond;
  font-weight: 500;
  color: #ffffff;
  line-height: 18px;
  text-align: center;
  vertical-align: middle;
}
.i {
  width: 27px;
  height: 27px;
  margin-right: 10px;
}
.enter-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 100%;
  margin-right: 10px;
  border: 1px solid #ddd;
}
.btn {
  margin-top: 15px;
  width: 120px;
  height: 36px;
  color: #003c9d;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #ffffff;
}
.btn:hover {
  border: 1px solid #003c9d;
}
.btn-ul {
  padding-top: 8px;
}
.btn-li {
  display: inline-block;
  border-radius: 2px;
  text-align: center;
  line-height: 34px;
  height: 34px;
  color: #fff;
  background-color: #105cfb;
  margin-left: 10px;
  font-size: 13px;
  padding: 0 20px;
  cursor: pointer;
}
.logo-out {
  text-align: center;
  cursor: pointer;
  line-height: 30px;
}
.logo-out:hover {
  background-color: #f4f4f4;
}
.bottom-slogin {
  font-size: 12px;
  color: #666;
  text-align: center;
  position: fixed;
  left: 0;
  background-color: #ffffff;
  bottom: 0;
  padding-bottom: 20px;
  width: 100%;
  border-top: 1px solid #f2f2f2;
  padding-top: 20px;
}
.no_enterprise {
  width: 200px;
  margin-bottom: 20px;
}
.no_enterprise-div {
  font-size: 14px;
  text-align: center;
  padding: 40px 0;
  color: #666666;
}
.store-view {
  margin-top: 20px;
  border-bottom: 1px solid #f2f2f2;
  background-color: #ffffff;
  border-radius: 8px;
  .title {
    font-size: 14px;
    font-weight: bold;
    line-height: 60px;
    border-bottom: 1px solid #f2f2f2;
    padding: 0 20px;
  }
  .store-sel-main {
    padding: 16px 26px;
    .store-li {
      position: relative;
      width: 100%;
      border-bottom: 1px solid #ecf0f7;
      padding: 24px 0;
      //width: calc(50% - 10px);
      //display: inline-block;
      border-radius: 4px;
      cursor: pointer;
      &:nth-child(even) {
        margin-right: 0;
      }
      .store-logo {
        padding-right: 12px;
        img {
          border-radius: 4px;
          width: 80px;
          height: 80px;
        }
      }
      .store-info {
        width: calc(100% - 180px);
        p {
          font-size: 12px;
          color: #999999;
          &:last-child {
            padding-bottom: 0;
          }
        }
        .store-name {
          //padding-bottom: 20px;
          //color: #333333;
          //font-size: 14px;
          padding-top: 3px;
          width: 300px;
          font-size: 16px;
          font-family: SourceHanSansCN-Bold, SourceHanSansCN;
          font-weight: bold;
          color: #2d405e;
          margin-bottom: 16px;
          //margin-left: 5px;
        }
      }
    }
  }
}
</style>
