<template>
  <container-tit>
    <div slot="headr">
      <el-button :loading="sub_load" type="primary" @click="submit"> 提交保存 </el-button>
    </div>
    <el-form
      ref="add_form"
      label-position="right"
      :model="add_form"
      :rules="form_rules"
      size="small"
      label-width="100px"
    >
      <div class="detail-tab-item">
        <div class="detail-tab-title">基础信息</div>
        <div class="detail-tab-main">
          <el-form-item label="排序">
            <el-input v-model="add_form.sort" style="width: 300px" placeholder="排序"></el-input>
          </el-form-item>
          <el-form-item label="商品类型">
            <el-select v-model="add_form.category" style="width: 300px" placeholder="请选择">
              <el-option
                v-for="item in type_options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="商品名称" prop="name">
            <el-input v-model="add_form.name" style="width: 300px" placeholder="商品名称"></el-input>
          </el-form-item>
          <el-form-item label="商品图片" prop="images">
            <UploadQiniu
              :limit="10"
              :file-list="img_list"
              @uploadSuccess="uploadSuccess"
              @imgSortChange="imgSortChange"
              @handleRemove="uploadRemove"
            />
          </el-form-item>
          <el-form-item label="积分价格" prop="integral">
            <el-input-number
              v-model="add_form.integral"
              style="width: 300px"
              :controls="false"
              :min="0"
              placeholder="积分价格"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="限购" prop="integral">
            <el-input-number
              v-model="add_form.limit"
              style="width: 300px"
              :controls="false"
              :min="0"
              placeholder="限购"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="兑换说明">
            <el-input v-model="add_form.desc" type="textarea" style="width: 300px" placeholder="兑换说明"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="detail-tab-item">
        <p class="detail-tab-title">商品详情</p>
        <div class="detail-tab-main">
          <Tinymce v-model="add_form.details" :height="300" />
        </div>
      </div>
    </el-form>
  </container-tit>
</template>

<script>
import Tinymce from "@/component/Tinymce";
import { getIntegralGoodsInfo, addIntegralGoods, updateIntegralGoods } from "@/api/Integral";
import UploadQiniu from "@/component/common/UploadQiniu.vue";
export default {
  name: "PointGoodsAdd",
  components: {
    Tinymce,
    UploadQiniu,
  },
  data() {
    const integral = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入积分价格"));
      } else if (value <= 0) {
        callback(new Error("积分价格需大于0!"));
      } else {
        callback();
      }
    };
    const limit = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入限购数量"));
      } else if (value <= 0) {
        callback(new Error("限购数量需大于0!"));
      } else {
        callback();
      }
    };
    const name = (rule, value, callback) => {
      if (!value.replace(/\s+/g, "")) {
        callback(new Error("请输入商品名称"));
      } else {
        callback();
      }
    };
    return {
      goods_id: "",
      sub_load: false,
      show_goods: false,
      form_rules: {
        name: [{ required: true, validator: name, trigger: "blur" }],
        integral: [{ required: true, validator: integral, trigger: "blur" }],
        limit: [{ required: true, validator: limit, trigger: "blur" }],
        images: [
          {
            type: "array",
            required: true,
            message: "请至少选择一张商品图片",
            trigger: "change",
          },
        ],
      },
      goods_data: {},
      img_list: [],
      add_form: {
        name: "",
        details: "",
        category: 5,
        images: [],
        integral: 0,
        sort: "",
        limit: 0,
        num: 0,
        desc: "",
      },
      type_options: [
        {
          label: "实物商品",
          value: 5,
        },
        {
          label: "虚拟商品",
          value: 4,
        },
      ],
    };
  },
  created() {
    if (this.$route.query.id) {
      this.goods_id = this.$route.query.id;
      this.getIntegralGoodsInfo();
    }
  },
  activated() {
    if (this.$_isInit()) return;
    if (this.$route.query.id) {
      this.goods_id = this.$route.query.id;
      this.getIntegralGoodsInfo();
    }
  },
  methods: {
    categoryChange() {},
    openGoodsModel() {
      this.show_goods = true;
    },
    // 获取商品详情
    async getIntegralGoodsInfo(id) {
      const { data } = await getIntegralGoodsInfo(this.goods_id);
      this.add_form = {
        name: data.name,
        category: data.category,
        images: data.images,
        integral: data.integral,
        sort: data.sort,
        limit: data.limit,
        num: data.num,
        desc: data.desc,
        details: data.details,
      };
      this.img_list = this.add_form.images.map((item) => {
        return {
          name: "",
          content: item,
        };
      });
    },
    // 选择图片
    uploadSuccess(val, res, file, fileList) {
      const imgArr = fileList.map((item) => {
        return item.content;
      });

      if (this.add_form.images.length) {
        this.add_form.images = this.base_form.images.concat(imgArr);
      } else {
        this.add_form.images = imgArr;
      }
      this.img_list = this.add_form.images.map((item) => {
        return {
          name: "",
          content: item,
        };
      });
    },
    // 图片拖拽排序
    imgSortChange(list) {
      this.add_form.images = list.map((item) => {
        return item.content;
      });
    },
    uploadRemove(file, fileList) {
      this.add_form.images = fileList.map((item) => {
        return item.content;
      });
    },
    async submit() {
      this.$refs["add_form"].validate(async (valid) => {
        if (valid) {
          this.sub_load = true;
          try {
            if (!this.goods_id) {
              const { data } = await addIntegralGoods(this.add_form);
              this.$message.success("新增成功");
            } else {
              const { data } = await updateIntegralGoods(this.goods_id, this.add_form);
              this.$message.success("修改成功");
            }
            this.sub_load = false;
            this.$closeCurrentGoEdit("/PointsMall/GoodsManage");
          } finally {
            this.sub_load = false;
          }
        }
      });
    },
  },
};
</script>

<style scoped></style>
