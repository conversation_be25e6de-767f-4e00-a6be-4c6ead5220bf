<template>
  <Container>
    <div slot="left">
      <el-input
        v-model="keyword"
        style="width: 220px"
        placeholder="商品名称/购买人"
        clearable
        @keyup.enter.native="pageChange(1)"
        @clear="pageChange(1)"
      >
        <el-button slot="append" @click="pageChange(1)">
          <i class="el-icon-search"></i>
        </el-button>
      </el-input>
    </div>
    <el-table ref="exchangeData" :data="goods_list">
      <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
      <el-table-column prop="no" label="流水号" min-width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="integralGoodsName" label="商品" fixed="left" min-width="160">
        <template slot-scope="scope">
          <div class="clearfix">
            <div class="float_left">
              <el-image fit="cover" :src="scope.row.images[0]"></el-image>
            </div>
            <div class="float_left goods-name-view">
              {{ scope.row.integralGoodsName }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="categoryName" label="商品类型" min-width="120"></el-table-column>
      <el-table-column prop="integral" label="兑换价格" min-width="120"></el-table-column>
      <el-table-column prop="num" label="兑换数量" min-width="120"></el-table-column>
      <el-table-column prop="amount" label="兑换总价" min-width="120"></el-table-column>
      <el-table-column prop="categoryName" label="兑换时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="购买人" min-width="120"></el-table-column>
      <el-table-column show-overflow-tooltip label="收货地址" min-width="160" prop="adress">
        <template slot-scope="scope">
          {{
            scope.row.address.area.provinceName +
            "-" +
            scope.row.address.area.cityName +
            "-" +
            scope.row.address.area.districtName +
            "-" +
            scope.row.address.area.address
          }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="categoryName"
        label="返回积分"
        min-width="120"
      ></el-table-column>-->
      <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="status" label="状态" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 5" class="success-status"> 已完成 </span>
          <span v-else-if="scope.row.status === 4" class="warning-status"> 待发货 </span>
          <span v-else-if="scope.row.status === 6" class="info-status"> 已失效 </span>
        </template>
      </el-table-column>
      <el-table-column prop="categoryName" label="操作" min-width="120" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.ExchangeRecordShipExchangeRecord)"
            type="text"
            :disabled="scope.row.status !== 4"
            @click="updateIntegralGoodsExchange(scope.row.id, 5)"
          >
            发货
          </el-button>
          <el-button
            v-if="$accessCheck($Access.ExchangeRecordCancelExchangeRecord)"
            type="text"
            :disabled="scope.row.status !== 4"
            @click="updateIntegralGoodsExchange(scope.row.id, 6)"
          >
            作废
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div slot="btn-div" class="foot-btn-div">
        <span class="batch-checkbox">
          <el-checkbox v-model="checkedAll" @change="checkAllChange"></el-checkbox>
        </span>
        <el-button size="mini" @click="batchUpdateAuditStatus"> 批量发货 </el-button>
      </div>
    </FooterPage>
  </Container>
</template>

<script>
import { getAllIntegralGoodsExchange, updateIntegralGoodsExchange } from "@/api/Integral";
export default {
  name: "ExchangeRecord",
  data() {
    return {
      pageSize: 10,
      page: 1,
      total: 0,
      keyword: "",
      checkedAll: false,
      goods_list: [],
    };
  },
  created() {
    this.getAllIntegralGoodsExchange();
  },
  methods: {
    async getAllIntegralGoodsExchange() {
      const { data, pageTotal } = await getAllIntegralGoodsExchange({
        search: this.keyword,
        page: this.page,
        pageSize: this.pageSize,
      });
      this.goods_list = data;
      this.total = pageTotal;
    },
    checkAllChange() {
      this.$refs.exchangeData.toggleAllSelection();
    },
    editGoods() {},
    batchUpdateAuditStatus() {},
    updateIntegralGoodsExchange(id, status) {
      this.$confirm(`确认要将该条兑换记录进行${status === 5 ? "发货" : "作废"}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateIntegralGoodsExchange(id, {
          status: status,
        });
        // console.log(dat);
        this.$message.success("操作成功");
        this.getAllIntegralGoodsExchange();
      });
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllIntegralGoodsExchange();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
