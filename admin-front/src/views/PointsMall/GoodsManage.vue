<template>
  <ContainerQuery>
    <div v-if="$accessCheck($Access.PointsGoodsManageAddPointsGoods)" slot="left">
      <el-button size="small" type="primary" @click="$router.push('/PointsMall/PointGoodsAdd')"> 新增商品 </el-button>
    </div>
    <div slot="more">
      <el-input
        v-model="keyword"
        style="width: 220px"
        placeholder="商品名称"
        clearable
        @keyup.enter.native="pageChange(1)"
        @clear="pageChange(1)"
      >
        <el-button slot="append" @click="pageChange(1)">
          <i class="el-icon-search"></i>
        </el-button>
      </el-input>
    </div>
    <el-table :data="goods_list">
      <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
      <el-table-column prop="materialName" label="商品" fixed="left" min-width="220">
        <template slot-scope="scope">
          <div class="clearfix">
            <div class="float_left">
              <el-image fit="cover" :src="scope.row.images[0]"></el-image>
            </div>
            <div class="float_left goods-name-view">
              {{ scope.row.name }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="integral" label="积分价格" min-width="120"></el-table-column>
      <!--      <el-table-column-->
      <!--        prop="num"-->
      <!--        label="可兑换数量"-->
      <!--        min-width="120"-->
      <!--      ></el-table-column>-->
      <el-table-column prop="changeNum" label="已兑换数量" min-width="120"></el-table-column>
      <el-table-column prop="limit" label="每人限兑" min-width="120"></el-table-column>
      <el-table-column prop="sort" label="排序" min-width="120"></el-table-column>
      <el-table-column prop="createTime" label="添加时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="categoryName" label="状态" min-width="120">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.PointsGoodsManageStatusUpdate)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="上架"
            inactive-text="下架"
            @change="changeGoodsStatus($event, scope.row.id)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 上架 </span>
            <span v-else class="info-status">下架</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="categoryName" label="操作" min-width="120">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.PointsGoodsManageDeletePpintsGoods)"
            type="text"
            @click="delGoods(scope.row.id)"
          >
            删除
          </el-button>
          <el-button
            v-if="$accessCheck($Access.PointsGoodsManageEditPointsGoods)"
            type="text"
            @click="editGoods(scope.row.id)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import { getAllIntegralGoods, enableIntegralGoods, deleteIntegralGoods } from "@/api/Integral";
export default {
  name: "GoodsManage",
  data() {
    return {
      pageSize: 10,
      page: 1,
      total: 0,
      keyword: "",
      goods_list: [],
    };
  },
  created() {
    this.getAllIntegralGoods();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllIntegralGoods();
  },
  methods: {
    async getAllIntegralGoods() {
      const { data, pageTotal } = await getAllIntegralGoods({
        search: this.keyword,
        page: this.page,
        pageSize: this.pageSize,
      });
      this.goods_list = data;
      this.total = pageTotal;
    },
    editGoods(id) {
      this.$router.push("/PointsMall/PointGoodsEdit?id=" + id);
    },
    async changeGoodsStatus(val, id) {
      const data = await enableIntegralGoods(id);

      this.$message.success("操作成功");
      this.getAllIntegralGoods();
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllIntegralGoods();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    delGoods(id) {
      this.$confirm("确定要删除该商品吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await deleteIntegralGoods(id);

        this.$message.success("删除成功");
        this.getAllIntegralGoods();
      });
    },
  },
};
</script>

<style scoped></style>
