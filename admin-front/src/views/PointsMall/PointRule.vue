<template>
  <ContainerQuery>
    <div v-if="$accessCheck($Access.PointRuleAddPointRule)" slot="left">
      <el-button size="small" type="primary" @click="$router.push('/PointsMall/PointRuleAdd')"> 新增规则 </el-button>
    </div>
    <div slot="more">
      <el-form size="small" :inline="true">
        <el-form-item>
          <el-input
            v-model="search_form.keyword"
            style="width: 220px"
            placeholder="积分规则"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" @click="pageChange(1)">
              <i class="el-icon-search"></i>
            </el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="创建开始时间"
            end-placeholder="创建结束时间"
            @change="changeTime"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select v-model="search_form.enableStatus" placeholder="启用/禁用 状态" clearable @change="pageChange(1)">
            <el-option
              v-for="item in status_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="rule_list">
      <el-table-column prop="title" label="积分规则" show-overflow-tooltip min-width="155"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="140">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="integralTotal" label="已发放积分" min-width="140"></el-table-column>
      <el-table-column prop="name" label="状态" min-width="120">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.PointRuleStatusPointRule)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="enableIntegralRule($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="info-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="操作" min-width="120">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.PointRuleEditPointRule)"
            type="text"
            @click="$router.push('/PointsMall/PointRuleEdit?id=' + scope.row.id)"
          >
            修改
          </el-button>
          <el-button v-if="$accessCheck($Access.PointRuleDelPointRule)" type="text" @click="delData(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>

<script>
import { enableIntegralRule, getAllIntegralRule, deleteIntegralRule } from "@/api/Integral";
export default {
  name: "PointRule",
  data() {
    return {
      rule_list: [],
      time: [],
      search_form: {
        keyword: "",
        start: "",
        end: "",
        enableStatus: "",
      },
      status_options: [
        {
          label: "启用",
          value: 5,
        },
        {
          label: "禁用",
          value: 4,
        },
      ],
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  created() {
    this.getAllIntegralRule();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllIntegralRule();
  },
  methods: {
    //  订单时间
    changeTime(val) {
      if (val && val.length) {
        this.search_form.start = val[0] / 1000;
        this.search_form.end = val[1] / 1000 + 86399;
      } else {
        this.search_form.start = "";
        this.search_form.end = "";
      }
      this.pageChange(1);
    },
    pageChange(page) {
      this.page = page;
      this.getAllIntegralRule();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    editData(index) {},
    async getAllIntegralRule() {
      const { data, pageTotal } = await getAllIntegralRule({
        page: this.page,
        pageSize: this.pageSize,
        search: this.search_form.keyword,
        enableStatus: this.search_form.enableStatus,
        star: this.search_form.start,
        end: this.search_form.end,
      });
      this.rule_list = data;
      this.total = pageTotal;
    },
    // 删除
    async delData(id) {
      this.$confirm("是否要将该积分规则删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await deleteIntegralRule(id);

        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.getAllIntegralRule();
      });
    },
    // 状态
    async enableIntegralRule(val, row) {
      try {
        const data = await enableIntegralRule(row.id);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.getAllIntegralRule();
      } catch (e) {
        this.getAllIntegralRule();
      }
    },
  },
};
</script>

<style scoped></style>
