<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" @click="setSubmit">保存</el-button>
    </div>
    <div style="background-color: #fff; padding: 20px">
      <el-form ref="basicData" :model="basicData" label-width="180px" size="small">
        <el-form-item label="启用代销功能：">
          <el-switch
            v-model="basicData.consignmentSettings.enableConsignment"
            active-text="启用"
            inactive-text="禁用"
          ></el-switch>
          <div class="form-tip">启用后，供应商可以使用代销功能</div>
        </el-form-item>

        <template v-if="basicData.consignmentSettings.enableConsignment">
          <el-form-item label="保证金要求：">
            <el-switch
              v-model="basicData.consignmentSettings.depositRequired"
              active-text="需要"
              inactive-text="不需要"
            ></el-switch>
            <div class="form-tip">启用后，供应商需要缴纳保证金才能使用代销功能</div>
          </el-form-item>

          <el-form-item v-if="basicData.consignmentSettings.depositRequired" label="最低保证金额：">
            <el-input-number
              v-model="basicData.consignmentSettings.minDepositAmount"
              :min="0"
              :precision="2"
              :step="100"
              style="width: 180px"
            >
              <template slot="append">元</template>
            </el-input-number>
            <div class="form-tip">供应商需要缴纳的最低保证金金额</div>
          </el-form-item>


        </template>
      </el-form>
    </div>
  </ContainerTit>
</template>

<script>
import { getBasicSetup, setting } from "@/api/System";

export default {
  name: "ConsignmentPermission",
  data() {
    return {
      basicData: {
        consignmentSettings: {
          enableConsignment: false,
          depositRequired: false,
          minDepositAmount: 1000,
        },
      },
    };
  },
  created() {
    this.getBasicSetup();
  },
  methods: {
    // 获取详情
    async getBasicSetup() {
      try {
        const { data } = await getBasicSetup();

        // 参考 BaseSet.vue 的数据合并方式
        this.basicData = { ...this.basicData, ...data.basicData };

        // 确保 consignmentSettings 存在并合并
        if (data && data.basicData && data.basicData.consignmentSettings) {
          this.basicData.consignmentSettings = {
            ...this.basicData.consignmentSettings,
            ...data.basicData.consignmentSettings,
          };
        }
      } catch (error) {
        this.$message.error("获取设置失败");
        console.error(error);
      }
    },

    // 提交
    async setSubmit() {
      try {
        await setting({
          basicData: this.basicData,
        });

        this.$message.success("保存成功");

        // 重新获取数据确保同步
        await this.getBasicSetup();
      } catch (error) {
        this.$message.error("保存失败：" + (error.message || "未知错误"));
        console.error(error);
      }
    },
  },
};
</script>

<style scoped>
.form-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}
</style>
