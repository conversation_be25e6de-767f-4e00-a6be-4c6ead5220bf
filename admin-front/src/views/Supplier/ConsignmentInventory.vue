<template>
  <ContainerQuery>
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <el-input
          v-model="queryParams.keyword"
          clearable
          style="width: 320px"
          placeholder="请输入商品名称/编码"
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.supplierId"
          filterable
          remote
          reserve-keyword
          placeholder="请输入供应商名称"
          :remote-method="remoteSupplierMethod"
          :loading="supplierLoading"
          clearable
          style="width: 220px"
          @change="handleSupplierChange"
        >
          <el-option
            v-for="item in supplierOptions"
            :key="item.supplierId"
            :label="item.title"
            :value="item.supplierId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.warehouseId"
          placeholder="请选择仓库"
          clearable
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="全部" value=""></el-option>
          <el-option
            v-for="warehouse in warehouseOptions"
            :key="warehouse.id"
            :label="warehouse.name"
            :value="warehouse.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.status"
          placeholder="请选择库存状态"
          clearable
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="正常" value="normal"></el-option>
          <el-option label="库存不足" value="low"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="inventoryList" style="width: 100%">
      <el-table-column prop="id" label="库存ID" width="80" align="center"></el-table-column>
      <el-table-column prop="basicGoodsCode" label="商品编码" width="140" show-overflow-tooltip></el-table-column>
      <el-table-column label="商品信息" min-width="250">
        <template slot-scope="scope">
          <div class="product-info">
            <el-image
              v-if="scope.row.images && scope.row.images.length > 0"
              :src="scope.row.images[0]"
              :preview-src-list="scope.row.images"
              class="product-image"
              fit="cover"
            ></el-image>
            <div class="product-details">
              <div class="basic-goods-name">{{ scope.row.basicGoodsName }}</div>
              <div class="sku-name">{{ scope.row.skuName }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="supplierName" label="供应商" min-width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="warehouseName" label="仓库" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="num" label="库存数量" width="120" align="center">
        <template slot-scope="scope">
          <span :class="{ 'text-red': parseFloat(scope.row.num) < 10 }">
            {{ $_common.formatNub(scope.row.num) }} {{ scope.row.unitName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="库存状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getInventoryStatusType(scope.row)">{{ getInventoryStatusText(scope.row) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="160" align="center">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.updateTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template slot-scope="scope">
          <div style="height: 34px; line-height: 34px">
            <el-button type="text" @click="handleViewHistory(scope.row)">库存记录</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="queryParams.pageSize"
      :total-page.sync="total"
      :current-page.sync="queryParams.pageIndex"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    />
  </ContainerQuery>
</template>

<script>
import { getConsignmentInventory, getConsignmentSuppliers } from "@/api/SupplierConsignment";

export default {
  name: "ConsignmentInventory",

  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 库存列表
      inventoryList: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        supplierId: null,
        keyword: "",
        warehouseId: null,
        status: "",
      },
      // 供应商选项
      supplierOptions: [],
      // 供应商加载状态
      supplierLoading: false,
      // 当前选中的供应商名称
      currentSupplierName: "",
      // 仓库选项
      warehouseOptions: [],
    };
  },
  created() {
    this.getData();
    this.remoteSupplierMethod("");
    this.getWarehouseList();
  },
  methods: {
    // 获取库存列表
    getData() {
      this.loading = true;
      getConsignmentInventory(this.queryParams)
        .then((response) => {
          if (response.state) {
            this.inventoryList = response.data || [];
            this.total = response.pageTotal || 0;
          } else {
            this.$message.error(response.message || "获取库存列表失败");
            this.inventoryList = [];
            this.total = 0;
          }
          this.loading = false;
        })
        .catch((error) => {
          console.error("获取库存列表失败:", error);
          this.$message.error("获取库存列表失败");
          this.inventoryList = [];
          this.total = 0;
          this.loading = false;
        });
    },

    // 获取仓库列表
    getWarehouseList() {
      // 这里应该调用获取仓库列表的API
      // 暂时使用模拟数据
      this.warehouseOptions = [
        { id: 5, name: "升辉配件-临沂仓" },
        { id: 6, name: "升辉配件-北京仓" },
        { id: 7, name: "升辉配件-上海仓" },
      ];
    },

    // 远程搜索供应商
    remoteSupplierMethod(query) {
      this.supplierLoading = true;
      getConsignmentSuppliers({
        keyword: query,
        page: 1,
        pageSize: 20,
      })
        .then((response) => {
          this.supplierOptions = response.data;
          this.supplierLoading = false;
        })
        .catch(() => {
          this.supplierLoading = false;
        });
    },

    // 供应商选择变更
    handleSupplierChange(supplierId) {
      if (supplierId) {
        const supplier = this.supplierOptions.find((item) => item.supplierId === supplierId);
        if (supplier) {
          this.currentSupplierName = supplier.title;
        }
      }
      this.pageChange(1);
    },

    // 切页
    pageChange(val) {
      this.queryParams.pageIndex = val;
      this.getData();
    },

    // 每页数据大小改变
    sizeChange(val) {
      this.queryParams.pageSize = val;
      this.pageChange(1);
    },

    // 查看库存历史
    handleViewHistory(row) {
      this.$router.push({
        path: "/Supplier/InventoryFlowing",
        query: {
          supplierId: row.supplierId,
          skuId: row.skuId,
        },
      });
    },

    // 获取库存状态类型
    getInventoryStatusType(row) {
      const num = parseFloat(row.num);

      if (num < 10) {
        return "danger"; // 库存不足
      } else {
        return "success"; // 正常
      }
    },

    // 获取库存状态文本
    getInventoryStatusText(row) {
      const num = parseFloat(row.num);

      if (num < 10) {
        return "库存不足";
      } else {
        return "正常";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.product-info {
  display: flex;
  align-items: center;

  .product-image {
    width: 50px;
    height: 50px;
    margin-right: 12px;
    border-radius: 4px;
    flex-shrink: 0;
  }

  .product-details {
    flex: 1;
    min-width: 0;

    .basic-goods-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .sku-name {
      font-size: 12px;
      color: #909399;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.text-red {
  color: #f56c6c;
}
</style>
