<template>
  <Container>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示：
      1、提现申请提交后将进入审核流程，请耐心等待；
      2、审核通过后将进入打款流程，实际到账时间以银行处理为准；
      3、审核状态为"申请中"的提现申请可以取消。
    </div>

    <div slot="left">
      <el-button type="primary" size="small" @click="goToApply">申请提现</el-button>
    </div>

    <div slot="right">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="queryParams.keyword"
            placeholder="提现单号"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleQuery"
            @clear="handleQuery"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="queryParams.type" placeholder="提现方式" clearable @change="handleQuery">
            <el-option label="全部" value=""></el-option>
            <el-option label="微信钱包" :value="1"></el-option>
            <el-option label="支付宝" :value="2"></el-option>
            <el-option label="银行卡" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="queryParams.auditStatus" placeholder="审核状态" clearable @change="handleQuery">
            <el-option label="全部" value=""></el-option>
            <el-option label="申请中" :value="1"></el-option>
            <el-option label="已打款" :value="2"></el-option>
            <el-option label="已拒绝" :value="3"></el-option>
            <el-option label="审核通过" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="timestamp"
            @change="handleDateChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="withdrawList"
      style="width: 100%"
    >
      <el-table-column prop="no" label="提现单号" width="180"></el-table-column>
      <el-table-column prop="money" label="申请金额" width="120">
        <template slot-scope="scope">
          ¥{{ formatMoney(scope.row.money) }}
        </template>
      </el-table-column>
      <el-table-column prop="nowMoney" label="预计到账金额" width="120">
        <template slot-scope="scope">
          ¥{{ formatMoney(scope.row.nowMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="type" label="提现方式" width="100">
        <template slot-scope="scope">
          {{ formatWithdrawType(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="accountContent" label="账户信息" min-width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.accountContent">
            <p v-if="scope.row.type === 1">
              微信账号: {{ getAccountInfo(scope.row.accountContent, 'account') }}<br>
              姓名: {{ getAccountInfo(scope.row.accountContent, 'realName') }}
            </p>
            <p v-else-if="scope.row.type === 2">
              支付宝账号: {{ getAccountInfo(scope.row.accountContent, 'account') }}<br>
              姓名: {{ getAccountInfo(scope.row.accountContent, 'realName') }}
            </p>
            <p v-else-if="scope.row.type === 3">
              银行: {{ getAccountInfo(scope.row.accountContent, 'bankName') }}<br>
              卡号: {{ getAccountInfo(scope.row.accountContent, 'account') }}<br>
              姓名: {{ getAccountInfo(scope.row.accountContent, 'realName') }}<br>
              支行: {{ getAccountInfo(scope.row.accountContent, 'branchName') }}
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="auditStatus" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.auditStatus)">
            {{ formatStatus(scope.row.auditStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="申请时间" width="160">
        <template slot-scope="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="auditTime" label="审核时间" width="160">
        <template slot-scope="scope">
          {{ scope.row.auditTime ? formatDate(scope.row.auditTime) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="payTime" label="打款时间" width="160">
        <template slot-scope="scope">
          {{ scope.row.payTime ? formatDate(scope.row.payTime) : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template slot-scope="scope">
          <div style="height: 34px; line-height: 34px">
            <el-button
              v-if="scope.row.auditStatus === 1"
              type="text"
              @click="handleCancel(scope.row)"
            >
              取消申请
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <FooterPage
      :page-size="queryParams.pageSize"
      :total-page.sync="total"
      :current-page.sync="queryParams.page"
      @pageChange="handlePageChange"
      @sizeChange="handleSizeChange"
    ></FooterPage>

    <!-- 取消申请对话框 -->
    <el-dialog title="取消提现申请" :visible.sync="cancelDialogVisible" width="500px">
      <el-form :model="cancelForm" :rules="cancelRules" ref="cancelForm" label-width="100px">
        <el-form-item label="取消原因" prop="reason">
          <el-input
            v-model="cancelForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入取消原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmCancel" :loading="cancelLoading">确 定</el-button>
      </div>
    </el-dialog>
  </Container>
</template>

<script>
import { cancelWithdraw, getSupplierWithdrawList } from "@/api/SupplierFinance";
import FooterPage from "@/component/common/FooterPage";

export default {
  name: "WithdrawList",
  components: {
    FooterPage,
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        keyword: "",
        type: "",
        auditStatus: "",
        startTime: "",
        endTime: "",
      },
      dateRange: [],
      loading: false,
      withdrawList: [],
      total: 0,

      // 取消申请相关
      cancelDialogVisible: false,
      cancelLoading: false,
      currentWithdraw: null,
      cancelForm: {
        reason: "",
      },
      cancelRules: {
        reason: [
          { required: true, message: "请输入取消原因", trigger: "blur" },
          { max: 200, message: "取消原因不能超过200个字符", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取提现列表
    async getList() {
      this.loading = true;
      try {
        const res = await getSupplierWithdrawList(this.queryParams);
        if (res.errorcode === 0) {
          this.withdrawList = res.data || [];
          this.total = res.pageTotal || 0;
        } else {
          this.$message.error(res.message || "获取提现列表失败");
        }
      } catch (error) {
        console.error("获取提现列表失败", error);
        this.$message.error("获取提现列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 查询
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },

    // 日期范围变化
    handleDateChange(val) {
      if (val) {
        this.queryParams.startTime = val[0] / 1000;
        this.queryParams.endTime = val[1] / 1000 + 86399;
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.handleQuery();
    },

    // 页码变化
    handlePageChange(page) {
      this.queryParams.page = page;
      this.getList();
    },

    // 每页条数变化
    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.queryParams.page = 1;
      this.getList();
    },

    // 跳转到申请页面
    goToApply() {
      this.$router.push("/Supplier/WithdrawApply");
    },

    // 取消申请
    handleCancel(row) {
      this.currentWithdraw = row;
      this.cancelForm.reason = "";
      this.cancelDialogVisible = true;
    },

    // 确认取消申请
    confirmCancel() {
      this.$refs.cancelForm.validate(async (valid) => {
        if (!valid) {
          return;
        }

        this.cancelLoading = true;
        try {
          const res = await cancelWithdraw(this.currentWithdraw.id, {
            reason: this.cancelForm.reason,
          });

          if (res.errorcode === 0) {
            this.$message.success("取消申请成功");
            this.cancelDialogVisible = false;
            this.getList();
          } else {
            this.$message.error(res.message || "取消申请失败");
          }
        } catch (error) {
          console.error("取消申请失败", error);
          this.$message.error("取消申请失败");
        } finally {
          this.cancelLoading = false;
        }
      });
    },

    // 格式化提现方式
    formatWithdrawType(type) {
      const types = {
        1: "微信钱包",
        2: "支付宝",
        3: "银行卡",
      };
      return types[type] || "未知";
    },

    // 格式化状态
    formatStatus(status) {
      const statuses = {
        1: "申请中",
        2: "已打款",
        3: "已拒绝",
        4: "审核通过",
      };
      return statuses[status] || "未知";
    },

    // 获取状态标签类型
    getStatusType(status) {
      const types = {
        1: "warning",
        2: "success",
        3: "danger",
        4: "primary",
      };
      return types[status] || "info";
    },

    // 格式化金额
    formatMoney(value) {
      return parseFloat(value).toFixed(2);
    },

    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return "-";
      return this.$_common.formatDate(timestamp * 1000);
    },

    // 获取账户信息
    getAccountInfo(accountContent, field) {
      if (!accountContent) return "-";
      try {
        const content = typeof accountContent === "string" ? JSON.parse(accountContent) : accountContent;
        return content[field] || "-";
      } catch (error) {
        return "-";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.el-tag {
  margin-right: 5px;
}
</style>
