<template>
  <ContainerQuery>
    <div slot="tip" class="page-tip-div" style="margin-top: 0">
      <i class="el-icon-info"></i>
      <span>温馨提示：</span>
      <span>1、此页面提供供应商结算数据的统计分析和报表功能；</span>
      <span>2、支持多维度数据统计，包括时间趋势、状态分布、供应商排行等；</span>
      <span>3、可通过筛选条件进行精准的数据分析和对比。</span>
    </div>
    <div slot="left">
      <el-button type="success" @click="exportData">导出统计报表</el-button>
      <el-button type="primary" plain @click="refreshData">刷新数据</el-button>
    </div>
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <SelectSupplier
          v-model="queryParams.supplierId"
          placeholder="请选择供应商"
          @clear="clearSupplier"
          @change="handleSupplierChange"
          @keyup.enter.native="handleQuery"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
        </SelectSupplier>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.settlementStatus"
          placeholder="请选择结算状态"
          clearable
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="待结算" :value="1"></el-option>
          <el-option label="结算中" :value="2"></el-option>
          <el-option label="已结算" :value="3"></el-option>
          <el-option label="结算失败" :value="4"></el-option>
          <el-option label="待审核" :value="5"></el-option>
          <el-option label="已审核" :value="6"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.period"
          placeholder="请选择统计周期"
          clearable
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="日" value="day"></el-option>
          <el-option label="周" value="week"></el-option>
          <el-option label="月" value="month"></el-option>
          <el-option label="季度" value="quarter"></el-option>
          <el-option label="年" value="year"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="timestamp"
          style="width: 220px"
          @change="handleDateRangeChange"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>

    <!-- 趋势分析控制面板 -->
    <div class="trend-control-panel">
      <el-card shadow="never" style="margin-bottom: 20px">
        <div slot="header" class="clearfix">
          <span style="font-weight: bold">趋势分析配置</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="toggleTrendPanel">
            {{ showTrendPanel ? "收起" : "展开" }}
          </el-button>
        </div>
        <div v-show="showTrendPanel">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="分析维度">
                <el-select
                  v-model="trendConfig.dimension"
                  placeholder="选择分析维度"
                  style="width: 100%"
                  @change="changeTrendDimension"
                >
                  <el-option label="结算金额" value="amount"></el-option>
                  <el-option label="订单数量" value="count"></el-option>
                  <el-option label="供应商参与度" value="supplier"></el-option>
                  <el-option label="结算效率" value="efficiency"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="时间周期">
                <el-select
                  v-model="trendConfig.period"
                  placeholder="选择时间周期"
                  style="width: 100%"
                  @change="changeTrendPeriod"
                >
                  <el-option label="按日" value="day"></el-option>
                  <el-option label="按周" value="week"></el-option>
                  <el-option label="按月" value="month"></el-option>
                  <el-option label="按季度" value="quarter"></el-option>
                  <el-option label="按年" value="year"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="数据点数">
                <el-input-number
                  v-model="trendConfig.limit"
                  :min="10"
                  :max="100"
                  style="width: 100%"
                  @change="updateTrendConfig({ limit: $event })"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="高级选项">
                <el-checkbox v-model="trendConfig.showForecast" @change="updateTrendConfig({ showForecast: $event })">
                  显示预测
                </el-checkbox>
                <el-checkbox
                  v-model="trendConfig.showComparison"
                  @change="updateTrendConfig({ showComparison: $event })"
                >
                  显示对比
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 对比数据展示 -->
          <div v-if="trendConfig.showComparison && extendedTrendData.comparison" class="comparison-data">
            <el-divider content-position="left">趋势对比数据</el-divider>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="comparison-item">
                  <div class="comparison-label">当期数据</div>
                  <div class="comparison-value">
                    {{ formatComparisonValue(extendedTrendData.comparison.current, trendConfig.dimension) }}
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="comparison-item">
                  <div class="comparison-label">环比增长</div>
                  <div
                    class="comparison-value"
                    :class="extendedTrendData.comparison.momGrowth > 0 ? 'trend-up' : 'trend-down'"
                  >
                    <i :class="extendedTrendData.comparison.momGrowth > 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
                    {{ Math.abs(extendedTrendData.comparison.momGrowth || 0).toFixed(2) }}%
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="comparison-item">
                  <div class="comparison-label">同比增长</div>
                  <div
                    class="comparison-value"
                    :class="extendedTrendData.comparison.yoyGrowth > 0 ? 'trend-up' : 'trend-down'"
                  >
                    <i :class="extendedTrendData.comparison.yoyGrowth > 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
                    {{ Math.abs(extendedTrendData.comparison.yoyGrowth || 0).toFixed(2) }}%
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-icon">
                <i class="el-icon-money"></i>
              </div>
              <div class="stat-title">总结算金额</div>
            </div>
            <div class="stat-value">{{ $_common.formatNub(summaryData.totalSettlementAmount) }}</div>
            <div class="stat-trend" :class="summaryData.settlementAmountTrend > 0 ? 'trend-up' : 'trend-down'">
              <i :class="summaryData.settlementAmountTrend > 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              {{ Math.abs(summaryData.settlementAmountTrend || 0) }}%
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-icon">
                <i class="el-icon-document"></i>
              </div>
              <div class="stat-title">结算单总数</div>
            </div>
            <div class="stat-value">{{ summaryData.totalSettlementCount || 0 }}</div>
            <div class="stat-trend" :class="summaryData.settlementCountTrend > 0 ? 'trend-up' : 'trend-down'">
              <i :class="summaryData.settlementCountTrend > 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              {{ Math.abs(summaryData.settlementCountTrend || 0) }}%
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-icon">
                <i class="el-icon-success"></i>
              </div>
              <div class="stat-title">结算成功率</div>
            </div>
            <div class="stat-value">{{ summaryData.settlementSuccessRate || 0 }}%</div>
            <div class="stat-trend" :class="summaryData.successRateTrend > 0 ? 'trend-up' : 'trend-down'">
              <i :class="summaryData.successRateTrend > 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              {{ Math.abs(summaryData.successRateTrend || 0) }}%
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-icon">
                <i class="el-icon-time"></i>
              </div>
              <div class="stat-title">待结算金额</div>
            </div>
            <div class="stat-value">{{ $_common.formatNub(summaryData.pendingSettlementAmount) }}</div>
            <div class="stat-sub">{{ summaryData.pendingSettlementCount || 0 }}笔待处理</div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-icon">
                <i class="el-icon-user"></i>
              </div>
              <div class="stat-title">活跃供应商</div>
            </div>
            <div class="stat-value">{{ summaryData.activeSupplierCount || 0 }}</div>
            <div class="stat-sub">本月有结算记录</div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-icon">
                <i class="el-icon-coin"></i>
              </div>
              <div class="stat-title">平均结算金额</div>
            </div>
            <div class="stat-value">{{ $_common.formatNub(summaryData.avgSettlementAmount) }}</div>
            <div class="stat-sub">单笔结算平均值</div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-icon">
                <i class="el-icon-warning"></i>
              </div>
              <div class="stat-title">异常结算</div>
            </div>
            <div class="stat-value error-value">{{ summaryData.errorSettlementCount || 0 }}</div>
            <div class="stat-sub">需要处理的异常</div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-icon">
                <i class="el-icon-date"></i>
              </div>
              <div class="stat-title">本月增长率</div>
            </div>
            <div class="stat-value" :class="summaryData.monthlyGrowthRate > 0 ? 'growth-positive' : 'growth-negative'">
              {{ summaryData.monthlyGrowthRate > 0 ? "+" : "" }}{{ summaryData.monthlyGrowthRate || 0 }}%
            </div>
            <div class="stat-sub">相比上月同期</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表展示 -->
    <div class="chart-container">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="结算趋势分析" name="trend">
          <div ref="trendChart" class="chart"></div>
        </el-tab-pane>
        <el-tab-pane label="结算状态分布" name="status">
          <div ref="statusChart" class="chart"></div>
        </el-tab-pane>
        <el-tab-pane label="供应商排行榜" name="ranking">
          <div ref="rankingChart" class="chart"></div>
        </el-tab-pane>
        <el-tab-pane label="月度对比分析" name="comparison">
          <div ref="comparisonChart" class="chart"></div>
        </el-tab-pane>
        <el-tab-pane label="结算效率分析" name="efficiency">
          <div ref="efficiencyChart" class="chart"></div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="statisticsList" border style="width: 100%; margin-top: 20px">
      <el-table-column prop="statisticDate" label="统计日期" width="120" align="center">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.statisticDate, "yyyy-MM-dd") }}
        </template>
      </el-table-column>
      <el-table-column prop="supplierName" label="供应商名称" min-width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="settlementCount" label="结算单数" width="100" align="center"></el-table-column>
      <el-table-column prop="settlementAmount" label="结算金额" width="130" align="right">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.settlementAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="successCount" label="成功结算" width="100" align="center"></el-table-column>
      <el-table-column prop="pendingCount" label="待结算" width="100" align="center"></el-table-column>
      <el-table-column prop="failedCount" label="失败结算" width="100" align="center"></el-table-column>
      <el-table-column prop="successRate" label="成功率" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getSuccessRateType(scope.row.successRate)"> {{ scope.row.successRate }}% </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="avgAmount" label="平均金额" width="120" align="right">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.avgAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="settlementStatus" label="主要状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.settlementStatus)">
            {{ getStatusText(scope.row.settlementStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="viewDetails(scope.row)">查看明细</el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="queryParams.limit"
      :total-page.sync="total"
      :current-page.sync="queryParams.page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    />
  </ContainerQuery>
</template>

<script>
import { getExtendedTrendAnalysis, getSettlementStatistics } from "@/api/SupplierConsignment";
import FooterPage from "@/component/common/FooterPage";
import SelectSupplier from "@/component/common/SelectSupplier";
import * as echarts from "echarts";

export default {
  name: "SettlementStatistics",
  components: {
    FooterPage,
    SelectSupplier,
  },

  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 统计列表
      statisticsList: [],
      // 查询参数
      queryParams: {
        page: 1,
        limit: 10,
        supplierId: null,
        settlementStatus: "",
        period: "month",
        startTime: "",
        endTime: "",
      },
      // 日期范围
      dateRange: [],
      // 当前选中的供应商名称
      currentSupplierName: "",
      // 活动标签页
      activeTab: "trend",
      // 图表实例
      trendChart: null,
      statusChart: null,
      rankingChart: null,
      comparisonChart: null,
      efficiencyChart: null,
      // 汇总数据
      summaryData: {
        totalSettlementAmount: 0,
        totalSettlementCount: 0,
        settlementSuccessRate: 0,
        pendingSettlementAmount: 0,
        pendingSettlementCount: 0,
        activeSupplierCount: 0,
        avgSettlementAmount: 0,
        errorSettlementCount: 0,
        monthlyGrowthRate: 0,
        settlementAmountTrend: 0,
        settlementCountTrend: 0,
        successRateTrend: 0,
      },
      // 图表数据
      chartData: {
        trend: [],
        status: [],
        ranking: [],
      },
      // 扩展趋势分析数据
      extendedTrendData: {
        trend: [],
        comparison: {},
        forecast: [],
        period: "month",
        dimension: "amount",
      },
      // 趋势分析配置
      trendConfig: {
        period: "month", // day, week, month, quarter, year
        dimension: "amount", // amount, count, supplier, efficiency
        showForecast: true,
        showComparison: true,
        limit: 30,
      },
      // 防抖定时器
      resizeTimer: null,
      // 趋势分析面板显示状态
      showTrendPanel: false,
    };
  },
  created() {
    this.getData();
  },
  mounted() {
    // 延迟初始化图表，确保DOM完全渲染
    this.$nextTick(() => {
      setTimeout(() => {
        this.initCharts();
      }, 100);
    });
    window.addEventListener("resize", this.resizeCharts);

    // 初始化扩展趋势分析
    this.applyUserPreferences();
    this.optimizeChartPerformance();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.resizeCharts);

    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
      this.resizeTimer = null;
    }

    // 销毁图表实例
    if (this.trendChart) this.trendChart.dispose();
    if (this.statusChart) this.statusChart.dispose();
    if (this.rankingChart) this.rankingChart.dispose();
    if (this.comparisonChart) this.comparisonChart.dispose();
    if (this.efficiencyChart) this.efficiencyChart.dispose();
  },
  methods: {
    // 获取统计列表
    getData() {
      this.loading = true;
      getSettlementStatistics(this.queryParams)
        .then((response) => {
          this.statisticsList = response.data.list;
          this.total = response.data.total;
          this.summaryData = response.data.summary;
          this.chartData = response.data.charts || {};
          this.loading = false;
          this.updateCharts();
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 查询按钮
    handleQuery() {
      this.pageChange(1);
    },

    // 切页
    pageChange(val) {
      this.queryParams.page = val;
      this.getData();
    },

    // 每页数据大小改变
    sizeChange(val) {
      this.queryParams.limit = val;
      this.pageChange(1);
    },

    // 获取扩展趋势分析数据
    getExtendedTrendData() {
      const params = {
        period: this.trendConfig.period,
        dimension: this.trendConfig.dimension,
        limit: this.trendConfig.limit,
        supplierId: this.queryParams.supplierId,
        settlementStatus: this.queryParams.settlementStatus,
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
      };

      // 移除空值参数
      Object.keys(params).forEach((key) => {
        if (params[key] === "" || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      return getExtendedTrendAnalysis(params)
        .then((response) => {
          if (response.errorcode === 0) {
            this.extendedTrendData = response.data;
            return response.data;
          } else {
            this.$message.error(response.data || "获取趋势分析数据失败");
            return null;
          }
        })
        .catch((error) => {
          console.error("获取扩展趋势分析数据失败:", error);
          this.$message.error("获取趋势分析数据失败");
          return null;
        });
    },

    // 更新趋势分析配置
    updateTrendConfig(config) {
      this.trendConfig = { ...this.trendConfig, ...config };
      this.getExtendedTrendData().then((data) => {
        if (data) {
          this.updateAdvancedTrendChart();
        }
      });
    },

    // 切换趋势分析周期
    changeTrendPeriod(period) {
      this.updateTrendConfig({ period });
    },

    // 切换趋势分析维度
    changeTrendDimension(dimension) {
      this.updateTrendConfig({ dimension });
    },

    // 切换趋势分析面板显示状态
    toggleTrendPanel() {
      this.showTrendPanel = !this.showTrendPanel;
    },

    // 格式化对比数据值
    formatComparisonValue(data, dimension) {
      if (!data || data.value === undefined) return "0";

      const value = parseFloat(data.value);
      switch (dimension) {
        case "amount":
          return this.$_common.formatNub(value) + "元";
        case "count":
          return value.toString() + "单";
        case "supplier":
          return value.toString() + "家";
        case "efficiency":
          return value.toFixed(2) + "小时";
        default:
          return value.toString();
      }
    },

    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        // 确保容器存在且有尺寸
        if (this.$refs.trendChart && this.$refs.trendChart.offsetWidth > 0) {
          this.trendChart = echarts.init(this.$refs.trendChart);
        }
        if (this.$refs.statusChart && this.$refs.statusChart.offsetWidth > 0) {
          this.statusChart = echarts.init(this.$refs.statusChart);
        }
        if (this.$refs.rankingChart && this.$refs.rankingChart.offsetWidth > 0) {
          this.rankingChart = echarts.init(this.$refs.rankingChart);
        }
        if (this.$refs.comparisonChart && this.$refs.comparisonChart.offsetWidth > 0) {
          this.comparisonChart = echarts.init(this.$refs.comparisonChart);
        }
        if (this.$refs.efficiencyChart && this.$refs.efficiencyChart.offsetWidth > 0) {
          this.efficiencyChart = echarts.init(this.$refs.efficiencyChart);
        }

        // 延迟更新图表数据，确保图表实例完全初始化
        setTimeout(() => {
          this.updateCharts();
        }, 50);
      });
    },

    // 更新图表数据
    updateCharts() {
      this.updateTrendChart();
      this.updateStatusChart();
      this.updateRankingChart();
      this.updateComparisonChart();
      this.updateEfficiencyChart();
    },

    // 更新高级趋势分析图表
    updateAdvancedTrendChart() {
      if (!this.trendChart || !this.extendedTrendData.trend) return;

      // 确保图表容器有正确的尺寸
      if (this.$refs.trendChart && this.$refs.trendChart.offsetWidth === 0) {
        setTimeout(() => this.updateAdvancedTrendChart(), 100);
        return;
      }

      const trendData = this.extendedTrendData.trend || [];
      const dimension = this.trendConfig.dimension;
      const period = this.trendConfig.period;

      // 根据维度配置图表
      const chartConfig = this.getChartConfigByDimension(dimension);

      // 处理时间轴数据
      const timeData = trendData.map((item) => this.formatDateByPeriod(item.date, period));
      const valueData = trendData.map((item) => parseFloat(item.value || 0));

      // 基础配置
      const option = {
        title: {
          text: chartConfig.title,
          left: "center",
          textStyle: {
            fontSize: 16,
            fontWeight: "bold",
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            animation: false,
          },
          formatter: (params) => {
            return this.formatTooltip(params, dimension, period);
          },
        },
        legend: {
          data: chartConfig.legend,
          top: 35,
          type: "scroll",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "10%",
          top: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: timeData,
          axisLabel: {
            rotate: period === "day" ? 45 : 0,
            interval: "auto",
          },
        },
        yAxis: chartConfig.yAxis,
        series: this.buildSeriesData(trendData, dimension, chartConfig),
        dataZoom: [
          {
            type: "inside",
            start: 0,
            end: 100,
          },
          {
            type: "slider",
            start: 0,
            end: 100,
            height: 20,
            bottom: 10,
          },
        ],
        toolbox: {
          feature: {
            dataZoom: {
              yAxisIndex: "none",
            },
            restore: {},
            saveAsImage: {
              name: `settlement_trend_${dimension}_${period}_${new Date().getTime()}`,
            },
          },
          right: 20,
          top: 10,
        },
      };

      // 添加预测数据
      if (this.trendConfig.showForecast && this.extendedTrendData.forecast) {
        this.addForecastData(option, this.extendedTrendData.forecast, dimension);
      }

      this.updateChartWithOption("trendChart", "trendChart", option);
    },

    // 更新结算趋势图
    updateTrendChart() {
      if (!this.trendChart || !this.chartData.trend) return;

      // 确保图表容器有正确的尺寸
      if (this.$refs.trendChart && this.$refs.trendChart.offsetWidth === 0) {
        setTimeout(() => this.updateTrendChart(), 100);
        return;
      }

      const trendData = this.chartData.trend || [];

      const option = {
        title: {
          text: "结算趋势分析",
          left: "center",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
        },
        legend: {
          data: ["结算金额", "结算单数"],
          top: 30,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: trendData.map((item) => this.$_common.formatDate(item.date, "yyyy-MM-dd")),
        },
        yAxis: [
          {
            type: "value",
            name: "金额",
            position: "left",
            axisLabel: {
              formatter: "{value}",
            },
          },
          {
            type: "value",
            name: "数量",
            position: "right",
            axisLabel: {
              formatter: "{value}",
            },
          },
        ],
        series: [
          {
            name: "结算金额",
            type: "line",
            yAxisIndex: 0,
            data: trendData.map((item) => this.$_common.formatNub(item.amount)),
            smooth: true,
            itemStyle: {
              color: "#409EFF",
            },
          },
          {
            name: "结算单数",
            type: "bar",
            yAxisIndex: 1,
            data: trendData.map((item) => item.count),
            itemStyle: {
              color: "#67C23A",
            },
          },
        ],
      };

      this.updateChartWithOption("trendChart", "trendChart", option);
    },

    // 更新结算状态分布图
    updateStatusChart() {
      if (!this.statusChart || !this.chartData.status) return;

      const statusData = (this.chartData.status || []).map((item) => {
        const statusNames = {
          1: "待结算",
          2: "结算中",
          3: "已结算",
          4: "结算失败",
          5: "待审核",
          6: "已审核",
        };
        return {
          name: statusNames[item.settlementStatus] || "未知",
          value: item.count,
        };
      });

      const option = {
        title: {
          text: "结算状态分布",
          left: "center",
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
        },
        legend: {
          orient: "vertical",
          left: "left",
          top: "middle",
        },
        series: [
          {
            name: "结算状态",
            type: "pie",
            radius: ["40%", "70%"],
            center: ["60%", "50%"],
            data: statusData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      this.statusChart.setOption(option);
    },

    // 更新供应商排行榜
    updateRankingChart() {
      if (!this.rankingChart || !this.chartData.ranking) return;

      const rankingData = this.chartData.ranking || [];

      const option = {
        title: {
          text: "供应商结算排行榜 (TOP 10)",
          left: "center",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
        },
        yAxis: {
          type: "category",
          data: rankingData.map((item) => item.supplierName),
        },
        series: [
          {
            name: "结算金额",
            type: "bar",
            data: rankingData.map((item) => this.$_common.formatNub(item.totalAmount)),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "#83bff6" },
                { offset: 0.5, color: "#188df0" },
                { offset: 1, color: "#188df0" },
              ]),
            },
          },
        ],
      };
      this.rankingChart.setOption(option);
    },

    // 更新月度对比分析图
    updateComparisonChart() {
      if (!this.comparisonChart) return;

      const option = {
        title: {
          text: "月度对比分析",
          left: "center",
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["本月", "上月"],
          top: 30,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["结算金额", "结算单数", "成功率", "平均金额"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: "本月",
            type: "bar",
            data: [
              this.$_common.formatNub(this.summaryData.currentMonthAmount) || 0,
              this.summaryData.currentMonthCount || 0,
              this.summaryData.currentMonthSuccessRate || 0,
              this.$_common.formatNub(this.summaryData.currentMonthAvgAmount) || 0,
            ],
            itemStyle: {
              color: "#409EFF",
            },
          },
          {
            name: "上月",
            type: "bar",
            data: [
              this.summaryData.lastMonthAmount || 0,
              this.summaryData.lastMonthCount || 0,
              this.summaryData.lastMonthSuccessRate || 0,
              this.summaryData.lastMonthAvgAmount || 0,
            ],
            itemStyle: {
              color: "#E6A23C",
            },
          },
        ],
      };
      this.comparisonChart.setOption(option);
    },

    // 更新结算效率分析图
    updateEfficiencyChart() {
      if (!this.efficiencyChart) return;

      const option = {
        title: {
          text: "结算效率分析",
          left: "center",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
        },
        legend: {
          data: ["平均处理时长", "处理量"],
          top: 30,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: this.statisticsList.map((item) => this.$_common.formatDate(item.statisticDate)),
        },
        yAxis: [
          {
            type: "value",
            name: "时长(小时)",
            position: "left",
          },
          {
            type: "value",
            name: "数量",
            position: "right",
          },
        ],
        series: [
          {
            name: "平均处理时长",
            type: "line",
            yAxisIndex: 0,
            data: this.statisticsList.map((item) => this.$_common.formatNub(item.avgProcessTime) || 0),
            smooth: true,
            itemStyle: {
              color: "#F56C6C",
            },
          },
          {
            name: "处理量",
            type: "bar",
            yAxisIndex: 1,
            data: this.statisticsList.map((item) => item.processedCount || 0),
            itemStyle: {
              color: "#909399",
            },
          },
        ],
      };
      this.efficiencyChart.setOption(option);
    },

    // 标签页切换事件
    handleTabClick(tab) {
      this.$nextTick(() => {
        // 延迟执行，确保tab内容完全显示
        setTimeout(() => {
          switch (tab.name) {
            case "trend":
              this.ensureChartInitialized("trendChart", "trendChart");
              this.updateTrendChart();
              break;
            case "status":
              this.ensureChartInitialized("statusChart", "statusChart");
              this.updateStatusChart();
              break;
            case "ranking":
              this.ensureChartInitialized("rankingChart", "rankingChart");
              this.updateRankingChart();
              break;
            case "comparison":
              this.ensureChartInitialized("comparisonChart", "comparisonChart");
              this.updateComparisonChart();
              break;
            case "efficiency":
              this.ensureChartInitialized("efficiencyChart", "efficiencyChart");
              this.updateEfficiencyChart();
              break;
          }

          // 重新计算当前显示图表的尺寸
          this.resizeCurrentChart(tab.name);
        }, 100);
      });
    },

    // 确保图表已初始化
    ensureChartInitialized(chartInstance, refName) {
      if (!this[chartInstance] && this.$refs[refName] && this.$refs[refName].offsetWidth > 0) {
        this[chartInstance] = echarts.init(this.$refs[refName]);
      }
    },

    // 通用图表更新方法
    updateChartWithOption(chartInstance, refName, option) {
      if (!this[chartInstance]) return;

      // 确保图表容器有正确的尺寸
      if (this.$refs[refName] && this.$refs[refName].offsetWidth === 0) {
        setTimeout(() => this.updateChartWithOption(chartInstance, refName, option), 100);
        return;
      }

      this[chartInstance].setOption(option);

      // 确保图表正确渲染
      this.$nextTick(() => {
        if (this[chartInstance]) {
          this[chartInstance].resize();
        }
      });
    },

    // 重新计算当前显示图表的尺寸
    resizeCurrentChart(tabName) {
      const chartMap = {
        trend: "trendChart",
        status: "statusChart",
        ranking: "rankingChart",
        comparison: "comparisonChart",
        efficiency: "efficiencyChart",
      };

      const chartInstance = chartMap[tabName];
      if (chartInstance && this[chartInstance]) {
        this[chartInstance].resize();
      }
    },

    // 调整图表大小
    resizeCharts() {
      // 使用防抖机制，避免频繁调用
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }

      this.resizeTimer = setTimeout(() => {
        if (this.trendChart) this.trendChart.resize();
        if (this.statusChart) this.statusChart.resize();
        if (this.rankingChart) this.rankingChart.resize();
        if (this.comparisonChart) this.comparisonChart.resize();
        if (this.efficiencyChart) this.efficiencyChart.resize();
      }, 100);
    },

    // 优化图表渲染性能
    optimizeChartPerformance() {
      const charts = [this.trendChart, this.statusChart, this.rankingChart, this.comparisonChart, this.efficiencyChart];

      charts.forEach((chart) => {
        if (chart) {
          // 启用动画优化
          chart.setOption({
            animation: true,
            animationDuration: 300,
            animationEasing: "cubicOut",
          });

          // 添加图表事件监听
          this.addChartEventListeners(chart);
        }
      });
    },

    // 添加图表事件监听器
    addChartEventListeners(chart) {
      // 点击事件
      chart.on("click", (params) => {
        this.handleChartClick(params);
      });

      // 双击事件 - 重置缩放
      chart.on("dblclick", () => {
        chart.dispatchAction({
          type: "dataZoom",
          start: 0,
          end: 100,
        });
      });

      // 鼠标悬停事件
      chart.on("mouseover", (params) => {
        this.handleChartMouseOver(params);
      });

      // 图例选择事件
      chart.on("legendselectchanged", (params) => {
        this.handleLegendChange(params);
      });

      // 数据缩放事件
      chart.on("datazoom", (params) => {
        this.handleDataZoom(params);
      });
    },

    // 处理图表点击事件
    handleChartClick(params) {
      if (params.componentType === "series") {
        // 可以根据点击的数据点进行详细查询
        const { seriesName, dataIndex, value } = params;
        console.log("图表点击:", { seriesName, dataIndex, value });

        // 这里可以添加跳转到详细页面的逻辑
        // this.$router.push({ path: '/detail', query: { ... } });
      }
    },

    // 处理图表鼠标悬停事件
    handleChartMouseOver(params) {
      // 可以在这里添加额外的悬停效果
      if (params.componentType === "series") {
        // 高亮相关数据
        params.event.target.style.cursor = "pointer";
      }
    },

    // 处理图例变更事件
    handleLegendChange(params) {
      // 记录用户的图例选择偏好
      const { selected } = params;
      localStorage.setItem("chart_legend_preference", JSON.stringify(selected));
    },

    // 处理数据缩放事件
    handleDataZoom(params) {
      // 记录用户的缩放偏好
      const { start, end } = params;
      localStorage.setItem("chart_zoom_preference", JSON.stringify({ start, end }));
    },

    // 应用用户偏好设置
    applyUserPreferences() {
      try {
        // 应用图例偏好
        const legendPreference = localStorage.getItem("chart_legend_preference");
        if (legendPreference) {
          const selected = JSON.parse(legendPreference);
          // 在图表配置中应用偏好设置
          this.defaultLegendSelected = selected;
        }

        // 应用缩放偏好
        const zoomPreference = localStorage.getItem("chart_zoom_preference");
        if (zoomPreference) {
          const { start, end } = JSON.parse(zoomPreference);
          this.defaultZoomRange = { start, end };
        }
      } catch (error) {
        console.warn("应用用户偏好设置失败:", error);
      }
    },

    // 响应式图表适配
    adaptChartToScreen() {
      const screenWidth = window.innerWidth;
      let chartConfig = {};

      if (screenWidth < 768) {
        // 移动端适配
        chartConfig = {
          grid: {
            left: "5%",
            right: "5%",
            top: "20%",
            bottom: "15%",
          },
          legend: {
            orient: "horizontal",
            bottom: 0,
            itemWidth: 15,
            itemHeight: 10,
            textStyle: { fontSize: 10 },
          },
          xAxis: {
            axisLabel: {
              rotate: 45,
              fontSize: 10,
              interval: "auto",
            },
          },
          yAxis: {
            axisLabel: { fontSize: 10 },
            nameTextStyle: { fontSize: 10 },
          },
        };
      } else if (screenWidth < 1200) {
        // 平板端适配
        chartConfig = {
          grid: {
            left: "4%",
            right: "4%",
            top: "18%",
            bottom: "12%",
          },
          legend: {
            itemWidth: 20,
            itemHeight: 12,
            textStyle: { fontSize: 12 },
          },
          xAxis: {
            axisLabel: { fontSize: 11 },
          },
          yAxis: {
            axisLabel: { fontSize: 11 },
            nameTextStyle: { fontSize: 11 },
          },
        };
      } else {
        // 桌面端配置
        chartConfig = {
          grid: {
            left: "3%",
            right: "4%",
            top: "15%",
            bottom: "10%",
          },
          legend: {
            itemWidth: 25,
            itemHeight: 14,
            textStyle: { fontSize: 14 },
          },
          xAxis: {
            axisLabel: { fontSize: 12 },
          },
          yAxis: {
            axisLabel: { fontSize: 12 },
            nameTextStyle: { fontSize: 12 },
          },
        };
      }

      return chartConfig;
    },

    // 清除供应商选择
    clearSupplier() {
      this.queryParams.supplierId = "";
      this.currentSupplierName = "";
      this.pageChange(1);
    },

    // 供应商选择变更
    handleSupplierChange(supplierId, supplierRow) {
      if (supplierId && supplierRow && supplierRow.length > 0) {
        this.currentSupplierName = supplierRow[0].title || "";
      } else {
        this.currentSupplierName = "";
      }
      this.pageChange(1);
    },

    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val && val.length === 2) {
        // 开始时间设置为当天00:00:00
        this.queryParams.startTime = Math.floor(val[0] / 1000);
        // 结束时间设置为当天23:59:59
        this.queryParams.endTime = Math.floor(val[1] / 1000) + 86399;
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.pageChange(1);
    },

    // 刷新数据
    refreshData() {
      this.getData();
    },

    // 查看明细
    viewDetails(row) {
      this.$router.push({
        path: "/Supplier/SettlementDetails",
        query: {
          supplierId: row.supplierId,
          startTime: row.startTime,
          endTime: row.endTime,
        },
      });
    },

    // 导出数据
    exportData() {
      this.loading = true;
      // 这里应该调用真实的导出API
      setTimeout(() => {
        this.loading = false;
        this.$message.success("统计报表导出成功");
      }, 2000);
    },

    // 获取成功率标签类型
    getSuccessRateType(rate) {
      if (rate >= 95) return "success";
      if (rate >= 80) return "warning";
      return "danger";
    },

    // 获取状态标签类型
    getStatusType(status) {
      const statusMap = {
        1: "info", // 待结算
        2: "warning", // 结算中
        3: "success", // 已结算
        4: "danger", // 结算失败
        5: "primary", // 待审核
        6: "success", // 已审核
      };
      return statusMap[status] || "info";
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: "待结算",
        2: "结算中",
        3: "已结算",
        4: "结算失败",
        5: "待审核",
        6: "已审核",
      };
      return statusMap[status] || "未知";
    },

    // 根据维度获取图表配置
    getChartConfigByDimension(dimension) {
      const configs = {
        amount: {
          title: "结算金额趋势分析",
          legend: ["结算金额", "平均金额"],
          yAxis: [
            {
              type: "value",
              name: "金额(元)",
              position: "left",
              axisLabel: {
                formatter: "{value}",
              },
            },
          ],
        },
        count: {
          title: "结算订单数量趋势分析",
          legend: ["订单总数", "成功订单", "待处理订单", "失败订单"],
          yAxis: [
            {
              type: "value",
              name: "数量",
              position: "left",
              axisLabel: {
                formatter: "{value}",
              },
            },
          ],
        },
        supplier: {
          title: "供应商参与度趋势分析",
          legend: ["参与供应商数", "活跃供应商数", "活跃率"],
          yAxis: [
            {
              type: "value",
              name: "供应商数",
              position: "left",
              axisLabel: {
                formatter: "{value}",
              },
            },
            {
              type: "value",
              name: "活跃率(%)",
              position: "right",
              axisLabel: {
                formatter: "{value}%",
              },
            },
          ],
        },
        efficiency: {
          title: "结算效率趋势分析",
          legend: ["平均处理时长(小时)", "快速处理率"],
          yAxis: [
            {
              type: "value",
              name: "时长(小时)",
              position: "left",
              axisLabel: {
                formatter: "{value}h",
              },
            },
            {
              type: "value",
              name: "快速处理率(%)",
              position: "right",
              axisLabel: {
                formatter: "{value}%",
              },
            },
          ],
        },
      };
      return configs[dimension] || configs.amount;
    },

    // 根据周期格式化日期
    formatDateByPeriod(timestamp, period) {
      const date = new Date(timestamp * 1000);
      switch (period) {
        case "day":
          return this.$_common.formatDate(timestamp, "MM-dd");
        case "week":
          return `第${Math.ceil(date.getDate() / 7)}周`;
        case "month":
          return this.$_common.formatDate(timestamp, "yyyy-MM");
        case "quarter":
          const quarter = Math.ceil((date.getMonth() + 1) / 3);
          return `${date.getFullYear()}Q${quarter}`;
        case "year":
          return date.getFullYear().toString();
        default:
          return this.$_common.formatDate(timestamp, "yyyy-MM-dd");
      }
    },

    // 格式化提示框内容
    formatTooltip(params, dimension, period) {
      if (!params || params.length === 0) return "";

      const time = params[0].axisValue;
      let content = `<div style="margin-bottom: 5px;"><strong>${time}</strong></div>`;

      params.forEach((param) => {
        const value = param.value;
        let formattedValue = value;

        if (dimension === "amount") {
          formattedValue = this.$_common.formatNub(value) + "元";
        } else if (dimension === "efficiency" && param.seriesName.includes("时长")) {
          formattedValue = value.toFixed(2) + "小时";
        } else if (param.seriesName.includes("率")) {
          formattedValue = value.toFixed(2) + "%";
        }

        content += `<div style="margin: 2px 0;">
          <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
          ${param.seriesName}: ${formattedValue}
        </div>`;
      });

      return content;
    },

    // 构建系列数据
    buildSeriesData(trendData, dimension, chartConfig) {
      const series = [];

      switch (dimension) {
        case "amount":
          series.push({
            name: "结算金额",
            type: "line",
            data: trendData.map((item) => parseFloat(item.value || 0)),
            smooth: true,
            itemStyle: { color: "#409EFF" },
            areaStyle: { opacity: 0.3 },
          });
          if (trendData[0] && trendData[0].avgValue !== undefined) {
            series.push({
              name: "平均金额",
              type: "line",
              data: trendData.map((item) => parseFloat(item.avgValue || 0)),
              smooth: true,
              itemStyle: { color: "#67C23A" },
              lineStyle: { type: "dashed" },
            });
          }
          break;

        case "count":
          series.push({
            name: "订单总数",
            type: "bar",
            data: trendData.map((item) => parseInt(item.value || 0)),
            itemStyle: { color: "#409EFF" },
          });
          if (trendData[0] && trendData[0].successCount !== undefined) {
            series.push({
              name: "成功订单",
              type: "line",
              data: trendData.map((item) => parseInt(item.successCount || 0)),
              smooth: true,
              itemStyle: { color: "#67C23A" },
            });
          }
          break;

        case "supplier":
          series.push({
            name: "参与供应商数",
            type: "bar",
            data: trendData.map((item) => parseInt(item.value || 0)),
            itemStyle: { color: "#409EFF" },
          });
          if (trendData[0] && trendData[0].activeSuppliers !== undefined) {
            series.push({
              name: "活跃供应商数",
              type: "line",
              data: trendData.map((item) => parseInt(item.activeSuppliers || 0)),
              smooth: true,
              itemStyle: { color: "#67C23A" },
            });
          }
          if (trendData[0] && trendData[0].activeRate !== undefined) {
            series.push({
              name: "活跃率",
              type: "line",
              yAxisIndex: 1,
              data: trendData.map((item) => parseFloat(item.activeRate || 0)),
              smooth: true,
              itemStyle: { color: "#E6A23C" },
            });
          }
          break;

        case "efficiency":
          series.push({
            name: "平均处理时长(小时)",
            type: "line",
            data: trendData.map((item) => parseFloat(item.value || 0)),
            smooth: true,
            itemStyle: { color: "#409EFF" },
          });
          if (trendData[0] && trendData[0].quickProcessRate !== undefined) {
            series.push({
              name: "快速处理率",
              type: "line",
              yAxisIndex: 1,
              data: trendData.map((item) => parseFloat(item.quickProcessRate || 0)),
              smooth: true,
              itemStyle: { color: "#67C23A" },
            });
          }
          break;
      }

      return series;
    },

    // 添加预测数据到图表
    addForecastData(option, forecastData, dimension) {
      if (!forecastData || forecastData.length === 0) return;

      // 添加预测时间点到x轴
      const forecastTimes = forecastData.map((item) => this.formatDateByPeriod(item.date, this.trendConfig.period));
      option.xAxis.data = option.xAxis.data.concat(forecastTimes);

      // 为每个系列添加预测数据
      option.series.forEach((series, index) => {
        const forecastValues = forecastData.map((item) => parseFloat(item.value || 0));
        const currentData = series.data || [];

        // 添加分隔点
        const separatorData = new Array(currentData.length).fill(null);
        separatorData.push(currentData[currentData.length - 1]); // 连接点

        // 添加预测数据
        const fullForecastData = separatorData.concat(forecastValues);

        // 创建预测系列
        option.series.push({
          name: series.name + "(预测)",
          type: series.type,
          data: fullForecastData,
          itemStyle: {
            color: series.itemStyle.color,
            opacity: 0.6,
          },
          lineStyle: {
            type: "dashed",
            opacity: 0.8,
          },
          areaStyle: series.areaStyle ? { opacity: 0.1 } : undefined,
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.statistics-cards {
  margin: 20px 0;

  .stat-card {
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    min-height: 140px;
    margin-bottom: 20px;
    position: relative;

    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .stat-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;

        i {
          font-size: 18px;
          color: #fff;
        }
      }

      .stat-title {
        font-size: 14px;
        color: #909399;
        font-weight: 500;
        flex: 1;
        line-height: 1.4;
      }
    }

    .stat-value {
      font-size: 28px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 8px;
      line-height: 1.2;
      word-break: break-all;

      &.error-value {
        color: #f56c6c;
      }

      &.growth-positive {
        color: #67c23a;
      }

      &.growth-negative {
        color: #f56c6c;
      }
    }

    .stat-trend {
      font-size: 12px;
      display: flex;
      align-items: center;

      &.trend-up {
        color: #67c23a;
      }

      &.trend-down {
        color: #f56c6c;
      }

      i {
        margin-right: 4px;
      }
    }

    .stat-sub {
      font-size: 12px;
      color: #c0c4cc;
      margin-top: 5px;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .stat-card {
      .stat-value {
        font-size: 24px;
      }
    }
  }

  @media (max-width: 992px) {
    .stat-card {
      padding: 15px;
      min-height: 120px;

      .stat-header {
        margin-bottom: 12px;

        .stat-icon {
          width: 36px;
          height: 36px;
          margin-right: 10px;

          i {
            font-size: 16px;
          }
        }

        .stat-title {
          font-size: 13px;
        }
      }

      .stat-value {
        font-size: 22px;
      }

      .stat-trend,
      .stat-sub {
        font-size: 11px;
      }
    }
  }

  @media (max-width: 768px) {
    .stat-card {
      margin-bottom: 15px;
      padding: 12px;
      min-height: 100px;

      .stat-header {
        margin-bottom: 10px;

        .stat-icon {
          width: 32px;
          height: 32px;
          margin-right: 8px;

          i {
            font-size: 14px;
          }
        }

        .stat-title {
          font-size: 12px;
        }
      }

      .stat-value {
        font-size: 18px;
        margin-bottom: 6px;
      }

      .stat-trend,
      .stat-sub {
        font-size: 10px;
      }
    }
  }

  @media (max-width: 576px) {
    .stat-card {
      text-align: center;
      padding: 15px;
      min-height: auto;

      .stat-header {
        justify-content: center;
        margin-bottom: 12px;

        .stat-icon {
          margin-right: 8px;
        }
      }

      .stat-value {
        font-size: 20px;
      }
    }
  }
}

.chart-container {
  margin: 20px 0;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .chart {
    height: 400px;
    width: 100%;
  }

  ::v-deep .el-tabs__header {
    margin-bottom: 20px;
  }

  ::v-deep .el-tabs__item {
    font-size: 14px;
    font-weight: 500;
  }

  ::v-deep .el-tabs__item.is-active {
    color: #409eff;
  }
}

// 表格样式优化
::v-deep .el-table {
  border-radius: 8px;
  overflow: hidden;

  .el-table__header {
    background-color: #f5f7fa;

    th {
      background-color: #f5f7fa !important;
      color: #606266;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 页面提示样式
.page-tip-div {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #bbdefb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;

  i {
    color: #2196f3;
    margin-right: 8px;
  }

  span {
    color: #424242;
    font-size: 13px;

    &:first-of-type {
      font-weight: 600;
      color: #1976d2;
    }
  }
}

// 趋势分析控制面板样式
.trend-control-panel {
  .comparison-data {
    margin-top: 15px;

    .comparison-item {
      text-align: center;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      .comparison-label {
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 8px;
      }

      .comparison-value {
        font-size: 18px;
        font-weight: bold;
        color: #495057;

        &.trend-up {
          color: #28a745;
        }

        &.trend-down {
          color: #dc3545;
        }

        i {
          margin-right: 4px;
        }
      }
    }
  }

  ::v-deep .el-card__header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;

    .clearfix {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  ::v-deep .el-card__body {
    padding: 20px;
  }

  ::v-deep .el-form-item {
    margin-bottom: 15px;
  }

  ::v-deep .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }

  ::v-deep .el-checkbox {
    margin-right: 15px;
    margin-bottom: 8px;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .trend-control-panel {
    .comparison-data {
      .comparison-item {
        margin-bottom: 10px;

        .comparison-value {
          font-size: 16px;
        }
      }
    }
  }
}
</style>
