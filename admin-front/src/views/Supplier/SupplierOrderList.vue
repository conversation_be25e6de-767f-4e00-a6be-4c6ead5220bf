<template>
  <Container>
    <div slot="tip" class="page-tip-div" style="margin-top: 0">
      <i class="el-icon-info"></i>
      <span>温馨提示：</span>
      <span>1、双击列表可查看对应订单详情</span>
      <span>2、此页面显示与您相关的所有订单</span>
      <span>3、可通过筛选条件查询特定订单</span>
    </div>
    <div v-if="$accessCheck($Access.orderQuerySearch)" slot="right">
      <el-form size="small" :inline="true" :model="search_form">
        <el-form-item>
          <el-input
            v-model="search_form.keyword"
            clearable
            style="width: 260px"
            placeholder="订单编号/商品名称/备注"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="timestamp"
            @change="orderDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select v-model="search_form.orderStatus" placeholder="订单状态" clearable @change="pageChange(1)">
            <el-option label="全部" value=""></el-option>
            <el-option label="待付款" :value="2"></el-option>
            <el-option label="待发货" :value="3"></el-option>
            <el-option label="待收货" :value="4"></el-option>
            <el-option label="已完成" :value="5"></el-option>
            <el-option label="已取消" :value="6"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      :data="order_list"
      style="width: 100%"
      @row-dblclick="goDetail"
    >
      <el-table-column prop="no" label="订单编号" min-width="180"></el-table-column>
      <el-table-column prop="customerName" label="客户名称" min-width="120"></el-table-column>
      <el-table-column prop="createTime" label="下单时间" min-width="180">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="payAmount" label="订单金额" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.payAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="orderStatus" label="订单状态" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
            {{ getOrderStatusText(scope.row.orderStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="outStatus" label="出库状态" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="getOutStatusType(scope.row.outStatus)">
            {{ getOutStatusText(scope.row.outStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="settlementStatus" label="结算状态" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="getSettlementStatusType(scope.row.settlementStatus)">
            {{ getSettlementStatusText(scope.row.settlementStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <div style="height: 34px; line-height: 34px">
            <el-button type="text" @click="goDetail(scope.row)">订单详情</el-button>
            <el-button
              v-if="scope.row.settlementStatus > 0"
              type="text"
              @click="viewSettlementDetail(scope.row)"
            >
              结算详情
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { exportSupplierOrders, getSupplierOrders } from "@/api/SupplierOrder";

export default {
  name: "SupplierOrderList",
  data() {
    return {
      loading: false,
      order_list: [],
      page: 1,
      pageSize: 10,
      total: 0,
      dateRange: [],
      search_form: {
        keyword: "",
        orderStatus: "",
        startTime: "",
        endTime: "",
      },
    };
  },
  created() {
    this.getData();
  },
  methods: {
    // 获取订单列表
    getData() {
      this.loading = true;
      getSupplierOrders({
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.search_form.keyword,
        orderStatus: this.search_form.orderStatus,
        startTime: this.search_form.startTime,
        endTime: this.search_form.endTime,
      })
        .then((res) => {
          if (res.errorcode === 0) {
            this.order_list = res.data || [];
            this.total = res.pageTotal || 0;
          } else {
            this.$message.error(res.msg || "获取订单列表失败");
          }
        })
        .catch(() => {
          this.$message.error("获取订单列表失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 订单时间筛选
    orderDate(val) {
      if (val && val.length) {
        this.search_form.startTime = val[0] / 1000;
        this.search_form.endTime = val[1] / 1000 + 86399;
      } else {
        this.search_form.startTime = "";
        this.search_form.endTime = "";
      }
      this.pageChange(1);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 查看订单详情
    goDetail(row) {
      this.$router.push({
        path: `/Supplier/SupplierOrderDetail/${row.id}`,
      });
    },
    // 查看结算详情
    viewSettlementDetail(row) {
      this.$router.push(`/Supplier/SettlementDetails?orderId=${row.id}`);
    },
    // 导出订单数据
    exportData() {
      exportSupplierOrders({
        keyword: this.search_form.keyword,
        orderStatus: this.search_form.orderStatus,
        startTime: this.search_form.startTime,
        endTime: this.search_form.endTime,
      }).then((res) => {
        const blob = new Blob([res], { type: "application/vnd.ms-excel" });
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = "供应商订单数据.xlsx";
        link.click();
      });
    },
    // 获取订单状态文本
    getOrderStatusText(status) {
      const statusMap = {
        1: "草稿",
        2: "待付款",
        3: "待发货",
        4: "待收货",
        5: "已完成",
        6: "已取消",
      };
      return statusMap[status] || "未知状态";
    },
    // 获取订单状态类型
    getOrderStatusType(status) {
      const typeMap = {
        1: "info",
        2: "warning",
        3: "primary",
        4: "success",
        5: "success",
        6: "danger",
      };
      return typeMap[status] || "info";
    },
    // 获取出库状态文本
    getOutStatusText(status) {
      const statusMap = {
        1: "未出库",
        2: "部分出库",
        3: "已出库",
        4: "已取消",
        5: "已完成",
      };
      return statusMap[status] || "未知状态";
    },
    // 获取出库状态类型
    getOutStatusType(status) {
      const typeMap = {
        1: "info",
        2: "warning",
        3: "success",
        4: "danger",
        5: "success",
      };
      return typeMap[status] || "info";
    },
    // 获取结算状态文本
    getSettlementStatusText(status) {
      const statusMap = {
        0: "未结算",
        1: "已结算",
        2: "部分结算",
      };
      return statusMap[status] || "未结算";
    },
    // 获取结算状态类型
    getSettlementStatusType(status) {
      const typeMap = {
        0: "info",
        1: "success",
        2: "warning",
      };
      return typeMap[status] || "info";
    },
  },
};
</script>

<style lang="scss" scoped>
.order-detail {
  padding: 20px;

  .detail-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }

    .detail-item {
      margin-bottom: 10px;

      .detail-label {
        color: #606266;
        margin-right: 10px;
      }

      .detail-value {
        color: #303133;
        font-weight: 500;
      }
    }
  }
}
</style>
