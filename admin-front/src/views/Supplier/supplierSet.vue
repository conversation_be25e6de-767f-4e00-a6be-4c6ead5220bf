<template>
  <ContainerTit>
    <Container></Container>
    <div slot="headr">
      <el-button type="primary" @click="setSubmit">保存</el-button>
    </div>
    <div style="background-color: #fff; padding: 20px">
      <el-form ref="basicData" :model="supplierSet" size="small" label-width="160px">
        <el-form-item label="是否启用：">
          <el-radio-group v-model="supplierSet.enable_istrue">
            <el-radio :label="5">是</el-radio>
            <el-radio :label="4">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="自动生成采购单时间：">
          <el-time-picker
            v-model="supplierSet.auto_ctime"
            placeholder="任意时间点"
            format="HH:mm"
            value-format="HH:mm"
            @change="supplierDate"
          ></el-time-picker>
        </el-form-item>
      </el-form>
    </div>
  </ContainerTit>
</template>

<script>
import { getBasicSetup, setting } from "@/api/System";
export default {
  name: "SupplierSet",
  data() {
    return {
      baseForm: {},
      supplierSet: {
        enable_istrue: 4,
        auto_ctime: "",
      },
    };
  },
  created() {
    this.getBasicSetup();
  },
  methods: {
    async setSubmit() {
      const data = await setting({
        basicData: {
          ...this.baseForm,
          supplierSet: this.supplierSet,
        },
      });
      this.$message.success("保存成功");
      this.getBasicSetup();
    },
    async getBasicSetup() {
      const { data } = await getBasicSetup();
      this.baseForm = data.basicData;
      if (this.baseForm.supplierSet) {
        this.supplierSet = this.baseForm.supplierSet;
      } else {
        this.supplierSet = {
          enable_istrue: 4,
          auto_ctime: "",
        };
      }
    },
    supplierDate(val) {
      if (val) {
        this.supplierSet.auto_ctime = this.$_common.formatDate(val.getTime(), "hh:mm");
      } else {
        this.supplierSet.auto_ctime = "";
      }
    },
  },
};
</script>

<style scoped></style>
