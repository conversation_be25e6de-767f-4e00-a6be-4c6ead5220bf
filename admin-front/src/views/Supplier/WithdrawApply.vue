<template>
  <ContainerQuery>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示：
      1、提现申请提交后将进入审核流程，请确保提现账户信息准确无误；
      2、提现申请审核通过后将进入打款流程，实际到账时间以银行处理为准；
      3、提现金额不能超过可提现余额。
    </div>

    <el-card class="balance-card">
      <div class="balance-info">
        <div class="balance-item">
          <div class="balance-label">可提现余额</div>
          <div class="balance-value">¥{{ formatMoney(balanceInfo.money || 0) }}</div>
        </div>
        <div class="balance-item">
          <div class="balance-label">待审核提现</div>
          <div class="balance-value">¥{{ formatMoney(balanceInfo.waitAuditWithdraw || 0) }}</div>
        </div>
        <div class="balance-item">
          <div class="balance-label">待打款金额</div>
          <div class="balance-value">¥{{ formatMoney(balanceInfo.auditWithdraw || 0) }}</div>
        </div>
        <div class="balance-item">
          <div class="balance-label">已提现金额</div>
          <div class="balance-value">¥{{ formatMoney(balanceInfo.withdraw || 0) }}</div>
        </div>
      </div>
    </el-card>

    <el-card class="withdraw-form-card">
      <div slot="header">
        <span>提现申请</span>
      </div>
      <el-form
        ref="withdrawForm"
        :model="withdrawForm"
        :rules="rules"
        label-width="120px"
        size="medium"
      >
        <el-form-item label="提现金额" prop="money">
          <el-input
            v-model="withdrawForm.money"
            type="number"
            placeholder="请输入提现金额"
            style="width: 300px"
          >
            <template slot="append">元</template>
          </el-input>
        </el-form-item>

        <el-form-item label="提现方式" prop="type">
          <el-select v-model="withdrawForm.type" placeholder="请选择提现方式" style="width: 300px">
            <el-option :value="1" label="微信钱包"></el-option>
            <el-option :value="2" label="支付宝"></el-option>
            <el-option :value="3" label="银行卡"></el-option>
          </el-select>
        </el-form-item>

        <div v-if="withdrawForm.type === 1">
          <el-form-item label="微信账号" prop="accountContent.account">
            <el-input
              v-model="withdrawForm.accountContent.account"
              placeholder="请输入微信账号"
              style="width: 300px"
            ></el-input>
          </el-form-item>
          <el-form-item label="真实姓名" prop="accountContent.realName">
            <el-input
              v-model="withdrawForm.accountContent.realName"
              placeholder="请输入真实姓名"
              style="width: 300px"
            ></el-input>
          </el-form-item>
        </div>

        <div v-if="withdrawForm.type === 2">
          <el-form-item label="支付宝账号" prop="accountContent.account">
            <el-input
              v-model="withdrawForm.accountContent.account"
              placeholder="请输入支付宝账号"
              style="width: 300px"
            ></el-input>
          </el-form-item>
          <el-form-item label="真实姓名" prop="accountContent.realName">
            <el-input
              v-model="withdrawForm.accountContent.realName"
              placeholder="请输入真实姓名"
              style="width: 300px"
            ></el-input>
          </el-form-item>
        </div>

        <div v-if="withdrawForm.type === 3">
          <el-form-item label="开户银行" prop="accountContent.bankName">
            <el-input
              v-model="withdrawForm.accountContent.bankName"
              placeholder="请输入开户银行"
              style="width: 300px"
            ></el-input>
          </el-form-item>
          <el-form-item label="银行卡号" prop="accountContent.account">
            <el-input
              v-model="withdrawForm.accountContent.account"
              placeholder="请输入银行卡号"
              style="width: 300px"
            ></el-input>
          </el-form-item>
          <el-form-item label="开户人姓名" prop="accountContent.realName">
            <el-input
              v-model="withdrawForm.accountContent.realName"
              placeholder="请输入开户人姓名"
              style="width: 300px"
            ></el-input>
          </el-form-item>
          <el-form-item label="开户支行" prop="accountContent.branchName">
            <el-input
              v-model="withdrawForm.accountContent.branchName"
              placeholder="请输入开户支行"
              style="width: 300px"
            ></el-input>
          </el-form-item>
        </div>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="withdrawForm.remark"
            type="textarea"
            placeholder="请输入备注信息（选填）"
            :rows="3"
            style="width: 400px"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitWithdraw" :loading="submitting">提交申请</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </ContainerQuery>
</template>

<script>
import { getSupplierBalance, addSupplierWithdraw } from "@/api/SupplierFinance";

export default {
  name: "WithdrawApply",
  data() {
    // 验证提现金额
    const validateMoney = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("请输入提现金额"));
      }
      if (parseFloat(value) <= 0) {
        return callback(new Error("提现金额必须大于0"));
      }
      if (parseFloat(value) > parseFloat(this.balanceInfo.money || 0)) {
        return callback(new Error("提现金额不能超过可提现余额"));
      }
      callback();
    };

    return {
      balanceInfo: {
        money: 0,
        waitAuditWithdraw: 0,
        auditWithdraw: 0,
        withdraw: 0,
      },
      withdrawForm: {
        money: "",
        type: 1,
        accountContent: {
          account: "",
          realName: "",
          bankName: "",
          branchName: "",
        },
        remark: "",
      },
      rules: {
        money: [{ validator: validateMoney, trigger: "blur" }],
        type: [{ required: true, message: "请选择提现方式", trigger: "change" }],
        "accountContent.account": [
          { required: true, message: "请输入账号", trigger: "blur" },
        ],
        "accountContent.realName": [
          { required: true, message: "请输入真实姓名", trigger: "blur" },
        ],
        "accountContent.bankName": [
          { required: true, message: "请输入开户银行", trigger: "blur" },
        ],
        "accountContent.branchName": [
          { required: true, message: "请输入开户支行", trigger: "blur" },
        ],
      },
      submitting: false,
    };
  },
  created() {
    this.getBalanceInfo();
  },
  methods: {
    // 获取余额信息
    async getBalanceInfo() {
      try {
        // 获取当前登录的供应商ID
        const supplierId = this.$store.getters.supplierId;
        if (!supplierId) {
          this.$message.error("未获取到供应商信息");
          return;
        }
        
        const res = await getSupplierBalance(supplierId);
        if (res.errorcode === 0) {
          this.balanceInfo = res.data;
        } else {
          this.$message.error(res.message || "获取余额信息失败");
        }
      } catch (error) {
        console.error("获取余额信息失败", error);
        this.$message.error("获取余额信息失败");
      }
    },

    // 提交提现申请
    submitWithdraw() {
      this.$refs.withdrawForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }

        this.submitting = true;
        try {
          const data = {
            money: parseFloat(this.withdrawForm.money),
            type: this.withdrawForm.type,
            accountContent: this.withdrawForm.accountContent,
            remark: this.withdrawForm.remark,
          };

          const res = await addSupplierWithdraw(data);
          if (res.errorcode === 0) {
            this.$message.success("提现申请提交成功");
            this.resetForm();
            this.getBalanceInfo();
          } else {
            this.$message.error(res.message || "提现申请提交失败");
          }
        } catch (error) {
          console.error("提现申请提交失败", error);
          this.$message.error("提现申请提交失败");
        } finally {
          this.submitting = false;
        }
      });
    },

    // 重置表单
    resetForm() {
      this.$refs.withdrawForm.resetFields();
      this.withdrawForm = {
        money: "",
        type: 1,
        accountContent: {
          account: "",
          realName: "",
          bankName: "",
          branchName: "",
        },
        remark: "",
      };
    },

    // 格式化金额
    formatMoney(value) {
      return parseFloat(value).toFixed(2);
    },
  },
};
</script>

<style lang="scss" scoped>
.balance-card {
  margin-bottom: 20px;
  
  .balance-info {
    display: flex;
    justify-content: space-between;
    
    .balance-item {
      text-align: center;
      padding: 0 20px;
      
      .balance-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 10px;
      }
      
      .balance-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
      }
    }
  }
}

.withdraw-form-card {
  margin-bottom: 20px;
}
</style>
