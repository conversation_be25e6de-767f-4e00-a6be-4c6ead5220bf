<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>结算记录列表</span>
      </div>
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="订单号">
          <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable></el-input>
        </el-form-item>
        <el-form-item label="结算状态">
          <el-select v-model="queryParams.settlementStatus" placeholder="请选择结算状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="待结算" :value="1"></el-option>
            <el-option label="结算中" :value="2"></el-option>
            <el-option label="已结算" :value="3"></el-option>
            <el-option label="结算失败" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="timestamp"
            @change="handleDateChange"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 统计卡片 -->
      <div class="statistics-cards" v-loading="statisticsLoading">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card shadow="hover" class="statistics-card">
              <div class="statistics-title">总结算金额</div>
              <div class="statistics-value">{{ $_common.formatNub(statistics.totalSettlementAmount || 0) }}</div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card shadow="hover" class="statistics-card">
              <div class="statistics-title">实际结算金额</div>
              <div class="statistics-value">{{ $_common.formatNub(statistics.totalActualAmount || 0) }}</div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="statistics-card">
              <div class="statistics-title">结算记录数</div>
              <div class="statistics-value">{{ statistics.totalCount || 0 }}</div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <el-table v-loading="loading" :data="settlementList" border style="width: 100%; margin-top: 20px">
        <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
        <el-table-column prop="orderNo" label="订单号" width="180"></el-table-column>
        <el-table-column prop="outNo" label="出库单号" width="180"></el-table-column>
        <el-table-column prop="materielName" label="商品名称" min-width="200">
          <template slot-scope="scope">
            <div class="product-info">
              <span>{{ scope.row.materielName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="materielCode" label="商品编码" width="120"></el-table-column>
        <el-table-column prop="num" label="数量" width="100" align="center"></el-table-column>
        <el-table-column prop="unitPrice" label="单价" width="120" align="right">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.unitPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalPrice" label="总价" width="120" align="right">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.totalPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="settlementAmount" label="结算金额" width="120" align="right">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.settlementAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="settlementStatus" label="结算状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getSettlementStatusType(scope.row.settlementStatus)">
              {{ getSettlementStatusText(scope.row.settlementStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog title="结算记录详情" :visible.sync="detailDialogVisible" width="700px">
      <div v-loading="detailLoading">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="结算记录ID">{{ detailData.id }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ detailData.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="出库单号">{{ detailData.outNo }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ detailData.materielName }}</el-descriptions-item>
          <el-descriptions-item label="商品编码">{{ detailData.materielCode }}</el-descriptions-item>
          <el-descriptions-item label="数量">{{ detailData.num }}</el-descriptions-item>
          <el-descriptions-item label="单价">{{ $_common.formatNub(detailData.unitPrice) }}</el-descriptions-item>
          <el-descriptions-item label="总价">{{ $_common.formatNub(detailData.totalPrice) }}</el-descriptions-item>
          <el-descriptions-item label="结算金额">{{ $_common.formatNub(detailData.settlementAmount) }}</el-descriptions-item>
          <el-descriptions-item label="实际结算金额">{{ $_common.formatNub(detailData.actualAmount) }}</el-descriptions-item>
          <el-descriptions-item label="结算状态">
            <el-tag :type="getSettlementStatusType(detailData.settlementStatus)">
              {{ getSettlementStatusText(detailData.settlementStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="结算单号">{{ detailData.settlementNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="结算时间">{{ detailData.settlementTime ? $_common.formatDate(detailData.settlementTime) : '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ $_common.formatDate(detailData.createTime) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSupplierSettlementDetail, getSupplierSettlementRecords, getSupplierSettlementStatistics } from "@/api/SupplierConsignment";

export default {
  name: "SupplierSettlementList",

  data() {
    return {
      // 遮罩层
      loading: false,
      // 统计数据加载状态
      statisticsLoading: false,
      // 总条数
      total: 0,
      // 结算列表
      settlementList: [],
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        orderNo: "",
        settlementStatus: "",
        startTime: "",
        endTime: "",
      },
      // 日期范围
      dateRange: [],
      // 统计数据
      statistics: {},

      // 详情对话框可见性
      detailDialogVisible: false,
      // 详情加载状态
      detailLoading: false,
      // 详情数据
      detailData: {},
    };
  },

  created() {
    this.getList();
    this.getStatistics();
  },

  methods: {
    // 获取结算列表
    getList() {
      this.loading = true;
      getSupplierSettlementRecords(this.queryParams)
        .then((response) => {
          if (response.errorcode === 0) {
            this.settlementList = response.data;
            this.total = response.pageTotal;
          } else {
            this.$message.error(response.data || "获取结算列表失败");
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$message.error("获取结算列表失败");
        });
    },

    // 获取统计数据
    getStatistics() {
      this.statisticsLoading = true;
      getSupplierSettlementStatistics({
        settlementStatus: this.queryParams.settlementStatus,
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
      })
        .then((response) => {
          if (response.errorcode === 0) {
            this.statistics = response.data || {};
          } else {
            this.$message.error(response.data || "获取统计数据失败");
          }
          this.statisticsLoading = false;
        })
        .catch(() => {
          this.statisticsLoading = false;
          this.$message.error("获取统计数据失败");
        });
    },

    // 查询按钮点击
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
      this.getStatistics();
    },

    // 重置按钮点击
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        page: 1,
        pageSize: 10,
        orderNo: "",
        settlementStatus: "",
        startTime: "",
        endTime: "",
      };
      this.getList();
      this.getStatistics();
    },

    // 日期范围变更
    handleDateChange(val) {
      if (val) {
        // 开始时间设置为当天00:00:00
        this.queryParams.startTime = Math.floor(val[0] / 1000);
        // 结束时间设置为当天23:59:59
        this.queryParams.endTime = Math.floor(val[1] / 1000) + 86399;
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
    },

    // 每页条数变更
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getList();
    },

    // 当前页变更
    handleCurrentChange(val) {
      this.queryParams.page = val;
      this.getList();
    },

    // 查看详情
    handleDetail(row) {
      this.detailDialogVisible = true;
      this.detailLoading = true;
      getSupplierSettlementDetail(row.id)
        .then((response) => {
          if (response.errorcode === 0) {
            this.detailData = response.data;
          } else {
            this.$message.error(response.data || "获取详情失败");
          }
          this.detailLoading = false;
        })
        .catch(() => {
          this.detailLoading = false;
          this.$message.error("获取详情失败");
        });
    },

    // 获取结算状态文本
    getSettlementStatusText(status) {
      const statusMap = {
        1: "待结算",
        2: "结算中",
        3: "已结算",
        4: "结算失败",
      };
      return statusMap[status] || "未知状态";
    },

    // 获取结算状态类型
    getSettlementStatusType(status) {
      const typeMap = {
        1: "info",
        2: "warning",
        3: "success",
        4: "danger",
      };
      return typeMap[status] || "";
    },
  },
};
</script>

<style scoped>
.statistics-cards {
  margin-top: 20px;
  margin-bottom: 20px;
}

.statistics-card {
  text-align: center;
  padding: 10px;
}

.statistics-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.statistics-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.product-info {
  display: flex;
  align-items: center;
}

.product-image {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  object-fit: cover;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
