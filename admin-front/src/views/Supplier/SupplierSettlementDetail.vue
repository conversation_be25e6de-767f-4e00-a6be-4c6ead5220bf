<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>结算明细</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
      </div>

      <div v-loading="loading">
        <!-- 结算基本信息 -->
        <el-descriptions title="基本信息" :column="3" border>
          <el-descriptions-item label="结算记录ID">{{ settlementDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ settlementDetail.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="出库单号">{{ settlementDetail.outNo }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ settlementDetail.materielName }}</el-descriptions-item>
          <el-descriptions-item label="商品编码">{{ settlementDetail.materielCode }}</el-descriptions-item>
          <el-descriptions-item label="单位">{{ settlementDetail.unitName }}</el-descriptions-item>
          <el-descriptions-item label="数量">{{ settlementDetail.num }}</el-descriptions-item>
          <el-descriptions-item label="单价">{{ $_common.formatNub(settlementDetail.unitPrice) }}</el-descriptions-item>
          <el-descriptions-item label="总价">{{
            $_common.formatNub(settlementDetail.totalPrice)
          }}</el-descriptions-item>
        </el-descriptions>

        <!-- 结算金额信息（已移除税费显示） -->
        <el-descriptions title="结算信息" :column="3" border style="margin-top: 20px">
          <el-descriptions-item label="结算金额">{{
            $_common.formatNub(settlementDetail.settlementAmount)
          }}</el-descriptions-item>
          <el-descriptions-item label="分账类型">{{
            getSettlementTypeText(settlementDetail.settlementType)
          }}</el-descriptions-item>
          <el-descriptions-item label="实际结算金额">{{
            $_common.formatNub(settlementDetail.actualAmount)
          }}</el-descriptions-item>
          <el-descriptions-item label="结算状态">
            <el-tag :type="getSettlementStatusType(settlementDetail.settlementStatus)">
              {{ getSettlementStatusText(settlementDetail.settlementStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="结算单号">{{ settlementDetail.settlementNo || "-" }}</el-descriptions-item>
          <el-descriptions-item label="结算时间">{{
            settlementDetail.settlementTime ? $_common.formatDate(settlementDetail.settlementTime) : "-"
          }}</el-descriptions-item>
        </el-descriptions>

        <!-- 时间信息 -->
        <el-descriptions title="时间信息" :column="2" border style="margin-top: 20px">
          <el-descriptions-item label="创建时间">{{
            $_common.formatDate(settlementDetail.createTime)
          }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{
            $_common.formatDate(settlementDetail.updateTime)
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getSupplierSettlementDetail } from "@/api/SupplierConsignment";

export default {
  name: "SupplierSettlementDetail",

  data() {
    return {
      // 遮罩层
      loading: false,
      // 结算明细ID
      settlementId: null,
      // 结算明细数据
      settlementDetail: {},
    };
  },

  created() {
    this.settlementId = this.$route.params.id;
    if (this.settlementId) {
      this.getDetail();
    } else {
      this.$message.error("结算记录ID不能为空");
      this.goBack();
    }
  },

  methods: {
    // 获取结算明细
    getDetail() {
      this.loading = true;
      getSupplierSettlementDetail(this.settlementId)
        .then((response) => {
          if (response.errorcode === 0) {
            this.settlementDetail = response.data;
          } else {
            this.$message.error(response.data || "获取结算明细失败");
            this.goBack();
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$message.error("获取结算明细失败");
          this.goBack();
        });
    },

    // 返回列表页
    goBack() {
      this.$router.push({ path: "/Supplier/SettlementList" });
    },

    // 获取结算状态文本
    getSettlementStatusText(status) {
      const statusMap = {
        1: "待结算",
        2: "结算中",
        3: "已结算",
        4: "结算失败",
      };
      return statusMap[status] || "未知状态";
    },

    // 获取结算状态类型
    getSettlementStatusType(status) {
      const typeMap = {
        1: "info",
        2: "warning",
        3: "success",
        4: "danger",
      };
      return typeMap[status] || "";
    },

    // 获取分账类型文本
    getSettlementTypeText(type) {
      const typeMap = {
        4: "固定金额",
      };
      return typeMap[type] || "固定金额";
    },
  },
};
</script>

<style scoped>
.el-descriptions {
  margin-bottom: 20px;
}
</style>
