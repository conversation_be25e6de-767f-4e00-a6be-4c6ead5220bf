<template>
  <ContainerQuery>
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <el-input
          v-model="queryParams.orderNo"
          clearable
          style="width: 320px"
          placeholder="请输入订单号/出库单号/结算单号"
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <SelectSupplier
          v-model="queryParams.supplierId"
          style="width: 220px"
          @clear="clearSupplier"
          @change="handleSupplierChange"
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.settlementStatus"
          placeholder="请选择结算状态"
          clearable
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="待结算" :value="1"></el-option>
          <el-option label="结算中" :value="2"></el-option>
          <el-option label="已结算" :value="3"></el-option>
          <el-option label="结算失败" :value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="timestamp"
          style="width: 220px"
          @change="handleDateRangeChange"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="detailsList" style="width: 100%">
      <el-table-column prop="id" label="明细ID" width="80" align="center"></el-table-column>
      <el-table-column prop="orderNo" label="订单号" width="160"></el-table-column>
      <el-table-column prop="outNo" label="出库单号" width="160"></el-table-column>
      <el-table-column prop="materielCode" label="商品编码" width="120"></el-table-column>
      <el-table-column prop="materielName" label="商品名称" min-width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="product-info">
            <span>{{ scope.row.materielName }}</span>
            <div v-if="scope.row.skuName" class="sku-info">{{ scope.row.skuName }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="supplierName" label="供应商" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="warehouseName" label="仓库" width="100" show-overflow-tooltip></el-table-column>
      <el-table-column prop="num" label="数量" width="80" align="center">
        <template slot-scope="scope"> {{ $_common.formatNub(scope.row.num) }} {{ scope.row.unitName }} </template>
      </el-table-column>
      <el-table-column prop="settlementAmount" label="结算金额" width="120" align="right">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.settlementAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="settlementStatus" label="结算状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.settlementStatus)">
            {{ getStatusText(scope.row.settlementStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160" align="center">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="ruleId" label="适用规则" width="100" align="center">
        <template slot-scope="scope">
          <div style="height: 34px; line-height: 34px">
            <el-button v-if="scope.row.ruleId" type="text" @click="viewRule(scope.row)">查看规则</el-button>
            <span v-else>-</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template slot-scope="scope">
          <div style="height: 34px; line-height: 34px">
            <el-button type="text" @click="handleDetail(scope.row)">详情</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="queryParams.pageSize"
      :total-page.sync="total"
      :current-page.sync="queryParams.page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    />

    <!-- 明细详情对话框 -->
    <el-dialog title="分账明细详情" :visible.sync="detailDialogVisible" width="800px">
      <div v-loading="detailLoading">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ detailData.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="出库单号">{{ detailData.outNo }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ detailData.materielName }}</el-descriptions-item>
          <el-descriptions-item label="商品编码">{{ detailData.materielCode }}</el-descriptions-item>
          <el-descriptions-item label="SKU名称">{{ detailData.skuName || "-" }}</el-descriptions-item>
          <el-descriptions-item label="供应商">{{ detailData.supplierName }}</el-descriptions-item>
          <el-descriptions-item label="仓库">{{ detailData.warehouseName || "-" }}</el-descriptions-item>
          <el-descriptions-item label="数量"
            >{{ $_common.formatNub(detailData.num) }} {{ detailData.unitName }}</el-descriptions-item
          >
          <el-descriptions-item label="结算金额">{{
            $_common.formatNub(detailData.settlementAmount)
          }}</el-descriptions-item>
          <el-descriptions-item label="结算状态">
            <el-tag :type="getStatusType(detailData.settlementStatus)">
              {{ getStatusText(detailData.settlementStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="结算单号">{{ detailData.settlementNo || "-" }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ $_common.formatDate(detailData.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="结算时间">{{
            $_common.formatDate(detailData.settlementTime)
          }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.ruleSnapshot" label="规则快照" :span="2">
            <el-button type="text" @click="showRuleSnapshot">查看规则快照</el-button>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 规则快照对话框 -->
    <el-dialog title="规则快照详情" :visible.sync="ruleSnapshotDialogVisible" width="1000px">
      <div v-if="ruleSnapshotData">
        <el-descriptions :column="1" border style="margin-bottom: 20px">
          <el-descriptions-item label="规则名称">{{ ruleSnapshotData.ruleName || "-" }}</el-descriptions-item>
        </el-descriptions>

        <!-- SKU固定金额表格 -->
        <div v-if="ruleSnapshotData.ruleContent && ruleSnapshotData.ruleContent.skuFixedAmounts">
          <h4 style="margin: 20px 0 10px 0; color: #303133">SKU固定金额配置</h4>
          <div v-if="ruleSnapshotData.ruleContent.skuFixedAmounts.length > 10" style="margin-bottom: 10px">
            <el-button type="text" style="color: #409eff" @click="toggleSkuTableExpanded">
              {{ skuTableExpanded ? "收起" : "展开全部" }}（共{{
                ruleSnapshotData.ruleContent.skuFixedAmounts.length
              }}条）
            </el-button>
          </div>
          <el-table :data="displaySkuData" border stripe style="width: 100%" :max-height="skuTableExpanded ? 600 : 400">
            <el-table-column prop="goodsCode" label="商品编码" width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="goodsName" label="商品名称" min-width="180" show-overflow-tooltip></el-table-column>
            <el-table-column label="规格信息" min-width="200" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ formatSpecGroup(scope.row.specGroup) }}
              </template>
            </el-table-column>
            <el-table-column prop="unitName" label="单位" width="80" align="center">
              <template slot-scope="scope">
                {{ scope.row.unitName || "-" }}
              </template>
            </el-table-column>
            <el-table-column label="固定金额" width="120" align="right">
              <template slot-scope="scope">
                {{ $_common.formatNub(scope.row.fixedAmount) }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 其他规则内容 -->
        <div v-else>
          <h4 style="margin: 20px 0 10px 0; color: #303133">规则内容</h4>
          <pre class="rule-content-pre">{{ JSON.stringify(ruleSnapshotData.ruleContent, null, 2) }}</pre>
        </div>
      </div>
      <div v-else style="text-align: center; padding: 40px; color: #909399">暂无规则快照数据</div>
    </el-dialog>
  </ContainerQuery>
</template>

<script>
import { getSettlementDetailList } from "@/api/SupplierConsignment";
import FooterPage from "@/component/common/FooterPage";
import SelectSupplier from "@/component/common/SelectSupplier";

export default {
  name: "SettlementDetails",
  components: {
    FooterPage,
    SelectSupplier,
  },

  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 明细列表
      detailsList: [],
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        supplierId: null,
        orderNo: "",
        outNo: "",
        settlementNo: "",
        settlementStatus: "",
        startTime: "",
        endTime: "",
      },
      // 日期范围
      dateRange: [],
      // 当前选中的供应商名称
      currentSupplierName: "",

      // 详情对话框可见性
      detailDialogVisible: false,
      // 详情加载状态
      detailLoading: false,
      // 详情数据
      detailData: {},

      // 规则快照对话框可见性
      ruleSnapshotDialogVisible: false,
      // 规则快照数据
      ruleSnapshotData: null,
      // SKU表格展开状态
      skuTableExpanded: false,
    };
  },
  computed: {
    // 显示的SKU数据（根据展开状态决定显示数量）
    displaySkuData() {
      if (
        !this.ruleSnapshotData ||
        !this.ruleSnapshotData.ruleContent ||
        !this.ruleSnapshotData.ruleContent.skuFixedAmounts
      ) {
        return [];
      }
      const skuData = this.ruleSnapshotData.ruleContent.skuFixedAmounts;
      if (skuData.length <= 10 || this.skuTableExpanded) {
        return skuData;
      }
      return skuData.slice(0, 10);
    },
  },
  created() {
    // 从路由参数中获取查询条件
    const supplierId = this.$route.query.supplierId;
    const orderNo = this.$route.query.orderNo;
    const outNo = this.$route.query.outNo;
    const settlementNo = this.$route.query.settlementNo;

    if (supplierId) {
      this.queryParams.supplierId = parseInt(supplierId);
    }

    if (orderNo) {
      this.queryParams.orderNo = orderNo;
    }

    if (outNo) {
      this.queryParams.outNo = outNo;
    }

    if (settlementNo) {
      this.queryParams.settlementNo = settlementNo;
    }

    this.getData();
  },
  methods: {
    // 获取明细列表
    getData() {
      this.loading = true;
      getSettlementDetailList(this.queryParams)
        .then((response) => {
          if (response.errorcode === 0) {
            this.detailsList = response.data || [];
            this.total = response.pageTotal || 0;
          } else {
            this.$message.error(response.data || "获取明细列表失败");
            this.detailsList = [];
            this.total = 0;
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.detailsList = [];
          this.total = 0;
        });
    },

    // 切页
    pageChange(val) {
      this.queryParams.page = val;
      this.getData();
    },

    // 每页数据大小改变
    sizeChange(val) {
      this.queryParams.pageSize = val;
      this.pageChange(1);
    },

    // 清除供应商选择
    clearSupplier() {
      this.queryParams.supplierId = "";
      this.currentSupplierName = "";
      this.pageChange(1);
    },

    // 供应商选择变更
    handleSupplierChange(supplierId, supplierRow) {
      if (supplierId && supplierRow && supplierRow.length > 0) {
        this.currentSupplierName = supplierRow[0].title || "";
      } else {
        this.currentSupplierName = "";
      }
      this.pageChange(1);
    },

    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val && val.length === 2) {
        // 开始时间设置为当天00:00:00
        this.queryParams.startTime = Math.floor(val[0] / 1000);
        // 结束时间设置为当天23:59:59
        this.queryParams.endTime = Math.floor(val[1] / 1000) + 86399;
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.pageChange(1);
    },

    // 查看详情
    handleDetail(row) {
      this.detailDialogVisible = true;
      this.detailLoading = true;
      this.detailData = { ...row };
      this.detailLoading = false;
    },

    // 查看规则
    viewRule(row) {
      if (row.ruleSnapshot) {
        // 如果有规则快照，显示快照内容
        this.showRuleSnapshot(row);
      } else if (row.ruleId) {
        // 如果没有快照但有规则ID，跳转到规则页面
        this.$router.push({
          path: "/Supplier/ConsignmentRules",
          query: { id: row.ruleId },
        });
      }
    },

    // 显示规则快照
    showRuleSnapshot(row = null) {
      const targetRow = row || this.detailData;
      if (targetRow.ruleSnapshot) {
        try {
          this.ruleSnapshotData =
            typeof targetRow.ruleSnapshot === "string" ? JSON.parse(targetRow.ruleSnapshot) : targetRow.ruleSnapshot;
          this.ruleSnapshotDialogVisible = true;
          // 重置展开状态
          this.skuTableExpanded = false;
        } catch (e) {
          this.$message.error("规则快照数据格式错误");
        }
      } else {
        this.$message.warning("暂无规则快照数据");
      }
    },

    // 切换SKU表格展开状态
    toggleSkuTableExpanded() {
      this.skuTableExpanded = !this.skuTableExpanded;
    },

    // 格式化规格组信息
    formatSpecGroup(specGroup) {
      if (!specGroup || !Array.isArray(specGroup) || specGroup.length === 0) {
        return "-";
      }
      return specGroup
        .map((spec) => {
          if (spec.specName && spec.specValueName) {
            return `${spec.specName}：${spec.specValueName}`;
          }
          return "";
        })
        .filter((item) => item)
        .join("；");
    },

    // 获取结算状态类型
    getStatusType(status) {
      const statusMap = {
        1: "warning", // 待结算
        2: "info", // 结算中
        3: "success", // 已结算
        4: "danger", // 结算失败
      };
      return statusMap[status] || "";
    },

    // 获取结算状态文本
    getStatusText(status) {
      const statusMap = {
        1: "待结算",
        2: "结算中",
        3: "已结算",
        4: "结算失败",
      };
      return statusMap[status] || status;
    },

    // 获取结算类型标签类型
    getSettlementTypeTagType(type) {
      const typeMap = {
        1: "primary", // 固定比例
        2: "success", // 阶梯佣金
        3: "warning", // 固定服务费
        4: "info", // 混合模式
      };
      return typeMap[type] || "";
    },


  },
};
</script>

<style lang="scss" scoped>
.product-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .sku-info {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.rule-content-pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}
</style>
