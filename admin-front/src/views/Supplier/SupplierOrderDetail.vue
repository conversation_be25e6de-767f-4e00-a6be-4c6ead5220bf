<template>
  <ContainerQuery>
    <div slot="tip" class="page-tip-div" style="margin-top: 0">
      <i class="el-icon-info"></i>
      <span>温馨提示：</span>
      <span>1、此页面显示订单详细信息</span>
      <span>2、可查看订单商品、收货信息、支付信息等详情</span>
    </div>
    <div slot="left">
      <el-button type="primary" icon="el-icon-back" @click="goBack">返回列表</el-button>
      <el-button type="primary" icon="el-icon-printer" @click="printOrder">打印订单</el-button>
    </div>
    <div slot="content">
      <div v-loading="loading" class="order-detail">
        <!-- 订单基本信息 -->
        <div class="detail-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">订单编号：</span>
                <span class="detail-value">{{ orderDetail.no }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">下单时间：</span>
                <span class="detail-value">{{ $utils.timeFormat(orderDetail.createTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">订单状态：</span>
                <span class="detail-value">
                  <el-tag :type="getOrderStatusType(orderDetail.orderStatus)">
                    {{ getOrderStatusText(orderDetail.orderStatus) }}
                  </el-tag>
                </span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">客户名称：</span>
                <span class="detail-value">{{ orderDetail.customerName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">联系电话：</span>
                <span class="detail-value">{{ orderDetail.mobile }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">支付方式：</span>
                <span class="detail-value">{{ orderDetail.paymentName }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 收货信息 -->
        <div class="detail-section">
          <div class="section-title">收货信息</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">收货人：</span>
                <span class="detail-value">{{ orderDetail.receiveData?.realName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">联系电话：</span>
                <span class="detail-value">{{ orderDetail.receiveData?.mobile }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">收货地址：</span>
                <span class="detail-value">{{ getFullAddress() }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 商品信息 -->
        <div class="detail-section">
          <div class="section-title">商品信息</div>
          <el-table :data="orderGoods" border style="width: 100%">
            <el-table-column label="商品图片" width="100">
              <template slot-scope="scope">
                <el-image 
                  style="width: 60px; height: 60px" 
                  :src="scope.row.goodsImages" 
                  fit="cover"
                  :preview-src-list="[scope.row.goodsImages]">
                </el-image>
              </template>
            </el-table-column>
            <el-table-column prop="goodsName" label="商品名称" min-width="200"></el-table-column>
            <el-table-column prop="barCode" label="商品条码" width="120"></el-table-column>
            <el-table-column label="规格" width="150">
              <template slot-scope="scope">
                <div v-for="(spec, index) in scope.row.specGroup" :key="index">
                  {{ spec.name }}: {{ spec.value }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="price" label="单价" width="100">
              <template slot-scope="scope">
                {{ $utils.formattedNumber(scope.row.price) }}
              </template>
            </el-table-column>
            <el-table-column prop="buyNum" label="数量" width="100"></el-table-column>
            <el-table-column prop="totalMoney" label="小计" width="120">
              <template slot-scope="scope">
                {{ $utils.formattedNumber(scope.row.totalMoney) }}
              </template>
            </el-table-column>
            <el-table-column label="发货状态" width="120">
              <template slot-scope="scope">
                <el-tag :type="getShippingStatusType(scope.row.shippingStatus)">
                  {{ getShippingStatusText(scope.row.shippingStatus) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 金额信息 -->
        <div class="detail-section">
          <div class="section-title">金额信息</div>
          <div class="amount-info">
            <div class="amount-item">
              <span class="amount-label">商品总额：</span>
              <span class="amount-value">{{ $utils.formattedNumber(orderDetail.goodsAmount) }}</span>
            </div>
            <div class="amount-item">
              <span class="amount-label">运费：</span>
              <span class="amount-value">{{ $utils.formattedNumber(orderDetail.freightAmount) }}</span>
            </div>
            <div class="amount-item">
              <span class="amount-label">优惠金额：</span>
              <span class="amount-value">{{ $utils.formattedNumber(orderDetail.discountAmount) }}</span>
            </div>
            <div class="amount-item">
              <span class="amount-label">实付金额：</span>
              <span class="amount-value highlight">{{ $utils.formattedNumber(orderDetail.payAmount) }}</span>
            </div>
          </div>
        </div>

        <!-- 分账信息 -->
        <div class="detail-section" v-if="settlementInfo.id">
          <div class="section-title">分账信息</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">分账状态：</span>
                <span class="detail-value">
                  <el-tag :type="getSettlementStatusType(settlementInfo.status)">
                    {{ getSettlementStatusText(settlementInfo.status) }}
                  </el-tag>
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">分账金额：</span>
                <span class="detail-value highlight">{{ $utils.formattedNumber(settlementInfo.amount) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">分账时间：</span>
                <span class="detail-value">{{ $utils.timeFormat(settlementInfo.settlementTime) }}</span>
              </div>
            </el-col>
          </el-row>
          <el-button type="primary" size="small" @click="viewSettlementDetail">查看分账详情</el-button>
        </div>
      </div>
    </div>
  </ContainerQuery>
</template>

<script>
import { getSupplierOrderDetail } from "@/api/SupplierOrder";
import { getOrderSettlementInfo } from "@/api/SupplierConsignment";

export default {
  name: "SupplierOrderDetail",
  data() {
    return {
      loading: false,
      orderId: this.$route.params.id,
      orderDetail: {},
      orderGoods: [],
      settlementInfo: {},
    };
  },
  created() {
    this.getOrderDetail();
  },
  methods: {
    // 获取订单详情
    getOrderDetail() {
      this.loading = true;
      getSupplierOrderDetail(this.orderId)
        .then((res) => {
          if (res.errorcode === 0) {
            this.orderDetail = res.data || {};
            this.orderGoods = res.data.details || [];
            // 获取分账信息
            this.getSettlementInfo();
          } else {
            this.$message.error(res.msg || "获取订单详情失败");
          }
        })
        .catch(() => {
          this.$message.error("获取订单详情失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取分账信息
    getSettlementInfo() {
      getOrderSettlementInfo(this.orderId)
        .then((res) => {
          if (res.errorcode === 0) {
            this.settlementInfo = res.data || {};
          }
        })
        .catch(() => {
          console.error("获取分账信息失败");
        });
    },
    // 返回列表
    goBack() {
      this.$router.push("/Supplier/SupplierOrderList");
    },
    // 打印订单
    printOrder() {
      window.print();
    },
    // 查看分账详情
    viewSettlementDetail() {
      this.$router.push(`/Supplier/SettlementDetails?orderId=${this.orderId}`);
    },
    // 获取完整地址
    getFullAddress() {
      const receiveData = this.orderDetail.receiveData || {};
      return `${receiveData.province || ''} ${receiveData.city || ''} ${receiveData.area || ''} ${receiveData.address || ''}`;
    },
    // 获取订单状态文本
    getOrderStatusText(status) {
      const statusMap = {
        1: "草稿",
        2: "待付款",
        3: "待发货",
        4: "待收货",
        5: "已完成",
        6: "已取消",
      };
      return statusMap[status] || "未知状态";
    },
    // 获取订单状态类型
    getOrderStatusType(status) {
      const typeMap = {
        1: "info",
        2: "warning",
        3: "primary",
        4: "success",
        5: "success",
        6: "danger",
      };
      return typeMap[status] || "info";
    },
    // 获取发货状态文本
    getShippingStatusText(status) {
      const statusMap = {
        0: "未发货",
        1: "已发货",
        2: "已收货",
      };
      return statusMap[status] || "未发货";
    },
    // 获取发货状态类型
    getShippingStatusType(status) {
      const typeMap = {
        0: "info",
        1: "primary",
        2: "success",
      };
      return typeMap[status] || "info";
    },
    // 获取结算状态文本
    getSettlementStatusText(status) {
      const statusMap = {
        0: "未结算",
        1: "已结算",
        2: "部分结算",
      };
      return statusMap[status] || "未结算";
    },
    // 获取结算状态类型
    getSettlementStatusType(status) {
      const typeMap = {
        0: "info",
        1: "success",
        2: "warning",
      };
      return typeMap[status] || "info";
    },
  },
};
</script>

<style lang="scss" scoped>
.order-detail {
  padding: 20px;
  
  .detail-section {
    margin-bottom: 30px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .detail-item {
      margin-bottom: 10px;
      
      .detail-label {
        color: #606266;
        margin-right: 10px;
      }
      
      .detail-value {
        color: #303133;
        font-weight: 500;
      }
    }
  }
  
  .amount-info {
    text-align: right;
    padding: 10px 20px;
    
    .amount-item {
      margin-bottom: 10px;
      
      .amount-label {
        color: #606266;
        margin-right: 10px;
      }
      
      .amount-value {
        color: #303133;
        font-weight: 500;
        
        &.highlight {
          color: #f56c6c;
          font-size: 18px;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
