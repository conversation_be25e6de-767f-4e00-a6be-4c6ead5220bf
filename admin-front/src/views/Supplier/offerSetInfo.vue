<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" @click="modifyPrice">生成调价单</el-button>
    </div>
    <el-form style="padding: 20px; background-color: #fff" label-width="120px" size="small">
      <el-row>
        <el-col :span="8">
          <el-form-item label="供应商名称：" prop="supplierName">
            {{ form.supplierName }}
          </el-form-item>
          <el-form-item label="报价单编码：" prop="no">
            {{ form.no }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商品数量：" prop="materielNum">
            {{ form.materielNum }}
          </el-form-item>
          <el-form-item label="审核状态：" prop="auditStatus">
            <span v-if="form.auditStatus === 1" class="info-status"> 待审核 </span>
            <span v-else class="success-status">已审核</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建日期：" prop="createTime">
            {{ $_common.formatDate(form.createTime) }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-table :data="details_list" :span-method="objectSpanMethod" style="width: 100%; margin-top: 20px" border>
        <el-table-column prop="title" label="商品" min-width="200px">
          <template slot-scope="scope">
            <div class="clearfix">
              <div class="float_left" style="width: 10%">
                <img :src="scope.row.img[0]" alt="" style="width: 50px; height: 50px" />
              </div>
              <div class="float_left" style="margin-left: 20px; width: 80%">
                <p>{{ scope.row.title }}</p>
                <p>{{ scope.row.code }}</p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="unitName" label="规格">
          <template slot-scope="scope">
            <span style="margin-right: 20px">{{ scope.row.unitName }}</span>
            <span v-for="(itemG, indexG) in scope.row.specGroup" :key="indexG">
              <span v-if="indexG > 0">_</span>
              {{ itemG.specValueName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="costPrice" label="成本价"></el-table-column>
      </el-table>
      <div></div>
    </el-form>
  </ContainerTit>
</template>

<script>
import { getSupplierOfferPrice } from "@/api/Supplier";
export default {
  name: "OfferSetInfo",
  data() {
    return {
      details_list: [],
      form: {},
      price_id: 0,
      spanArr: [],
    };
  },
  created() {
    this.price_id = this.$route.query.id;
    this.getSupplierOfferPrice();
  },
  methods: {
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (["商品"].includes(column.label)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    async getSupplierOfferPrice() {
      const data = await getSupplierOfferPrice(this.price_id);
      this.form = data.data;
      let details_list = [];
      data.data.details.forEach((item) => {
        for (let i in item.offerPrice) {
          details_list.push({
            title: item.materielName,
            code: item.materielCode,
            img: item.materielImages,
            id: item.materielId,
            skuId: i,
            ...item.offerPrice[i],
          });
        }
      });
      this.details_list = details_list;
      // 合并单元格
      const getSpanArr = this.$_common.getSpanArr(this.details_list, "id");
      this.spanArr = getSpanArr.spanArr;
    },
    modifyPrice() {
      this.$router.push("/goods/sale/AddGoodsAdjustPrice?price_id=" + this.price_id);
    },
  },
};
</script>

<style scoped>
.table_th {
  height: 80px;
  background-color: #f5f7fa;
  font-weight: 500;
  line-height: 80px;
  border: 1px solid #ebeef5;
}
.table_td {
  border: 1px solid #ebeef5;
}
.table_td:hover {
  background-color: #f5f7fa;
}
</style>
