<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>保证金账户信息</span>
      </div>
      <div v-loading="loading">
        <div v-if="accountInfo" class="account-info">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card shadow="hover" class="account-card">
                <div class="card-content">
                  <div class="amount">{{ $_common.formatNub(accountInfo.depositAccount) }}</div>
                  <div class="label">账户总金额</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover" class="account-card">
                <div class="card-content">
                  <div class="amount">{{ $_common.formatNub(accountInfo.frozenDeposit) }}</div>
                  <div class="label">冻结金额</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover" class="account-card">
                <div class="card-content">
                  <div class="amount">{{ $_common.formatNub(accountInfo.availableDeposit) }}</div>
                  <div class="label">可用余额</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        <div v-else class="empty-data">
          <el-empty description="暂无账户信息"></el-empty>
        </div>
      </div>
    </el-card>

    <el-card class="box-card mt-20">
      <div slot="header" class="clearfix">
        <span>保证金流水记录</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-refresh"
          @click="fetchHistoryData"
        >刷新</el-button>
      </div>
      <div class="filter-container">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item label="流水类型">
            <el-select v-model="queryParams.operationType" placeholder="请选择" clearable>
              <el-option
                v-for="item in operationTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleDateRangeChange"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchHistoryData">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        v-loading="historyLoading"
        :data="historyList"
        border
        style="width: 100%"
        :empty-text="historyList.length === 0 ? '暂无数据' : '加载中...'"
      >
        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column prop="no" label="流水号" width="150" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="operationType" label="类型" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getTypeTag(scope.row.operationType)" size="mini">
              {{ getTypeText(scope.row.operationTypeName) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="120" align="right">
          <template slot-scope="scope">
            <span :class="scope.row.amount >= 0 ? 'text-success' : 'text-danger'">
              {{ $_common.formatNub(scope.row.amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="beforeAmount" label="操作前余额" width="120" align="right">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.beforeAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="afterAmount" label="操作后余额" width="120" align="right">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.afterAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="operatorName" label="操作人" width="120" align="center"></el-table-column>
        <el-table-column prop="receiptTimeFormat" label="操作时间" width="160" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200" show-overflow-tooltip></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </el-card>

    <el-card class="box-card mt-20">
      <div slot="header" class="clearfix">
        <span>保证金说明</span>
      </div>
      <div class="deposit-info">
        <h4>什么是保证金？</h4>
        <p>保证金是供应商在进行代销合作时，按照平台要求缴纳的一定金额的资金，用于保障交易安全和履约能力。</p>

        <h4>保证金的用途</h4>
        <ul>
          <li>确保供应商履行合同义务</li>
          <li>作为违约赔偿的保障</li>
          <li>提高平台交易的安全性和可靠性</li>
        </ul>

        <h4>保证金管理规则</h4>
        <ol>
          <li>保证金金额根据供应商等级和销售规模确定</li>
          <li>保证金可能会被冻结用于特定交易担保</li>
          <li>合作终止且无争议时，保证金将全额退还</li>
          <li>如有违约行为，平台有权从保证金中扣除相应金额</li>
        </ol>

        <div class="contact-info">
          <p>如有疑问，请联系客服：<el-tag size="small">400-123-4567</el-tag></p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getSupplierOwnDepositAccount, getSupplierOwnDepositHistory } from "@/api/SupplierConsignment";

export default {
  name: "SupplierDepositAccount",
  data() {
    return {
      loading: false,
      historyLoading: false,
      accountInfo: null,
      historyList: [],
      total: 0,
      dateRange: null,
      queryParams: {
        page: 1,
        pageSize: 10,
        operationType: null,
        startTime: null,
        endTime: null
      },
      operationTypeOptions: [
        { value: 1, label: "充值" },
        { value: 2, label: "扣除" },
        { value: 3, label: "冻结" },
        { value: 4, label: "解冻" }
      ]
    };
  },
  created() {
    this.fetchData();
    this.fetchHistoryData();
  },
  methods: {


    // 获取账户信息
    fetchData() {
      this.loading = true;
      getSupplierOwnDepositAccount()
        .then(response => {
          if (response.errorcode === 0) {
            this.accountInfo = response.data;
          } else {
            this.$message.error(response.data || "获取账户信息失败");
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$message.error("获取账户信息失败");
        });
    },

    // 获取流水记录
    fetchHistoryData() {
      this.historyLoading = true;
      getSupplierOwnDepositHistory(this.queryParams)
        .then(response => {
          if (response.errorcode === 0) {
            this.historyList = response.data.list;
            this.total = response.data.total;
          } else {
            this.$message.error(response.data || "获取流水记录失败");
          }
          this.historyLoading = false;
        })
        .catch(() => {
          this.historyLoading = false;
          this.$message.error("获取流水记录失败");
        });
    },

    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val) {
        this.queryParams.startTime = val[0] / 1000;
        this.queryParams.endTime = val[1] / 1000;
      } else {
        this.queryParams.startTime = null;
        this.queryParams.endTime = null;
      }
    },

    // 重置查询条件
    resetQuery() {
      this.dateRange = null;
      this.queryParams = {
        page: 1,
        pageSize: 10,
        operationType: null,
        startTime: null,
        endTime: null
      };
      this.fetchHistoryData();
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.queryParams.page = val;
      this.fetchHistoryData();
    },

    // 处理每页条数变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.queryParams.page = 1;
      this.fetchHistoryData();
    },

    // 获取操作类型标签样式
    getTypeTag(type) {
      const typeMap = {
        1: "success",
        2: "danger",
        3: "warning",
        4: "info"
      };
      return typeMap[type] || "";
    },

    // 获取操作类型文本
    getTypeText(typeName) {
      return typeName || "未知";
    }
  }
};
</script>

<style lang="scss" scoped>
.account-info {
  margin-top: 20px;
}

.account-card {
  .card-content {
    text-align: center;
    padding: 20px 0;

    .amount {
      font-size: 24px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 10px;
    }

    .label {
      font-size: 14px;
      color: #606266;
    }
  }
}

.mt-20 {
  margin-top: 20px;
}

.empty-data {
  padding: 40px 0;
}

.filter-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

.deposit-info {
  padding: 10px;

  h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #303133;
  }

  p {
    line-height: 1.6;
    color: #606266;
  }

  ul, ol {
    padding-left: 20px;
    margin: 10px 0;
    color: #606266;

    li {
      line-height: 1.8;
    }
  }

  .contact-info {
    margin-top: 20px;
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 4px;
    text-align: center;
  }
}
</style>
