<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? "编辑分账规则" : "创建分账规则" }}</span>
      </div>
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称" style="width: 400px"></el-input>
        </el-form-item>

        <el-form-item label="规则类型" prop="ruleType">
          <el-radio-group v-model="ruleForm.ruleType">
            <el-radio :label="4">固定金额</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="适用供应商" prop="supplierId">
          <SelectSupplier v-model="ruleForm.supplierId" :width="400" @change="handleSupplierChange" />
          <span class="form-desc">必须指定供应商</span>
        </el-form-item>

        <!-- 适用商品固定为"指定商品" -->

        <el-form-item label="有效期" prop="startDate">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 400px"
            @change="handleDateRangeChange"
          >
          </el-date-picker>
          <div class="form-desc">开始时间为当天00:00:00，结束时间为当天23:59:59</div>
        </el-form-item>

        <!-- 固定金额模式 -->
        <div>
          <el-form-item label="商品选择" prop="scopeGoods">
            <div class="clearfix">
              <span class="float_left" style="color: #999">选择参与分账的商品并设置固定金额</span>
              <el-button type="primary" size="small" class="float_left" @click="handleSelectGoods">
                选择商品
              </el-button>
            </div>
            <el-table :data="ruleForm.scopeGoods" style="width: 100%; margin-top: 10px" border>
              <el-table-column prop="title" label="商品名称" width="180">
                <template slot-scope="scope">
                  <div class="goods-title">
                    {{ scope.row.title }}
                  </div>
                  <div class="goods-no">
                    {{ scope.row.code }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="unitName" label="规格" width="200">
                <template slot-scope="scope">
                  <span>{{ scope.row.unitName }}</span>
                  <span v-for="(itemS, indexS) in scope.row.specGroup" :key="indexS">
                    {{ itemS.specValueName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="供应商固定金额" width="180">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.fixedAmount"
                    :min="0"
                    :precision="2"
                    :step="1"
                    placeholder="供应商固定金额"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" @click="removeScopeGoods(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="form-desc" style="margin-top: 10px">
              注意：固定金额模式下，供应商将按每个SKU获得固定金额的分成，而不是按比例分成
            </div>
          </el-form-item>
        </div>

        <el-form-item label="优先级" prop="priority">
          <el-input-number
            v-model="ruleForm.priority"
            :min="1"
            :max="999"
            :precision="0"
            :step="1"
            style="width: 200px"
          ></el-input-number>
          <span class="form-desc">数字越小优先级越高</span>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch v-model="ruleForm.status" :active-value="5" :inactive-value="4"></el-switch>
          <span class="form-desc">{{ ruleForm.status === 5 ? "启用" : "禁用" }}</span>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="ruleForm.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
            style="width: 400px"
          ></el-input>
        </el-form-item>

        <el-form-item v-if="!isDialog">
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
        <el-form-item v-else>
          <div class="dialog-footer">
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="submitForm">确定</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- SKU商品选择弹窗 -->
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="选择商品SKU"
      :visible.sync="goods_show"
      :append-to-body="true"
      width="70%"
      @close="cancelSelectGoods"
    >
      <div class="clearfix" style="padding-bottom: 10px">
        <el-input
          v-model="skuKeyword"
          style="width: 240px"
          size="mini"
          placeholder="请输入商品名称或编码"
          clearable
          @keyup.enter.native="skuPageChange(1)"
          @clear="skuPageChange(1)"
        >
          <el-button slot="append" @click="skuPageChange(1)">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
      </div>
      <el-table ref="skuTable" border height="500" :data="skuList" size="small" @selection-change="skuSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="title" label="商品名称" min-width="180">
          <template slot-scope="scope">
            <div class="goods-title">{{ scope.row.title }}</div>
            <div class="goods-no">{{ scope.row.code }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="unitName" label="规格" min-width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.unitName }}</span>
            <span v-if="scope.row.specGroup && scope.row.specGroup.length > 0">
              <span v-for="(spec, specIndex) in scope.row.specGroup" :key="specIndex">
                {{ spec.specValueName }}
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="categoryName" label="商品分类" min-width="120"></el-table-column>
        <el-table-column prop="isMaster" label="主/辅单位" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isMaster === 5 ? 'success' : 'info'" size="mini">
              {{ scope.row.isMaster === 5 ? "主" : "辅" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-check" @click="selectSingleSku(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
      <FooterPage
        :page-size="skuPageSize"
        :total-page.sync="skuTotal"
        :current-page.sync="skuPage"
        @pageChange="skuPageChange"
        @sizeChange="skuSizeChange"
      ></FooterPage>
      <div slot="footer" style="width: 100%; border-top: 1px solid #eee; padding-top: 10px">
        <el-button size="small" type="primary" @click="confirmSelectSku">确定</el-button>
        <el-button size="small" @click="cancelSelectGoods">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  checkRuleConflicts,
  createConsignmentRule,
  getConsignmentRuleDetail,
  getConsignmentSuppliers,
  updateConsignmentRule,
} from "@/api/SupplierConsignment";
import { getAllGoodsBasicBySku, getGoodsBasicInfoById } from "@/api/goods";
import FooterPage from "@/component/common/FooterPage";
import SelectSupplier from "@/component/common/SelectSupplier";

export default {
  name: "ConsignmentRuleForm",
  components: {
    FooterPage,
    SelectSupplier,
  },
  props: {
    ruleId: {
      type: [Number, String],
      default: null,
    },
    isDialog: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isEdit: false,
      dateRange: [],
      goods_show: false,
      currentSupplierName: "",

      // 表单数据
      ruleForm: {
        ruleName: "",
        ruleType: 4,
        supplierId: null,
        goodsType: 2, // 固定为"指定商品"
        startDate: "",
        endDate: "",
        priority: 10,
        status: 5, // 修改默认值为5(启用)
        remark: "",
        scopeGoods: [], // 参与分账的商品列表
      },

      // 表单验证规则
      rules: {
        ruleName: [
          { required: true, message: "请输入规则名称", trigger: "blur" },
          { max: 50, message: "规则名称不能超过50个字符", trigger: "blur" },
        ],
        ruleType: [{ required: true, message: "请选择规则类型", trigger: "change" }],
        supplierId: [{ required: true, message: "请选择供应商", trigger: "change" }],
        startDate: [{ required: true, message: "请选择开始日期", trigger: "change" }],
        endDate: [{ required: true, message: "请选择结束日期", trigger: "change" }],
        scopeGoods: [{ required: true, type: "array", message: "请选择商品", trigger: "change" }],
        priority: [{ required: true, message: "请输入优先级", trigger: "blur" }],
        remark: [{ max: 200, message: "备注不能超过200个字符", trigger: "blur" }],
      },

      // 供应商选项
      supplierOptions: [],
      supplierLoading: false,

      // 商品选项
      goodsOptions: [],
      goodsLoading: false,

      // SKU选择相关数据
      skuList: [],
      skuPage: 1,
      skuPageSize: 20,
      skuTotal: 0,
      skuKeyword: "",
      skuSelectedList: [],
    };
  },

  watch: {
    // 监听ruleId变化，当ruleId变化时重新初始化表单
    ruleId: {
      handler(newVal) {
        if (newVal) {
          this.isEdit = true;
          this.getRuleDetail(newVal);
        } else {
          this.isEdit = false;
          this.resetForm();
        }
      },
      immediate: false,
    },
  },
  created() {
    // 判断是否为编辑模式
    if (this.ruleId) {
      this.isEdit = true;
      this.getRuleDetail();
    } else if (this.$route && this.$route.query && this.$route.query.id) {
      this.isEdit = true;
      this.getRuleDetail(this.$route.query.id);
    }
    // 首次进入页面时请求供应商接口
    this.getSupplierList();
  },
  methods: {
    // 获取规则详情
    getRuleDetail(id) {
      const ruleId = id || this.ruleId;
      getConsignmentRuleDetail(ruleId).then(async (response) => {
        const data = response.data;

        // 解析规则内容
        const ruleContent = data.ruleContent || {};

        // 确保规则类型为固定金额模式
        data.ruleType = 4;

        // 准备商品数据
        let scopeGoods = [];
        if (ruleContent.skuFixedAmounts && Array.isArray(ruleContent.skuFixedAmounts)) {
          // 先创建基本的商品数据
          scopeGoods = ruleContent.skuFixedAmounts.map((item) => ({
            skuId: item.skuId,
            goodsId: item.goodsId,
            fixedAmount: item.fixedAmount,
            title: item.goodsName,
            code: item.goodsCode,
            // 添加空的规格信息，后续会填充
            unitName: "",
            specGroup: [],
          }));

          // 获取每个商品的详细信息，包括规格信息
          const goodsIds = [...new Set(scopeGoods.map((item) => item.goodsId))];

          // 使用Promise.all并行获取所有商品详情
          const goodsDetailsPromises = goodsIds.map((goodsId) =>
            getGoodsBasicInfoById(goodsId, { isAddGoods: 5 })
              .then((response) => ({ goodsId, details: response.data }))
              .catch(() => ({ goodsId, details: null }))
          );

          const goodsDetails = await Promise.all(goodsDetailsPromises);

          // 将获取到的详细信息合并到scopeGoods中
          scopeGoods = scopeGoods.map((item) => {
            const goodsDetail = goodsDetails.find((detail) => detail.goodsId === item.goodsId);

            if (goodsDetail && goodsDetail.details) {
              const details = goodsDetail.details;

              // 查找对应的SKU信息
              let skuInfo = null;
              if (details.specMultiple && details.specMultiple.length > 0) {
                skuInfo = details.specMultiple.find((spec) => spec.id === item.skuId);
              }

              if (skuInfo) {
                // 合并SKU信息
                return {
                  ...item,
                  unitName: skuInfo.unitName || "",
                  unitId: skuInfo.unitId,
                  specGroup: skuInfo.specGroup || [],
                  specGroupHash: skuInfo.specGroupHash,
                  isMaster: skuInfo.isMaster,
                  conversion: skuInfo.conversion,
                };
              } else if (details.unitData && details.unitData.length > 0) {
                // 如果找不到对应的SKU，使用第一个单位信息
                const unitInfo = details.unitData[0];
                return {
                  ...item,
                  unitName: unitInfo.unitName || "个",
                  unitId: unitInfo.unitId,
                  specGroup: [],
                  isMaster: unitInfo.isMaster,
                  conversion: unitInfo.conversion,
                };
              }
            }

            return item;
          });
        }

        this.ruleForm = {
          ruleName: data.ruleName,
          ruleType: data.ruleType,
          supplierId: data.supplierId,
          goodsType: 2, // 固定为"指定商品"
          startDate: "",
          endDate: "",
          priority: data.priority || 10,
          status: data.status, // 使用转换后的状态值
          remark: data.remarks,
          scopeGoods: scopeGoods,
        };

        // 设置日期范围
        if (data.startTime && data.endTime) {
          // 将时间戳转换为日期字符串
          const startDate = this.$_common.formatDate(data.startTime, "yyyy-MM-dd");
          const endDate = this.$_common.formatDate(data.endTime, "yyyy-MM-dd");
          this.dateRange = [startDate, endDate];
          this.ruleForm.startDate = startDate;
          this.ruleForm.endDate = endDate;
        }

        // 获取供应商名称
        if (data.supplierId) {
          this.getSupplierName(data.supplierId);
        }
      });
    },

    // 获取供应商列表
    getSupplierList() {
      this.supplierLoading = true;
      getConsignmentSuppliers({
        page: 1,
        pageSize: 20,
      })
        .then((response) => {
          this.supplierOptions = response.data;
          this.supplierLoading = false;
        })
        .catch(() => {
          this.supplierLoading = false;
        });
    },

    // 供应商选择变更
    handleSupplierChange(supplierId, supplierRow) {
      if (supplierId && supplierRow && supplierRow.length > 0) {
        this.currentSupplierName = supplierRow[0].title;
      } else {
        this.currentSupplierName = "";
      }

      // 当供应商变更时，清空已选择的商品，因为不同供应商的商品不应该混合
      if (this.ruleForm.scopeGoods.length > 0) {
        this.$confirm("更换供应商将清空已选择的商品，是否继续？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.ruleForm.scopeGoods = [];
          })
          .catch(() => {
            // 用户取消，恢复之前的供应商选择
            // 这里需要根据实际的SelectSupplier组件实现来处理
          });
      }
    },

    // 获取供应商名称
    getSupplierName(supplierId) {
      const supplier = this.supplierOptions.find((item) => item.supplierId === supplierId);
      if (supplier) {
        this.currentSupplierName = supplier.title;
      } else {
        // 如果在当前列表中找不到，尝试从接口获取
        getConsignmentSuppliers({
          page: 1,
          pageSize: 1,
          supplierId: supplierId,
        }).then((response) => {
          if (response.data && response.data.length > 0) {
            this.currentSupplierName = response.data[0].title;
          }
        });
      }
    },

    // 处理选择商品按钮点击
    handleSelectGoods() {
      if (!this.ruleForm.supplierId) {
        this.$message.warning("请先选择供应商");
        return;
      }
      this.goods_show = true;
      this.initSkuSelection();
    },

    // 初始化SKU选择
    initSkuSelection() {
      this.skuPage = 1;
      this.skuKeyword = "";
      this.skuSelectedList = [];
      this.getSkuList();
    },

    // 获取SKU列表
    async getSkuList() {
      try {
        const params = {
          page: this.skuPage,
          pageSize: this.skuPageSize,
          isRevealSku: 4, // 4: 主辅助单位都显示, 5: 只显示基本单位
        };

        // 添加供应商ID过滤
        if (this.ruleForm.supplierId) {
          params.supplierId = this.ruleForm.supplierId;
        }

        // 添加关键词搜索
        if (this.skuKeyword) {
          params.keyword = this.skuKeyword;
        }

        const { data, pageTotal } = await getAllGoodsBasicBySku(params);
        this.skuList = data || [];
        this.skuTotal = pageTotal || 0;
      } catch (error) {
        console.error("获取SKU列表失败:", error);
        this.$message.error("获取商品列表失败");
      }
    },

    // SKU分页变化
    skuPageChange(page) {
      this.skuPage = page;
      this.getSkuList();
    },

    // SKU分页大小变化
    skuSizeChange(size) {
      this.skuPageSize = size;
      this.skuPage = 1;
      this.getSkuList();
    },

    // SKU选择变化
    skuSelectionChange(selection) {
      this.skuSelectedList = selection;
    },

    // 选择单个SKU
    selectSingleSku(row) {
      this.$refs.skuTable.toggleRowSelection(row, true);
    },

    // 确认选择SKU
    confirmSelectSku() {
      if (this.skuSelectedList.length === 0) {
        this.$message.warning("请选择至少一个商品SKU");
        return;
      }

      // 转换SKU数据格式，与现有的selScopeGoods方法兼容
      const skuData = this.skuSelectedList.map((item) => ({
        skuId: item.skuId, // 使用正确的 skuId 字段
        goodsId: item.id, // 商品ID是 item.id
        fixedAmount: 0, // 默认固定金额为0
        title: item.title,
        code: item.code || "",
        unitId: item.unitId,
        unitName: item.unitName,
        isMaster: item.isMaster,
        conversion: item.conversion,
        specGroup: item.specGroup || [],
        specGroupHash: item.specGroupHash,
        categoryName: item.categoryName,
      }));

      // 使用现有的去重逻辑
      this.ruleForm.scopeGoods = this.$_common
        ? this.$_common.unique(this.ruleForm.scopeGoods.concat(skuData), ["skuId"])
        : [...this.ruleForm.scopeGoods, ...skuData];

      this.goods_show = false;
    },

    // 取消选择商品
    cancelSelectGoods() {
      this.goods_show = false;
      this.skuSelectedList = [];
    },

    // 删除参与分账的商品
    removeScopeGoods(index) {
      this.ruleForm.scopeGoods.splice(index, 1);
    },

    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val) {
        this.ruleForm.startDate = val[0];
        this.ruleForm.endDate = val[1];

        // 验证日期范围的有效性
        this.validateDateRange();
      } else {
        this.ruleForm.startDate = "";
        this.ruleForm.endDate = "";
      }
    },

    /**
     * 验证日期范围的有效性
     */
    validateDateRange() {
      if (this.ruleForm.startDate && this.ruleForm.endDate) {
        const startDate = new Date(this.ruleForm.startDate);
        const endDate = new Date(this.ruleForm.endDate);

        if (startDate > endDate) {
          this.$message.warning("开始日期不能晚于结束日期");
          return false;
        }
      }
      return true;
    },

    /**
     * 验证时间戳格式是否正确
     * @param {number} timestamp - Unix时间戳
     * @returns {boolean} - 是否为有效的时间戳
     */
    validateTimestamp(timestamp) {
      // 检查是否为数字且为整数
      if (typeof timestamp !== "number" || !Number.isInteger(timestamp)) {
        return false;
      }

      // 检查时间戳范围（1970年到2100年之间）
      const minTimestamp = 0; // 1970-01-01
      const maxTimestamp = 4102444800; // 2100-01-01

      return timestamp >= minTimestamp && timestamp <= maxTimestamp;
    },

    /**
     * 测试时间戳格式化功能（开发调试用）
     * 可以在浏览器控制台调用此方法验证时间戳格式
     */
    testTimestampFormat() {
      const testDate = "2024-01-15";

      const startTimestamp = this.formatDateToTimestamp(testDate, false);
      const endTimestamp = this.formatDateToTimestamp(testDate, true);

      console.group("时间戳格式测试");
      console.log("测试日期:", testDate);
      console.log(
        "开始时间戳:",
        startTimestamp,
        "类型:",
        typeof startTimestamp,
        "是否整数:",
        Number.isInteger(startTimestamp)
      );
      console.log(
        "结束时间戳:",
        endTimestamp,
        "类型:",
        typeof endTimestamp,
        "是否整数:",
        Number.isInteger(endTimestamp)
      );
      console.log("开始时间还原:", new Date(startTimestamp * 1000).toLocaleString());
      console.log("结束时间还原:", new Date(endTimestamp * 1000).toLocaleString());
      console.log("时间戳验证 - 开始:", this.validateTimestamp(startTimestamp));
      console.log("时间戳验证 - 结束:", this.validateTimestamp(endTimestamp));
      console.groupEnd();

      return { startTimestamp, endTimestamp };
    },

    /**
     * 格式化日期为Unix时间戳
     * @param {string} dateStr - 日期字符串 (格式: yyyy-MM-dd)
     * @param {boolean} isEndDate - 是否为结束日期
     * @returns {number|null} - Unix时间戳（秒级精度的整数）
     */
    formatDateToTimestamp(dateStr, isEndDate = false) {
      if (!dateStr) return null;

      const date = new Date(dateStr);

      if (isEndDate) {
        // 结束时间设置为当天的23:59:59
        // 精确到秒级别，确保包含整个结束日期
        date.setHours(23, 59, 59, 0);
      } else {
        // 开始时间设置为当天的00:00:00
        // 精确到秒级别，确保从一天的开始计算
        date.setHours(0, 0, 0, 0);
      }

      // 转换为Unix时间戳（秒）
      // 使用Math.floor确保返回整数类型
      return Math.floor(date.getTime() / 1000);
    },

    // 提交表单
    submitForm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 验证日期范围
          if (!this.validateDateRange()) {
            return;
          }

          // 检查规则冲突
          this.checkConflicts().then((hasConflict) => {
            if (hasConflict) {
              this.$confirm("当前规则与已有规则存在冲突，是否继续保存?", "规则冲突提示", {
                confirmButtonText: "继续保存",
                cancelButtonText: "取消",
                type: "warning",
              })
                .then(() => {
                  this.saveRule();
                })
                .catch(() => {
                  // 用户取消保存
                });
            } else {
              this.saveRule();
            }
          });
        }
      });
    },

    // 检查规则冲突
    checkConflicts() {
      return new Promise((resolve) => {
        if (this.isEdit) {
          checkRuleConflicts(this.ruleId)
            .then((response) => {
              resolve(response.data.hasConflict);
            })
            .catch(() => {
              resolve(false);
            });
        } else {
          // 创建新规则时，暂时无法检查冲突，直接返回false
          resolve(false);
        }
      });
    },

    // 保存规则
    saveRule() {
      try {
        const formData = { ...this.ruleForm };

        // 检查是否有选择商品
        if (!formData.scopeGoods || formData.scopeGoods.length === 0) {
          this.$message.error("请选择至少一个商品");
          return;
        }

        // 准备规则内容
        let ruleContent = {};

        // 固定金额模式
        ruleContent = {
          skuFixedAmounts: formData.scopeGoods.map((item) => {
            // 确保所有必需字段都存在且有效
            const skuId = parseInt(item.skuId);
            const goodsId = parseInt(item.goodsId);
            const fixedAmount = parseFloat(item.fixedAmount || 0);

            // 验证必需字段
            if (!skuId || !goodsId) {
              console.error("商品数据不完整:", item);
              throw new Error(`商品 ${item.title || "未知"} 的数据不完整，缺少skuId或goodsId`);
            }

            return {
              skuId: skuId,
              goodsId: goodsId,
              fixedAmount: fixedAmount,
              goodsName: item.title || "",
              goodsCode: item.code || "",
              // 保存规格信息，以便在编辑时能够显示
              unitName: item.unitName || "",
              unitId: item.unitId || null,
              specGroup: item.specGroup || [],
              specGroupHash: item.specGroupHash || "",
            };
          }),
        };

        // 设置规则内容
        formData.ruleContent = ruleContent;

        // 处理有效时间字段，确保时间格式统一
        if (formData.startDate) {
          const startTimestamp = this.formatDateToTimestamp(formData.startDate, false);

          // 验证时间戳格式
          if (!this.validateTimestamp(startTimestamp)) {
            throw new Error("开始时间戳格式不正确");
          }

          formData["startTime"] = startTimestamp;
          console.log(
            `开始时间处理: ${formData.startDate} -> ${startTimestamp} (Unix时间戳，整数类型: ${Number.isInteger(
              startTimestamp
            )})`
          );
          console.log(`开始时间验证: ${new Date(startTimestamp * 1000).toLocaleString()}`);
          delete formData.startDate;
        }
        if (formData.endDate) {
          const endTimestamp = this.formatDateToTimestamp(formData.endDate, true);

          // 验证时间戳格式
          if (!this.validateTimestamp(endTimestamp)) {
            throw new Error("结束时间戳格式不正确");
          }

          formData["endTime"] = endTimestamp;
          console.log(
            `结束时间处理: ${formData.endDate} -> ${endTimestamp} (Unix时间戳，整数类型: ${Number.isInteger(
              endTimestamp
            )})`
          );
          console.log(`结束时间验证: ${new Date(endTimestamp * 1000).toLocaleString()}`);
          delete formData.endDate;
        }

        // 将备注字段映射为后端期望的参数名
        if (formData.remark !== undefined) {
          // 使用索引访问来避免TypeScript错误
          formData["remarks"] = formData.remark;
          delete formData.remark;
        }

        // 确保优先级字段被正确传递
        if (formData.priority !== undefined) {
          formData.priority = parseInt(formData.priority);
        }

        // 删除不需要的字段
        delete formData.scopeGoods;

        if (this.isEdit) {
          updateConsignmentRule(this.ruleId, formData).then(() => {
            this.$message.success("规则更新成功");
            if (this.isDialog) {
              this.$emit("save-success");
            } else {
              this.goBack();
            }
          });
        } else {
          createConsignmentRule(formData).then(() => {
            this.$message.success("规则创建成功");
            if (this.isDialog) {
              this.$emit("save-success");
            } else {
              this.goBack();
            }
          });
        }
      } catch (error) {
        console.error("保存规则时出错:", error);
        this.$message.error(error.message || "保存失败，请检查数据是否完整");
      }
    },

    // 取消
    cancel() {
      if (this.isDialog) {
        this.$emit("cancel");
      } else {
        this.goBack();
      }
    },

    // 返回列表页
    goBack() {
      this.$router.push("/Supplier/ConsignmentRules");
    },

    // 重置表单
    resetForm() {
      this.dateRange = [];
      this.ruleForm = {
        ruleName: "",
        ruleType: 4,
        supplierId: null,
        goodsType: 2, // 固定为"指定商品"
        startDate: "",
        endDate: "",
        priority: 10,
        status: 5, // 默认启用
        remark: "",
        scopeGoods: [], // 参与分账的商品列表
      };

      // 如果表单已经被渲染，则重置验证状态
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.form-tip {
  margin-left: 5px;
  color: #606266;
}

.form-desc {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.tier-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.range-separator {
  margin: 0 5px;
}

.float_left {
  float: left;
  margin-right: 10px;
}

.clearfix:after {
  content: "";
  display: table;
  clear: both;
}

.goods-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.goods-no {
  color: #999;
  font-size: 12px;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.new-tag {
  cursor: pointer;
}

/* SKU选择弹窗样式 */
.sku-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.sku-search {
  margin-bottom: 15px;
}

.sku-table {
  .goods-title {
    font-weight: bold;
    color: #303133;
    margin-bottom: 4px;
  }

  .goods-no {
    color: #909399;
    font-size: 12px;
  }
}

.sku-spec {
  color: #606266;
  font-size: 13px;
}

.sku-pagination {
  margin-top: 15px;
  text-align: center;
}
</style>
