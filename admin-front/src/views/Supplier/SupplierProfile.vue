<template>
  <ContainerTit>
    <div class="supplier-profile">
      <el-card class="profile-card">
        <div slot="header" class="card-header">
          <span>个人信息</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="editProfile"
            v-if="!isEditing"
          >
            编辑信息
          </el-button>
        </div>
        
        <!-- 查看模式 -->
        <div v-if="!isEditing" class="profile-view">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">供应商编号：</span>
                <span class="value">{{ supplierInfo.code || '--' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">供应商名称：</span>
                <span class="value">{{ supplierInfo.title || '--' }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">联系人：</span>
                <span class="value">{{ supplierInfo.realName || '--' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">联系电话：</span>
                <span class="value">{{ supplierInfo.mobile || '--' }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">座机电话：</span>
                <span class="value">{{ supplierInfo.phone || '--' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">联系人职务：</span>
                <span class="value">{{ supplierInfo.position || '--' }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">电子邮箱：</span>
                <span class="value">{{ supplierInfo.email || '--' }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="info-item">
                <span class="label">地址：</span>
                <span class="value">
                  {{ getFullAddress() || '--' }}
                </span>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 编辑模式 -->
        <div v-else class="profile-edit">
          <el-form 
            :model="editForm" 
            :rules="rules" 
            ref="editForm" 
            label-width="100px"
            size="small"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="联系人" prop="realName">
                  <el-input v-model="editForm.realName" placeholder="请输入联系人姓名"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="mobile">
                  <el-input v-model="editForm.mobile" placeholder="请输入联系电话"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="座机电话" prop="phone">
                  <el-input v-model="editForm.phone" placeholder="请输入座机电话"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人职务" prop="position">
                  <el-input v-model="editForm.position" placeholder="请输入联系人职务"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="电子邮箱" prop="email">
                  <el-input v-model="editForm.email" placeholder="请输入电子邮箱"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="所在地区">
                  <RegionSelect 
                    v-model="regionModel" 
                    @change="handleRegionChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="详细地址" prop="address">
                  <el-input 
                    v-model="editForm.address" 
                    type="textarea" 
                    :rows="2" 
                    placeholder="请输入详细地址"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item>
              <el-button type="primary" @click="submitForm">保存</el-button>
              <el-button @click="cancelEdit">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
  </ContainerTit>
</template>

<script>
import { getSupplierProfile, updateSupplierProfile } from "@/api/SupplierProfile";
import RegionSelect from "@/component/common/RegionSelectJSON";

export default {
  name: "SupplierProfile",
  components: {
    RegionSelect
  },
  data() {
    return {
      supplierInfo: {},
      isEditing: false,
      editForm: {
        realName: "",
        mobile: "",
        phone: "",
        position: "",
        email: "",
        provinceCode: "",
        cityCode: "",
        districtCode: "",
        address: ""
      },
      regionModel: {
        provinceCode: "",
        cityCode: "",
        districtCode: ""
      },
      rules: {
        realName: [
          { required: true, message: "请输入联系人姓名", trigger: "blur" },
          { max: 50, message: "长度不能超过50个字符", trigger: "blur" }
        ],
        mobile: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        email: [
          { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.fetchSupplierProfile();
  },
  methods: {
    // 获取供应商个人信息
    async fetchSupplierProfile() {
      try {
        const response = await getSupplierProfile();
        if (response.errorcode === 0 && response.data) {
          this.supplierInfo = response.data;
          this.initEditForm();
        } else {
          this.$message.error(response.msg || "获取供应商信息失败");
        }
      } catch (error) {
        console.error("获取供应商信息出错:", error);
        this.$message.error("获取供应商信息失败");
      }
    },
    
    // 初始化编辑表单
    initEditForm() {
      const info = this.supplierInfo;
      this.editForm = {
        realName: info.realName || "",
        mobile: info.mobile || "",
        phone: info.phone || "",
        position: info.position || "",
        email: info.email || "",
        provinceCode: info.provinceCode || "",
        cityCode: info.cityCode || "",
        districtCode: info.districtCode || "",
        address: info.address || ""
      };
      
      this.regionModel = {
        provinceCode: info.provinceCode || "",
        cityCode: info.cityCode || "",
        districtCode: info.districtCode || ""
      };
    },
    
    // 获取完整地址
    getFullAddress() {
      const info = this.supplierInfo;
      if (!info.area) return "";
      
      let address = "";
      if (info.area.provinceName) address += info.area.provinceName;
      if (info.area.cityName) address += " " + info.area.cityName;
      if (info.area.districtName) address += " " + info.area.districtName;
      if (info.area.address) address += " " + info.area.address;
      
      return address;
    },
    
    // 切换到编辑模式
    editProfile() {
      this.isEditing = true;
      this.initEditForm();
    },
    
    // 取消编辑
    cancelEdit() {
      this.isEditing = false;
      this.initEditForm();
    },
    
    // 处理地区选择变化
    handleRegionChange(data) {
      this.editForm.provinceCode = data.provinceCode || "";
      this.editForm.cityCode = data.cityCode || "";
      this.editForm.districtCode = data.districtCode || "";
    },
    
    // 提交表单
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          try {
            const response = await updateSupplierProfile(this.editForm);
            if (response.errorcode === 0) {
              this.$message.success("个人信息更新成功");
              this.isEditing = false;
              this.fetchSupplierProfile(); // 重新获取最新信息
            } else {
              this.$message.error(response.msg || "更新失败");
            }
          } catch (error) {
            console.error("更新供应商信息出错:", error);
            this.$message.error("更新供应商信息失败");
          }
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.supplier-profile {
  padding: 20px;
  
  .profile-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .info-item {
      margin-bottom: 15px;
      
      .label {
        font-weight: bold;
        color: #606266;
        margin-right: 10px;
      }
      
      .value {
        color: #333;
      }
    }
  }
}
</style>
