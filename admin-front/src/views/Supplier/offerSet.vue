<template>
  <Container>
    <div slot="right">
      <el-form size="small" :inline="true">
        <el-form-item label="供应商">
          <SelectSupplier v-model="searchDate.supplierId" @clear="clearSupplier" @change="selUnitSupplier" />
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            v-model="searchDate.time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="offer_list" style="width: 100%">
      <el-table-column prop="no" label="报价单编码" min-width="180">
        <template slot-scope="scope">
          <span class="click-div" @click="goDetail(scope.row)">
            {{ scope.row.no }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="supplierName" label="供应商" min-width="140"></el-table-column>
      <el-table-column prop="materielNum" label="商品数量" min-width="140"></el-table-column>
      <el-table-column prop="name" label="创建时间" min-width="180">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="审核状态" min-width="140">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 1" class="info-status"> 待审核 </span>
          <span v-else class="success-status">已审核</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$accessCheck($Access.offerSet_auditSupplierOfferPrice)"
        prop="address"
        label="操作"
        min-width="140"
      >
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.offerSet_auditSupplierOfferPrice)"
            size="mini"
            type="text"
            :disabled="scope.row.auditStatus !== 1"
            @click="auditSupplier(scope.row.id)"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { getAllSupplierOfferPrice, auditSupplierOfferPrice } from "@/api/Supplier";
import SelectSupplier from "@/component/common/SelectSupplier";
import { updatePurchaseStatus } from "@/api/Purchase";
export default {
  name: "OfferSet",
  components: {
    SelectSupplier,
  },
  data() {
    return {
      searchDate: {
        supplierId: "",
        time: "",
        start: "",
        end: "",
      },
      offer_list: [],
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  created() {
    this.getAllSupplierOfferPrice();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllSupplierOfferPrice();
  },
  methods: {
    pageChange(val) {
      this.page = val;
      this.getAllSupplierOfferPrice();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    timeChange(val) {
      if (val && val.length) {
        this.searchDate.start = parseInt(val[0] / 1000);
        this.searchDate.end = parseInt(val[1] / 1000) + 86399;
      } else {
        this.searchDate.start = "";
        this.searchDate.end = "";
      }
      this.pageChange(1);
    },
    async getAllSupplierOfferPrice() {
      const data = await getAllSupplierOfferPrice({
        page: this.page,
        pageSize: this.pageSize,
        supplierId: this.searchDate.supplierId,
        startTime: this.searchDate.start,
        endTime: this.searchDate.end,
      });
      this.offer_list = data.data;
      this.total = data.pageTotal;
    },
    async auditSupplier(id) {
      this.$confirm("确定审核该报价单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await auditSupplierOfferPrice(id);

        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.getAllSupplierOfferPrice();
      });
    },
    goDetail(row) {
      this.$router.push(`/Supplier/offerSetInfo?id=` + row.id);
    },
    clearSupplier() {
      this.searchDate.supplierId = "";
      this.pageChange(1);
    },
    selUnitSupplier(val) {
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
