<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>结算周期配置</span>
      </div>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="默认配置" name="default">
          <el-form ref="defaultForm" :model="defaultForm" :rules="rules" label-width="120px">
            <el-form-item label="结算周期" prop="cycleType">
              <el-radio-group v-model="defaultForm.cycleType">
                <el-radio :label="1">T+N</el-radio>
                <el-radio :label="2">按周结算</el-radio>
                <el-radio :label="3">按月结算</el-radio>
                <el-radio :label="4">按季结算</el-radio>
                <el-radio :label="5">按年结算</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="defaultForm.cycleType === 1" label="账期天数" prop="cycleDays">
              <el-input-number
                v-model="defaultForm.cycleDays"
                :min="1"
                :max="90"
                placeholder="请输入T+N中的N天数"
              ></el-input-number>
              <span class="form-tip">天</span>
            </el-form-item>

            <el-form-item v-if="defaultForm.cycleType === 2" label="结算日" prop="cycleDay">
              <el-select v-model="defaultForm.cycleDay" placeholder="请选择结算日">
                <el-option label="周一" :value="1"></el-option>
                <el-option label="周二" :value="2"></el-option>
                <el-option label="周三" :value="3"></el-option>
                <el-option label="周四" :value="4"></el-option>
                <el-option label="周五" :value="5"></el-option>
                <el-option label="周六" :value="6"></el-option>
                <el-option label="周日" :value="7"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item v-if="defaultForm.cycleType === 3" label="结算日" prop="cycleDay">
              <el-select v-model="defaultForm.cycleDay" placeholder="请选择结算日">
                <el-option v-for="i in 28" :key="i" :label="`每月${i}日`" :value="i"></el-option>
                <el-option key="last" label="每月最后一天" :value="0"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item v-if="defaultForm.cycleType === 4" label="结算月/日" prop="cycleMonth">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-select v-model="defaultForm.cycleMonth" placeholder="请选择结算月">
                    <el-option label="第一个月" :value="1"></el-option>
                    <el-option label="第二个月" :value="2"></el-option>
                    <el-option label="第三个月" :value="3"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="defaultForm.cycleDay" placeholder="请选择结算日">
                    <el-option v-for="i in 28" :key="i" :label="`${i}日`" :value="i"></el-option>
                    <el-option key="last" label="月末" :value="0"></el-option>
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item v-if="defaultForm.cycleType === 5" label="结算月/日" prop="cycleMonth">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-select v-model="defaultForm.cycleMonth" placeholder="请选择结算月">
                    <el-option v-for="i in 12" :key="i" :label="`${i}月`" :value="i"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="defaultForm.cycleDay" placeholder="请选择结算日">
                    <el-option v-for="i in 28" :key="i" :label="`${i}日`" :value="i"></el-option>
                    <el-option key="last" label="月末" :value="0"></el-option>
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item v-if="defaultForm.cycleType !== 1" label="账期天数" prop="cycleDays">
              <el-input-number
                v-model="defaultForm.cycleDays"
                :min="0"
                :max="90"
                placeholder="结算后多少天内付款"
              ></el-input-number>
              <span class="form-tip">天</span>
            </el-form-item>

            <el-form-item label="自动生成结算单" prop="autoGenerate">
              <el-switch v-model="defaultForm.autoGenerate"></el-switch>
            </el-form-item>

            <el-form-item label="自动审核" prop="autoApprove">
              <el-switch v-model="defaultForm.autoApprove"></el-switch>
              <span class="form-tip">开启后，系统将自动审核结算单</span>
            </el-form-item>

            <el-form-item label="最小结算金额" prop="minSettlementAmount">
              <el-input-number
                v-model="defaultForm.minSettlementAmount"
                :min="0"
                :precision="2"
                :step="100"
                placeholder="最小结算金额"
              ></el-input-number>
              <span class="form-tip">元，低于此金额将累计到下一结算周期</span>
            </el-form-item>

            <el-form-item label="结算通知" prop="notificationEnabled">
              <el-switch v-model="defaultForm.notificationEnabled"></el-switch>
            </el-form-item>

            <el-form-item v-if="defaultForm.notificationEnabled" label="通知方式" prop="notificationMethods">
              <el-checkbox-group v-model="defaultForm.notificationMethods">
                <el-checkbox label="email">邮件</el-checkbox>
                <el-checkbox label="sms">短信</el-checkbox>
                <el-checkbox label="system">系统消息</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveDefaultConfig">保存默认配置</el-button>
              <el-button @click="resetDefaultForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="供应商配置" name="supplier">
          <el-form :inline="true" :model="queryParams" class="demo-form-inline">
            <el-form-item label="供应商">
              <SelectSupplier
                v-model="queryParams.supplierId"
                @clear="clearSupplier"
                @change="handleSupplierChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="fetchSupplierConfig">查询</el-button>
            </el-form-item>
          </el-form>

          <div v-if="supplierSelected">
            <el-alert
              title="供应商特定配置将覆盖默认配置"
              type="info"
              :closable="false"
              show-icon
              style="margin-bottom: 20px"
            ></el-alert>

            <el-form ref="supplierForm" :model="supplierForm" :rules="rules" label-width="140px">
              <el-form-item label="使用默认配置" prop="useDefaultConfig">
                <el-switch v-model="supplierForm.useDefaultConfig"></el-switch>
                <span class="form-tip">开启后，将使用系统默认配置</span>
              </el-form-item>

              <template v-if="supplierForm.useDefaultConfig">
                <el-alert
                  title="当前使用系统默认配置，如需自定义配置，请关闭上方开关"
                  type="info"
                  :closable="false"
                  show-icon
                  style="margin: 15px 0"
                ></el-alert>

                <!-- 显示默认配置的只读信息 -->
                <el-form-item label="默认结算周期">
                  <span>{{ getCycleTypeText(defaultForm.cycleType) }}</span>
                </el-form-item>

                <el-form-item v-if="defaultForm.cycleType === 1" label="默认账期天数">
                  <span>{{ defaultForm.cycleDays }} 天</span>
                </el-form-item>

                <el-form-item v-if="defaultForm.cycleType === 2" label="默认结算日">
                  <span>{{ getWeekdayText(defaultForm.cycleDay) }}</span>
                </el-form-item>

                <el-form-item v-if="defaultForm.cycleType === 3" label="默认结算日">
                  <span>{{ defaultForm.cycleDay === 0 ? "每月最后一天" : `每月${defaultForm.cycleDay}日` }}</span>
                </el-form-item>

                <el-form-item v-if="defaultForm.cycleType === 4" label="默认结算月/日">
                  <span
                    >第{{ defaultForm.cycleMonth }}个月
                    {{ defaultForm.cycleDay === 0 ? "月末" : `${defaultForm.cycleDay}日` }}</span
                  >
                </el-form-item>

                <el-form-item v-if="defaultForm.cycleType === 5" label="默认结算月/日">
                  <span
                    >{{ defaultForm.cycleMonth }}月
                    {{ defaultForm.cycleDay === 0 ? "月末" : `${defaultForm.cycleDay}日` }}</span
                  >
                </el-form-item>

                <el-form-item v-if="defaultForm.cycleType !== 1" label="默认账期天数">
                  <span>{{ defaultForm.cycleDays }} 天</span>
                </el-form-item>

                <el-form-item label="默认自动生成结算单">
                  <span>{{ defaultForm.autoGenerate ? "是" : "否" }}</span>
                </el-form-item>

                <el-form-item label="默认自动审核">
                  <span>{{ defaultForm.autoApprove ? "是" : "否" }}</span>
                </el-form-item>

                <el-form-item label="默认最小结算金额">
                  <span>{{ defaultForm.minSettlementAmount }} 元</span>
                </el-form-item>

                <el-form-item label="默认结算通知">
                  <span>{{ defaultForm.notificationEnabled ? "开启" : "关闭" }}</span>
                </el-form-item>

                <el-form-item v-if="defaultForm.notificationEnabled" label="默认通知方式">
                  <span>{{ getNotificationMethodsText(defaultForm.notificationMethods) }}</span>
                </el-form-item>
              </template>

              <template v-else>
                <el-form-item label="结算周期" prop="cycleType">
                  <el-radio-group v-model="supplierForm.cycleType">
                    <el-radio :label="1">T+N</el-radio>
                    <el-radio :label="2">按周结算</el-radio>
                    <el-radio :label="3">按月结算</el-radio>
                    <el-radio :label="4">按季结算</el-radio>
                    <el-radio :label="5">按年结算</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item v-if="supplierForm.cycleType === 1" label="账期天数" prop="cycleDays">
                  <el-input-number
                    v-model="supplierForm.cycleDays"
                    :min="1"
                    :max="90"
                    placeholder="请输入T+N中的N天数"
                  ></el-input-number>
                  <span class="form-tip">天</span>
                </el-form-item>

                <el-form-item v-if="supplierForm.cycleType === 2" label="结算日" prop="cycleDay">
                  <el-select v-model="supplierForm.cycleDay" placeholder="请选择结算日">
                    <el-option label="周一" :value="1"></el-option>
                    <el-option label="周二" :value="2"></el-option>
                    <el-option label="周三" :value="3"></el-option>
                    <el-option label="周四" :value="4"></el-option>
                    <el-option label="周五" :value="5"></el-option>
                    <el-option label="周六" :value="6"></el-option>
                    <el-option label="周日" :value="7"></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item v-if="supplierForm.cycleType === 3" label="结算日" prop="cycleDay">
                  <el-select v-model="supplierForm.cycleDay" placeholder="请选择结算日">
                    <el-option v-for="i in 28" :key="i" :label="`每月${i}日`" :value="i"></el-option>
                    <el-option key="last" label="每月最后一天" :value="0"></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item v-if="supplierForm.cycleType === 4" label="结算月/日" prop="cycleMonth">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-select v-model="supplierForm.cycleMonth" placeholder="请选择结算月">
                        <el-option label="第一个月" :value="1"></el-option>
                        <el-option label="第二个月" :value="2"></el-option>
                        <el-option label="第三个月" :value="3"></el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="supplierForm.cycleDay" placeholder="请选择结算日">
                        <el-option v-for="i in 28" :key="i" :label="`${i}日`" :value="i"></el-option>
                        <el-option key="last" label="月末" :value="0"></el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </el-form-item>

                <el-form-item v-if="supplierForm.cycleType === 5" label="结算月/日" prop="cycleMonth">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-select v-model="supplierForm.cycleMonth" placeholder="请选择结算月">
                        <el-option v-for="i in 12" :key="i" :label="`${i}月`" :value="i"></el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="supplierForm.cycleDay" placeholder="请选择结算日">
                        <el-option v-for="i in 28" :key="i" :label="`${i}日`" :value="i"></el-option>
                        <el-option key="last" label="月末" :value="0"></el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </el-form-item>

                <el-form-item v-if="supplierForm.cycleType !== 1" label="账期天数" prop="cycleDays">
                  <el-input-number
                    v-model="supplierForm.cycleDays"
                    :min="0"
                    :max="90"
                    placeholder="结算后多少天内付款"
                  ></el-input-number>
                  <span class="form-tip">天</span>
                </el-form-item>

                <el-form-item label="自动生成结算单" prop="autoGenerate">
                  <el-switch v-model="supplierForm.autoGenerate"></el-switch>
                </el-form-item>

                <el-form-item label="自动审核" prop="autoApprove">
                  <el-switch v-model="supplierForm.autoApprove"></el-switch>
                  <span class="form-tip">开启后，系统将自动审核结算单</span>
                </el-form-item>

                <el-form-item label="最小结算金额" prop="minSettlementAmount">
                  <el-input-number
                    v-model="supplierForm.minSettlementAmount"
                    :min="0"
                    :precision="2"
                    :step="100"
                    placeholder="最小结算金额"
                  ></el-input-number>
                  <span class="form-tip">元，低于此金额将累计到下一结算周期</span>
                </el-form-item>

                <el-form-item label="结算通知" prop="notificationEnabled">
                  <el-switch v-model="supplierForm.notificationEnabled"></el-switch>
                </el-form-item>

                <el-form-item v-if="supplierForm.notificationEnabled" label="通知方式" prop="notificationMethods">
                  <el-checkbox-group v-model="supplierForm.notificationMethods">
                    <el-checkbox label="email">邮件</el-checkbox>
                    <el-checkbox label="sms">短信</el-checkbox>
                    <el-checkbox label="system">系统消息</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </template>

              <el-form-item>
                <el-button type="primary" @click="saveSupplierConfig">保存供应商配置</el-button>
                <el-button @click="resetSupplierForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div v-else class="empty-data">
            <el-empty description="请选择供应商"></el-empty>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { getConsignmentSuppliers, getSettlementCycle, updateSettlementCycle } from "@/api/SupplierConsignment";
import SelectSupplier from "@/component/common/SelectSupplier";

export default {
  name: "SettlementCycle",
  components: {
    SelectSupplier,
  },
  data() {
    return {
      activeTab: "default",
      // 默认配置表单
      defaultForm: {
        cycleType: 1, // 1-T+N, 2-周结, 3-月结, 4-季结, 5-年结
        cycleDay: 1,
        cycleMonth: 1, // 季结和年结时使用
        cycleDays: 7,
        autoGenerate: true,
        autoApprove: false,
        minSettlementAmount: 100,
        notificationEnabled: true,
        notificationMethods: ["email", "system"],
        status: 5, // 默认启用
      },
      // 供应商配置表单
      supplierForm: {
        supplierId: null,
        useDefaultConfig: true,
        cycleType: 1,
        cycleDay: 1,
        cycleMonth: 1,
        cycleDays: 7,
        autoGenerate: true,
        autoApprove: false,
        minSettlementAmount: 100,
        notificationEnabled: true,
        notificationMethods: ["email", "system"],
        status: 5, // 默认启用
      },
      // 表单校验规则
      rules: {
        cycleType: [{ required: true, message: "请选择结算周期", trigger: "change" }],
        cycleDay: [{ required: true, message: "请选择结算日", trigger: "change" }],
        cycleMonth: [{ required: true, message: "请选择结算月", trigger: "change" }],
        cycleDays: [{ required: true, message: "请输入账期天数", trigger: "blur" }],
        minSettlementAmount: [{ required: true, message: "请输入最小结算金额", trigger: "blur" }],
        notificationMethods: [{ required: true, message: "请选择通知方式", trigger: "change" }],
      },
      // 查询参数
      queryParams: {
        supplierId: null,
      },
      // 供应商选项
      supplierOptions: [],
      // 供应商加载状态
      supplierLoading: false,
      // 是否已选择供应商
      supplierSelected: false,
    };
  },
  created() {
    this.fetchDefaultConfig();
    this.getSupplierList();
  },
  methods: {
    // 获取默认配置
    fetchDefaultConfig() {
      getSettlementCycle({ supplierId: 0 }).then((response) => {
        const data = response.data;
        this.defaultForm = {
          cycleType: data.cycleType,
          cycleDay: data.cycleDay,
          cycleMonth: data.cycleMonth || 1,
          cycleDays: data.cycleDays,
          autoGenerate: data.autoGenerate,
          autoApprove: data.autoApprove,
          minSettlementAmount: data.minSettlementAmount,
          notificationEnabled: data.notificationEnabled,
          notificationMethods: Array.isArray(data.notificationMethods)
            ? data.notificationMethods
            : data.notificationMethods
            ? data.notificationMethods.split(",")
            : ["email", "system"],
          status: data.status || 5,
        };
      });
    },

    // 保存默认配置
    saveDefaultConfig() {
      this.$refs.defaultForm.validate((valid) => {
        if (valid) {
          const formData = {
            useDefaultConfig: true, // 明确标记为默认配置
            supplierId: 0, // 默认配置的供应商ID为0
            cycleType: this.defaultForm.cycleType,
            cycleDay: this.defaultForm.cycleDay,
            cycleMonth: this.defaultForm.cycleMonth,
            cycleDays: this.defaultForm.cycleDays,
            autoGenerate: this.defaultForm.autoGenerate,
            autoApprove: this.defaultForm.autoApprove,
            minSettlementAmount: this.defaultForm.minSettlementAmount,
            notificationEnabled: this.defaultForm.notificationEnabled,
            notificationMethods: this.defaultForm.notificationEnabled ? this.defaultForm.notificationMethods : [],
            status: this.defaultForm.status,
          };

          updateSettlementCycle(formData).then(() => {
            this.$message.success("默认配置保存成功");
          });
        }
      });
    },

    // 重置默认表单
    resetDefaultForm() {
      this.fetchDefaultConfig();
    },

    // 获取供应商列表
    getSupplierList() {
      this.supplierLoading = true;
      getConsignmentSuppliers({
        page: 1,
        pageSize: 20,
      })
        .then((response) => {
          this.supplierOptions = response.data;
          this.supplierLoading = false;
        })
        .catch(() => {
          this.supplierLoading = false;
        });
    },

    // 清除供应商选择
    clearSupplier() {
      this.queryParams.supplierId = null;
      this.supplierSelected = false;
    },

    // 供应商选择变更
    handleSupplierChange(supplierId, supplierRow) {
      if (supplierId) {
        this.queryParams.supplierId = supplierId;
        this.supplierSelected = true;
        this.fetchSupplierConfig();
      } else {
        this.supplierSelected = false;
      }
    },

    // 获取供应商配置
    fetchSupplierConfig() {
      if (!this.queryParams.supplierId) {
        this.$message.warning("请选择供应商");
        return;
      }

      getSettlementCycle(this.queryParams.supplierId).then((response) => {
        const data = response.data;
        this.supplierForm = {
          supplierId: this.queryParams.supplierId,
          useDefaultConfig: data.useDefaultConfig,
          cycleType: data.cycleType,
          cycleDay: data.cycleDay,
          cycleMonth: data.cycleMonth || 1,
          cycleDays: data.cycleDays,
          autoGenerate: data.autoGenerate,
          autoApprove: data.autoApprove,
          minSettlementAmount: data.minSettlementAmount,
          notificationEnabled: data.notificationEnabled,
          notificationMethods: Array.isArray(data.notificationMethods)
            ? data.notificationMethods
            : data.notificationMethods
            ? data.notificationMethods.split(",")
            : ["email", "system"],
          status: data.status || 5,
        };
        this.supplierSelected = true;
      });
    },

    // 保存供应商配置
    saveSupplierConfig() {
      if (!this.queryParams.supplierId) {
        this.$message.warning("请选择供应商");
        return;
      }

      if (this.supplierForm.useDefaultConfig) {
        // 使用默认配置
        updateSettlementCycle({
          supplierId: this.queryParams.supplierId,
          useDefaultConfig: true,
        }).then(() => {
          this.$message.success("供应商配置保存成功");
        });
      } else {
        // 使用自定义配置
        this.$refs.supplierForm.validate((valid) => {
          if (valid) {
            const formData = {
              supplierId: this.queryParams.supplierId,
              useDefaultConfig: false,
              cycleType: this.supplierForm.cycleType,
              cycleDay: this.supplierForm.cycleDay,
              cycleMonth: this.supplierForm.cycleMonth,
              cycleDays: this.supplierForm.cycleDays,
              autoGenerate: this.supplierForm.autoGenerate,
              autoApprove: this.supplierForm.autoApprove,
              minSettlementAmount: this.supplierForm.minSettlementAmount,
              notificationEnabled: this.supplierForm.notificationEnabled,
              notificationMethods: this.supplierForm.notificationEnabled ? this.supplierForm.notificationMethods : [],
              status: this.supplierForm.status,
            };

            updateSettlementCycle(formData).then(() => {
              this.$message.success("供应商配置保存成功");
            });
          }
        });
      }
    },

    // 重置供应商表单
    resetSupplierForm() {
      this.fetchSupplierConfig();
    },

    // 获取周期类型文本
    getCycleTypeText(cycleType) {
      const typeMap = {
        1: "T+N",
        2: "按周结算",
        3: "按月结算",
        4: "按季结算",
        5: "按年结算",
      };
      return typeMap[cycleType] || "未知类型";
    },

    // 获取星期几文本
    getWeekdayText(day) {
      const weekMap = {
        1: "周一",
        2: "周二",
        3: "周三",
        4: "周四",
        5: "周五",
        6: "周六",
        7: "周日",
      };
      return weekMap[day] || "未知";
    },

    // 获取通知方式文本
    getNotificationMethodsText(methods) {
      if (!methods || methods.length === 0) {
        return "无";
      }

      const methodMap = {
        email: "邮件",
        sms: "短信",
        system: "系统消息",
      };

      return methods.map((method) => methodMap[method] || method).join("、");
    },
  },
};
</script>

<style lang="scss" scoped>
.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.empty-data {
  padding: 40px 0;
}
</style>
