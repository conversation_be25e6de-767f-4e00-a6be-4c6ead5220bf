<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>供应商提现管理</span>
        <div style="float: right;">
          <el-button type="primary" size="small" @click="goToWithdrawApply">申请提现</el-button>
          <el-button type="info" size="small" @click="goToWithdrawList">提现记录</el-button>
        </div>
      </div>

      <el-form :inline="true" :model="searchDate" class="demo-form-inline">
        <el-form-item label="供应商">
          <SelectSupplier v-model="searchDate.supplierId" @clear="clearSupplier" @change="selUnitSupplier" />
        </el-form-item>
        <el-form-item label="提现方式">
          <el-select v-model="searchDate.settlement_type" clearable placeholder="请选择提现方式">
            <el-option v-for="item in type_form" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model="searchDate.time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="settlementDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-tabs v-model="activeName" type="card" @tab-click="tabChange">
        <el-tab-pane label="全部" name="0"></el-tab-pane>
        <el-tab-pane label="待审核" name="1"></el-tab-pane>
        <el-tab-pane label="待打款" name="4"></el-tab-pane>
        <el-tab-pane label="已结算" name="2"></el-tab-pane>
        <el-tab-pane label="已拒绝" name="3"></el-tab-pane>
      </el-tabs>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        empty-text="暂无数据"
        highlight-current-row
      >
        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column prop="accountContent.name" label="姓名" width="100" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="accountContent.account" label="账号" width="140" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="supplierName" label="供应商" width="120" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="money" label="提现金额" width="120" align="right">
          <template slot-scope="scope">
            <span class="text-green">{{ $_common.formattedNumber(scope.row.money) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="提现方式" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getTypeTag(scope.row.type)" size="mini">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="auditStatus" label="提现状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.auditStatus === 3" effect="dark" :content="scope.row.reason" placement="top">
              <el-tag :type="getStatusTag(scope.row.auditStatus)" size="mini">
                {{ getStatusText(scope.row.auditStatus) }}
              </el-tag>
            </el-tooltip>
            <el-tag v-else :type="getStatusTag(scope.row.auditStatus)" size="mini">
              {{ getStatusText(scope.row.auditStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" width="160" align="center">
          <template slot-scope="scope">
            {{ scope.row.createTime ? $_common.formatDate(scope.row.createTime) : "-" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              v-if="$accessCheck($Access.SupplierSettlement_SupplierWithdrawalUpStatus)"
              type="primary"
              size="mini"
              plain
              :disabled="scope.row.auditStatus === 2 || scope.row.auditStatus === 3 || scope.row.auditStatus === 4"
              @click="editSel(4, scope.row.id)"
            >
              审核
            </el-button>
            <el-button
              v-if="$accessCheck($Access.SupplierSettlement_SupplierWithdrawalRefulse)"
              type="danger"
              size="mini"
              plain
              :disabled="scope.row.auditStatus === 2 || scope.row.auditStatus === 3 || scope.row.auditStatus === 4"
              @click="deleteSelt(scope.row.id)"
            >
              拒绝
            </el-button>
            <el-button
              v-if="$accessCheck($Access.SupplierSettlement_SupplierWithdrawalCash)"
              type="success"
              size="mini"
              plain
              :disabled="scope.row.auditStatus !== 4"
              @click="editSel(2, scope.row.id)"
            >
              打款
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <FooterPage
        v-show="total > 0"
        :total-page="total"
        :current-page.sync="page"
        :page-size="pageSize"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      />
    </el-card>

    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="拒绝原因"
      :visible.sync="deleteSel"
      width="30%"
    >
      <span>
        <el-form ref="add_form" :rules="rules" label-width="100px" class="demo-ruleForm" :model="add_form">
          <el-form-item label="拒绝原因" prop="delete_reason">
            <el-input v-model="add_form.delete_reason" placeholder="请输入拒绝原因" size="small"></el-input>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteSel = false">取 消</el-button>
        <el-button type="primary" @click="deleteSon">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAllSupplierWithdrawal, updateAuditStatus } from "@/api/Supplier";
import SelectSupplier from "@/component/common/SelectSupplier";
export default {
  name: "Settlement",
  components: {
    SelectSupplier,
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      searchDate: {
        supplierId: "",
        settlement_type: "",
        settlement_status: "",
        time: [],
        start: "",
        end: "",
      },
      add_form: {
        delete_reason: "",
      },
      deleteSel: false,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      del_id: 0,
      rules: {
        delete_reason: [{ required: true, message: "请输入拒绝原因", trigger: "blur" }],
      },
      activeName: "0",
      type_form: [
        {
          value: 0,
          label: "全部",
        },
        {
          value: 1,
          label: "微信钱包",
        },
        {
          value: 2,
          label: "支付宝",
        },
        {
          value: 3,
          label: "银行卡",
        },
      ],
    };
  },
  created() {
    this.getAllSupplierWithdrawal();
  },
  methods: {
    tabChange() {
      this.searchDate.settlement_status = parseInt(this.activeName);
      this.pageChange(1);
    },
    pageChange(val) {
      this.page = val;
      this.getAllSupplierWithdrawal();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    settlementDate(val) {
      if (val && val.length) {
        this.searchDate.start = val[0] / 1000;
        this.searchDate.end = val[1] / 1000 + 86399;
      } else {
        this.searchDate.start = "";
        this.searchDate.end = "";
      }
      this.pageChange(1);
    },
    async getAllSupplierWithdrawal() {
      this.loading = true;
      try {
        const data = await getAllSupplierWithdrawal({
          page: this.page,
          pageSize: this.pageSize,
          auditStatus: this.searchDate.settlement_status,
          startTime: this.searchDate.start,
          endTime: this.searchDate.end,
          type: this.searchDate.settlement_type,
          supplierId: this.searchDate.supplierId,
        });
        this.tableData = data.data;
        this.total = data.pageTotal;
      } catch (error) {
        console.error("获取数据失败:", error);
      } finally {
        this.loading = false;
      }
    },
    editSel(status, id) {
      const tip = status === 4 ? "通过审核" : "打款";
      this.$confirm(`确定要${tip}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await updateAuditStatus(id, {
          status: status,
        });
        this.$message.success("操作成功");
        this.getAllSupplierWithdrawal();
      });
    },
    deleteSelt(id) {
      this.deleteSel = true;
      this.del_id = id;
    },
    async deleteSon() {
      this.$refs.add_form.validate(async (valid) => {
        if (valid) {
          await updateAuditStatus(this.del_id, {
            status: 3,
            reason: this.add_form.delete_reason,
          });
          this.deleteSel = false;
          this.$message.success("操作成功");
          this.getAllSupplierWithdrawal();
        }
      });
    },
    clearSupplier() {
      this.searchDate.supplierId = "";
      this.pageChange(1);
    },
    selUnitSupplier() {
      this.pageChange(1);
    },
    // 查询按钮
    handleQuery() {
      this.page = 1;
      this.getAllSupplierWithdrawal();
    },
    // 重置查询
    resetQuery() {
      this.searchDate = {
        supplierId: "",
        settlement_type: "",
        settlement_status: "",
        time: [],
        start: "",
        end: "",
      };
      this.activeName = "0";
      this.page = 1;
      this.getAllSupplierWithdrawal();
    },
    // 获取提现方式标签样式
    getTypeTag(type) {
      const typeMap = {
        1: "success", // 微信钱包
        2: "warning", // 支付宝
        3: "info", // 银行卡
      };
      return typeMap[type] || "";
    },
    // 获取提现方式文本
    getTypeText(type) {
      const typeMap = {
        1: "微信钱包",
        2: "支付宝",
        3: "银行卡",
      };
      return typeMap[type] || "其他";
    },
    // 获取状态标签样式
    getStatusTag(status) {
      const statusMap = {
        1: "info", // 待审核
        2: "success", // 已结算
        3: "danger", // 已拒绝
        4: "primary", // 待打款
      };
      return statusMap[status] || "";
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: "待审核",
        2: "已结算",
        3: "已拒绝",
        4: "待打款",
      };
      return statusMap[status] || "其他";
    },
    // 跳转到提现申请页面
    goToWithdrawApply() {
      this.$router.push("/Supplier/WithdrawApply");
    },
    // 跳转到提现记录页面
    goToWithdrawList() {
      this.$router.push("/Supplier/WithdrawList");
    },
  },
};
</script>

<style lang="scss" scoped>
.text-red {
  color: #f56c6c;
}

.text-green {
  color: #67c23a;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
