<template>
  <Container>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示：单据模板用于打印各类单据时，根据不同的单据类型，按照模板的样式进行打印，你可以根据你的需要定制你的单据模板。
    </div>
    <!--    <el-form-->
    <!--      slot="right"-->
    <!--      :inline="true"-->
    <!--      size="small"-->
    <!--    >-->
    <!--      <el-form-item>-->
    <!--        <el-select-->
    <!--          v-model="value"-->
    <!--          placeholder="请选择"-->
    <!--        >-->
    <!--          <el-option-->
    <!--            v-for="item in options"-->
    <!--            :key="item.value"-->
    <!--            :label="item.label"-->
    <!--            :value="item.value"-->
    <!--          >-->
    <!--          </el-option>-->
    <!--        </el-select>-->
    <!--      </el-form-item>-->
    <!--    </el-form>-->
    <div>
      <el-table :data="tableData">
        <el-table-column prop="title" label="单据名称"></el-table-column>
        <el-table-column prop="printTypeMsg" label="打印类型"></el-table-column>
        <el-table-column prop="operatorName" label="最近修改人"></el-table-column>
        <el-table-column prop="createTime" label="最近修改时间">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              v-if="$accessCheck($Access.TemplateListsave) && $accessCheck($Access.TemplateListgetInfo)"
              size="mini"
              type="text"
              @click="editFn(scope.row.id)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { getAllReceiptTemplate } from "@/api/System";
export default {
  name: "TemplateList",
  data() {
    return {
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      options: [
        {
          value: "选项1",
          label: "全部类型",
        },
        {
          value: "选项2",
          label: "舜津科技店铺销售单",
        },
        {
          value: "选项3",
          label: "舜津科技店铺出库单",
        },
      ],
      value: "选项1",
    };
  },
  created() {
    this.getAllReceiptTemplate();
  },
  methods: {
    pageChange(val) {
      this.page = val;
      this.getAllReceiptTemplate();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getAllReceiptTemplate();
    },
    editFn(id) {
      this.$router.push("/BillTemplate/EditTemplate/" + id);
    },
    async getAllReceiptTemplate() {
      const data = await getAllReceiptTemplate();

      this.tableData = data.data;
      this.total = data.pageTotal;
    },
  },
};
</script>

<style scoped></style>
