<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary" style="margin-right: 20px" @click="saveTem"> 保存 </el-button>
    </div>
    <div class="page-div">
      <el-row class="tem-row" :gutter="20">
        <el-col :span="14">
          <div class="tem-img">
            <div class="top_view" style="margin-right: 40px; margin-left: 20px">
              <div class="time_view">
                <p>2020-08-25 09:10 09</p>
                <p v-if="form_obj.other.indexOf('printingNum') > -1">第1次打印</p>
              </div>
              <div class="name_view">
                <span v-if="form_obj.basic.indexOf('enterprise') > -1">
                  {{ enterprise_title }}
                </span>
                {{ info_list.title }}
              </div>
              <div class="num_view">
                <p style="font-weight: bold; padding-bottom: 3px">202010101010</p>
                <p>{{ enterprise_title }}提供技术支持</p>
              </div>
            </div>
            <div v-if="form_obj.basic.indexOf('printing') > -1" class="print-tag">
              <img :src="require('@/assets/img/print-tag.png')" alt="" />
            </div>
            <div class="infomation">
              <el-row :gutter="20">
                <el-col :span="7">
                  <div v-if="form_obj.basic.indexOf('operatorName') > -1">制单人：肖肖</div>
                </el-col>
                <el-col :span="10">
                  <div v-if="form_obj.basic.indexOf('orderNo') > -1">单据号：XSCK45202008240199885036</div>
                </el-col>
                <el-col :span="7">
                  <div v-if="form_obj.basic.indexOf('createTime') > -1">制单日期：2020-08-22</div>
                </el-col>
              </el-row>
            </div>
            <div class="infomation">
              <div v-if="form_obj.basic.indexOf('customerName') > -1" style="display: inline-block; margin-right: 20px">
                客户姓名：肖肖
              </div>
              <div
                v-if="form_obj.basic.indexOf('customerMobile') > -1"
                style="display: inline-block; margin-right: 20px"
              >
                客户电话：18888888888
              </div>
              <div v-if="form_obj.basic.indexOf('deliveryType') > -1" style="display: inline-block; margin-right: 20px">
                配送方式：上门自提
              </div>
              <div v-if="form_obj.basic.indexOf('receivedName') > -1" style="display: inline-block; margin-right: 20px">
                {{ target_id === 6 ? "退货人" : "收货人" }}：舜津科技联系人
              </div>
              <div
                v-if="form_obj.basic.indexOf('receivedMobile') > -1"
                style="display: inline-block; margin-right: 20px"
              >
                {{ target_id === 6 ? "退货" : "收货" }}电话：18888888888
              </div>
              <div
                v-if="target_id != 6 && form_obj.basic.indexOf('customerAddress') > -1"
                style="display: inline-block; margin-right: 20px"
              >
                收货地址：陕西省西安市未央区大明宫万达广场
              </div>
              <p v-if="form_obj.basic.indexOf('remark') > -1" style="font-weight: 560">{{ self_label }}：销售单</p>
            </div>

            <div style="margin-left: 20px">
              <div id="second">
                <table border="1" style="width: 94%; margin-top: 10px" class="goods-table">
                  <tr>
                    <th v-for="(item, index) in tabelTd" :key="index">
                      {{ item.fields }}
                    </th>
                  </tr>
                  <tr v-for="(td, tdI) in tableData" :key="tdI">
                    <template v-for="(item, index) in tabelTd">
                      <td :key="index">
                        <span v-if="item.alias !== 'images'">
                          {{ td[item.alias] }}
                        </span>
                        <img v-else :src="td[item.alias]" style="width: 50px; height: 50px" />
                      </td>
                    </template>
                  </tr>
                  <tr>
                    <td style="text-align: center">合计：</td>
                    <template v-for="(item, index) in tabelTd">
                      <td v-if="index > 0" :key="index">
                        <span v-if="item.alias.indexOf('num') > -1 || item.alias.indexOf('Num') > -1"> 2.00 </span>
                        <span v-else-if="item.alias.indexOf('total') > -1 || item.alias.indexOf('Total') > -1">
                          200
                        </span>
                        <span v-else>--</span>
                      </td>
                    </template>
                  </tr>
                  <tr>
                    <td
                      v-if="form_obj.other.indexOf('shopDescribe') > -1"
                      :colspan="add_form.goods.length"
                      style="text-align: left"
                    >
                      主营：进口食品,化妆品
                    </td>
                  </tr>
                </table>
              </div>
            </div>
            <div v-if="target_id === 1" style="font-weight: bold; margin: 10px 0 0 20px">
              <span v-if="form_obj.fiance.indexOf('accountName') > -1" class="add_num"> 收款账户：肖肖 </span>
              <span v-if="form_obj.fiance.indexOf('receivable') > -1" class="add_num"> 应收：90 </span>
              <span v-if="form_obj.fiance.indexOf('orderPreferential') > -1" class="add_num"> 优惠：10 </span>
              <span v-if="form_obj.fiance.indexOf('netReceipts') > -1" class="add_num"> 实收：90元 </span>
              <span v-if="form_obj.fiance.indexOf('currentShortage') > -1" class="add_num"> 本单欠：90元 </span>
              <span v-if="form_obj.fiance.indexOf('upOwe') > -1" class="add_num"> 上欠：90元 </span>
              <span v-if="form_obj.fiance.indexOf('totalDeficit') > -1" class="add_num"> 总欠：90元 </span>
            </div>
            <div class="infomation">
              <el-row :gutter="20">
                <el-col :span="7">
                  <div v-if="form_obj.other.indexOf('shopName') > -1">商家名称：{{ enterprise_title }}</div>
                </el-col>
                <el-col :span="7">
                  <div v-if="form_obj.other.indexOf('shopMobile') > -1">商家电话：18888888888</div>
                </el-col>
                <el-col :span="10">
                  <div v-if="form_obj.other.indexOf('shopAddress') > -1">
                    商家地址：陕西省西安市未央区大明宫万达广场
                  </div>
                </el-col>
              </el-row>
            </div>
            <div class="sign clearfix">
              <span v-if="form_obj.other.indexOf('signature') > -1" class="float_left">
                {{ target_id === 3 ? "签字确认:" : "客户签字:" }}
              </span>
              <span class="float_right">经手人: {{ enterprise_title }}</span>
            </div>
            <div
              v-if="(info_list.describe && target_id === 1) || (info_list.describe && target_id === 5)"
              class="sign"
              style="text-align: left"
            >
              <pre>{{ info_list.describe }}</pre>
            </div>
          </div>
        </el-col>
        <el-col :span="9" style="margin-left: 50px">
          <div style="overflow-y: auto">
            <el-form :model="info_list" size="small" label-width="100px">
              <span class="info-tit">基本信息</span>
              <el-form-item label="模板名称：">
                <el-input v-model="info_list.title" placeholder="请输入内容" style="width: 200px"></el-input>
              </el-form-item>
              <el-form-item v-if="target_id === 1 || target_id === 5" label="打印说明：">
                <el-input v-model="info_list.describe" type="textarea" placeholder="请输入内容" :rows="2"></el-input>
              </el-form-item>
            </el-form>
            <span class="info-tit">模板信息</span>
            <el-form size="small" label-width="100px">
              <el-form-item v-if="add_form.basic && add_form.basic.length > 0" label="基本信息：" prop="name">
                <el-checkbox-group v-model="form_obj.basic">
                  <div
                    v-for="(item, index) in add_form.basic"
                    :key="index"
                    style="display: inline-block; padding-right: 30px"
                  >
                    <el-checkbox :label="item.alias">
                      <span v-if="item.fields === '备注'">
                        {{ item.selfLabel || item.fields }}
                      </span>
                      <span v-else>{{ item.fields }}</span>
                    </el-checkbox>
                    <div
                      v-if="item.fields === '备注'"
                      style="display: inline-block; padding-left: 5px; cursor: pointer"
                      @click="dialogVisible = true"
                    >
                      <i style="font-size: 14px" class="el-icon-edit"></i>
                    </div>
                  </div>
                </el-checkbox-group>
                <!--<div>
                  <span class="font-label">字号：</span>
                  <el-input-number
                    v-model="value"
                    size="small"
                    :min="12"
                  ></el-input-number>
                </div>-->
              </el-form-item>
              <el-form-item v-if="add_form.fiance && add_form.fiance.length > 0" label="合计信息：" prop="name">
                <el-checkbox-group v-model="form_obj.fiance">
                  <el-checkbox v-for="(item, index) in add_form.fiance" :key="index" :label="item.alias">
                    {{ item.fields }}
                  </el-checkbox>
                </el-checkbox-group>
                <!--<div>
                  <span class="font-label">字号：</span>
                  <el-input-number
                    v-model="value"
                    size="small"
                    :min="12"
                  ></el-input-number>
                </div>-->
              </el-form-item>
              <el-form-item v-if="add_form.goods && add_form.goods.length > 0" label="商品信息：" prop="name">
                <el-checkbox-group v-model="form_obj.goods" @change="tableChange">
                  <el-checkbox v-for="(item, index) in add_form.goods" :key="index" :label="item.alias">
                    {{ item.fields }}
                  </el-checkbox>
                </el-checkbox-group>
                <!--<div>
                  <span class="font-label">字号：</span>
                  <el-input-number
                    v-model="value"
                    size="small"
                    :min="12"
                  ></el-input-number>
                </div>-->
              </el-form-item>
              <el-form-item v-if="add_form.other && add_form.other.length > 0" label="其他信息：" prop="name">
                <el-checkbox-group v-model="form_obj.other">
                  <el-checkbox v-for="(item, index) in add_form.other" :key="index" :label="item.alias">
                    {{ item.fields }}
                  </el-checkbox>
                </el-checkbox-group>
                <!-- <div>
                  <span class="font-label">字号：</span>
                  <el-input-number
                    v-model="value"
                    size="small"
                    :min="12"
                  ></el-input-number>
                </div>-->
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="名称修改"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <span>
        <el-input v-model="markName" placeholder="请输入新的名称"></el-input>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="enFields">确 定</el-button>
      </span>
    </el-dialog>
  </ContainerTit>
</template>

<script>
import { saveReceiptTemplate, getInfoReceiptTemplate } from "@/api/System";
export default {
  name: "EditTemplate",
  data() {
    return {
      value: 14,
      sortable: "",
      dropCol: [],
      info_list: {
        describe: "",
      },
      markName: "",
      self_label: "备注",
      dialogVisible: false,
      tableData: [
        {
          goodsId: 1,
          images: "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
          goodsName: "男士衬衫",
          barCode: 562969523266,
          strageCode: "09-09-09",
          purchase: 100,
          unitName: "件",
          unitPrice: 100,
          totalPrice: 100,
          originTotalPrice: 100,
          buyNum: "1.00",
          outNum: "1.00",
          purchaseNum: "1.00",
          WarehousingNum: "1.00",
          specName: "白色L",
          skuValue: "1箱*10盒",
        },
        {
          goodsId: 1,
          images: "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
          goodsName: "男士衬衫",
          barCode: 562969523266,
          strageCode: "09-09-09",
          purchase: 100,
          unitName: "件",
          unitPrice: 100,
          totalPrice: 100,
          originTotalPrice: 100,
          buyNum: "1.00",
          outNum: "1.00",
          specName: "白色L",
          skuValue: "1箱*10盒",
          purchaseNum: "1.00",
          WarehousingNum: "1.00",
        },
      ],
      add_form: {},
      form_obj: {
        basic: [],
        fiance: [],
        goods: [],
        other: [],
      },
      tabelTd: [],
      meal_id: "",
      num: 6,
      target_id: "",
    };
  },
  mounted() {
    // this.rowDrop()
    this.meal_id = this.$route.params.id;
    // this.columnDrop() // 表格拖拽事件
    this.getInfoReceiptTemplate();
  },
  methods: {
    tableChange(val) {
      this.tabelTd = this.add_form.goods.filter((item) => val.indexOf(item.alias) > -1);
    },
    enFields() {
      const target = this.$_common.deepClone(this.add_form);
      target.basic.forEach((item) => {
        if (item.fields === "备注") {
          item.selfLabel = this.markName;
        }
      });
      this.add_form = target;
      this.self_label = this.markName;
      this.dialogVisible = false;
    },
    // 保存
    async saveTem() {
      let basic = [];
      if (this.add_form.basic && this.add_form.basic.length) {
        basic = this.add_form.basic.map((item) => {
          return {
            ...item,
            isShow: this.form_obj.basic.indexOf(item.alias) > -1 ? 5 : 4,
          };
        });
      }
      let goods = [];
      if (this.add_form.goods && this.add_form.goods.length) {
        goods = this.add_form.goods.map((item) => {
          return {
            ...item,
            isShow: this.form_obj.goods.indexOf(item.alias) > -1 ? 5 : 4,
          };
        });
      }

      let fiance = [];
      if (this.add_form.fiance && this.add_form.fiance.length) {
        fiance = this.add_form.fiance.map((item) => {
          return {
            ...item,
            isShow: this.form_obj.fiance.indexOf(item.alias) > -1 ? 5 : 4,
          };
        });
      }
      let other = [];
      if (this.add_form.other && this.add_form.other.length) {
        other = this.add_form.other.map((item) => {
          return {
            ...item,
            isShow: this.form_obj.other.indexOf(item.alias) > -1 ? 5 : 4,
          };
        });
      }
      const data = await saveReceiptTemplate({
        id: this.meal_id,
        title: this.info_list.title,
        describe: this.info_list.describe,
        fieldsData: {
          templateName: this.info_list.title,
          templateInfo: {
            basic: basic,
            goods: goods,
            fiance: fiance,
            other: other,
          },
        },
      });

      this.$message.success("操作成功");
      this.getInfoReceiptTemplate();
    },
    // 详情
    async getInfoReceiptTemplate() {
      const { data } = await getInfoReceiptTemplate(this.meal_id);

      if (JSON.stringify(data) === "{}") return;
      this.target_id = data.receiptType;
      this.info_list = data;
      if (!this.info_list.describe) {
        this.info_list.describe = `以上商品均已履行进货检查验收法定程序，索验票证齐全，商家特此声明。↵此联由批发单位直接用于批发台账资料留存。`;
      } else {
        this.info_list.describe = this.info_list.describe.replace(/<br\/>/g, "\n");
      }
      this.add_form = data.fieldsData.templateInfo;
      this.form_obj.basic = this.add_form.basic.filter((item) => item.isShow === 5).map((item) => item.alias);

      if (this.add_form.fiance && this.add_form.fiance.length) {
        this.form_obj.fiance = this.add_form.fiance.filter((item) => item.isShow === 5).map((item) => item.alias);
      }
      //表格显示字段
      this.tabelTd = this.add_form.goods.filter((item) => item.isShow === 5);
      this.form_obj.goods = this.tabelTd.map((item) => item.alias);

      this.add_form.basic.forEach((item) => {
        if (item.fields === "备注") {
          if (!item.selfLabel) {
            item.selfLabel = "";
          } else {
            this.self_label = item.selfLabel;
          }
        }
      });
      this.form_obj.other = this.add_form.other.filter((item) => item.isShow === 5).map((item) => item.alias);
    },
  },
};
</script>

<style scoped lang="scss">
.tem-img {
  box-shadow: 0 3px 10px #dcdcdc;
  width: 100%;
  padding: 20px 0 20px 0;
  font-size: 13px;
  position: relative;
  .print-tag {
    position: absolute;
    right: 190px;
    top: 0;
    img {
      width: 120px;
    }
  }
}
.infomation {
  margin: 6px 20px;
}
.sign {
  margin: 10px 20px;
}
.add_num {
  margin-right: 10px;
}
.goods-table th,
.goods-table td {
  text-align: center;
  line-height: 22px;
}
.info-tit {
  font-size: 14px;
  margin-bottom: 10px;
  display: block;
  font-weight: 600;
}
.top_view {
  font-size: 12px;
  position: relative;
  height: 40px;
}
.time_view {
  position: absolute;
  left: 0;
  top: 0;
}
.name_view {
  text-align: center;
  font-size: 22px;
}
.num_view {
  position: absolute;
  right: 0;
  top: 0;
}
.font-label {
  color: #6c6c6c;
}
</style>
