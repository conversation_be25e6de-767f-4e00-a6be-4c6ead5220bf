<template>
  <ContainerQuery>
    <!--    <div slot="left">-->
    <!--      <el-button size="small" type="primary">导出</el-button>-->
    <!--    </div>-->
    <div slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <SelectCustomer
            v-model="currentUnit"
            placeholder="客户"
            :clearable="false"
            width="120"
            @change="customerSel"
          />
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="staffNme"
            clearable
            style="width: 150px"
            placeholder="请选择员工"
            @clear="staff = ''"
            @blur="staff = ''"
          >
            <i slot="suffix" class="el-input__icon el-icon-search" @click="saleFn(true)"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="search_time"
            type="daterange"
            :unlink-panels="true"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTimeChange"
          ></el-date-picker>
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <DepartmentSel-->
        <!--            v-model="department"-->
        <!--            clearable-->
        <!--            placeholder="部门"-->
        <!--            width="150"-->
        <!--            @change="selBranch"-->
        <!--          />-->
        <!--        </el-form-item>-->
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="recommenderName" label="推荐人"></el-table-column>
      <el-table-column prop="recommenderType" label="推荐人类型">
        <template slot-scope="scope">
          <span v-if="scope.row.recommenderType === 3">客户</span>
          <span v-if="scope.row.recommenderType === 4">员工</span>
        </template>
      </el-table-column>
      <el-table-column prop="recommenderTotal" label="拉新客户数"></el-table-column>
      <el-table-column prop="orderNum" label="订单数">
        <template slot-scope="scope">
          <span v-if="scope.row.orderNum">{{ scope.row.orderNum }}</span>
          <span v-else>0</span>
        </template>
      </el-table-column>
      <el-table-column prop="goodsNum" label="订货数量">
        <template slot-scope="scope">
          <span v-if="scope.row.goodsNum">
            {{ $_common.formatNub(scope.row.goodsNum) }}
          </span>
          <span v-else>0</span>
        </template>
      </el-table-column>
      <el-table-column prop="OrderTotalMoney" label="订货金额">
        <template slot-scope="scope">
          <span v-if="scope.row.OrderTotalMoney">
            {{ $_common.formattedNumber(scope.row.OrderTotalMoney) }}
          </span>
          <span v-else>0</span>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <staffListModal
      v-if="staff_show"
      :is-show="staff_show"
      :is-check="false"
      :isserch="isserch"
      @cancel="staff_show = false"
      @confirm="staffSel"
    />
  </ContainerQuery>
</template>
<script>
import DepartmentSel from "@/component/common/DepartmentSel";
import SelectCustomer from "@/component/common/SelectCustomer.vue";
import staffListModal from "@/component/common/staffListModal";
import { recommenderStatic } from "@/api/Customer";
export default {
  components: {
    // DepartmentSel,
    SelectCustomer,
    staffListModal,
  },
  data() {
    return {
      search_time: "",
      department: "",
      departmentId: "",
      keyword: "",
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
      currentUnit: "",
      customerId: "",
      staff_show: false,
      staffId: "",
      staffNme: "",
      startTime: "",
      endTime: "",
    };
  },
  created() {
    this.recommenderStatic();
  },
  methods: {
    searchTimeChange(val) {
      if (val && val.length) {
        this.startTime = val[0] / 1000;
        this.endTime = val[1] / 1000 + 86399;
      } else {
        this.startTime = "";
        this.endTime = "";
      }
      this.pageChange(1);
    },
    // 选择部门
    selBranch(val) {
      this.departmentId = val[val.length - 1];
    },
    pageChange(val) {
      this.page = val;
      this.recommenderStatic();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.recommenderStatic();
    },
    customerSel(val, row) {
      this.customerId = row[0].id;
      this.pageChange(1);
    },
    saleFn(isserch) {
      this.staff_show = true;
      this.isserch = isserch;
    },
    // 选择员工
    staffSel(val) {
      const row = val[0];
      this.staffNme = row.staffName;
      this.staffId = row.id;
      this.pageChange(1);
    },
    async recommenderStatic() {
      const { data, pageTotal } = await recommenderStatic({
        page: this.page,
        pageSize: this.pageSize,
        startTime: this.startTime,
        endTime: this.endTime,
        customerId: this.customerId,
        staffId: this.staffId,
      });
      this.tableData = data;
      this.total = pageTotal;
    },
  },
};
</script>
<style></style>
