<template>
  <Container>
    <vab-chart
      style="width: 100%; height: 450px"
      :autoresize="true"
      :options="chart_options"
      theme="vab-echarts-theme"
    />
  </Container>
</template>

<script>
import VabChart from "@/extra/vabCharts";
import { getCustomerDistributed } from "@/api/Customer";
export default {
  name: "EchartsMapVue",
  components: { VabChart },
  data() {
    return {
      chart_options: {},
    };
  },
  mounted() {
    this.getCustomerDistributed();
  },
  methods: {
    async getCustomerDistributed() {
      const res = await getCustomerDistributed();
      this.chart_options = {
        tooltip: {},
        legend: {
          orient: "vertical",
          left: "left",
          data: ["2"],
        },
        visualMap: {
          min: 0,
          max: 1500,
          left: "10%",
          top: "bottom",
          text: ["高", "低"],
          calculable: true,
          color: ["#0b50b9", "#c3e2f4"],
        },
        selectedMode: "single",
        series: [
          {
            zoom: 1.2,
            name: "",
            type: "map",
            mapType: "china",
            itemStyle: {
              normal: {
                borderColor: "#DCDCDC", //省份边框颜色
              },
              emphasis: {
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                shadowBlur: 20,
                borderWidth: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
                // areaColor: "red",选中区域颜色
              },
            },
            showLegendSymbol: true,
            label: {
              normal: {
                show: true,
              },
              emphasis: {
                show: true,
              },
            },
            data: res.data,
          },
        ],
      };
    },
  },
};
</script>
