<template>
  <ContainerQuery>
    <!--    <div slot="left">-->
    <!--      <el-button size="small" type="primary">导出</el-button>-->
    <!--    </div>-->
    <div slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="customerName"
            style="width: 220px"
            placeholder="客户名称"
            clearable
            @keyup.enter.native="searchData"
            @clear="searchData"
          >
            <el-button slot="append" @click="searchData">
              <i class="el-icon-search"></i>
            </el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="currentUnit"
            :clearable="true"
            style="width: 200px"
            placeholder="业务员"
            @clear="clearCurrentUnit"
          >
            <i slot="suffix" class="el-input__icon el-icon-search" @click="saleFn(true)"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <RegionSelect
            v-model="area"
            :clearable="true"
            placeholder="地区"
            style="width: 100%"
            :check-strictly="true"
            @change="regionChange"
          />
        </el-form-item>
      </el-form>
    </div>
    <el-row>
      <el-col :span="2">
        <el-container style="height: 730px; border: 1px solid #eee">
          <el-aside width="100%" style="background-color: rgb(238, 241, 246); padding-top: 20px">
            <div style="height: 70px; width: 100%; position: relative">
              <span style="margin-left: 10px">异常分组</span>
              <div>
                <el-button
                  v-if="!buttonFlag"
                  type="text"
                  style="position: absolute; right: 10px; bottom: 0"
                  @click="buttonFlag = true"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="buttonFlag"
                  size="mini"
                  style="position: absolute; right: 60px; bottom: 5px"
                  @click="buttonFlag = false"
                >
                  取消
                </el-button>
                <el-button
                  v-if="buttonFlag"
                  type="primary"
                  size="mini"
                  style="position: absolute; right: 0; bottom: 5px"
                  @click="addData"
                >
                  保存
                </el-button>
              </div>
            </div>
            <ul v-if="!buttonFlag" class="edit-list">
              <li class="item-box-outline">
                <div class="item-box item-box-view" :class="[actived2 ? 'actived' : '']" @click="changeMenu(2)">
                  <div class="space-block"></div>
                  <div class="item-content">
                    <div class="title-box">
                      <div class="title">久未订货({{ analysis_form.intervalDay }})</div>
                    </div>
                  </div>
                </div>
              </li>
              <li class="item-box-outline">
                <div class="item-box item-box-view" :class="[actived3 ? 'actived' : '']" @click="changeMenu(3)">
                  <div class="space-block"></div>
                  <div class="item-content">
                    <div class="title-box">
                      <div class="title">新注册未下单({{ analysis_form.newRegisterOrder }})</div>
                    </div>
                  </div>
                </div>
              </li>
              <li class="item-box-outline">
                <div class="item-box item-box-view" :class="[actived4 ? 'actived' : '']" @click="changeMenu(4)">
                  <div class="space-block"></div>
                  <div class="item-content">
                    <div class="title-box">
                      <div class="title">未拜访客户统计({{ analysis_form.notVisit }})</div>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
            <ul v-if="buttonFlag" class="edit-list">
              <li class="item-box-outline">
                <el-popover placement="left-end" trigger="hover">
                  <div>
                    距上次订货
                    <span class="el-icon-arrow-right"></span>
                    <el-input-number
                      v-model="analysis_form.intervalDay"
                      :controls="false"
                      style="width: 50px"
                      :min="1"
                    ></el-input-number>
                  </div>
                  <div
                    slot="reference"
                    class="item-box item-box-view"
                    :class="[actived2 ? 'actived' : '']"
                    @click="changeMenu(2)"
                  >
                    <div class="space-block"></div>
                    <div class="item-content">
                      <div class="title-box">
                        <div class="title">久未订货</div>
                        <div class="title-explain">
                          距上次订货
                          <span class="el-icon-arrow-right"> {{ analysis_form.intervalDay }}天 </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-popover>
              </li>
              <li class="item-box-outline">
                <el-popover placement="left-end" trigger="hover">
                  <div>
                    注册天数
                    <span class="el-icon-arrow-left"></span>
                    <el-input-number
                      v-model="analysis_form.newRegisterOrder"
                      :controls="false"
                      style="width: 50px"
                      :min="1"
                    ></el-input-number>
                    未下单
                  </div>
                  <div
                    slot="reference"
                    class="item-box item-box-view"
                    :class="[actived3 ? 'actived' : '']"
                    @click="changeMenu(3)"
                  >
                    <div class="space-block"></div>
                    <div class="item-content">
                      <div class="title-box">
                        <div class="title">新注册未下单</div>
                        <div class="title-explain">
                          注册天数
                          <span class="el-icon-arrow-left"> {{ analysis_form.newRegisterOrder }}天未下单 </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-popover>
              </li>
              <li class="item-box-outline">
                <el-popover placement="left-end" trigger="hover">
                  <div>
                    客户
                    <span class="el-icon-arrow-left"></span>
                    <el-input-number
                      v-model="analysis_form.notVisit"
                      :controls="false"
                      style="width: 50px"
                      :min="1"
                    ></el-input-number>
                    未拜访
                  </div>
                  <div
                    slot="reference"
                    class="item-box item-box-view"
                    :class="[actived4 ? 'actived' : '']"
                    @click="changeMenu(4)"
                  >
                    <div class="space-block"></div>
                    <div class="item-content">
                      <div class="title-box">
                        <div class="title">客户未拜访统计</div>
                        <div class="title-explain">
                          客户
                          <span class="el-icon-arrow-left"> {{ analysis_form.notVisit }}天未拜访 </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-popover>
              </li>
            </ul>
          </el-aside>
        </el-container>
      </el-col>
      <el-col :span="22">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="name" label="客户名称"></el-table-column>
          <el-table-column prop="code" label="客户编码"></el-table-column>
          <el-table-column prop="address" label="客户地区">
            <template slot-scope="scope">
              <span v-if="scope.row.area">
                {{ scope.row.area.provinceName + " " + scope.row.area.cityName + " " + scope.row.area.districtName }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="customerType" label="客户类型">
            <template slot-scope="scope">
              <span v-if="scope.row.customerType">
                {{ scope.row.customerType }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="salesManName" label="业务员">
            <template slot-scope="scope">
              <span v-if="scope.row.salesManName">
                {{ scope.row.salesManName }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="province" label="注册天数">
            <template slot-scope="scope">
              <span v-if="scope.row.customerDays">
                {{ scope.row.customerDays }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="本年订单金额">
            <template slot-scope="scope">
              <span v-if="scope.row.year">
                {{ $_common.formattedNumber(scope.row.year.orderMoney) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="本月订单金额">
            <template slot-scope="scope">
              <span v-if="scope.row.thisMonth">
                {{ $_common.formattedNumber(scope.row.thisMonth.orderMoney) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="上月订单金额">
            <template slot-scope="scope">
              <span v-if="scope.row.lastMonth">
                {{ $_common.formattedNumber(scope.row.lastMonth.orderMoney) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="本年订单个数">
            <template slot-scope="scope">
              <span v-if="scope.row.year">
                {{ $_common.formatNub(scope.row.year.orderNum) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="本月订单个数">
            <template slot-scope="scope">
              <span v-if="scope.row.thisMonth">
                {{ $_common.formatNub(scope.row.thisMonth.orderNum) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="上月订单个数">
            <template slot-scope="scope">
              <span v-if="scope.row.lastMonth">
                {{ $_common.formatNub(scope.row.lastMonth.orderNum) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="本年拜访次数" prop="yearNum">
            <template slot-scope="scope">
              <span v-if="scope.row.yearNum">
                {{ scope.row.yearNum }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column width="135" label="距上次拜访多少天" prop="customerDays">
            <template slot-scope="scope">
              <span v-if="scope.row.intervalDay">
                {{ scope.row.intervalDay }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
        <FooterPage
          :page-size="pageSize"
          :total-page.sync="total"
          :current-page.sync="page"
          @pageChange="pageChange"
          @sizeChange="sizeChange"
        ></FooterPage>
      </el-col>
    </el-row>
    <staffListModal
      v-if="staff_show"
      :is-show="staff_show"
      :is-check="false"
      :isserch="isserch"
      @cancel="staff_show = false"
      @confirm="staffSel"
    />
  </ContainerQuery>
</template>
<script>
import staffListModal from "@/component/common/staffListModal";
import RegionSelect from "@/component/common/RegionSelectJSON";
import { noOrderCustomer, intervalNoOrderCustomer, getCustomerNoVisit } from "@/api/Customer";
import { setting, getBasicSetup } from "@/api/System";
export default {
  components: {
    staffListModal,
    RegionSelect,
  },
  data() {
    return {
      customerName: "", // 客户名称
      currentUnit: "",
      staff_show: false,
      isserch: true,
      salesManId: "", // 业务员ID
      area: [],
      provinceCode: "",
      cityCode: "",
      districtCode: "",
      activeName: "1",
      tableData: [],
      radio1: "",
      on_menu: 0,
      buttonFlag: false,
      actived1: false,
      actived2: true,
      actived3: false,
      actived4: false,
      actived5: false,
      analysis_form: {
        intervalDay: 1,
        newRegisterOrder: 15,
        notVisit: 15,
      },
      index: "",
      basicData: {},
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  async created() {
    await this.getBasicSetup();
    await this.changeMenu(2);
  },
  async activated() {
    await this.getBasicSetup();
    await this.changeMenu(2);
  },
  methods: {
    changeMenu(index) {
      this.index = index;
      this.customerName = "";
      this.currentUnit = "";
      this.salesManId = "";
      this.provinceCode = "";
      this.cityCode = "";
      this.districtCode = "";
      this.page = 1;
      this.pageSize = 10;
      this.area = [];
      if (index === 1) {
        this.actived1 = true;
        this.actived2 = false;
        this.actived3 = false;
        this.actived4 = false;
        this.actived5 = false;
      } else if (index === 2) {
        this.actived1 = false;
        this.actived2 = true;
        this.actived3 = false;
        this.actived4 = false;
        this.actived5 = false;
        this.intervalNoOrderCustomer();
      } else if (index === 3) {
        this.actived1 = false;
        this.actived2 = false;
        this.actived3 = true;
        this.actived4 = false;
        this.actived5 = false;
        this.noOrderCustomer();
      } else if (index === 4) {
        this.actived1 = false;
        this.actived2 = false;
        this.actived3 = false;
        this.actived4 = true;
        this.actived5 = false;
        this.getCustomerNoVisit();
      } else if (index === 5) {
        this.actived1 = false;
        this.actived2 = false;
        this.actived3 = false;
        this.actived4 = false;
        this.actived5 = true;
      }
    },
    searchData() {
      switch (this.index) {
        case 2:
          this.intervalNoOrderCustomer();
          break;
        case 3:
          this.noOrderCustomer();
          break;
        case 4:
          this.getCustomerNoVisit();
      }
    },
    saleFn(isserch) {
      this.staff_show = true;
      this.isserch = isserch;
    },
    // 选择员工
    staffSel(val) {
      const row = val[0];
      this.currentUnit = row.staffName;
      this.salesManId = row.id;
      this.searchData();
    },
    //  区域
    regionChange(val) {
      this.provinceCode = val[0];
      this.cityCode = val[1];
      this.districtCode = val[2];
      this.searchData();
    },
    tabChange() {},
    // 新注册未下单列表
    async noOrderCustomer() {
      const { data, pageTotal } = await noOrderCustomer({
        page: this.page,
        pageSize: this.pageSize,
        intervalDay: Number(this.analysis_form.newRegisterOrder),
        customerName: this.customerName,
        salesManId: this.salesManId,
        provinceCode: this.provinceCode,
        cityCode: this.cityCode,
        districtCode: this.districtCode,
      });
      this.tableData = data;
      this.total = pageTotal;
    },
    async addData() {
      if (this.index === 2) {
        await this.intervalNoOrderCustomer();
        this.buttonFlag = false;
      }
      if (this.index === 3) {
        await this.noOrderCustomer();
        this.buttonFlag = false;
      }
      if (this.index === 4) {
        await this.getCustomerNoVisit();
        this.buttonFlag = false;
      }
      const data = await setting({
        basicData: {
          ...this.basicData,
          analysis_form: this.analysis_form,
        },
      });
      this.$message({
        message: "提交成功",
        type: "success",
      });
      await this.getBasicSetup();
    },
    // 久未订货列表
    async intervalNoOrderCustomer() {
      const { data, pageTotal } = await intervalNoOrderCustomer({
        page: this.page,
        pageSize: this.pageSize,
        intervalDay: Number(this.analysis_form.intervalDay),
        customerName: this.customerName,
        salesManId: this.salesManId,
        provinceCode: this.provinceCode,
        cityCode: this.cityCode,
        districtCode: this.districtCode,
      });
      this.tableData = data;
      this.total = pageTotal;
    },
    //  获取详情
    async getBasicSetup() {
      const { data } = await getBasicSetup();
      this.basicData = data.basicData;
      if (data.basicData.analysis_form) {
        this.analysis_form = data.basicData.analysis_form;
      } else {
        this.analysis_form = {
          intervalDay: 1,
          newRegisterOrder: 15,
          notVisit: 15,
        };
      }
    },
    clearData() {
      this.customerName = "";
      this.currentUnit = "";
      this.salesManId = "";
      this.provinceCode = "";
      this.cityCode = "";
      this.districtCode = "";
    },
    clearCurrentUnit() {
      this.salesManId = "";
      this.searchData();
    },
    async getCustomerNoVisit() {
      const { data, pageTotal } = await getCustomerNoVisit({
        page: this.page,
        pageSize: this.pageSize,
        intervalDay: Number(this.analysis_form.notVisit),
        customerName: this.customerName,
        staffId: this.salesManId,
        province: this.provinceCode,
        city: this.cityCode,
        district: this.districtCode,
      });
      this.tableData = data;
      this.total = pageTotal;
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    pageChange(val) {
      this.page = val;
      this.searchData();
    },
  },
};
</script>
<style scoped lang="scss">
.edit-list {
  .item-box-outline {
    position: relative;
    .hoverdiv {
      position: absolute;
      right: -50px;
      top: 50%;
      padding: 10px;
      background-color: #fff;
      z-index: 999;
    }
    .item-box {
      min-height: 50px;
      position: relative;
      display: flex;
      align-items: center;
      &:hover {
        background-color: #e1f0fa;
      }
      &.actived {
        background-color: #fff;
        .space-block {
          width: 3px;
          min-height: 50px;
          height: 100%;
          position: absolute;
          background-color: $base-color-blue;
          top: 0;
          left: 0;
        }
      }
      .item-content {
        padding: 12px 6px 12px 12px;
        display: flex;
        cursor: context-menu;
        .title-box {
          .title-explain {
            font-size: 12px;
            color: #999fab;
          }
        }
      }
    }
  }
}
</style>
