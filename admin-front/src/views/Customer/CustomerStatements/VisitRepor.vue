<template>
  <ContainerQuery>
    <div slot="more" class="Chart">
      <el-form :inline="true">
        <el-form-item>
          <el-date-picker
            v-model="time"
            style="margin-right: 20px"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="reportDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <DepartmentSel v-model="departmentPidPath" clearable @change="departmentChange" />
        </el-form-item>
      </el-form>
    </div>
    <vab-chart
      style="width: 100%; height: 450px"
      :autoresize="true"
      :options="chart_options"
      theme="vab-echarts-theme"
    />
    <el-table :data="tablelist">
      <el-table-column label="公司总排名" prop="staffId"></el-table-column>
      <el-table-column label="员工" prop="staffName"></el-table-column>
      <el-table-column label="部门" prop="departmentName"></el-table-column>
      <el-table-column label="客户数" prop="customerNum"></el-table-column>
      <el-table-column label="订单数" prop="customerOrderNum"></el-table-column>
      <el-table-column label="下单金额" prop="customerOrderAmount">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.customerOrderAmount) }}
        </template>
      </el-table-column>
      <el-table-column label="跟进次数" prop="num">
        <template slot-scope="scope">
          <div class="sku-btn" @click="visit(scope.row.staffId)">
            {{ scope.row.num }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--    明细弹窗-->
    <el-dialog title="拜访明细" :visible.sync="centerDialogVisible" width="50%" center>
      <el-table modal="false" :data="Follow">
        <el-table-column label="序号" prop="id" type="index"></el-table-column>
        <el-table-column label="客户名称" prop="name"></el-table-column>
        <el-table-column label="拜访时间" prop="time">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.time) }}
          </template>
        </el-table-column>
        <el-table-column label="地址" prop="location"></el-table-column>
        <el-table-column label="拜访内容" prop="content"></el-table-column>
        <el-table-column label="图片" prop="picture">
          <template slot-scope="scope">
            <el-image style="width: 50px; height: 50px" :src="scope.row.picture"></el-image>
          </template>
        </el-table-column>
      </el-table>
      <FooterPage
        :page-size="pageSizes"
        :total-page.sync="totalpage"
        :current-page.sync="pages"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      ></FooterPage>
    </el-dialog>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>
<script>
import DepartmentSel from "@/component/common/DepartmentSel";
import { getCustomerVisitInfo, getCustomerCallOnReportForm } from "@/api/Customer";
import VabChart from "@/extra/vabCharts";
export default {
  name: "DetailedVue",
  components: {
    DepartmentSel,
    VabChart,
  },
  data() {
    return {
      chart_options: {},
      departmentPidPath: [],
      pages: 1,
      totalpage: 1,
      pageSizes: 10,
      page: 1,
      total: 1,
      pageSize: 10,
      time: "",
      options: [
        {
          value: 1,
          label: "总裁办",
        },
        {
          value: 2,
          label: "销售部",
        },
      ],
      centerDialogVisible: false,
      Follow: [],
      tablelist: [],
      start: "",
      end: "",
      name: [],
      Visit_time: [],
      staffId: "",
    };
  },
  created() {
    this.getCustomerCallOnReportForm();
  },
  methods: {
    async visit(staffId) {
      this.staffId = staffId;
      this.centerDialogVisible = true;
      const res = await getCustomerVisitInfo({
        staffId: staffId,
        page: this.pages,
        pageSize: this.pageSizes,
      });
      this.Follow = res.data;
      this.totalpage = res.pageTotal;
    },
    async getCustomerCallOnReportForm() {
      const res = await getCustomerCallOnReportForm({
        page: this.page,
        pageSize: this.pageSize,
        departmentId: this.departmentId,
        start: this.start,
        end: this.end,
      });
      this.tablelist = res.data;
      this.total = res.pageTotal;
      let name = [];
      let time = [];
      this.tablelist.forEach((item) => {
        time.push(item.num);
        name.push(item.staffName);
      });
      this.chart_options = {
        color: ["#409EFF"],
        title: { text: "跟进次数排行(按拜访时间统计)" },
        tooltip: {},
        xAxis: {
          min: 0,
        },
        yAxis: {
          type: "category",
          data: name,
        },
        series: [
          {
            name: "拜访时间次数",
            type: "bar",
            data: time,
          },
        ],
      };
    },
    departmentChange(val) {
      if (!val.length) {
        this.departmentId = "";
      } else {
        this.departmentId = [val[val.length - 1]];
      }
      this.pageChange(1);
    },
    // 拜访明细
    pageChange(val) {
      if (this.centerDialogVisible) {
        this.pages = val;
        this.visit(this.staffId);
      } else {
        this.page = val;
        this.getCustomerCallOnReportForm();
      }
    },
    sizeChange(val) {
      if (this.centerDialogVisible) {
        this.pageSizes = val;
        this.pageChange(1);
      } else {
        this.pageSize = val;
        this.pageChange(1);
      }
    },
    reportDate(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.pageChange(1);
    },
  },
};
</script>

<style scoped lang="scss">
.sku-btn {
  width: 71px;
  height: 22px;
  line-height: 22px;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #cad0d7;
  text-align: center;
  font-size: 12px;
  cursor: pointer;
}
</style>
