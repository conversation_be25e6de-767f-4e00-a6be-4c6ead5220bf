<template>
  <ContainerQuery>
    <div slot="left">
      <el-button size="small" type="primary">导出</el-button>
    </div>
    <div slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="keyword"
            style="width: 220px"
            placeholder="员工名称"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" @click="pageChange(1)">
              <i class="el-icon-search"></i>
            </el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="search_time"
            type="daterange"
            :unlink-panels="true"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTimeChange"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <DepartmentSel v-model="department" clearable placeholder="部门" width="150" @change="selBranch" />
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="date" label="提成对象"></el-table-column>
      <el-table-column prop="name" label="总拉新客户"></el-table-column>
      <el-table-column prop="address" label="本期拉新客户"></el-table-column>
      <el-table-column prop="address" label="本期订货合计金额"></el-table-column>
      <el-table-column prop="address" label="本期拉新提成(参考)"></el-table-column>
      <el-table-column prop="address" label="待完成合计金额合计"></el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>
<script>
import DepartmentSel from "@/component/common/DepartmentSel";

export default {
  components: {
    DepartmentSel,
  },
  data() {
    return {
      keyword: "",
      search_time: "",
      department: "",
      departmentId: "",
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
    };
  },
  methods: {
    pageChange() {},
    sizeChange() {},
    searchTimeChange() {},
    // 选择部门
    selBranch(val) {
      this.departmentId = val[val.length - 1];
      this.pageChange();
    },
  },
};
</script>
<style></style>
