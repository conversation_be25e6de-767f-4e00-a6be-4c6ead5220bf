<template>
  <Container>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示：1、在商铺基础设置中开启客户审核后，前端商城新客户注册后，需审核后客户在商城才能正常操作！
      2、亦可进行拒绝审核操作，拒绝后客户需要重新再小程序商城修改提交的资料，重新提交！
    </div>
    <el-form slot="left" style="margin-bottom: 0" :inline="true" size="small">
      <el-form-item prop="classify">
        <el-input
          v-model="keyword"
          placeholder="客户名称/手机/备注"
          clearable
          width="400px;"
          @clear="searchCheck"
          @keyup.enter.native="searchCheck"
        >
          <el-button slot="append" icon="el-icon-search" @click="searchCheck"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item v-show="path === 'FinishCheck'">
        <el-date-picker
          v-model="search_form.time"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="timestamp"
          @change="timeChange"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <el-table ref="filterTable" :data="tableData" @row-dblclick="goDetail">
      <el-table-column prop="id" label="ID" fixed="left" width="50"></el-table-column>
      <el-table-column v-if="timeFlag" prop="createTime" label="注册时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="客户" min-width="280">
        <template slot-scope="scope">
          <div class="clearfix">
            <div class="float_left customer-img-view">
              <img v-if="scope.row.avatar" class="customer-img" :src="scope.row.avatar" />
            </div>

            <div class="float_left customer-name-view">
              <p class="customer-name">
                {{ scope.row.name }}
              </p>
              <p>{{ scope.row.code }}</p>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="联系信息" min-width="160" prop="contact">
        <template slot-scope="scope">
          <div>
            <p v-if="scope.row.contact[0]">
              <span class="table-label">联系人：</span>
              <span class="table-val">
                {{ scope.row.contact[0].name || "--" }}
              </span>
            </p>
            <p>
              <span class="table-label">账号/电话：</span>
              <span class="table-val">{{ scope.row.mobile || "--" }}</span>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="auditStatusFlag" prop="status" label="审核状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 1" class="warning-status"> 待审核 </span>
          <span v-if="scope.row.status === 0" class="warning-status"> 待完善资料 </span>
          <span v-if="scope.row.status === 3" class="danger-status"> 已驳回 </span>
          <p v-if="scope.row.status === 3">原因：{{ scope.row.auditFailReason }}</p>
        </template>
      </el-table-column>
      <el-table-column v-if="sourceWay" prop="source" label="来源方式" min-width="100">
        <template slot-scope="scope">
          {{
            scope.row.source === "ios" || scope.row.source === 1
              ? "ios"
              : scope.row.source === "android" || scope.row.source === 2
              ? "安卓"
              : scope.row.source === 3 || scope.row.source === "miniProgram"
              ? "小程序"
              : scope.row.source === 4 || scope.row.source === "manage"
              ? "后台创建"
              : scope.row.source === 5 || scope.row.source === "H5"
              ? "H5页面"
              : scope.row.source === 6 || scope.row.source === "Pc"
              ? "Pc页面"
              : "其他"
          }}
        </template>
      </el-table-column>
      <el-table-column v-if="clientTypeFlag" prop="customerType" label="客户类型" min-width="100"></el-table-column>
      <el-table-column
        v-if="shopFlag"
        show-overflow-tooltip
        prop="shopName"
        label="商铺"
        min-width="120"
      ></el-table-column>
      <el-table-column header-align="left" align="left" label="操作" fixed="right" min-width="160">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span class="operation">操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button v-if="$accessCheck($Access.CustomerListEditCustomer)" type="text" @click="editData(scope.row)">
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.CustomerListUpdateCustomerCheckStatus)"
            type="text"
            @click="updateCustomerCheckStatus('keep', scope.row)"
          >
            通过
          </el-button>
          <el-button
            v-if="$accessCheck($Access.CustomerListUpdateCustomerCheckStatus)"
            :disabled="scope.row.status === 3"
            type="text"
            @click="updateCustomerCheckStatus('', scope.row)"
          >
            拒绝
          </el-button>
          <el-button
            v-if="$accessCheck($Access.CustomerListGetCustomerInfo)"
            type="text"
            @click="$router.push(`/Customer/CustomerAdmin/CustomerDetail/${scope.row.id}`)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <el-drawer class="edit-drawer" size="50%" :title="drawer_tit" :visible.sync="drawer">
      <EditCustomer v-if="drawer" :customer-id="customer_id" @subSuccess="subSuccess" />
    </el-drawer>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="拒绝审核"
      :visible.sync="is_reject"
      width="30%"
    >
      <el-form ref="reject" :model="reject">
        <el-form-item label="输入拒绝原因" prop="reason">
          <el-input v-model="reject.reason" maxlength="20" style="width: 240px" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="is_reject = false">取 消</el-button>
        <el-button type="primary" @click="rejectCustomer">确 定</el-button>
      </div>
    </el-dialog>
  </Container>
</template>

<script>
import EditCustomer from "../CustomerAdmin/AddCustomer";
import FooterPage from "@/component/common/FooterPage";
import { updateCustomerCheckStatus, getAllCustomer, searchCustomer } from "@/api/Customer";
export default {
  name: "CustomerCheck",
  components: {
    FooterPage,
    EditCustomer,
  },
  data() {
    return {
      is_reject: false, // 拒绝按钮打开
      reject: {
        reason: "",
      },
      path: "",
      keyword: "",
      formLabelWidth: "100px",
      checked: false,
      search_form: {
        time: [],
      },
      customer_id: "",
      total: 0,
      page: 1,
      pageSize: 10,
      tableData: [],
      drawer: false,
      drawer_tit: "",
      status: "",
      start: "", // 时间
      end: "",
      checkList: ["注册时间", "审核状态", "来源方式", "客户类型", "商铺"],
      columns: [
        {
          label: "注册时间",
        },
        {
          label: "审核状态",
        },
        {
          label: "来源方式",
        },
        {
          label: "客户类型",
        },
        {
          label: "商铺",
        },
      ],
      timeFlag: true,
      auditStatusFlag: true,
      sourceWay: true,
      clientTypeFlag: true,
      shopFlag: true,
    };
  },
  created() {
    this.path = this.$route.name;
    if (this.path === "NotCheck") {
      this.status = 1;
    } else {
      this.status = 0;
    }
    this.getAllCustomer();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    subSuccess() {
      this.pageChange(1);
      this.drawer = false;
    },
    editData(row) {
      this.customer_id = row.id;
      this.drawer_tit = row.name;
      this.drawer = true;
    },
    goDetail(row) {
      if (!this.$accessCheck(this.$Access.CustomerListGetCustomerInfo)) {
        return;
      }
      this.$router.push(`/Customer/CustomerAdmin/CustomerDetail/${row.id}`);
    },
    async rejectCustomer() {
      if (!this.reject.reason) {
        this.$message("请填写拒绝审核的原因");
        return;
      }
      const data = await updateCustomerCheckStatus({
        id: this.customer_id,
        status: 3,
        reason: this.reject.reason,
      });

      this.is_reject = false;
      this.getAllCustomer();
      this.$message({
        type: "success",
        message: "提交成功",
      });
    },
    async updateCustomerCheckStatus(arrow, row) {
      this.customer_id = row.id;
      if (arrow) {
        this.$confirm("确定要审核此用户吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          const data = await updateCustomerCheckStatus({
            id: row.id,
            status: 2,
          });

          this.getAllCustomer();
          this.$message({
            type: "success",
            message: "审核成功",
          });
        });
      } else {
        this.is_reject = true;
      }
    },
    //  获取用户列表
    async getAllCustomer() {
      const data = await getAllCustomer({
        page: this.page,
        pageSize: this.pageSize,
        status: this.status,
        keyword: this.keyword,
      });

      this.tableData = data.data.map((item) => {
        return {
          ...item,
          callName: item.contact[0].name,
        };
      });
      this.total = data.pageTotal;
    },
    // 进行搜索  getAllCustomer  获取用户的状态
    async searchCustomer() {
      const data = await searchCustomer({
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        status: this.status,
        start: this.start,
        end: this.end,
      });

      this.tableData = data.data;
      this.total = data.pageTotal;
    },
    //  判断
    getData() {
      const obj = {
        keyword: this.keyword,
        start: this.start,
        end: this.end,
      };
      // const isKey = this.$_common.isSerch(obj);
      // if (isKey) {
      //   this.searchCustomer();
      // } else {
      //   this.getAllCustomer();
      // }
      this.getAllCustomer();
    },
    timeChange(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.pageChange(1);
    },
    //  点击搜索
    searchCheck() {
      this.pageChange(1);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    change() {
      this.timeFlag = this.checkList.some((item) => item === "注册时间");
      this.auditStatusFlag = this.checkList.some((item) => item === "审核状态");
      this.sourceWay = this.checkList.some((item) => item === "来源方式");
      this.clientTypeFlag = this.checkList.some((item) => item === "客户类型");
      this.shopFlag = this.checkList.some((item) => item === "商铺");
    },
  },
};
</script>
<style scoped lang="scss">
.customer-img-view {
  width: 50px;
  height: 50px;
  background-color: #f4f4f4;
  margin-right: 10px;
}
.customer-img {
  width: 50px;
  height: 50px;
}
.customer-name-view {
  width: calc(100% - 86px);
}
.customer-name {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
