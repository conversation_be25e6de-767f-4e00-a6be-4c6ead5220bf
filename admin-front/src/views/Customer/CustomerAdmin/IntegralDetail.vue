<template>
  <Container>
    <div slot="left">
      <el-form :inline="true" size="small">
        <el-form-item label="客户:">
          {{ name }}
        </el-form-item>
        <el-form-item label="|"></el-form-item>
        <el-form-item label="当前积分:">{{ integral }}</el-form-item>
        <el-form-item label="|"></el-form-item>
        <el-form-item>
          <div class="block">
            <el-date-picker
              v-model="time"
              clearable
              type="daterange"
              value-format="timestamp"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="orderDate"
            ></el-date-picker>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="createTime" label="积分变动时间" width="180">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="originNo" label="关联订单号" width="180"></el-table-column>
      <el-table-column prop="title" label="备注说明"></el-table-column>
      <el-table-column prop="userCenterId" label="操作人"></el-table-column>
      <el-table-column prop="title" label="摘要"></el-table-column>
      <el-table-column prop="changeAmount" label="变动分值">
        <template slot-scope="scope">
          <span :class="[parseInt(scope.row.type) === 4 ? 'danger-status' : 'success-status']"></span>
          <span>{{ parseInt(scope.row.type) === 4 ? "-" : "+" }}</span>
          {{ scope.row.changeAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="积分账户余额"></el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>
<script>
import { getAllCustomerIntegralDesc, getCustomerInfo } from "@/api/Customer";
export default {
  data() {
    return {
      name: "",
      integral: "",
      customerId: "",
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
      time: [],
      star: "",
      end: "",
    };
  },
  activated() {
    if (this.$_isInit()) return;
    // this.getCustomerInfo();
  },
  created() {
    this.integral = this.$route.params.integral;
    this.customerId = this.$route.params.id;
    this.getAllCustomerIntegralDesc();
  },
  methods: {
    orderDate(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.pageChange(1);
    },
    //获取积分明细
    async getAllCustomerIntegralDesc() {
      const data = await getAllCustomerIntegralDesc({
        customerId: this.customerId,
        page: this.page,
        pageSize: this.pageSize,
        star: this.start,
        end: this.end,
      });
      this.integral = data.data.customer.integral;
      this.name = data.data.customer.name;
      this.tableData = data.data.lists;
      this.total = data.pageTotal;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllCustomerIntegralDesc();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    cut() {},
  },
};
</script>
<style></style>
