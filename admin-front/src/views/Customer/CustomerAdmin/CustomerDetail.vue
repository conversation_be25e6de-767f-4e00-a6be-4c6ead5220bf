<template>
  <ContainerTit class="CustomerDetail">
    <div style="position: relative">
      <div class="btn-top-div">
        <el-button
          v-if="parseInt(cousterdetail.status) === 1"
          :disabled="parseInt(cousterdetail.status) === 3"
          type="warning"
          :loading="loading"
          @click="EditAuditStatus('keep')"
        >
          点击审核
        </el-button>
        <el-button
          v-if="parseInt(cousterdetail.status) === 1"
          :disabled="parseInt(cousterdetail.status) === 3"
          type="danger"
          :loading="loading"
          @click="EditAuditStatus()"
        >
          拒绝审核
        </el-button>
        <el-button type="primary" @click="$router.push(`/Customer/CustomerAdmin/EditCustomer/${$route.params.id}`)">
          编辑
        </el-button>
      </div>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="客户详情" name="one">
        <el-row style="padding-bottom: 13px">
          <el-col :span="24">
            <p style="padding-left: 20px" class="order_midden_left_text el-icon-arrow-right">客户信息</p>
          </el-col>
          <el-col :span="24">
            <div
              v-if="cousterdetail.extend && cousterdetail.extend.license"
              class="clearfix form"
              style="margin: 0 0 20px 0"
            >
              <span class="float_left form_left extend-span">营业执照</span>
              <el-image
                class="float_left extend-img"
                :src="cousterdetail.extend.license"
                :preview-src-list="[cousterdetail.extend.license]"
                fit="contain"
              ></el-image>
            </div>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">客户编号</span>
            <span class="form_right">{{ cousterdetail.code }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">客户名称</span>
            <span class="form_right">{{ cousterdetail.name }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">登录账号</span>
            <span class="form_right">{{ cousterdetail.mobile }}</span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">审核状态</span>
            <span class="form_right">
              <span v-if="cousterdetail.status === 2" class="success-status"> 已审核 </span>
              <span v-else-if="cousterdetail.status === 1" class="warning-status"> 待审核 </span>
              <span v-else-if="cousterdetail.status === 0" class="info-status"> 待完善资料 </span>
              <span v-else-if="cousterdetail.status === 3" class="danger-status"> 已驳回 </span>
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">注册日期</span>
            <span class="form_right">
              {{ cousterdetail.createTime ? $_common.formatDate(cousterdetail.createTime, "yyyy-MM-dd") : "--" }}
            </span>
          </el-col>
          <el-col class="form" :span="6" style="padding-left: 54px">
            <span class="form_left">业务员</span>
            <span class="form_right">
              {{ cousterdetail.salesManName || "未设置" }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">客户生日</span>
            <span class="form_right">
              {{ cousterdetail.birthday ? $_common.formatDate(cousterdetail.birthday, "yyyy-MM-dd") : "未设置" }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">客户类型</span>
            <span class="form_right">
              {{ cousterdetail.customerType }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">归属区域</span>
            <span class="form_right">
              {{ cousterdetail.area.provinceName }}-{{ cousterdetail.area.cityName }}-{{
                cousterdetail.area.districtName
              }}-{{ cousterdetail.area.address }}
            </span>
          </el-col>
          <el-col class="form" :span="6">
            <span class="form_left">标签</span>
            <span class="form_right">
              <el-tag v-for="(item, index) in cousterdetail.tagLib" :key="index" effect="plain">
                {{ item }}
              </el-tag>
            </span>
          </el-col>
          <el-col class="form" :span="24" style="padding-left: 64px">
            <span class="form_left">备注</span>
            <span class="form_right">
              {{ cousterdetail.remark || "无" }}
            </span>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <div class="order_bottom">
      <p class="text">联系方式</p>
      <div class="detail-tab-item">
        <div class="detail-tab-main" style="padding: 0">
          <el-table border size="mini" :data="tableTr">
            <el-table-column type="index" label="#" width="60px"></el-table-column>
            <el-table-column prop="name" label="联系人" min-width="120px"></el-table-column>
            <el-table-column prop="mobile" label="联系电话" min-width="120px"></el-table-column>
          </el-table>
          <div v-if="systemType === 1" class="table-b-div" @click="createPhone()">
            <el-button type="text" size="mini">
              <i class="el-icon-plus"></i>
              新增联系人
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="order_bottom">
      <p class="text">相关订单</p>
      <el-table v-if="$accessCheck($Access.orderQueryGetAllOrder)" :data="order_list" size="small">
        <el-table-column prop="no" label="订单编号" min-width="160" fixed="left"></el-table-column>
        <el-table-column prop="createTime" label="下单时间" min-width="160">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="realName" show-overflow-tooltip label="收货人" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.receiveData.realName || "--" }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="salesman" label="所属员工" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.salesman ? scope.row.salesman : "未分配" }}
          </template>
        </el-table-column>
        <el-table-column prop="mobile" label="联系电话" min-width="110">
          <template slot-scope="scope">
            {{ scope.row.receiveData.mobile }}
          </template>
        </el-table-column>
        <el-table-column prop="payAmount" label="订单金额" min-width="100">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.payAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="orderMsg" label="订单状态" min-width="80">
          <template slot-scope="scope">
            <span
              :class="[
                scope.row.orderMsg === '待审核'
                  ? 'warning-status'
                  : scope.row.orderMsg === '已关闭'
                  ? 'info-status'
                  : scope.row.orderMsg === '已出库'
                  ? 'primary-status'
                  : scope.row.orderMsg === '已完成'
                  ? 'success-status'
                  : scope.row.orderMsg === '待出库'
                  ? 'danger-status'
                  : 'primary-status',
              ]"
            >
              {{ scope.row.orderMsg }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="payStatus" label="收款状态" min-width="80">
          <template slot-scope="scope">
            <span
              :class="[
                parseInt(scope.row.payStatus) === 4
                  ? 'danger-status'
                  : parseInt(scope.row.payStatus) === 5
                  ? 'success-status'
                  : 'warning-status',
              ]"
            >
              {{
                parseInt(scope.row.payStatus) === 4
                  ? "未支付"
                  : parseInt(scope.row.payStatus) === 5
                  ? "已支付"
                  : "部分支付"
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="deliveryType" label="配送方式" min-width="80">
          <template slot-scope="scope">
            {{
              parseInt(scope.row.deliveryType) === 1
                ? "商品配送"
                : parseInt(scope.row.deliveryType) === 2
                ? "上门自提"
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column prop="source" label="订单来源" :show-overflow-tooltip="true" min-width="80">
          <template slot-scope="scope">
            {{
              parseInt(scope.row.source) === 1
                ? "ios"
                : parseInt(scope.row.source) === 2
                ? "安卓"
                : parseInt(scope.row.source) === 3
                ? "微信小程序"
                : parseInt(scope.row.source) === 4
                ? "后台创建"
                : parseInt(scope.row.source) === 5
                ? "H5页面"
                : parseInt(scope.row.source) === 6
                ? "pc页面"
                : parseInt(scope.row.source) === 6
                ? "字节跳动小程序"
                : "其他"
            }}
          </template>
        </el-table-column>
        <el-table-column prop="auditStatus" label="订单审核" min-width="80">
          <!--        ,1待审核 2审核通过 3审核驳回 4审核中-->
          <template slot-scope="scope">
            <span
              :class="[
                parseInt(scope.row.auditStatus) === 1
                  ? 'warning-status'
                  : parseInt(scope.row.auditStatus) === 2
                  ? 'success-status'
                  : parseInt(scope.row.auditStatus) === 3
                  ? 'danger-status'
                  : parseInt(scope.row.auditStatus) === 4
                  ? 'warning-status'
                  : 'warning-status',
              ]"
            >
              {{
                parseInt(scope.row.auditStatus) === 1
                  ? "待审核"
                  : parseInt(scope.row.auditStatus) === 2
                  ? "已审核"
                  : parseInt(scope.row.auditStatus) === 3
                  ? "已驳回"
                  : parseInt(scope.row.auditStatus) === 4
                  ? "审核中"
                  : "其他"
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" show-overflow-tooltip label="订单备注" min-width="120"></el-table-column>
        <el-table-column label="操作" min-width="180" fixed="right">
          <template slot-scope="scope">
            <el-button-group class="table-btn-group">
              <el-button
                v-if="$accessCheck($Access.newOrderLitGetOrderInfoById)"
                type="text"
                @click="goDetail(scope.row)"
              >
                查看
              </el-button>
              <el-button
                v-if="$accessCheck($Access.orderQueryUpdateAuditStatus)"
                :disabled="parseInt(scope.row.auditStatus) === 2"
                type="text"
                @click="updateAuditStatus(scope.row)"
              >
                审核
              </el-button>
              <el-button
                v-if="$accessCheck($Access.orderQueryUpdateOrderStatus)"
                :disabled="parseInt(scope.row.auditStatus) === 2"
                type="text"
                @click="updateOrderStatus(scope.row)"
              >
                取消订单
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="false" style="padding: 0 10px">
      <el-tabs type="card">
        <el-tab-pane v-if="false" label="客户分析">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="analyze-li" :style="{ borderColor: $_common.colorRgba(themeA, 0.5) }">
                <p class="clearfix">
                  <span class="de_label">订货频率：</span>
                  <span class="analyze_val" :style="{ color: themeA }"> 20 天/次 </span>
                </p>
                <p class="clearfix">
                  <span class="de_label">客单价：</span>
                  <span class="analyze_val" :style="{ color: themeA }"> 20万 平均/笔 </span>
                </p>
                <p class="clearfix">
                  <span class="de_label">本月成交额：</span>
                  <span class="analyze_val" :style="{ color: themeA }"> 100万 </span>
                </p>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="analyze-li" :style="{ borderColor: $_common.colorRgba(themeA, 0.5) }">
                <p class="clearfix">
                  <span class="de_label">本月新增应收款：</span>
                  <span class="analyze_val" :style="{ color: themeA }"> 200000 </span>
                </p>
                <p class="clearfix">
                  <span class="de_label">历史累计应收款：</span>
                  <span class="analyze_val" :style="{ color: themeA }"> 20万 </span>
                </p>
                <p class="clearfix">
                  <span class="de_label">本月已回款：</span>
                  <span class="analyze_val" :style="{ color: themeA }"> 100万 </span>
                </p>
              </div>
            </el-col>
          </el-row>
          <h4 style="padding: 20px 0">登录日志</h4>

          <el-table border style="width: 50%" size="mini" :data="login_tabel">
            <el-table-column prop="name" label="事件"></el-table-column>
            <el-table-column prop="time" label="日期"></el-table-column>
            <el-table-column prop="set" label="设备"></el-table-column>
          </el-table>
          <h4 style="padding: 20px 0">下单日志</h4>

          <el-table border style="width: 50%" size="mini" :data="order_tabel">
            <el-table-column prop="name" label="事件"></el-table-column>
            <el-table-column prop="time" label="日期"></el-table-column>
            <el-table-column prop="set" label="设备"></el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane v-if="false" label="工作日志">
          <el-table border size="mini" :data="order_tabel">
            <el-table-column prop="name" label="拜访者"></el-table-column>
            <el-table-column prop="time" label="相关工作"></el-table-column>
            <el-table-column prop="set" label="停留时间"></el-table-column>
            <el-table-column prop="set" label="拜访日期"></el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="新增联系方式"
      :visible.sync="isCreate"
      width="40%"
    >
      <div>
        <el-form ref="add_form" :model="add_form" :rules="base_rules" label-width="100px" title="新增联系人">
          <el-form-item label="姓名:" prop="name" style="width: 80%; margin-bottom: 20px">
            <el-input v-model="add_form.name" placeholder="请输入联系人姓名"></el-input>
          </el-form-item>
          <el-form-item label="手机号:" prop="mobile" style="width: 80%; margin-bottom: 20px">
            <el-input v-model="add_form.mobile" placeholder="请输入手机号"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isCreate = false">取 消</el-button>
        <el-button type="primary" @click="addContact('add_form')"> 确 定 </el-button>
      </span>
    </el-dialog>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="拒绝审核"
      :visible.sync="is_reject"
      width="30%"
    >
      <el-form ref="reject" :model="reject">
        <el-form-item label="输入拒绝原因" prop="reason">
          <el-input v-model="reject.reason" maxlength="20" style="width: 240px" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="is_reject = false">取 消</el-button>
        <el-button type="primary" @click="Reject">确 定 </el-button>
      </div>
    </el-dialog>
    <el-drawer :visible.sync="drawer" :with-header="false">
      <div v-for="(item, index) in customer_status" :key="index">
        <div v-if="item.deleteStatus === 5" class="detail-tab-item">
          <div class="detail-tab-title clearfix">
            <span class="float_left">{{ item.name }}</span>
          </div>
          <div class="detail-tab-main">
            <el-checkbox-group v-for="(item1, index1) in item.children" :key="index1" v-model="radio1">
              <el-checkbox-button
                v-if="item1.deleteStatus === 5"
                :label="item1.id"
                class="float_left"
                style="margin: 0 10px 0 0"
                @change="changeCheckbox"
              >
                {{ item1.name }}
              </el-checkbox-button>
            </el-checkbox-group>
          </div>
        </div>
      </div>
      <div class="detail-tab-main float_right">
        <el-button type="primary" @click="updateCustomerTagLibById"> 保 存 </el-button>
      </div>
    </el-drawer>
  </ContainerTit>
</template>

<script>
import {
  updateCustomerCheckStatus,
  getCustomerInfo,
  addCustomerContact,
  // addCustomerTag,
  delCustomerTag,
  getAllCustomerTagLib,
  updateCustomerTagLibById,
} from "@/api/Customer";
import { getAllOrder, updateOrderStatus, updateAuditStatus } from "@/api/Order";

export default {
  name: "CustomerDetail",
  data() {
    return {
      is_reject: false, // 拒绝按钮打开
      reject: {
        reason: "",
      },
      bigimg_src: "",
      customer_id: "",
      tags: [],
      taglib: [],
      searchWord: "",
      add_form: {
        customerId: "",
        name: "",
        mobile: "",
      },
      base_rules: {
        // 基本信息验证
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        mobile: [{ required: true, message: "联系电话不能为空", trigger: "blur" }],
      },
      order_tabel: [], // 订单表格
      login_tabel: [], // 下单日志
      cousterdetail: {
        area: {},
        contact: [
          {
            name: "",
            mobile: "",
          },
        ],
      },
      tableTr: [],
      order_list: [],
      isCreate: false,
      loading: false,
      drawer: false,
      customer_status: [],
      radio1: [],
      activeName: "one",
    };
  },
  async created() {
    this.customer_id = this.$route.params.id;
    await this.addTag("one");
    await this.getCustomerInfo();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getCustomerInfo();
  },
  methods: {
    // 拒绝审核按钮
    async Reject() {
      if (!this.reject.reason) {
        this.$message("请填写拒绝审核的原因");
        return;
      }
      try {
        this.loading = true;
        const data = await updateCustomerCheckStatus({
          id: this.customer_id,
          status: 3,
          reason: this.reject.reason,
        });

        this.is_reject = false;
        await this.getCustomerInfo();
        this.$message({
          type: "success",
          message: "提交成功",
        });
        this.loading = false;
      } catch {
        this.loading = false;
      }
    },
    // 审核与拒绝审核  $route.params.id
    EditAuditStatus(row) {
      this.loading = true;
      if (row) {
        this.$confirm("确定要审核此用户吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            try {
              const data = await updateCustomerCheckStatus({
                id: this.customer_id,
                status: 2,
              });
              await this.getCustomerInfo();
              this.loading = false;
              this.$message({
                type: "success",
                message: "审核成功",
              });
              this.loading = false;
            } catch {
              this.loading = false;
            }
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        this.is_reject = true;
      }
    },
    // 获取列表
    async getAllOrder(userCenterId) {
      // 验证订单列表权限
      if (!this.$accessCheck(this.$Access.orderQueryGetAllOrder)) {
        return;
      }
      let search = {
        userCenterId: userCenterId,
      };
      const data = await getAllOrder({
        page: this.page,
        pageSize: this.pageSize,
        search: search,
      });

      this.order_list = data.data;
      this.total = data.pageTotal;
    },
    goDetail(row) {
      this.$router.push({
        path: `/order/manageO/OrderDetails/${row.userCenterId}/${row.id}`,
      });
    },
    // 取消订单
    async updateOrderStatus(row) {
      this.$confirm("确定要取消该订单吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateOrderStatus(row.userCenterId, {
          orderId: row.id,
        });

        this.$message({
          type: "success",
          message: "操作成功",
        });
        this.pageChange(1);
      });
    },
    // 审核订单
    async updateAuditStatus(row) {
      this.$confirm("确定要审核通过该订单吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateAuditStatus(row.userCenterId, {
          orderId: row.id,
          auditStatus: 2,
          audit: this.userName,
        });

        this.$message({
          type: "success",
          message: "操作成功",
        });
        this.pageChange(1);
      });
    },
    //  客户详情 getCustomerInfo
    async getCustomerInfo() {
      if (!this.$route.params.id) {
        return;
      }
      const { data } = await getCustomerInfo(this.$route.params.id);

      this.tableTr = data.contact;
      this.cousterdetail = data;
      await this.getAllOrder(data.userCenterId);
      // this.taglib = data.taglib;
      let arr = [];
      this.customer_status.forEach((item) => arr.push(...item.children));
      let arr2 = [];
      // this.taglib.forEach((item1) =>
      //   arr2.push(...arr.filter((item2) => item2.id === item1))
      // );
      this.tags = arr2;
    },
    addContact(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const data = await addCustomerContact({
            // 刷新页面 contact内有customerId
            customerId: this.$route.params.id,
            name: this.add_form.name,
            mobile: this.add_form.mobile,
          });

          this.isCreate = false;
          this.$message.success("添加成功");
          //    重新刷新页面
          await this.getCustomerInfo();
        }
      });
    },
    //  新增联系人电话
    createPhone() {
      this.isCreate = true;
    },

    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    async addTag(id) {
      if (id === "two") {
        this.drawer = true;
      }
      const { data } = await getAllCustomerTagLib();
      this.customer_status = data;
      // if (!this.$accessCheck(this.$Access.BaseDataListUpdateCategory)) {
      //   return;
      // }
      // // 关键字不能为空
      // if (!this.searchWord) {
      //   this.$message({
      //     message: "请输入有效文字",
      //     type: "warning",
      //   });
      //   return;
      // }
      // const data = await addCustomerTag({
      //   id: this.$route.params.id,
      //   tag: this.searchWord,
      // });
      //
      // this.$message({
      //   message: "添加成功",
      //   type: "success",
      // });
      // this.searchWord = "";
      // this.getCustomerInfo();
    },
    // 删除标签
    delCustomerTag(tag) {
      if (!this.$accessCheck(this.$Access.CustomerListDelCustomerTag)) {
        return;
      }
      this.$confirm("是否确认删除此标签", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delCustomerTag({
          id: this.$route.params.id,
          tag: tag,
        });

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        await this.getCustomerInfo();
      });
    },
    changeCheckbox(value) {
      console.log(value);
    },
    async updateCustomerTagLibById() {
      const { data } = await updateCustomerTagLibById({
        id: this.customer_id,
        taglib: this.radio1,
      });
      await this.getCustomerInfo();
      this.drawer = false;
      this.$message.success("修改成功");
    },
  },
};
</script>

<style scoped lang="scss">
.extend-img {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  border: 1px solid #ddd;
  object-fit: contain;
  cursor: pointer;
}

.extend-span {
  padding-right: 20px;
  margin-top: 50px;
}

.itemStyle {
  display: flex;
  justify-content: space-around;
}

.formItem {
  width: 200px;
}

.table {
  background-color: #eee;
  border-spacing: 0;
  border-collapse: collapse;
  margin-left: 20px;
}

.table > tr {
  height: 24px;
  line-height: 24px;
}

.table > tr > td {
  height: 20px;
  font-size: 13px;
  padding: 6px 0 0 10px;
  border-top: 1px solid #eaeff0;
  vertical-align: middle;
}

.AddMan {
  color: #428bca;
  font-size: 15px;
  padding: 20px;
}

.otherInfo {
  font-size: 16px;
}

.otherInfo p {
  margin-left: 20px;
  padding-bottom: 20px;
  font: 13px "Hiragino Sans GB", Arial, "Microsoft Himalaya";
  color: #58666e;
}

.add-btn {
  height: 32px;
  line-height: 32px;
  width: 90px;
  text-align: center;
  background: #ff7557;
  color: #fff;
  font-size: 14px;
  border-radius: 5px;
  display: inline-block;
  cursor: pointer;
  vertical-align: middle;
  margin-left: 20px;
}

.analyze-li {
  border: 1px solid #333;
  padding: 20px 40px;
}

.analyze_val {
  font-size: 24px;
  line-height: 24px;
  float: right;
}

.analyze-li > p {
  margin-top: 20px;
}

.analyze-li > p:first-child {
  margin-top: 0;
}

.table-b-div {
  border: 1px solid #ebeef5;
  text-align: center;
  line-height: 40px;
  border-top: 0 none;
  cursor: pointer;
}

.de_from_row {
  .de_val {
    display: inline-block;
    vertical-align: middle;
    height: 36px;
    line-height: 36px;
  }
}

.order_bottom {
  border-top: 16px solid #f6f8f9;
  width: 100%;
  background-color: #ffffff;
  border-radius: 3px;

  .text {
    font-size: 16px;
    font-weight: 600;
    color: #2d405e;
    line-height: 64px;
    padding: 0px 24px;
  }
}

.order_midden_left_text {
  font-weight: bold;
  font-size: 16px;
  margin: 0 0 27px 0;
}

.form {
  padding-left: 40px;
  font-size: 14px;
  margin-bottom: 19px;

  .form_left {
    margin-right: 32px;
    color: #62738e;
    font-weight: 400;
  }

  .form_right {
    color: #2d405e;
    font-weight: 500;
  }
}

.btn-top-div {
  position: absolute;
  right: 20px;
  top: 15px;
  z-index: 999;
}
</style>
<style>
.CustomerDetail {
  background-color: #fff;
}

.CustomerDetail .el-tabs__item {
  font-size: 16px !important;
  height: 60px !important;
  line-height: 60px !important;
}

.CustomerDetail .is-active {
  font-weight: 700;
  color: #000;
}

.CustomerDetail .el-tabs__nav {
  margin-left: 24px;
}
</style>
