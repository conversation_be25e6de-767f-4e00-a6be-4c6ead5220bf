<template>
  <div style="padding: 15px">
    <div style="text-align: right; padding-bottom: 10px">
      <RegionSelect
        v-model="region"
        :props="{
          label: 'name',
          value: 'code',
          checkStrictly: true,
        }"
        clearable
        @change="regionChange"
      />
      <el-select v-model="customer" size="small" placeholder="请选择">
        <el-option
          v-for="item in customer_options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <span class="today">客户分布</span>
    </div>
    <baidu-map
      v-if="false"
      class="bm-view"
      :center="mapCenter"
      :style="{ height: $store.state.Router.pageHeight + 'px' }"
    >
      <!--              地区检索-->
      <bm-local-search
        :keyword="mapCenter"
        :auto-viewport="true"
        :location="regionName"
        @searchcomplete="searchcomplete"
      ></bm-local-search>
      <bm-navigation anchor="BMAP_ANCHOR_TOP_RIGHT"></bm-navigation>
      <bm-point-collection
        :points="points"
        shape="MAP_POINT_SHAPE_CIRCLE"
        color="red"
        size="BMAP_POINT_SIZE_NORMAL"
        @click="clickHandler"
      ></bm-point-collection>
    </baidu-map>
  </div>
</template>

<script>
import RegionSelect from "@/component/common/RegionSelectJSON";
import { getCustomerLocation } from "@/api/Customer";
export default {
  name: "CustomerMap",
  components: {
    RegionSelect,
  },
  data() {
    return {
      customer: "1",
      region: [],
      code: "",
      points: [],
      regionName: "",
      mapCenter: "北京",
      customer_options: [
        {
          label: "全部客户",
          value: "1",
        },
        {
          label: "三级代理",
          value: "2",
        },
        {
          label: "黄金代理商",
          value: "3",
        },
        {
          label: "白银代理商",
          value: "4",
        },
        {
          label: "青铜代理商",
          value: "5",
        },
      ],
    };
  },
  created() {
    // this.addPoints()
    this.getCustomerLocation();
  },
  methods: {
    searchcomplete(res) {
      console.log(res);
    },
    // 获取数据
    async getCustomerLocation() {
      const data = await getCustomerLocation({
        code: this.code,
      });

      this.points = data.data;
    },
    clickHandler(e) {
      alert(`单击点的坐标为：${e.point.lng}, ${e.point.lat}`);
    },
    regionChange(val, params) {
      this.mapCenter =
        (params[0] ? params[0].name : "") + (params[1] ? params[1].name : "") + (params[2] ? params[2].name : "");
      this.code = val.length ? val[val.length - 1] : "";
      // this.getCustomerLocation()
    },
  },
};
</script>

<style scoped>
.bm-view {
  width: 100%;
  height: 300px;
}
.today {
  display: inline-block;
  font-size: 14px;
}
.today:before {
  content: " ";
  display: inline-block;
  width: 10px;
  border-radius: 100%;
  height: 10px;
  background: red;
  margin-right: 10px;
}
</style>
