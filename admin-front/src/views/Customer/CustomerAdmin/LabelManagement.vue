<template>
  <Container>
    <div v-if="$accessCheck($Access.LabelManagementaddTabs)" slot="left">
      <el-button type="primary" @click="compileFlag">创建标签组 </el-button>
    </div>
    <div v-for="(item, index) in customer_status" :key="index">
      <div v-if="item.deleteStatus === 5" class="detail-tab-item">
        <div class="detail-tab-title clearfix">
          <span class="float_left">{{ item.name }}</span>
          <div class="float_right">
            <el-button
              v-if="$accessCheck($Access.LabelManagementdeleteTags)"
              style="float: right; padding: 3px 0; margin-right: 10px; color: orange"
              type="text"
              @click="delTagGroups(item)"
            >
              删除
            </el-button>
            <el-button
              v-if="$accessCheck($Access.LabelManagementeditTags)"
              class="float_left"
              style="padding: 3px 0; margin-right: 10px; color: rgb(55, 63, 70)"
              type="text"
              @click="compileTag(item)"
            >
              编辑标签组
            </el-button>
          </div>
        </div>
        <div class="detail-tab-main">
          <span v-for="(item1, index1) in item.children" :key="index1">
            <el-tag
              v-if="item1.deleteStatus === 5"
              size="medium"
              effect="plain"
              style="margin-right: 5px; color: #fff"
              :color="
                (() => {
                  if (item.colour === 1) {
                    return '#409EFF';
                  } else if (item.colour === 2) {
                    return '#67C23A';
                  } else if (item.colour === 3) {
                    return 'rgb(245, 147, 119)';
                  } else if (item.colour === 4) {
                    return '#E6A23C';
                  }
                })()
              "
            >
              {{ item1.name }}
            </el-tag>
          </span>
        </div>
      </div>
    </div>
    <el-dialog :title="compile_or_establish" :visible.sync="dialogVisible" width="40%" @close="closeTag">
      <div>
        <div style="font-weight: 700; font-size: 16px; margin-bottom: 10px">标题</div>
        <div style="width: 50%">
          <el-input v-model="title_value" placeholder="请输入内容"></el-input>
        </div>
      </div>
      <div>
        <div style="font-weight: 700; font-size: 16px; margin: 20px 0 10px 0">创建客户时必填</div>
        <div>
          <el-switch
            v-model="value"
            active-color="#36B365"
            inactive-color="rgb(223,224,224)"
            active-value="5"
            inactive-value="4"
          ></el-switch>
        </div>
      </div>
      <div>
        <div style="font-weight: 700; font-size: 16px; margin: 20px 0 10px 0">颜色</div>
        <div>
          <el-radio-group v-model="radio">
            <el-radio :label="1" style="color: rgb(61, 115, 255)">蓝 </el-radio>
            <el-radio :label="2" style="color: rgb(100, 212, 138)">绿 </el-radio>
            <el-radio :label="3" style="color: rgb(245, 147, 119)">粉 </el-radio>
            <el-radio :label="4" style="color: rgb(247, 192, 91)">橙 </el-radio>
          </el-radio-group>
        </div>
        <div>
          <div style="font-weight: 700; font-size: 16px; margin: 20px 0 10px 0">标签</div>
          <div>
            <el-tag
              v-for="(tag, index) in dynamicTags"
              :key="index"
              class="radius"
              size="medium"
              closable
              :disable-transitions="false"
              effect="plain"
              type="info"
              @close="handleClose(tag)"
            >
              {{ tag.name || tag }}
            </el-tag>
            <el-input
              v-if="inputVisible"
              ref="saveTagInput"
              v-model="inputValue"
              class="input-new-tag"
              size="small"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            ></el-input>
            <el-button v-else class="button-new-tag radius" size="mini" @click="showInput"> 创建新标签 </el-button>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeTag">取 消</el-button>
        <el-button v-if="establish_flag" type="primary" @click="confirmCompileTag"> 确 定 </el-button>
        <el-button v-else type="primary" @click="establish">创 建</el-button>
      </span>
    </el-dialog>
  </Container>
</template>
<script>
import {
  addCustomerTagLib,
  delCustomerTagLib,
  editCustomerTagLib,
  getAllCustomerTagLib,
  getCustomerTagLibInfo,
} from "@/api/Customer";

export default {
  data() {
    return {
      dialogVisible: false,
      value: "4",
      radio: 1,
      dynamicTags: [],
      inputVisible: false,
      inputValue: "",
      customer_status: [],
      title_value: "",
      compile_or_establish: "创建标签组",
      establish_flag: false,
      dynamicTagsId: "",
    };
  },
  created() {
    this.getAllCustomerTagLib();
  },
  methods: {
    async handleClose(tag) {
      if (this.dynamicTags.length <= 1) {
        this.$message.warning("必须保留一个标签");
        return;
      }
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      if (this.establish_flag) {
        const { data } = await delCustomerTagLib({
          pid: tag.pid,
          id: tag.id,
        });
        this.$message.success("删除成功");
        await this.getAllCustomerTagLib();
      }
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      let inputValue = this.inputValue.trim();
      if (inputValue) {
        this.dynamicTags.push({
          name: inputValue,
          pid: this.dynamicTags[0].pid,
        });
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    async getAllCustomerTagLib() {
      const { data } = await getAllCustomerTagLib();
      this.customer_status = data;
    },
    async establish() {
      if (this.title_value.trim() === "") {
        this.$message.warning("标题不能为空");
        return;
      }
      if (!this.dynamicTags.length) {
        this.$message.warning("必须有一项标签");
        return;
      }
      this.dialogVisible = false;
      const data = await addCustomerTagLib({
        name: this.title_value,
        mustValue: this.value - 0,
        colour: this.radio,
        tags: this.dynamicTags,
      });
      await this.getAllCustomerTagLib();
    },
    async delTag(item) {
      const { data } = await delCustomerTagLib({
        pid: item.pid,
        id: item.id,
      });
      await this.getAllCustomerTagLib();
    },
    async compileTag(item) {
      const { data } = await getCustomerTagLibInfo(item.id);
      this.establish_flag = true;
      this.compile_or_establish = "编辑标签组";
      this.dialogVisible = true;
      this.title_value = item.name;
      this.value = item.mustValue + "";
      this.radio = item.colour;
      this.dynamicTags = data[0].children;
      this.dynamicTagsId = data[0].id;
    },
    compileFlag() {
      this.dialogVisible = true;
      this.establish_flag = false;
      this.dynamicTags = [{ name: "默认标签" }];
    },
    closeTag() {
      this.dialogVisible = false;
      this.title_value = "";
      this.value = "4";
      this.radio = 1;
      this.dynamicTags = [];
    },
    async confirmCompileTag() {
      if (this.title_value.trim() === "") {
        this.$message.warning("标题不能为空");
        return;
      }
      if (!this.dynamicTags.length) {
        this.$message.warning("必须有一项标签");
        return;
      }
      const { data } = await editCustomerTagLib({
        update: [
          {
            id: this.dynamicTagsId,
            name: this.title_value,
            mustValue: this.value - 0,
            colour: this.radio,
          },
        ],
        add: this.dynamicTags.filter((item) => !item.deleteStatus),
      });
      this.$message.success("编辑成功");
      await this.getAllCustomerTagLib();
      this.dialogVisible = false;
    },
    delTagGroups(item) {
      console.log(item);
      this.$confirm("确定更要删除这个标签组？, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const { data } = await delCustomerTagLib({
          pid: item.pid,
          id: item.id,
        });
        await this.getAllCustomerTagLib();
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
  },
};
</script>
<style>
.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

.radius {
  border-radius: 10px;
}
</style>
