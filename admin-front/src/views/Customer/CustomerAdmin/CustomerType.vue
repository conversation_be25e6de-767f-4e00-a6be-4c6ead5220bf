<template>
  <Container>
    <div v-if="$accessCheck($Access.CustomerTypeAddCustomerSource)" slot="left">
      <el-button size="small" type="primary" @click="openModel(false)"> 新增类型 </el-button>
      <el-button size="small" type="primary">导出</el-button>
    </div>
    <div slot="tip" class="page-tip-div">
      <i class="el-icon-info"></i>
      温馨提示：系统默认客户类型不允许操作
    </div>
    <el-table :data="type_list">
      <el-table-column width="50" label="ID" prop="id"></el-table-column>
      <el-table-column prop="name" label="客户类型" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.name }}
          <el-tag v-if="scope.row.type === 1" size="mini" type="primary"> 系统 </el-tag>
        </template>
      </el-table-column>
      <el-table-column min-width="100" prop="defaultStatus" label="是否默认">
        <template slot-scope="scope">
          <span v-if="scope.row.defaultStatus === 5" class="success-status"> 默认类型 </span>
          <span v-else class="info-status">否</span>
        </template>
      </el-table-column>

      <el-table-column prop="enableStatus" label="状态" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.CustomerTypeUpdateCustomerSourceStatus)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="statusSet($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作" width="260px">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.CustomerTypeUpdateDefaultStatus)"
            :disabled="scope.row.enableStatus === 4 || scope.row.defaultStatus === 5"
            type="text"
            @click="defaultData(scope.row.id)"
          >
            设为默认
          </el-button>
          <el-button
            v-if="
              $accessCheck($Access.CustomerTypeEditCustomerSource) &&
              $accessCheck($Access.CustomerTypeGetCustomerSourceInfo)
            "
            :disabled="scope.row.type === 1 || scope.row.enableStatus === 5"
            type="text"
            @click="openModel(true, scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.CustomerTypeDelCustomerSource)"
            :disabled="scope.row.type === 1 || scope.row.enableStatus === 5"
            type="text"
            @click="delData(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <CutomerTypeAdd
      v-if="show_model"
      :is-edit="is_edit"
      :type-id="type_id"
      :is-show="show_model"
      :form="add_form"
      @cancel="show_model = false"
      @confirm="addData"
    ></CutomerTypeAdd>
  </Container>
</template>

<script>
import CutomerTypeAdd from "@/component/customer/CutomerTypeAdd.vue";
import {
  getAllCustomerSource,
  delCustomerSource,
  updateCustomerSourceDefaultStatus,
  updateCustomerSourceStatus,
} from "@/api/System";
export default {
  name: "CustomerTypeVue",
  components: {
    CutomerTypeAdd,
  },
  data() {
    return {
      pageSize: 10,
      page: 1,
      total: 0,
      show_model: false,
      is_edit: false,
      type_id: 0,
      type_list: [],
    };
  },
  created() {
    this.getAllCustomerSource();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllCustomerSource();
  },
  methods: {
    async getAllCustomerSource() {
      const data = await getAllCustomerSource({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.type_list = data.data;
      this.total = data.pageTotal;
    },
    async addData() {
      this.pageChange(1);
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    pageChange(page) {
      this.page = page;
      this.getAllCustomerSource();
    },
    openModel(isEdit, row) {
      this.add_form = {
        name: "",
        defaultStatus: 5,
        enableStatus: 5,
        modelType: [],
      };
      this.show_model = true;
      this.is_edit = isEdit;
      if (row) {
        this.type_id = row.id;
        this.add_form = {
          name: row.name,
          defaultStatus: row.defaultStatus,
          enableStatus: row.enableStatus,
          modelType: row.modelType ? row.modelType.split(",") : [],
        };
      }
    },
    delData(id) {
      this.$confirm("确定要删除该客户类型吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delCustomerSource(id);

        this.getAllCustomerSource();
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
    defaultData(id) {
      this.$confirm("确定要将该客户类型设为默认吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateCustomerSourceDefaultStatus({
          id: id,
          defaultStatus: "5",
        });

        this.getAllCustomerSource();
        this.$message({
          type: "success",
          message: "设置成功!",
        });
      });
    },
    async statusSet(val, row) {
      try {
        const data = await updateCustomerSourceStatus({
          id: row.id,
          enableStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        this.getAllCustomerSource();
      }
    },
  },
};
</script>

<style scoped></style>
