<template>
  <Container>
    <div slot="left">
      <el-form size="small" inline style="margin-bottom: 0">
        <el-form-item>
          <el-input
            v-model="query_form.keyword"
            clearable
            style="width: 220px"
            placeholder="客户姓名/手机号"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="query_form.salesManId"
            clearable
            placeholder="选择业务员"
            style="width: 150px"
            @change="pageChange(1)"
          >
            <el-option
              v-for="(item, index) in staff_list"
              :key="index"
              :label="item.staffName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <SelectShop
            v-model="query_form.shopId"
            :clearable="true"
            placeholder="所属店铺"
            width="150"
            @clear="delShop"
            @change="selShop"
          />
        </el-form-item>
        <el-form-item>
          <RegionSelect v-model="region" placeholder="所属区域" width="150" @change="regionChange" />
        </el-form-item>

        <el-form-item>
          <el-select
            v-model="query_form.enableStatus"
            clearable
            placeholder="客户状态"
            style="width: 150px"
            @change="pageChange(1)"
          >
            <el-option
              v-for="item in customerStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="query_form.source"
            clearable
            placeholder="注册来源"
            style="width: 150px"
            @change="pageChange(1)"
          >
            <el-option
              v-for="(item, index) in source_list"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="query_form.tag"
            clearable
            placeholder="客户标签"
            style="width: 155px"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" size="small">
      <el-table-column prop="id" label="ID" fixed="left" width="50"></el-table-column>
      <el-table-column prop="name" label="客户" min-width="280">
        <template slot-scope="scope">
          <div class="clearfix">
            <div class="float_left customer-img-view">
              <el-image :src="scope.row.avatar" fit="cover" />
            </div>

            <div class="float_left customer-name-view">
              <p class="customer-name">
                {{ scope.row.name }}
              </p>
              <p>{{ scope.row.code }}</p>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="联系信息" min-width="160" prop="contact">
        <template slot-scope="scope">
          <div>
            <p v-if="scope.row.contact[0]">
              <span class="table-label">联系人：</span>
              <span class="table-val">
                {{ scope.row.contact[0].name || "--" }}
              </span>
            </p>
            <p>
              <span class="table-label">账号/电话：</span>
              <span class="table-val">{{ scope.row.mobile || "--" }}</span>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="salesmanFlag" prop="salesManName" label="业务员" min-width="120">
        <template slot-scope="scope">
          <div>
            <p>
              <span class="table-label">业务员：</span>
              <span class="table-val">
                {{ scope.row.salesManName || "--" }}
              </span>
            </p>
            <p>
              <span class="table-label">销售部门：</span>
              <span class="table-val">
                {{ scope.row.departmentName || "--" }}
              </span>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="clientTypeFlag" prop="customerType" label="客户类型" min-width="100"></el-table-column>
      <el-table-column
        v-if="shopFlag"
        prop="shopName"
        label="商铺"
        min-width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column v-if="accountStatusFlag" prop="inventory" label="账号状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
          <span v-else class="danger-status">禁用</span>
        </template>
      </el-table-column>
      <el-table-column v-if="timeFlag" prop="createTime" label="注册时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column v-if="auditStatusFlag" prop="status" label="审核状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 2" class="success-status"> 已审核 </span>
          <span v-if="scope.row.status === 1" class="warning-status"> 待审核 </span>
          <span v-if="scope.row.status === 0" class="info-status"> 待完善资料 </span>
          <span v-if="scope.row.status === 3" class="success-status"> 已驳回 </span>
        </template>
      </el-table-column>
      <el-table-column prop="tag" label="操作" width="160" fixed="right">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span class="operation">操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.CustomerListGetCustomerInfo)"
            type="text"
            @click="$router.push(`/Customer/CustomerAdmin/CustomerDetail/${scope.row.id}`)"
          >
            查看
          </el-button>
          <el-button v-if="$accessCheck($Access.CustomerListEditCustomer)" type="text" @click="editData(scope.row)">
            编辑
          </el-button>
          <!--          <el-button-->
          <!--            size="mini"-->
          <!--            type="danger"-->
          <!--            @click="delData(scope.row)"-->
          <!--          >-->
          <!--            删除-->
          <!--          </el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <GoodsChooseShop
      v-if="show_shop"
      :is-check="false"
      :dialog-visible="show_shop"
      @close="show_shop = false"
      @confirm="selShop"
    ></GoodsChooseShop>
    <el-drawer class="edit-drawer" size="50%" :title="drawer_tit" :visible.sync="drawer">
      <EditCustomer v-if="drawer" :customer-id="customer_id" @subSuccess="subSuccess" />
    </el-drawer>
  </Container>
</template>

<script>
import RegionSelect from "@/component/common/RegionSelectJSON";
import GoodsChooseShop from "@/component/goods/GoodsChooseShop.vue";
import EditCustomer from "./AddCustomer";
import SelectShop from "@/component/goods/SelectShop.vue";
import { query } from "@/api/Customer";
import { getAllStaff } from "@/api/Department";

export default {
  name: "BrandManage",
  components: {
    RegionSelect,
    GoodsChooseShop,
    EditCustomer,
    SelectShop,
  },
  data() {
    return {
      customer_id: "",
      drawer: false,
      drawer_tit: "",
      source_list: [
        {
          label: "ios",
          tag: "ios",
          value: 1,
        },
        {
          label: "安卓APP",
          tag: "android",
          value: 2,
        },
        {
          label: "小程序",
          tag: "miniProgram",
          value: 3,
        },
        {
          label: "后台创建",
          tag: "manage",
          value: 4,
        },
        {
          label: "H5页面",
          tag: "H5",
          value: 5,
        },
        {
          label: "Pc页面",
          tag: "Pc",
          value: 6,
        },
      ],
      customerStatus: [
        {
          value: "5",
          label: "已启用",
        },
        {
          value: "4",
          label: "已停用",
        },
      ],
      show_shop: false,
      region: [],
      top_sel: "all",
      total: 0,
      page: 1,
      pageSize: 10,
      interestCustomerNum: "",
      newCustomerNum: "",
      customerTotalNum: "",
      tableData: [],
      activeNames: ["1"],
      staff_list: [],
      shop: "",
      query_form: {
        keyword: "",
        birthday_start: "",
        birthday_end: "",
        provinceCode: "",
        cityCode: "",
        districtCode: "",
        enableStatus: "",
        shopId: "",
        salesManId: "",
        tag: "",
        source: "",
      },
      checkList: ["业务员", "客户类型", "商铺", "帐号状态", "注册时间", "审核状态"],
      columns: [
        {
          label: "业务员",
        },
        {
          label: "客户类型",
        },
        {
          label: "商铺",
        },
        {
          label: "帐号状态",
        },
        {
          label: "注册时间",
        },
        {
          label: "审核状态",
        },
      ],
      salesmanFlag: true,
      clientTypeFlag: true,
      shopFlag: true,
      accountStatusFlag: true,
      timeFlag: true,
      auditStatusFlag: true,
    };
  },
  computed: {
    type_on_color() {
      return {
        // color: this.themeA
      };
    },
    type_on_style() {
      return {
        // borderColor: this.themeA,
        // backgroundColor: this.hexToRgba(this.themeA, 10).rgba,
      };
    },
  },
  created() {
    this.getAllStaff();
    this.query();
  },
  activated() {
    if (this.$_isInit()) return;
    this.query();
  },
  methods: {
    subSuccess() {
      this.pageChange(1);
      this.drawer = false;
    },
    editData(row) {
      this.customer_id = row.id;
      this.drawer_tit = row.name;
      this.drawer = true;
    },
    //  客户查询
    async query() {
      const { data, pageTotal, customerTotalNum, newCustomerNum, interestCustomerNum } = await query({
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.query_form.keyword,
        birthday_start: this.query_form.birthday_start,
        birthday_end: this.query_form.birthday_end,
        provinceCode: this.query_form.provinceCode,
        cityCode: this.query_form.cityCode,
        districtCode: this.query_form.districtCode,
        enableStatus: this.query_form.enableStatus,
        shopId: this.query_form.shopId,
        salesManId: this.query_form.salesManId,
        tag: this.query_form.tag,
        source: this.query_form.source,
      });

      this.tableData = data;
      this.customerTotalNum = customerTotalNum;
      this.newCustomerNum = newCustomerNum;
      this.interestCustomerNum = interestCustomerNum;
      this.total = pageTotal;
    },
    //  时间
    timeChange(val) {
      if (val && val.length) {
        this.query_form.birthday_start = val[0] / 1000;
        this.query_form.birthday_end = val[1] / 1000 + 86399;
      } else {
        this.query_form.birthday_start = "";
        this.query_form.birthday_end = "";
      }
      this.pageChange(1);
    },
    // 获取业务员
    async getAllStaff() {
      // 搜索
      const data = await getAllStaff({
        page: 1,
        pageSize: 9999,
      });

      this.staff_list = data.data;
    },
    // 选择区域
    regionChange(val) {
      this.query_form.provinceCode = val[0];
      this.query_form.cityCode = val[1];
      this.query_form.districtCode = val[2];
      this.pageChange(1);
    },
    // 选择店铺
    selShop(val) {
      this.pageChange(1);
    },
    delShop() {
      this.query_form.shopId = "";
      this.pageChange(1);
    },
    // 顶部筛选
    selTopType(tag) {
      this.top_sel = tag;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.query();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    searchData(val) {
      this.page = 1;
      this.pageChange(1);
    },
    delData(row) {},
    change() {
      this.salesmanFlag = this.checkList.some((item) => item === "业务员");
      this.clientTypeFlag = this.checkList.some((item) => item === "客户类型");
      this.shopFlag = this.checkList.some((item) => item === "商铺");
      this.accountStatusFlag = this.checkList.some((item) => item === "帐号状态");
      this.timeFlag = this.checkList.some((item) => item === "注册时间");
      this.auditStatusFlag = this.checkList.some((item) => item === "审核状态");
    },
  },
};
</script>
<style scoped lang="scss">
.customer-type-li {
  background: #fff;
  padding: 10px;
  border: 1px solid #eee;
  cursor: pointer;
  box-shadow: 0 0 4px 0 hsla(0, 0%, 73%, 0.5);
}
.num-p {
  font-size: 12px;
  color: #969799;
}
.customer-type-li .el-icon-question {
  color: #dcdee0;
}
.type-row {
  /*border-bottom: 1px solid #dcdee0;*/
  padding-bottom: 10px;
}
.customer-img-view {
  width: 50px;
  height: 50px;
  background-color: #f4f4f4;
  margin-right: 10px;
}
.customer-img {
  width: 50px;
  height: 50px;
}
.customer-name-view {
  width: calc(100% - 86px);
}
.customer-name {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
</style>
