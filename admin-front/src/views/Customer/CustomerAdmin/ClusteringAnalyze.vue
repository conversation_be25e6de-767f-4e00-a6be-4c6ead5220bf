<template>
  <div style="padding: 15px">
    <el-row :gutter="10" style="margin-bottom: 10px">
      <el-col :span="18">
        <div class="group-div">
          <p>兴趣人群</p>
          <p class="group-description">近7天有加商品到购物车，但是没有购买过商品的客户</p>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="group-div">
          <p>
            <span style="font-size: 12px; color: #333">人数</span>
            <el-popover placement="bottom" width="300" trigger="hover" content="该人群统计人数，统计时间截止至前一天">
              <i slot="reference" class="el-icon-question icon-question"></i>
            </el-popover>
          </p>
          <p style="font-size: 30px">0</p>
        </div>
      </el-col>
    </el-row>
    <div class="info-model">
      <TitltItem>
        <span slot="left">人群行为</span>
        <div slot="right" style="font-size: 12px; color: #2a2a2a">最近7天统计数据（统计截止至前一天，人数去重）</div>
      </TitltItem>
      <div>
        <el-row class="group-charts">
          <el-col
            v-for="(item, index) in behavir_list"
            :key="index"
            class="group-board-item"
            :style="[check_be === item.label ? activeItem : '']"
            :span="6"
          >
            <div class="group-board-content" @click="selBehavir(item)">
              <p class="title-p">
                <span>{{ item.label }}</span>
                <el-popover placement="bottom" width="300" trigger="hover" :content="item.descraption">
                  <i slot="reference" class="el-icon-question icon-question"></i>
                </el-popover>
              </p>
              <p class="content-p">
                {{ item.num }}
              </p>
              <p class="desc-tip">
                <span style="padding-right: 20px">占比：</span>
                <span>{{ item.desc }}%</span>
              </p>
            </div>
          </el-col>
        </el-row>
        <div>
          <ve-line :colors="colors" :legend-visible="false" :data="chartData"></ve-line>
        </div>
      </div>
    </div>
    <div class="info-model">
      <TitltItem>
        <span slot="left">商品兴趣</span>
      </TitltItem>
      <el-tabs v-model="activeName" style="margin-top: 10px" type="card">
        <el-tab-pane label="防问商品排行" name="first"></el-tab-pane>
        <el-tab-pane label="加购商品排行" name="second"></el-tab-pane>
        <el-tab-pane label="成交商品排行" name="third"></el-tab-pane>
      </el-tabs>
      <el-table border size="mini" :data="goods_data" style="width: 100%">
        <el-table-column prop="date" label="排名" align="center"></el-table-column>
        <el-table-column prop="date" label="商品" align="center"></el-table-column>
        <el-table-column prop="date" label="价格" align="center"></el-table-column>
        <el-table-column prop="date" label="人数" align="center"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import TitltItem from "@/component/style/Title";
export default {
  name: "ClusteringAnalyze",
  components: {
    TitltItem,
  },

  data() {
    return {
      goods_data: [],
      activeName: "first",
      colors: ["#5499F6"],
      chartData: {
        columns: ["日期", "访客人数"],
        rows: [],
      },
      check_be: "访客人数",
      behavir_list: [
        {
          label: "访客人数",
          descraption: "最近7天，该人群访问店铺的累计去重人数",
          num: 100,
          desc: 20,
        },
        {
          label: "领券客户数",
          descraption: "最近7天，该人群领取优惠券的累计去重人数",
          num: 100,
          desc: 20,
        },
        {
          label: "加购客户数",
          descraption: "最近7天，该人群有将商品加入购物车行为的累计去重人数",
          num: 100,
          desc: 20,
        },
        {
          label: "成交客户数",
          descraption: "最近7天，该人群有已付款订单的累计去重人数",
          num: 100,
          desc: 20,
        },
      ],
      activeItem: {},
    };
  },
  created() {
    this.indexGetDataToBarChart();
    this.activeItem = {
      border: "1px solid " + this.themeA,
    };
  },
  methods: {
    // 折线图
    async indexGetDataToBarChart() {
      const lineData = [
        { value: 0, date: "2019-10-24" },
        { value: 0, date: "2019-10-25" },
        { value: 0, date: "2019-10-26" },
        { value: 0, date: "2019-10-27" },
        { value: 0, date: "2019-10-28" },
        { value: 0, date: "2019-10-29" },
        { value: 0, date: "2019-10-30" },
      ];
      this.chartData.columns = ["日期", this.check_be];
      this.chartData.rows = lineData.reduce((container, item) => {
        const { value, date } = item;
        let target = {};
        target["日期"] = date;
        target[this.check_be] = value;
        container.push(target);
        return container;
      }, []);
    },
    selBehavir(row) {
      this.check_be = row.label;
      this.indexGetDataToBarChart();
    },
  },
};
</script>

<style scoped>
.group-div {
  padding: 24px;
  line-height: 24px;
  font-size: 16px;
  background-color: #f8f8f8;
}
.group-description {
  font-size: 12px;
  color: #666;
}
.icon-question {
  font-size: 12px;
  color: #cacaca;
}
.group-board-item {
  cursor: pointer;
  position: relative;
  background-color: #fff;
  border-left: 1px solid #eee;
  padding: 14px 0;
  text-align: center;
  color: #333;
}
.group-board-item .group-board-content {
  display: inline-block;
}
.group-board-item .title-p {
  text-align: left;
  min-width: 82px;
  font-size: 12px;
  padding-bottom: 5px;
  padding-right: 5px;
}
.group-board-item .content-p {
  font-size: 26px;
  padding-top: 5px;
  padding-bottom: 12px;
  text-align: left;
}
.group-board-item .group-board-content .desc-tip {
  text-align: left;
  font-size: 12px;
}
.group-charts {
  border: 1px solid #eee;
  border-left: none;
  margin-top: 10px;
}
</style>
