<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary">提交</el-button>
    </div>
    <el-card shadow="hover" class="box-card">
      <div slot="header" class="clearfix">
        <span>人群名称</span>
        <el-popover placement="top" width="200" trigger="hover">
          <div>
            1. 人群名称不能为空；
            <br />
            2. 不能和已有的人群名称重复；
            <br />
            3. 30个字符以内，且不能包含特殊字符和空格。
          </div>
          <i slot="reference" style="color: #cacaca" class="el-icon-question"></i>
        </el-popover>
      </div>

      <el-input
        v-model="add_form.name"
        maxlength="30"
        clearable
        show-word-limit
        style="width: 300px"
        placeholder="请输入30字以内的人群名称"
      ></el-input>
    </el-card>
    <el-card shadow="hover" class="box-card">
      <div slot="header" class="clearfix">
        <span>行为关系</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="addBehavior">
          <i class="el-icon-plus"></i>
          添加筛选条件
        </el-button>
      </div>
      <ul>
        <li v-for="(itemB, indexB) in behavior_arr" :key="indexB" class="clearfix behavio-li">
          <div class="float_left">
            <el-select
              v-model="itemB.behavior"
              size="small"
              placeholder="请选择"
              @change="behaviorChange(itemB.behavior, indexB)"
            >
              <el-option
                v-for="(item, index) in behavior"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <div v-if="itemB.sel_type === 'day'" style="display: inline-block">
              <el-select v-model="itemB.value" size="small" placeholder="请选择">
                <el-option v-for="(item, index) in behavior_day" :key="index" :label="item" :value="item"></el-option>
              </el-select>
              <span>天内</span>
            </div>
            <div v-if="itemB.sel_type === 'region'" style="display: inline-block">
              <el-select v-model="itemB.region_tag" style="width: 100px" size="small" placeholder="请选择">
                <el-option
                  v-for="(item, index) in qujian_list"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-input
                v-model="itemB.region_num1"
                placeholder="请输入数值"
                size="small"
                style="width: 100px"
              ></el-input>
              ~
              <el-input
                v-model="itemB.region_num2"
                placeholder="请输入数值"
                size="small"
                style="width: 100px"
              ></el-input>
            </div>
            <div v-if="itemB.sel_type === 'timePicker'" style="display: inline-block">
              <el-date-picker
                v-model="itemB.value"
                size="small"
                type="daterange"
                value-format="timestamp"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </div>
          </div>
          <el-button
            :disabled="behavior_arr.length === 1"
            class="float_right"
            type="danger"
            size="mini"
            @click="delBehavior"
          >
            <i class="el-icon-delete"></i>
          </el-button>
        </li>
      </ul>
    </el-card>
    <el-card shadow="hover" class="box-card">
      <div slot="header" class="clearfix">
        <span>基本属性</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="addBase">
          <i class="el-icon-plus"></i>
          添加筛选条件
        </el-button>
      </div>
      <ul>
        <li v-for="(itemB, indexB) in base_info_list" :key="indexB" class="clearfix behavio-li">
          <div class="float_left">
            <el-select
              v-model="itemB.label_base"
              size="small"
              placeholder="请选择"
              @change="baseChange(itemB.label_base, indexB)"
            >
              <el-option
                v-for="(item, index) in baseType"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-select v-model="itemB.label_val" size="small" placeholder="请选择">
              <el-option
                v-for="(item, index) in base_value_sel"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>

          <el-button
            :disabled="base_info_list.length === 1"
            class="float_right"
            type="danger"
            size="mini"
            @click="delBase"
          >
            <i class="el-icon-delete"></i>
          </el-button>
        </li>
      </ul>
    </el-card>
  </ContainerTit>
</template>

<script>
export default {
  name: "AddClustering",

  data() {
    return {
      add_form: {
        name: "",
      },
      baseType: [
        {
          label: "客户来源方式",
          value: 1,
        },
        {
          label: "性别",
          value: 2,
        },
      ],
      base_info_list: [
        {
          label_base: "",
          label_val: "",
        },
      ],
      base_value_sel: [
        {
          label: "安卓",
          value: 1,
        },
        {
          label: "IOS",
          value: 2,
        },
        {
          label: "微信小程序",
          value: 3,
        },
        {
          label: "后台添加",
          value: 4,
        },
        {
          label: "字节跳动小程序",
          value: 6,
        },
      ],
      behavior_arr: [
        {
          behavior: "",
          value: "",
          sel_type: "",
        },
      ],
      value: "",
      input: "",
      qujian_list: [
        {
          label: "区间",
          value: 1,
        },
        {
          label: "大于",
          value: 2,
        },
        {
          label: "小于",
          value: 3,
        },
      ],
      behavior_day: [1, 2, 3, 7, 15, 30, 90, 180, 360, 720],
      behavior: [
        {
          label: "最近有访问",
          value: 1,
          sel_type: "day",
        },
        {
          label: "最近没有访问",
          value: 2,
          sel_type: "day",
        },
        {
          label: "最近有加入购物车",
          value: 3,
          sel_type: "day",
        },
        {
          label: "最近没有加入购物车",
          value: 4,
          sel_type: "day",
        },
        {
          label: "最近有购买",
          value: 5,
          sel_type: "day",
        },
        {
          label: "最近没有购买",
          value: 6,
          sel_type: "day",
        },
        {
          label: "累计消费订单数",
          value: 7,
          sel_type: "region",
        },
        {
          label: "最近没有访问",
          value: 8,
          sel_type: "day",
        },
        {
          label: "无退货退款",
          value: 9,
          sel_type: "day",
        },
        {
          label: "累计消费金额",
          value: 10,
          sel_type: "region",
        },
        {
          label: "客单价",
          value: 11,
          sel_type: "region",
        },
        {
          label: "购买商品分类",
          value: 12,
          sel_type: "select",
        },
        {
          label: "购买商品品相",
          value: 13,
          sel_type: "select",
        },
        {
          label: "客户注册时间",
          value: 14,
          sel_type: "timePicker",
        },
        {
          label: "客户审核时间",
          value: 15,
          sel_type: "timePicker",
        },
        {
          label: "客户欠款",
          value: 16,
          sel_type: "region",
        },
      ],
    };
  },
  methods: {
    behaviorChange(val, index) {
      const selType = this.behavior.find((item) => item.value === val).sel_type;
      switch (selType) {
        case "region":
          this.behavior_arr[index].region_tag = 1;
          this.behavior_arr[index].region_num1 = "";
          this.behavior_arr[index].region_num2 = "";
          break;
        case "timePicker":
          this.behavior_arr[index].value = [];
          break;
        case "select":
          this.behavior_arr[index].value = "";
          break;
        case "day":
          this.behavior_arr[index].value = "";
          break;
      }
      this.behavior_arr[index].sel_type = selType;
    },
    addBehavior() {
      this.behavior_arr.push({
        behavior: "",
        value: "",
        sel_type: "",
      });
    },
    delBehavior(index) {
      this.behavior_arr.splice(index, 1);
    },
    addBase() {
      this.base_info_list.push({ label_base: "", label_val: "" });
    },
    delBase(index) {
      this.base_info_list.splice(index, 1);
    },
    baseChange(val, index) {
      if (val === 1) {
        this.base_value_sel = [
          {
            label: "安卓",
            value: 1,
          },
          {
            label: "IOS",
            value: 2,
          },
          {
            label: "小程序",
            value: 3,
          },
          {
            label: "后台添加",
            value: 4,
          },
        ];
      } else {
        this.base_value_sel = [
          {
            label: "男",
            value: 1,
          },
          {
            label: "女",
            value: 2,
          },
        ];
      }
    },
  },
};
</script>

<style scoped>
.behavio-li {
  margin-bottom: 10px;
}
</style>
