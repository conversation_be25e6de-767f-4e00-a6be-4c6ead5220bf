<template>
  <div style="padding: 15px">
    <div class="view-model">
      <div class="title-view">
        <span class="tit-line" :style="{ backgroundColor: themeA }"></span>
        重点运营人群
      </div>
      <el-row :gutter="20">
        <el-col v-for="(item, index) in qun_list_top" :key="index" :span="8">
          <div class="qun-li">
            <div class="clearfix qun-top-view" :style="{ backgroundColor: item.color }">
              <div class="float_left icon-div">
                <i :class="item.icon"></i>
              </div>
              <div class="float_left qun-info-view">
                <p class="qun-name-p">
                  {{ item.name }}
                </p>
                <p class="qun-description-p">
                  {{ item.description }}
                </p>
              </div>
            </div>
            <ul class="qun-num-ul">
              <li class="qun-num-li">
                <p>人群总数</p>
                <p class="num-text-p">0</p>
              </li>
              <li class="qun-num-li">
                <p>昨日访问</p>
                <p class="num-text-p">0</p>
              </li>
              <li class="qun-num-li">
                <p>昨日成交</p>
                <p class="num-text-p">0</p>
              </li>
            </ul>
            <ul class="qun-btn-ul">
              <li @click="$router.push('/Customer/CustomerAdmin/ClusteringAnalyze/1')">人群分析</li>
              <li @click="$router.push('/Customer/CustomerAdmin/CustomerList')">客户列表</li>
            </ul>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="view-model">
      <div class="title-view">
        <span class="tit-line" :style="{ backgroundColor: themeA }"></span>
        我的人群库
      </div>
      <div>
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane name="first" label="自定义人群">
            <div class="clearfix" style="padding-bottom: 10px">
              <div class="float_left">
                <el-input v-model="qun_name" size="mini" placeholder="请输入人群名称">
                  <el-button slot="append" icon="el-icon-search"></el-button>
                </el-input>
              </div>
              <el-button
                class="float_right"
                type="primary"
                size="mini"
                @click="$router.push('/Customer/CustomerAdmin/AddClustering')"
              >
                新建人群
              </el-button>
            </div>
          </el-tab-pane>
          <el-tab-pane name="second" label="系统推荐人群"></el-tab-pane>
        </el-tabs>
        <el-table border :data="qun_list" style="width: 100%">
          <el-table-column prop="name" label="人群名称" width="180" align="center"></el-table-column>
          <el-table-column
            prop="dingyi"
            label="人群定义"
            min-width="300"
            align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column prop="num" label="人群数量" width="180" align="center"></el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="180" align="center"></el-table-column>
          <el-table-column prop="handelren" label="操作人" width="180" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="200" align="center">
            <template slot-scope="scope">
              <el-tooltip effect="dark" content="短信群发" placement="top">
                <el-button type="warning" size="mini">
                  <i class="el-icon-chat-dot-square"></i>
                </el-button>
              </el-tooltip>
              <el-tooltip v-if="activeName === 'first'" effect="dark" content="编辑" placement="top">
                <el-button type="primary" size="mini" @click="editData(scope.row)">
                  <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                </el-button>
              </el-tooltip>
              <el-tooltip v-if="activeName === 'first'" effect="dark" content="删除" placement="top">
                <el-button type="danger" size="mini">
                  <i class="el-icon-delete"></i>
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <FooterPage
          :page-size="pageSize"
          :total-page.sync="total"
          :current-page.sync="page"
          @pageChange="pageChange"
          @sizeChange="sizeChange"
        ></FooterPage>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CustomerClustering",
  data() {
    return {
      qun_list_top: [
        {
          name: "兴趣人群",
          icon: "fa fa-heart-o",
          description: "近7天有加商品到购物车，但是没有购买过商品的客户",
          color: "rgb(38, 159, 255)",
        },

        {
          name: "新成交客户人群",
          icon: "fa fa-handshake-o",
          description: "近720天有且仅有一次成交，且成交发生在近90天内的客户",
          color: "rgb(47, 174, 68)",
        },
      ],
      activeName: "first",
      pageSize: 10,
      total: 0,
      page: 1,
      qun_list: [
        {
          name: "最具购买力人群",
          num: 10,
          updateTime: "2019-10-31 09:47:45",
          handelren: "小米",
          dingyi:
            "最近7天内有访问;\n" +
            "\n" +
            "最近2天内有加入购物车;\n" +
            "\n" +
            "最近2天内有下单;\n" +
            "\n" +
            "最近3天内有购买;\n" +
            "\n" +
            "累计消费订单数（2年内）大于1000;\n" +
            "\n" +
            "性别（男）;\n" +
            "\n" +
            "来源[手工录入];",
        },
      ],
      qun_name: "",
    };
  },
  methods: {
    pageChange(page) {
      this.page = page;
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    editData() {
      this.$router.push("/Customer/CustomerAdmin/EditClustering/1");
    },
  },
};
</script>

<style scoped>
.qun-li {
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 0 4px 0 hsla(0, 0%, 73%, 0.5);
  margin: -10px 16px 33px 0;
}
.qun-top-view {
  background: rgb(38, 159, 255);
  padding: 14px;
  color: #fff;
}
.icon-div {
  width: 48px;
  height: 48px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 48px;
  font-size: 26px;
  margin-right: 10px;
}
.qun-description-p {
  height: 28px;
  line-height: 14px;
  font-size: 10px;
  opacity: 0.7;
  padding-top: 6px;
}
.qun-info-view {
  width: calc(100% - 80px);
}
.qun-num-ul {
  display: flex;
  padding: 11px 5px 2px;
}
.qun-num-li {
  flex: 3;
  text-align: center;
  color: #666;
  font-size: 12px;
}
.qun-num-li:nth-child(2) {
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
}
.num-text-p {
  font-size: 18px;
  color: #333;
}
.qun-btn-ul {
  display: flex;
  padding: 8px 11px;
}
.qun-btn-ul > li {
  flex: 2;
  text-align: center;
  color: #38f;
  border: 1px solid #38f;
  line-height: 30px;
  font-size: 12px;
  cursor: pointer;
}
.qun-btn-ul > li:first-child {
  margin-right: 5px;
}
.qun-btn-ul > li:last-child {
  margin-left: 5px;
}
.title-view {
  margin-bottom: 20px;
  font-size: 14px;
  padding: 15px;
  background: #f8f8f8;
}
.title-view .tit-line {
  display: inline-block;
  width: 4px;
  height: 16px;
  /*background: green;*/
  margin-right: 5px;

  transform: translateY(3px);
}
</style>
