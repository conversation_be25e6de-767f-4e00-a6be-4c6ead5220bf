<template>
  <Container>
    <div slot="left">
      <el-form inline>
        <el-form-item label="客户:"></el-form-item>
        <el-form-item>{{ name }}</el-form-item>
        <el-form-item v-if="Number(money)" label="|"></el-form-item>
        <el-form-item v-if="Number(money)" label="当前余额:">
          {{ money }}
        </el-form-item>
        <el-form-item v-if="Number(money)" label="|"></el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            @change="orderDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="purpose" label="类型"></el-table-column>
        <el-table-column prop="money" label="变动金额">
          <template slot-scope="scope">
            <span :class="[parseInt(scope.row.type) === 4 ? 'danger-status' : 'success-status']"></span>
            <span>{{ parseInt(scope.row.type) === 4 ? "-" : "+" }}</span>
            {{ scope.row.money }}
          </template>
        </el-table-column>
        <el-table-column prop="afterMoney" label="账户金额"></el-table-column>
        <el-table-column label="关联订单">
          <template slot-scope="scope">
            <span v-if="scope.row.orderIdDate && scope.row.orderIdDate.length">
              <span
                v-for="(item, index) in scope.row.orderIdDate"
                :key="index"
                class="click-div"
                @click="goDetail(item)"
              >
                {{ item.no }}
                <br />
              </span>
            </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="时间">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.createTime, "yyyy-MM-dd hh:mm:ss") }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { getAllMemberBalanceDetail } from "@/api/Customer";

export default {
  data() {
    return {
      time: "",
      // 余额明细
      name: "",
      // 当前余额
      money: "",
      customerId: "",
      xxx: "",
      total: 0,
      page: 1,
      pageSize: 10,
      tableData: [],
      start: "",
      end: "",
    };
  },
  created() {
    this.money = this.$route.query.money;
    this.name = this.$route.query.name;
    this.customerId = this.$route.query.id;
    this.getAllMemberBalanceDetail();
  },
  activated() {
    if (this.$_isInit()) return;
    // this.getCustomerInfo();
  },
  methods: {
    // 时间筛选
    orderDate(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.pageChange(1);
    },
    //获取余额明细
    async getAllMemberBalanceDetail() {
      const { data, pageTotal } = await getAllMemberBalanceDetail({
        customerId: this.customerId,
        page: this.page,
        pageSize: this.pageSize,
        start: this.start,
        end: this.end,
      });
      this.tableData = data;
      this.total = pageTotal;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllMemberBalanceDetail();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    goDetail(row) {
      this.$router.push({
        path: `/order/manageO/OrderDetails/${row.userCenterId}/${row.id}`,
      });
    },
  },
};
</script>

<style scoped></style>
