<template>
  <ContainerTit class="ContainerTit">
    <div class="header">
      <el-steps align-center :active="steps_active">
        <el-step title="客户导入文件" icon="el-icon-upload"></el-step>
        <el-step title="客户导入预览" icon="el-icon-s-order"></el-step>
        <el-step title="客户导入完成" icon="el-icon-s-claim"></el-step>
      </el-steps>
    </div>
    <!--    客户导入预览-->
    <div v-if="steps_active === 0">
      <div slot="tip" class="page-tip-div" style="margin-top: 0">
        <i class="el-icon-info"></i>
        温馨提示：
        <span>1、登录账号必须为客户微信绑定的手机号！</span>
        <span>2、客户类型必须在后台存在！</span>
        <span>3、联系人地区格式为省市区独立字段！</span>
        <span>4、商铺/销售部门/业务员必须在后台存在！</span>
        <span>5、客户生日格式为2021-01-01！</span>
      </div>
      <div class="span">
        <a href="https://upload.qianniao.vip/template/kehu.xlsx" target="_blank">
          <i class="iconfont icon-xiazai-xue"></i>
          <span>下载模板</span>
        </a>
      </div>
      <div class="content">
        <p class="title">
          <i class="required">*</i>
          客户文件
        </p>
        <div class="input-up">
          <i class="iconfont icon-lianjie"></i>
          <el-upload
            ref="upload"
            action="#"
            accept="xlsx,xls"
            :auto-upload="false"
            :file-list="fileList"
            list-type="text"
            :before-upload="beforeUpload"
            :on-remove="onRemove"
            :on-change="onChange"
          >
            <div slot="trigger" style="width: 360px; padding: 0 10px; text-align: left">
              <span v-if="file_name">{{ file_name }}</span>
              <span v-else style="color: #999999">选取文件</span>
            </div>
          </el-upload>
        </div>
      </div>
    </div>
    <!--    商品导入预览-->
    <div v-else-if="steps_active === 1" class="steps-two">
      <vxe-grid
        ref="xTable"
        border="inner"
        :columns="tableColumn"
        :data="goods_data"
        :import-config="{ message: true }"
      ></vxe-grid>
    </div>
    <div v-else class="steps-three">
      <p class="title">导入情况</p>
      <div class="desc">
        <span v-html="Import_the_information"></span>
        <span v-if="flag > 0">
          <a target="_blank" :href="href">点击查看错误原因</a>
        </span>
        <!--        <el-button type="text">下载失败数据</el-button>-->
      </div>
    </div>
    <div slot="headr">
      <el-button @click="closePage">
        {{ steps_active === 2 ? "完成" : "取消" }}
      </el-button>
      <el-button v-if="steps_active === 0" type="primary" @click="previewData"> 下一步 </el-button>
      <el-button v-if="steps_active === 1" type="primary" @click="exportsData"> 确定导入 </el-button>
      <el-button v-if="steps_active === 2" type="primary" @click="againExport"> 重新上传 </el-button>
    </div>
  </ContainerTit>
</template>

<script>
import { customerImport } from "@/api/Customer";

export default {
  name: "GoodsImport",
  data() {
    return {
      file_name: "",
      file: "",
      fileList: [],
      goods_data: [],
      steps_active: 0,
      tableColumn: [
        { field: "name", title: "客户名称" },
        { field: "mobile", title: "登陆账号" },
        { field: "type", title: "客户类型" },
        { field: "contacts", title: "联系人" },
        { field: "nameMobile", title: "联系电话" },
        { field: "provinceName", title: "省" },
        { field: "cityName", title: "市" },
        { field: "areaName", title: "区" },
        { field: "address", title: "详细地址" },
        { field: "shopName", title: "商铺" },
        { field: "departmentId ", title: "销售部门ID" },
        { field: "salesManId", title: "业务员ID" },
        { field: "birthday", title: "客户生日" },
        { field: "remark", title: "客户备注" },
      ],
      Import_the_information: "",
      flag: "",
      href: "",
    };
  },
  methods: {
    lookDetail() {},
    // 取消/完成 按钮
    closePage() {
      if (this.steps_active === 1) {
        this.steps_active = 0;
      } else {
        this.$closeCurrentGoEdit("/Customer/CustomerAdmin/CustomerList");
      }
    },

    // 下一步
    previewData() {
      // this.$refs.xTable.importData();
      const fileReader = new FileReader();
      fileReader.onload = (ev) => {
        const data = ev.target.result;
        const workbook = this.$XLSX.read(data, { type: "binary" });
        const csvData = this.$XLSX.utils.sheet_to_csv(workbook.Sheets.Sheet1);
        let tableData = [];
        console.log("csvData", csvData.split("\n"));
        // 解析数据
        csvData.split("\n").forEach((vRow, rindex) => {
          if (vRow && rindex > 0) {
            const vCols = vRow.split(",");
            // console.log("vCols", vCols);
            // 过滤空行
            if (vCols.length >= 1 && vCols[0] === "") {
              return;
            }
            const item = {};
            vCols.forEach((val, cIndex) => {
              const column = this.tableColumn[cIndex];
              if (column && column.field) {
                item[column.field] = val;
              }
            });
            tableData.push(item);
          }
        });
        this.goods_data = tableData;
        console.log(this.goods_data);
      };
      fileReader.readAsBinaryString(this.file);
      this.steps_active = 1;
    },
    //确定导入
    async exportsData() {
      this.steps_active = 2;
      if (JSON.stringify(this.goods_data) === "[]") {
        this.$message.error("上传失败，数据异常");
        return;
      }
      const { data } = await customerImport(this.goods_data);
      this.Import_the_information = data.successMsg;
      this.flag = data.errorNum;
      this.href = data.url;
    },
    //重新上传
    againExport() {
      this.steps_active = 0;
    },
    //覆盖默认的上传行为，可以自定义上传的实现
    submitUpload(request) {
      console.log("submitUpload", request);
      // this.$refs.upload.submit();
    },
    //上传文件之前的钩子，参数为上传的文件，若返回 false 或者返回 Promise 且被 reject，则停止上传。
    beforeUpload(file) {
      console.log("beforeUpload", file);
    },
    //文件列表移除文件时的钩子
    onRemove(file, fileList) {
      console.log("onRemove", file, fileList);
    },
    onChange(file) {
      console.log("onChange", file);
      this.file_name = file.name;
      this.file = file.raw;
    },
  },
};
</script>
<style scoped lang="scss">
.ContainerTit {
  background-color: #ffffff;
}

.span {
  border-radius: 5px;
  height: 15px;
  padding: 0 20px 20px;
  width: 98%;
  margin: 20px auto;
  a {
    color: #333333;
    .icon-xiazai-xue {
      font-size: 16px;
      margin-right: 4px;
    }
  }
}
.header {
  margin: 40px auto 20px;
  width: 60%;
}
.content {
  width: 98%;
  margin: 20px auto;
  padding: 24px 20px;
  border-radius: 4px 4px 0 0;
  background-color: #f5f7fc;
  .title {
    padding-bottom: 20px;
    .required {
      color: #ff4400;
    }
  }
  .input-up {
    width: 360px;
    text-align: left;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #d5dae0 !important;
    position: relative;
    overflow: hidden;
    .icon-lianjie {
      position: absolute;
      top: 50%;
      right: 20px;
      color: #768696;
      transform: translateY(-50%);
      display: block;
    }
  }
}
.steps-three {
  margin-top: 90px;
  text-align: center;
  .title {
    color: #333;
    font-size: 24px;
  }
  .desc {
    color: #333;
    font-size: 14px;
    margin: 18px 0 60px;
  }
}
</style>
