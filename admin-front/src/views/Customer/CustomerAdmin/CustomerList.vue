<template>
  <ContainerQuery>
    <div slot="tip" class="page-tip-div" style="margin-top: 0">
      <i class="el-icon-info"></i>
      温馨提示：
      <span>1、已审核客户只有在禁用后才能编辑！</span>
      <span>2、后台手动新增的客户自动审核！</span>
    </div>
    <div v-if="systemType === 1" slot="left">
      <el-dropdown
        v-if="$accessCheck($Access.CustomerListAddCustomer)"
        split-button
        type="primary"
        @click="$router.push('/Customer/CustomerAdmin/AddCustomer')"
      >
        新增客户
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <div v-if="$accessCheck($Access.CustomerListExport)" class="dropdown-div" @click="exportData">导出</div>
          </el-dropdown-item>
          <el-dropdown-item>
            <div class="dropdown-div" @click="$router.push('/Customer/CustomerAdmin/CustomerImport')">导入</div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button v-else size="small" type="primary" @click="exportData"> 导出 </el-button>
      <el-dialog
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        title="批量设置业务员"
        :visible.sync="batchSettingVisible"
      >
        <el-form :model="batchSetting" :inline="true">
          <el-form-item label="部门：" :label-width="batchSettingWidth">
            <DepartmentSel v-model="departmentPidPath" clearable size="small" @change="departmentChange" />
          </el-form-item>
          <el-form-item label="业务员：" :label-width="batchSettingWidth">
            <el-select v-model="saleman" placeholder="请选择业务员">
              <el-option v-for="item in options" :key="item.id" :label="item.staffName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="batchSettingVisible = false">取 消</el-button>
          <el-button type="primary" @click="batchSettingCon">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        title="批量客户类型"
        :visible.sync="batchSettingType"
        width="30%"
        @close="closeSettingType"
      >
        <el-form :model="batchSetting" :inline="true" label-width="100px">
          <el-form-item label="客户类型：">
            <el-select v-model="batchSetting.type" placeholder="请选择业务员">
              <el-option v-for="item in customerType" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeSettingType">取 消</el-button>
          <el-button type="primary" @click="setCustomerType">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <div v-if="$accessCheck($Access.CustomerListSearch)" slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-input
            v-model="search_form.keyword"
            placeholder="客户名称/联系人电话"
            style="width: 220px"
            clearable
            @keyup.enter.native="getUserList()"
            @clear="getUserList()"
          >
            <el-button slot="append" icon="el-icon-search" @click="getUserList()"></el-button>
          </el-input>
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <el-input-->
        <!--            v-model="search_form.phone"-->
        <!--            placeholder="请输入客户手机号"-->
        <!--            style="width: 220px"-->
        <!--            clearable-->
        <!--            @keyup.enter.native="getUserList()"-->
        <!--            @clear="getUserList()"-->
        <!--          >-->
        <!--            <el-button-->
        <!--              slot="append"-->
        <!--              icon="el-icon-search"-->
        <!--              @click="getUserList()"-->
        <!--            ></el-button>-->
        <!--          </el-input>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <el-select
            v-model="search_form.salesManId"
            clearable
            placeholder="选择业务员"
            style="width: 150px"
            @change="pageChange(1)"
          >
            <el-option
              v-for="(item, index) in options"
              :key="index"
              :label="item.staffName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="false">
          <el-select v-model="search_form.type" placeholder="客户类型">
            <el-option v-for="item in UserType" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <RegionSelect
            v-model="region"
            size="small"
            clearable
            check-strictly
            placeholder="所属区域"
            style="width: 150px"
            @change="regionChange"
          />
        </el-form-item>
        <el-form-item v-if="systemType === 1">
          <SelectShop
            v-model="search_form.shopId"
            :clearable="true"
            placeholder="所属商铺"
            width="150"
            @clear="shopClear"
            @change="selShop"
          />
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="search_form.enableStatus"
            style="width: 150px"
            placeholder="客户状态"
            @change="customerChange"
          >
            <el-option
              v-for="item in customerStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <!--        <el-form-item v-if="systemType === 1">-->
        <!--          <SelMembership-->
        <!--            v-model="search_form.membershipId"-->
        <!--            :clearable="true"-->
        <!--            placeholder="选择会员卡"-->
        <!--            width="150"-->
        <!--            @clear="membershipClear"-->
        <!--            @change="selMembershipId"-->
        <!--          />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item>-->
        <!--          <DepartmentSel-->
        <!--            v-model="department"-->
        <!--            clearable-->
        <!--            placeholder="所属部门"-->
        <!--            width="150"-->
        <!--            @change="selBranch"-->
        <!--          />-->
        <!--        </el-form-item>-->
        <!--<el-form-item>
          <el-select
            v-model="search_form.source"
            clearable
            placeholder="注册来源"
            style="width: 150px"
            @change="pageChange(1)"
          >
            <el-option
              v-for="(item, index) in source_list"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>-->
        <!--<el-form-item>
          <el-input
            v-model="search_form.tag"
            clearable
            placeholder="客户标签"
            style="width: 155px"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="pageChange(1)"
            ></el-button>
          </el-input>
        </el-form-item>-->
      </el-form>
    </div>
    <el-table ref="customerTable" :data="tableData" @selection-change="handleSelectionChange">
      <el-table-column align="center" type="selection" width="55"></el-table-column>
      <el-table-column prop="id" label="ID" fixed="left" width="80"></el-table-column>
      <el-table-column prop="name" label="客户" fixed="left" min-width="230">
        <template slot-scope="scope">
          <div class="clearfix">
            <div class="float_left customer-img-view">
              <img v-if="scope.row.avatar" class="customer-img" :src="scope.row.avatar" />
            </div>
            <div class="float_left customer-name-view">
              <p class="customer-name">
                {{ scope.row.name }}
              </p>
              <p>{{ scope.row.code }}</p>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="客户姓名" min-width="100" prop="contact">
        <template slot-scope="scope">
          {{ scope.row.contact[0].name || "无" }}
        </template>
      </el-table-column>
      <el-table-column label="账号/电话" min-width="180" prop="contact">
        <template slot-scope="scope">
          {{ scope.row.mobile }}
        </template>
      </el-table-column>
      <el-table-column prop="area" label="区域" min-width="180">
        <template slot-scope="scope">
          <span>{{
            `${scope.row.area.provinceName} / ${scope.row.area.cityName} / ${scope.row.area.districtName}`
          }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="typeFlag" prop="customerType" label="客户类型" min-width="100"></el-table-column>
      <el-table-column v-if="systemType === 1" prop="memberBalance" label="余额" min-width="180">
        <!--        <template slot-scope="scope">-->
        <!--          <div-->
        <!--            v-if="-->
        <!--              scope.row.balance_false_visible &&-->
        <!--              $accessCheck($Access.CustomerListupdateBalance)-->
        <!--            "-->
        <!--          >-->
        <!--            &lt;!&ndash;            <el-select&ndash;&gt;-->
        <!--            &lt;!&ndash;              v-model="balance_type"&ndash;&gt;-->
        <!--            &lt;!&ndash;              style="width: 60px"&ndash;&gt;-->
        <!--            &lt;!&ndash;              placeholder="请选择"&ndash;&gt;-->
        <!--            &lt;!&ndash;              size="mini"&ndash;&gt;-->
        <!--            &lt;!&ndash;            >&ndash;&gt;-->
        <!--            &lt;!&ndash;              <el-option&ndash;&gt;-->
        <!--            &lt;!&ndash;                v-for="item in type_options"&ndash;&gt;-->
        <!--            &lt;!&ndash;                :key="item.type"&ndash;&gt;-->
        <!--            &lt;!&ndash;                :label="item.label"&ndash;&gt;-->
        <!--            &lt;!&ndash;                :value="item.type"&ndash;&gt;-->
        <!--            &lt;!&ndash;              ></el-option>&ndash;&gt;-->
        <!--            &lt;!&ndash;            </el-select>&ndash;&gt;-->
        <!--            <el-input-number-->
        <!--              v-model="member_balance"-->
        <!--              style="width: calc(100% - 88px)"-->
        <!--              :controls="false"-->
        <!--              size="mini"-->
        <!--              @keyup.enter.native="rechargeMemberBalance(scope.$index)"-->
        <!--            ></el-input-number>-->
        <!--            <div-->
        <!--              class="virtualSalesNum-btn"-->
        <!--              @click="rechargeMemberBalance(scope.$index)"-->
        <!--            >-->
        <!--              <i class="el-icon-check"></i>-->
        <!--            </div>-->
        <!--          </div>-->

        <!--          <div v-else @click="showSetBalance(scope.$index)">-->
        <!--            {{ scope.row.memberBalance }}-->
        <!--            <el-button-->
        <!--              v-if="$accessCheck($Access.CustomerListupdateBalance)"-->
        <!--              type="text"-->
        <!--              icon="el-icon-edit"-->
        <!--            ></el-button>-->
        <!--          </div>-->
        <!--        </template>-->
      </el-table-column>
      <el-table-column v-if="systemType === 1" prop="integral" label="积分" min-width="180">
        <template slot-scope="scope">
          <div v-if="scope.row.integral_false_visible && $accessCheck($Access.CustomerListupdateIntegral)">
            <el-select v-model="integral_type" style="width: 60px" placeholder="请选择" size="mini">
              <el-option
                v-for="item in type_options"
                :key="item.type"
                :label="item.label"
                :value="item.type"
              ></el-option>
            </el-select>
            <el-input-number
              v-model="integral_num"
              style="width: calc(100% - 88px)"
              :controls="false"
              size="mini"
              @keyup.enter.native="updateCustomerIntegral(scope.$index)"
            ></el-input-number>
            <div class="virtualSalesNum-btn" @click="updateCustomerIntegral(scope.$index)">
              <i class="el-icon-check"></i>
            </div>
          </div>

          <div v-else @click="showSetIntegral(scope.$index)">
            {{ scope.row.integral }}
            <el-button
              v-if="$accessCheck($Access.CustomerListupdateIntegral)"
              type="text"
              icon="el-icon-edit"
            ></el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="shopFlag && systemType === 1"
        prop="shopName"
        label="商铺"
        min-width="120"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          {{ scope.row.shopName || "平台" }}
        </template>
      </el-table-column>
      <el-table-column v-if="salesmanFlag" prop="salesManName" label="业务员" min-width="140">
        <template slot-scope="scope">
          <div>
            <p>
              <span class="table-label">业务员：</span>
              <span class="table-val">
                {{ scope.row.salesManName || "--" }}
              </span>
            </p>
            <p>
              <span class="table-label">部门：</span>
              <span class="table-val">
                {{ scope.row.departmentName || "--" }}
              </span>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="auditStatusFlag && systemType === 1" prop="status" label="审核状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 2" class="success-status"> 已审核 </span>
          <span v-if="scope.row.status === 1" class="warning-status"> 待审核 </span>
          <span v-if="scope.row.status === 0" class="info-status"> 待完善资料 </span>
          <span v-if="scope.row.status === 3" class="success-status"> 已驳回 </span>
        </template>
      </el-table-column>
      <el-table-column v-if="timeFlag" prop="createTime" label="注册时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="accountStatusFlag && systemType === 1"
        prop="enableStatus"
        label="账号状态"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.CustomerListUpdateCustomerStatus)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="updateEnableStatus($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="systemType === 1" label="操作" header- fixed="right" width="170">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{ scope.$index }}</span>
          <span class="operation">操作</span>
          <el-popover popper-class="custom-table-checkbox" trigger="click">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                @change="change"
              ></el-checkbox>
            </el-checkbox-group>
            <el-button slot="reference" icon="el-icon-setting" type="text"></el-button>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.CustomerListGetCustomerInfo)"
            type="text"
            @click="$router.push(`/Customer/CustomerAdmin/CustomerDetail/${scope.row.id}`)"
          >
            查看
          </el-button>
          <el-button
            v-if="$accessCheck($Access.CustomerListEditCustomer)"
            type="text"
            :disabled="scope.row.shopId !== storeData.id && systemType !== 1"
            @click="editData(scope.row)"
          >
            编辑
          </el-button>
          <el-dropdown>
            <span class="el-dropdown-link">
              更多
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="$accessCheck($Access.CustomerListvisitedLogs)">
                <div class="dropdown-div" @click="openFollowUpRecords(scope.row.id)">跟进记录</div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$accessCheck($Access.CustomerListIntegralDetail)">
                <div
                  class="dropdown-div"
                  @click="$router.push(`/Customer/CustomerAdmin/IntegralDetail/${scope.row.id}`)"
                >
                  积分明细
                </div>
              </el-dropdown-item>
              <el-dropdown-item v-if="$accessCheck($Access.CustomerListSaldoDetail)">
                <div
                  class="dropdown-div"
                  @click="
                    $router.push(
                      '/Customer/CustomerAdmin/SaldoDetail?id=' +
                        scope.row.id +
                        '&name=' +
                        scope.row.name +
                        '&money=' +
                        scope.row.memberBalance
                    )
                  "
                >
                  余额明细
                </div>
              </el-dropdown-item>
              <el-dropdown-item>
                <div class="dropdown-div" @click="delCustomer(scope.row.userCenterId)">删除</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column v-else label="操作" header- fixed="right" width="100">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.CustomerListGetCustomerInfo)"
            type="text"
            @click="$router.push(`/Customer/CustomerAdmin/CustomerDetail/${scope.row.id}`)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div slot="btn-div" class="foot-btn-div">
        <span class="batch-checkbox">
          <el-checkbox v-model="checkedAll" @change="checkAllChange"></el-checkbox>
        </span>
        <el-button v-if="$accessCheck($Access.CustomerListExport)" size="mini" @click="clickBatchSetting">
          批量设置业务员
        </el-button>
        <el-button size="mini" @click="clickSettingType"> 批量设置客户类型 </el-button>
        <el-button size="mini" @click="batchDelCustomer"> 批量删除客户 </el-button>
      </div>
    </FooterPage>
    <el-drawer class="edit-drawer" size="50%" :title="drawer_tit" :visible.sync="drawer">
      <EditCustomer v-if="drawer" :customer-id="customer_id" @subSuccess="subSuccess" @goShop="drawer = false" />
    </el-drawer>
    <!--    跟进记录弹出框-->
    <el-dialog
      title="跟进记录"
      :visible.sync="followUpRecordsFlag"
      width="40%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div style="margin-bottom: 10px">
        <el-input
          v-model="search_form.content"
          type="textarea"
          :rows="3"
          placeholder="请填写跟进内容(最多500字)"
          style="width: 250px"
          size="mini"
        ></el-input>
        <span style="margin: 0 20px 0 20px">
          跟进人
          <el-input
            v-model="search_form.staff"
            clearable
            placeholder="请选择业务员"
            style="width: 130px"
            size="mini"
            @clear="staffClear"
            @blur="search_form.staff = ''"
          >
            <i slot="suffix" class="el-input__icon el-icon-search" @click="saleFn(true)"></i>
          </el-input>
        </span>
        <span>
          时间
          <el-date-picker
            v-model="search_form.time"
            type="datetime"
            placeholder="选择日期时间"
            size="mini"
            style="width: 130px; margin-bottom: 18px"
          ></el-date-picker>
        </span>
        <span style="margin-left: 20px">
          <el-button type="primary" size="mini" @click="addCustomerCommunication"> 添加 </el-button>
        </span>
      </div>
      <el-divider></el-divider>
      <el-table :data="followList" style="width: 100%">
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="content" label="内容"></el-table-column>
        <el-table-column prop="staffName" label="跟进人"></el-table-column>
        <el-table-column prop="time" label="时间">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="updateFollow(scope.row)"> 修改 </el-button>
            <el-button type="text" @click="delCustomerCommunication(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="clearfix">
        <FooterPage
          :page-size="followPageSize"
          :total-page.sync="followTotal"
          :current-page.sync="followPage"
          layout="prev, pager, next"
          @pageChange="handleCurrentChange"
        ></FooterPage>
      </div>
      <el-dialog
        width="20%"
        title="修改"
        :visible.sync="coreFlag"
        append-to-body
        center
        :close-on-press-escape="false"
        :close-on-click-modal="false"
      >
        <el-input v-model="updateFollowList" type="textarea" :rows="7"></el-input>
        <div class="clearfix" style="margin-top: 10px">
          <el-button size="mini" class="float_right" @click="coreFlag = false"> 取消 </el-button>
          <el-button type="primary" size="mini" class="float_right" style="margin-right: 10px" @click="putUpdateFollow">
            确定
          </el-button>
        </div>
      </el-dialog>
    </el-dialog>
    <staffListModal
      v-if="staff_show"
      :is-show="staff_show"
      :is-check="false"
      :isserch="isserch"
      @cancel="staff_show = false"
      @confirm="staffSel"
    />
  </ContainerQuery>
</template>

<script>
import staffListModal from "@/component/common/staffListModal";
import DepartmentSel from "@/component/common/DepartmentSel";
import RegionSelect from "@/component/common/RegionSelectJSON";
import EditCustomer from "./AddCustomer";
import SelectShop from "@/component/goods/SelectShop.vue";
// import SelMembership from "@/component/goods/SelMembership.vue";
import { getAllStaff } from "@/api/Department";
import {
  batchEditCustomer,
  updateCustomerStatus,
  updateCustomerCheckStatus,
  getAllCustomer,
  customerExport,
  // searchCustomer,
  delCustomer,
  getAllCustomerCommunication,
  addCustomerCommunication,
  delCustomerCommunication,
  updateCustomerCommunication,
  updateCustomerIntegral,
  rechargeMemberBalance,
  setCustomerType,
} from "@/api/Customer";
import { getCustomerSourceList } from "@/api/System";
import { mapGetters } from "vuex";
export default {
  name: "CustomerList",
  components: {
    DepartmentSel,
    RegionSelect,
    EditCustomer,
    SelectShop,
    staffListModal,
    // SelMembership,
  },
  data() {
    return {
      type_options: [
        {
          type: 5,
          label: "加",
        },
        {
          type: 4,
          label: "减",
        },
      ],
      balance_type: 5, // 积分
      integral_type: 5, // 余额
      integral_num: 0, // 积分
      member_balance: 0, //余额
      followList: [],
      order_id: "",
      staff_show: false,
      checkedAll: false,
      customer_id: 0,
      drawer_tit: "",
      drawer: false,
      contact: [],
      department: [],
      region: [],
      search_form: {
        phone: "",
        page: this.page,
        pageSize: this.pageSize,
        keyword: "",
        type: "",
        provinceCode: "",
        cityCode: "",
        districtCode: "",
        enableStatus: "",
        departmentId: "",
        shopId: "", // 商铺Id
        // membershipId: "", //会员卡id
        time: "",
        content: "",
        salesManId: "",
        source: "",
        tag: "",
      },
      UserType: [
        {
          value: "1",
          label: "客户类型",
        },
      ],
      customerLabel: [
        {
          value: "选项1",
          label: "客户标签",
        },
      ],
      customerStatus: [
        {
          value: "5",
          label: "已启用",
        },
        {
          value: "4",
          label: "已停用",
        },
      ],
      value: "",
      formLabelWidth: "100px",
      checked: false,
      form: {
        key: "",
        type: "",
        classify: [],
        brand: "",
      },
      total: 0,
      page: 1,
      pageSize: 10,
      roleId: "",
      search_key: "",
      departmentPidPath: [],
      tableData: [],
      total_count: 0,
      batchSettingVisible: false, // 批量设置业务员显示控制
      batchSettingType: false, // 批量设置客户类型显示控制
      batchSetting: {
        region: "",
        type: "",
      },
      batchSettingWidth: "80px", // 批量设置业务员中"部门："的长度
      options: [], // 业务员列表数组
      ids: [], // 客户id数组
      userCenterIds: [], // 客户id数组
      departmentId: "", // 部门id
      saleman: "", // 业务员id
      selection: [], // 客户列表选中数组
      checkList: ["客户类型", "商铺", "业务员", "审核状态", "注册时间", "帐号状态"],
      columns: [
        {
          label: "客户类型",
        },
        {
          label: "商铺",
        },
        {
          label: "业务员",
        },
        {
          label: "审核状态",
        },
        {
          label: "注册时间",
        },
        {
          label: "帐号状态",
        },
      ],
      typeFlag: true,
      shopFlag: true,
      salesmanFlag: true,
      auditStatusFlag: true,
      timeFlag: true,
      accountStatusFlag: true,
      followUpRecordsFlag: false,
      coreFlag: false,
      isserch: true,
      searchDate: {},
      customerId: "",
      followPage: 1,
      followPageSize: 10,
      followTotal: 0,
      updateFollowValue: {},
      updateFollowList: "",
      source_list: [
        {
          label: "ios",
          tag: "ios",
          value: 1,
        },
        {
          label: "安卓APP",
          tag: "android",
          value: 2,
        },
        {
          label: "小程序",
          tag: "miniProgram",
          value: 3,
        },
        {
          label: "后台创建",
          tag: "manage",
          value: 4,
        },
        {
          label: "H5页面",
          tag: "H5",
          value: 5,
        },
        {
          label: "Pc页面",
          tag: "Pc",
          value: 6,
        },
      ],
      customerType: [],
    };
  },
  computed: {
    ...mapGetters({
      storeData: "MUser/storeData",
    }),
  },
  async mounted() {
    if ([2, 3].includes(this.systemType)) {
      // this.search_form.shopId = this.storeData.id;
    }
    // 到期提示
    await this.enterExpireTime();
    await this.getUserList();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getUserList();
  },
  created() {
    this.getAllStaff();
    this.getCustomerSourceList();
  },
  methods: {
    showSetIntegral(index) {
      this.tableData.forEach((item) => (item.integral_false_visible = false));
      this.integral_num = this.tableData[index].integral;
      this.tableData[index].integral_false_visible = true;
    },
    showSetBalance(index) {
      this.tableData.forEach((item) => (item.balance_false_visible = false));
      this.member_balance = this.tableData[index].memberBalance;
      this.tableData[index].balance_false_visible = true;
    },
    // 修改积分
    async updateCustomerIntegral(index) {
      if (this.integral_num <= 0) {
        this.$message.warning("请输入大于0的值");
        return;
      }
      const data = await updateCustomerIntegral({
        customerId: this.tableData[index].id,
        integral: this.integral_num,
        type: this.integral_type,
        desc: "系统充值",
      });
      this.$message.success("修改成功");
      this.tableData[index].integral_false_visible = false;
      this.getUserList();
    },
    // 修改余额
    async rechargeMemberBalance(index) {
      if (this.member_balance <= 0) {
        this.$message.warning("请输入大于0的值");
        return;
      }
      const data = await rechargeMemberBalance({
        customerId: this.tableData[index].id,
        money: this.member_balance,
        type: this.balance_type,
        purpose: "系统充值",
      });
      this.$message.success("修改成功");
      this.tableData[index].balance_false_visible = false;
      this.getUserList();
    },
    // 批量设置业务员确定
    departmentChange(val) {
      if (val.length) {
        this.departmentId = val;
        this.getAllStaff(val[val.length - 1]);
      }
    },
    //  获取列表
    async getAllStaff(departmentId) {
      const data = await getAllStaff({
        page: 1,
        pageSize: 50,
        departmentId: departmentId,
      });

      this.options = data.data;
    },
    // 列表双击事件
    // goDetail(row) {
    //   if (!this.$accessCheck(this.$Access.CustomerListGetCustomerInfo)) {
    //     return;
    //   }
    //   this.$router.push(`/Customer/CustomerAdmin/CustomerDetail/${row.id}`);
    // },
    // 导出时事件
    async exportData() {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.search_form.keyword,
        type: this.search_form.type,
        provinceCode: this.search_form.provinceCode,
        cityCode: this.search_form.cityCode,
        districtCode: this.search_form.districtCode,
        status: 2,
        enableStatus: this.search_form.enableStatus, // 禁用启用
        departmentId: this.search_form.departmentId,
        shopId: this.search_form.shopId,
        salesManId: "",
        start: "",
        end: "",
      };
      const target = await customerExport(params);
    },
    // 没有用到事件
    async updateAuditStatus(row) {
      this.$confirm("确定要审核此用户吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await updateCustomerCheckStatus({
          id: row.id,
          status: 2,
        });

        this.getUserList();
        this.$message({
          type: "success",
          message: "审核成功",
        });
      });
    },
    // 查看组件子传父
    subSuccess() {
      this.drawer = false;
      this.pageChange(1);
    },
    // 查看事件
    editData(row) {
      this.customer_id = row.id;
      this.drawer_tit = row.name;
      this.drawer = true;
    },
    // 选择商铺
    selShop(val, row) {
      this.pageChange(1);
    },
    //选择会员卡
    // selMembershipId() {
    //   this.pageChange(1);
    // },
    shopClear() {
      this.search_form.shopId = "";
      this.pageChange(1);
    },
    // membershipClear() {
    //   this.search_form.membershipId = "";
    //   this.pageChange(1);
    // },
    // 选择部门
    selBranch(val) {
      this.search_form.departmentId = val[val.length - 1];
      this.pageChange(1);
    },
    // 选择区域
    regionChange(val) {
      this.search_form.provinceCode = val[0];
      this.search_form.cityCode = val[1];
      this.search_form.districtCode = val[2];
      this.pageChange(1);
    },
    //  客户状态
    customerChange(val) {
      this.search_form.enableStatus = val;
      this.pageChange(1);
    },
    // 获取客户列表  getAllCustomer
    async getAllCustomer() {
      const data = await getAllCustomer({
        page: this.page,
        pageSize: this.pageSize,
        status: 2,
        shopId: this.search_form.shopId,
        keyword: this.search_form.keyword,
        enableStatus: this.search_form.enableStatus, // 禁用启用
        salesManId: this.search_form.salesManId,
        source: this.search_form.source,
        tag: this.search_form.tag,
        provinceCode: this.search_form.provinceCode,
        cityCode: this.search_form.cityCode,
        districtCode: this.search_form.districtCode,
      });

      this.tableData = data.data.map((item) => {
        return {
          ...item,
          integral_false_visible: false,
          balance_false_visible: false,
        };
      });
      this.contact = data.data.contact;
      this.total = data.pageTotal;
    },
    //  判断
    getUserList() {
      this.getAllCustomer();
    },
    //  删除接口
    // delCustomer(id) {
    //   this.$confirm("你确定删除该用户", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   }).then(async () => {
    //     const data = await delCustomer(id);
    //
    //     this.getUserList();
    //     this.$message({
    //       type: "success",
    //       message: "删除成功!",
    //     });
    //   });
    // },
    // 禁用启用
    async updateEnableStatus(val, row) {
      try {
        const data = await updateCustomerStatus({
          id: row.id,
          enableStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        this.getUserList();
      }
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getUserList();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 点击-批量设置业务员
    clickBatchSetting() {
      if (this.selection.length > 0) {
        this.batchSettingVisible = true;
      } else {
        this.$message({
          type: "warning",
          message: "请选择要操作的客户",
        });
      }
    },
    //批量设置客户类型
    clickSettingType() {
      if (this.selection.length > 0) {
        this.batchSettingType = true;
      } else {
        this.$message({
          type: "warning",
          message: "请选择要操作的客户",
        });
      }
    },
    // 批量删除客户
    batchDelCustomer() {
      if (this.selection.length > 0) {
        this.$confirm("确定要删除这些客户吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          this.userCenterIds = this.selection.map((item) => item.userCenterId);
          const { data } = await delCustomer(this.userCenterIds);
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.pageChange(1);
        });
      } else {
        this.$message({
          type: "warning",
          message: "请选择要操作的客户",
        });
      }
    },
    // 删除客户
    delCustomer(id) {
      this.$confirm("确定要删除该客户吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const { data } = await delCustomer([id]);
        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.pageChange(1);
      });
    },
    //批量设置客户类型关闭
    closeSettingType() {
      this.batchSetting.type = "";
      this.batchSettingType = false;
    },
    // 请求-批量设置业务员
    async batchEditCustomer() {
      this.ids = this.selection.map((item) => item.id);
      const params = {
        ids: this.ids,
        departmentId: this.departmentId[0],
        salesManId: this.saleman,
      };
      const data = await batchEditCustomer(params);
      this.getUserList();
    },
    // 确认-批量修改业务员
    batchSettingCon() {
      if (this.departmentId === "" || this.saleman === "") {
        this.$message.error("请填写完整信息");
      } else {
        this.batchSettingVisible = false;
        this.batchEditCustomer();
      }
    },
    // 确认-批量修改客户类型
    async setCustomerType() {
      this.ids = this.selection.map((item) => item.id);
      const params = {
        customerIds: this.ids,
        type: this.batchSetting.type,
      };
      const { data } = await setCustomerType(params);
      this.$message.success("修改成功");
      await this.closeSettingType();
      this.getUserList();
    },
    handleSelectionChange(val) {
      this.checkedAll = val.length === this.tableData.length;
      this.selection = val;
    },
    checkAllChange() {
      this.$refs.customerTable.toggleAllSelection();
    },
    change() {
      this.typeFlag = this.checkList.some((item) => item === "客户类型");
      this.shopFlag = this.checkList.some((item) => item === "商铺");
      this.salesmanFlag = this.checkList.some((item) => item === "业务员");
      this.auditStatusFlag = this.checkList.some((item) => item === "审核状态");
      this.timeFlag = this.checkList.some((item) => item === "注册时间");
      this.accountStatusFlag = this.checkList.some((item) => item === "帐号状态");
    },
    // 跟进记录第一次渲染列表
    openFollowUpRecords(id) {
      this.followPage = 1;
      this.followUpRecordsFlag = true;
      this.customerId = id;
      this.followUpRecords();
    },
    // 跟进记录列表
    async followUpRecords() {
      const { data, pageTotal } = await getAllCustomerCommunication({
        // staffId: "",
        customerId: this.customerId,
        // star: "",
        // end: "",
        page: this.followPage,
        pageSize: this.followPageSize,
      });
      this.followList = data;
      this.followTotal = pageTotal;
    },
    // 清除员工输入框
    staffClear() {
      this.searchDate.salesManId = "";
      this.search_form.staff = "";
      this.pageChange(1);
    },
    // 选择员工
    staffSel(val) {
      const row = val[0];
      this.searchDate.salesManId = row.id;
      this.search_form.staff = row.staffName;
      this.handleCurrentChange(1);
    },
    async saleFn(isserch, id) {
      this.staff_show = true;
      this.isserch = isserch;
      if (id) {
        this.order_id = id;
      }
    },
    // 跟进记录列表切页
    async handleCurrentChange(val) {
      this.followPage = val;
      await this.followUpRecords();
    },
    // 跟进记录列表添加
    async addCustomerCommunication() {
      if (!this.search_form.content.trim()) {
        this.$message.warning("跟进内容不能为空");
        return;
      }
      const time = new Date(this.search_form.time).getTime() / 1000;
      if (this.searchDate.salesManId && time && this.search_form.content) {
        const data = await addCustomerCommunication({
          customerId: this.customerId,
          staffId: this.searchDate.salesManId,
          content: this.search_form.content,
          time: time,
        });
        await this.followUpRecords();
      } else {
        this.$message.warning("必须填写内容跟进人和时间");
      }
    },
    // 跟进记录删除
    delCustomerCommunication(id) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const { data } = await delCustomerCommunication(id);
          await this.followUpRecords();
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 跟进记录修改获取
    updateFollow(value) {
      this.coreFlag = true;
      this.updateFollowValue = value;
      this.updateFollowList = value.content;
    },
    // 跟进记录修改确认
    async putUpdateFollow() {
      this.coreFlag = false;
      const { data } = await updateCustomerCommunication(this.updateFollowValue.id, {
        customerId: this.updateFollowValue.customerId,
        staffId: this.updateFollowValue.staffId,
        content: this.updateFollowList,
        time: this.updateFollowValue.time,
      });
      this.$message.success("修改成功");
      await this.followUpRecords();
    },
    async getCustomerSourceList() {
      const data = await getCustomerSourceList();
      if (data.data.length) {
        this.customerType = data.data;
      } else {
        this.customerType = [];
      }
    },
  },
};
</script>
<style scoped lang="scss">
.customer-img-view {
  width: 50px;
  height: 50px;
  background-color: #f4f4f4;
  margin-right: 10px;
  border-radius: 4px;
}
.customer-img {
  width: 50px;
  height: 50px;
  border-radius: 4px;
}
.customer-name-view {
  width: calc(100% - 86px);
}
.customer-name {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.custom-table-checkbox {
  .el-checkbox {
    display: block !important;
    margin: 0 0 $base-padding/4 0;
  }
}
.virtualSalesNum-btn {
  width: 26px;
  text-align: center;
  line-height: 28px;
  height: 28px;
  vertical-align: middle;
  font-size: 12px;
  background: #f5f5f5;
  border: 1px solid #cad0d7;
  display: inline-block;
  cursor: pointer;
  border-left: 0;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
</style>
