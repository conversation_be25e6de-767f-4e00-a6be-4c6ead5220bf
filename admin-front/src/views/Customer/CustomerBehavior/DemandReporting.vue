<template>
  <ContainerQuery>
    <!--    <div slot="left">-->
    <!--      <el-button size="small" type="primary" plain>导出</el-button>-->
    <!--    </div>-->
    <div slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <el-date-picker
            v-model="searchDate.time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="id" label="ID" min-width="100"></el-table-column>
      <el-table-column prop="name" label="客户" min-width="180"></el-table-column>
      <el-table-column prop="demand" label="需求" min-width="180"></el-table-column>
      <el-table-column prop="address" label="时间" min-width="180">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作" min-width="180">
        <template slot-scope="scope">
          <el-button type="text" @click="getInfoDemand(scope.row.id)"> 查看 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <el-dialog title="需求查看" :visible.sync="demand_istrue" width="30%">
      <el-form size="small" label-width="80px">
        <el-form-item label="客户">
          <el-input v-model="demand_form.name" style="width: 90%"></el-input>
        </el-form-item>
        <el-form-item label="需求">
          <el-input v-model="demand_form.demand" type="textarea" :rows="2" style="width: 90%"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="demand_istrue = false">取 消</el-button>
        <el-button type="primary" @click="demand_istrue = false"> 确 定 </el-button>
      </span>
    </el-dialog>
  </ContainerQuery>
</template>

<script>
import { getAllCustomerdemand, getCustomerdemandInfo } from "@/api/Customer";
export default {
  name: "DemandReporting",
  data() {
    return {
      searchDate: {
        time: [],
        start: "",
        end: "",
      },
      tableData: [{}],
      total: 0,
      page: 1,
      pageSize: 10,
      demand_istrue: false,
      demand_form: {},
    };
  },
  created() {
    this.getAllCustomerdemand();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllCustomerdemand();
  },
  methods: {
    timeChange(val) {
      if (val && val.length) {
        this.searchDate.start = parseInt(val[0] / 1000);
        this.searchDate.end = parseInt(val[1] / 1000) + 86399;
      } else {
        this.searchDate.start = "";
        this.searchDate.end = "";
      }
      this.pageChange(1);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getAllCustomerdemand();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    async getInfoDemand(id) {
      this.demand_istrue = true;
      const data = await getCustomerdemandInfo(id);
      this.demand_form = data.data;
    },
    async getAllCustomerdemand() {
      const data = await getAllCustomerdemand({
        page: this.page,
        pageSize: this.pageSize,
        beginTime: this.searchDate.start,
        endTime: this.searchDate.end,
      });
      this.tableData = data.data;
      this.total = data.pageTotal;
    },
  },
};
</script>

<style scoped></style>
