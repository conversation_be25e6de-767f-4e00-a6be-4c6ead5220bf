<template>
  <ContainerQuery>
    <!--    <div slot="left">-->
    <!--      <el-button size="small" type="primary">导出</el-button>-->
    <!--    </div>-->
    <div slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <SelectCustomer v-model="customer_name" @clear="customerClear" @change="customerSel" />
        </el-form-item>
        <el-form-item>
          <GoodsCategory v-model="cate_id" :width="160" check-strictly clearable size="small" @change="goodsChane" />
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="time"
            clearable
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            start-placeholder="浏览开始日期"
            end-placeholder="浏览结束日期"
            @change="timeChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="goodsName" label="商品名称" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="customerName" label="客户名称"></el-table-column>
      <el-table-column prop="mobile" label="客户电话"></el-table-column>
      <el-table-column prop="createTime" label="浏览时间">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>
<script>
import { getAllCustomerVisitsLog } from "@/api/Customer";
import GoodsCategory from "@/component/common/GoodsCategory.vue";
import SelectCustomer from "@/component/common/SelectCustomer.vue";
export default {
  components: { GoodsCategory, SelectCustomer },
  data() {
    return {
      customer_name: "",
      page: 1,
      pageSize: 10,
      cate_id: [],
      time: [],
      total: 0,
      tableData: [],
      search_form: {
        startTime: "",
        endTime: "",
        categoryId: "",
        customerId: "",
      },
    };
  },
  created() {
    this.getAllCustomerVisitsLog();
  },
  methods: {
    async getAllCustomerVisitsLog() {
      const data = await getAllCustomerVisitsLog({
        ...this.search_form,
        page: this.page,
        pageSize: this.pageSize,
      });
      this.tableData = data.data;
      this.total = data.pageTotal;
    },
    pageChange(val) {
      this.page = val;
      this.getAllCustomerVisitsLog();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.getAllCustomerVisitsLog();
    },
    goodsChane(val) {
      if (val.length) {
        this.search_form.categoryId = val[val.length - 1];
      } else {
        this.search_form.categoryId = "";
      }
      this.pageChange(1);
    },
    //  订单时间
    timeChange(val) {
      if (val && val.length) {
        this.search_form.startTime = val[0] / 1000;
        this.search_form.endTime = val[1] / 1000 + 86399;
      } else {
        this.search_form.startTime = "";
        this.search_form.endTime = "";
      }
      this.pageChange(1);
    },
    // 选择客户
    customerSel(val, list) {
      this.search_form.customerId = list[0].id;
      this.pageChange(1);
    },
    customerClear() {
      this.search_form.customerId = "";
      this.customer_name = "";
      this.pageChange(1);
    },
  },
};
</script>
<style></style>
