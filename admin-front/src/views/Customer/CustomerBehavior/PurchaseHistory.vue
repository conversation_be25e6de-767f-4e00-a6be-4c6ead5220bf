<template>
  <ContainerQuery>
    <div slot="left">
      <el-button size="small" type="primary" @click="searchCustomerBuyLog(1)"> 导出 </el-button>
    </div>
    <div slot="more">
      <el-form :inline="true" size="small">
        <el-form-item>
          <SelectCustomer v-model="customer_name" @clear="customerClear" @change="customerSel" />
        </el-form-item>
        <el-form-item>
          <el-input v-model="keyword" placeholder="商品名称" clearable style="width: 220px" @clear="pageChange(1)">
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <GoodsCategory v-model="categoryId" :width="160" check-strictly clearable size="small" @change="goodsChane" />
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="time"
            type="daterange"
            :unlink-panels="true"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="orderDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="customerName" label="客户"></el-table-column>
      <el-table-column prop="goodsName" label="商品"></el-table-column>
      <el-table-column prop="unitName" label="单位"></el-table-column>
      <el-table-column prop="specGroup" label="属性">
        <template slot-scope="scope">
          <div v-if="scope.row.specGroup !== []">
            <span v-for="(item, index) in scope.row.specGroup" :key="index">
              <span>{{ item.specName }}</span>
              ,
              <span>{{ item.specValueName }}</span>
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="price" label="单价">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.price) }}
        </template>
      </el-table-column>
      <el-table-column prop="buyNum" label="购买数量">
        <template slot-scope="scope">
          <span>
            {{ $_common.formatNub(scope.row.buyNum) }}
            <span v-if="scope.row.isEq === 5"> ({{ $_common.formatNub(scope.row.otherNum) }}) </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="totalMoney" label="商品总价">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="outNum" label="发货数量">
        <template slot-scope="scope">
          <span>{{ $_common.formatNub(scope.row.outNum) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="goodsCode" label="商品条码"></el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </ContainerQuery>
</template>
<script>
import { searchCustomerBuyLog, exportsearchCustomerBuyLog } from "@/api/Customer";
import GoodsCategory from "@/component/common/GoodsCategory.vue";
import SelectCustomer from "@/component/common/SelectCustomer.vue";
import { exportgetInventoryByWarehouseId, getInventoryByWarehouseId } from "@/api/Stock";
export default {
  components: { GoodsCategory, SelectCustomer },
  data() {
    return {
      page: 1,
      pageSize: 10,
      tableData: [],
      total: 0,
      keyword: "",
      time: "",
      options: [],
      categoryId: [],
      userCenterId: 0,
      customer_name: "",
      endTime: "",
      startTime: "",
    };
  },
  created() {
    this.searchCustomerBuyLog();
  },
  activated() {
    this.searchCustomerBuyLog();
  },
  methods: {
    async searchCustomerBuyLog(exports) {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        userCenterId: this.userCenterId,
        keyword: this.keyword,
        categoryId: this.categoryId.join(","),
        startTime: this.startTime,
        endTime: this.endTime,
      };
      if (exports) {
        params.export = 1;
        const target = await exportsearchCustomerBuyLog({
          ...params,
        });
      } else {
        const res = await searchCustomerBuyLog({
          ...params,
        });
        this.tableData = res.data;
        this.total = res.pageTotal;
      }
      // const data = await searchCustomerBuyLog({
      //   ...params,
      // });
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.searchCustomerBuyLog();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.searchCustomerBuyLog();
    },
    goodsChane() {
      this.pageChange(1);
    },
    // 选择客户
    customerSel(val, list) {
      this.userCenterId = list[0].userCenterId;
      this.pageChange(1);
    },
    customerClear() {
      this.userCenterId = "";
      this.customer_name = "";
      this.pageChange(1);
    },
    orderDate(val) {
      console.log(val);
      if (val && val.length) {
        this.startTime = parseInt(val[0] / 1000);
        this.endTime = parseInt(val[1] / 1000);
      } else {
        this.startTime = "";
        this.endTime = "";
      }
      this.pageChange(1);
    },
  },
};
</script>
<style></style>
