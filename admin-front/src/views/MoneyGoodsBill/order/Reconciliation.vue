<template>
  <div class="print-box">
    <el-row class="tem-row">
      <el-col :span="24">
        <div id="printTest" class="tem-img">
          <div class="top_view" style="font-size: 12px">
            <div class="time_view">
              <p>
                {{ no_time }}
              </p>
              <p>制单人:{{ userName }}</p>
            </div>
            <div class="name_view">
              <span> {{ myDate }}{{ id === "1" ? "库存" : id === "2" ? "销售" : id === "3" ? "财务" : "" }}日报 </span>
            </div>
            <!--            <div v-else-if="id === '2'" class="name_view">-->
            <!--              <span>{{ myDate }}销售日报</span>-->
            <!--            </div>-->
            <!--            <div v-else class="name_view">-->
            <!--              <span>{{ myDate }}财务日报</span>-->
            <!--            </div>-->
            <div class="num_view">
              <p>{{ enterprise_title }}提供技术支持</p>
            </div>
          </div>
          <table border="1" style="width: 100%; margin-top: 10px" class="goods-table">
            <tr>
              <th>商品名称</th>
              <th v-if="unitNameFlag">商品规格</th>
              <th v-if="topEndNumFlag">昨日结存总数量</th>
              <th v-if="selfInNumFlag">今日入库总数量</th>
              <th v-if="selfOutNumFlag">今日出库总数量</th>
              <th v-if="selfPurchaseInNumFlag">采购入库</th>
              <th v-if="selfPurchaseReturnOutNumFlag">采购退货出库</th>
              <th v-if="selfSaleOutNumFlag">销售出库</th>
              <th v-if="selfSaleReturnInNumFlag">销售退货入库</th>
              <th v-if="selfAllocateInNumFlag">调拨入库</th>
              <th v-if="selfAllocateOutNumFlag">调拨出库</th>
              <th v-if="selfEndNumFlag">今日结存总数量</th>
            </tr>
            <tr v-for="(item, index) in tableData" :key="index">
              <td>
                {{ item.materielName }}
              </td>
              <td v-if="unitNameFlag">
                <span>{{ item.unitName }}</span>
                <span v-if="item.skuName">_{{ item.skuName }}</span>
              </td>
              <td v-if="topEndNumFlag">
                {{ item.topEndNum }}
              </td>
              <td v-if="selfInNumFlag">
                {{ item.selfInNum }}
              </td>
              <td v-if="selfOutNumFlag">
                {{ item.selfOutNum }}
              </td>
              <td v-if="selfPurchaseInNumFlag">
                {{ item.selfPurchaseInNum }}
              </td>
              <td v-if="selfPurchaseReturnOutNumFlag">
                {{ item.selfPurchaseReturnOutNum }}
              </td>
              <td v-if="selfSaleOutNumFlag">
                {{ item.selfSaleOutNum }}
              </td>
              <td v-if="selfSaleReturnInNumFlag">
                {{ item.selfSaleReturnInNum }}
              </td>

              <td v-if="selfAllocateInNumFlag">
                {{ item.selfAllocateInNum }}
              </td>
              <td v-if="selfAllocateOutNumFlag">
                {{ item.selfAllocateOutNum }}
              </td>
              <td v-if="selfEndNumFlag">
                {{ item.selfEndNum }}
              </td>
            </tr>
            <tr>
              <td colspan="1" style="text-align: center">合计：</td>
              <td v-if="unitNameFlag">--</td>
              <td v-if="topEndNumFlag">{{ totalNum }}</td>
              <td v-if="selfInNumFlag">{{ totalNum3 }}</td>
              <td v-if="selfOutNumFlag">{{ totalNum4 }}</td>
              <td v-if="selfPurchaseInNumFlag">{{ totalNum5 }}</td>
              <td v-if="selfPurchaseReturnOutNumFlag">{{ totalNum6 }}</td>
              <td v-if="selfSaleOutNumFlag">{{ totalNum8 }}</td>
              <td v-if="selfSaleReturnInNumFlag">{{ totalNum7 }}</td>
              <td v-if="selfAllocateInNumFlag">{{ totalNum9 }}</td>
              <td v-if="selfAllocateOutNumFlag">{{ totalNum10 }}</td>
              <td v-if="selfEndNumFlag">{{ totalNum2 }}</td>
            </tr>
          </table>
        </div>
      </el-col>
    </el-row>
    <div style="text-align: center">
      <el-button v-print="'#printTest'" type="primary" @click="PrintNumPrintIncr"> 确认打印 </el-button>
    </div>
  </div>
</template>

<script>
import { inventoryStatistics } from "@/api/Stock";
import { statistics } from "@/api/Order";
export default {
  name: "OutgoingPrinting",
  data() {
    return {
      no_time: "",
      tableData: [],
      endTime: "",
      startTime: "",
      id: "",
      warehouseId: "",
      printingNum: 0,
      pageSize: 10,
      page: 1,
      total: 1,
      checkList: [],
      unitNameFlag: true,
      topEndNumFlag: true,
      selfEndNumFlag: true,
      selfInNumFlag: true,
      selfOutNumFlag: true,
      selfPurchaseInNumFlag: true,
      selfPurchaseReturnOutNumFlag: true,
      selfSaleOutNumFlag: true,
      selfSaleReturnInNumFlag: true,
      selfAllocateInNumFlag: true,
      selfAllocateOutNumFlag: true,
    };
  },
  computed: {
    totalNum() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return this.tableData[0].topEndNum - 0;
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, item.topEndNum - 0);
        }
        return sum;
      }
    },
    totalNum2() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return this.tableData[0].selfEndNum - 0;
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, item.selfEndNum - 0);
        }
        return sum;
      }
    },
    totalNum3() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return this.tableData[0].selfInNum - 0;
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, item.selfInNum - 0);
        }
        return sum;
      }
    },
    totalNum4() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return this.tableData[0].selfOutNum - 0;
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, item.selfOutNum - 0);
        }
        return sum;
      }
    },
    totalNum5() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return this.tableData[0].selfPurchaseInNum - 0;
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, item.selfPurchaseInNum - 0);
        }
        return sum;
      }
    },
    totalNum6() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return this.tableData[0].selfPurchaseReturnOutNum - 0;
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, item.selfPurchaseReturnOutNum - 0);
        }
        return sum;
      }
    },
    totalNum7() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return this.tableData[0].selfSaleReturnInNum - 0;
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, item.selfSaleReturnInNum - 0);
        }
        return sum;
      }
    },
    totalNum8() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return this.tableData[0].selfSaleOutNum - 0;
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, item.selfSaleOutNum - 0);
        }
        return sum;
      }
    },
    totalNum9() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return this.tableData[0].selfAllocateInNum - 0;
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, item.selfAllocateInNum - 0);
        }
        return sum;
      }
    },
    totalNum10() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return this.tableData[0].selfAllocateOutNum - 0;
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, item.selfAllocateOutNum - 0);
        }
        return sum;
      }
    },
    myDate() {
      return this.$_common.formatDate(this.startTime, "yyyy-MM-dd");
    },
  },
  created() {
    this.startTime = this.$route.query.startTime;
    this.endTime = this.$route.query.endTime;
    this.no_time = this.$_common.formatDate(new Date().getTime());
    this.id = this.$route.query.id;
    this.warehouseId = this.$route.query.warehouseId;
    this.page = this.$route.query.page;
    this.pageSize = this.$route.query.pageSize;
    this.checkList = JSON.parse(this.$route.query.checkList);
    if (this.id === "1") {
      this.inventoryStatistics();
    } else if (this.id === "2") {
      this.statistics();
    }
    this.unitNameFlag = this.checkList.some((item) => item === "商品规格");
    this.topEndNumFlag = this.checkList.some((item) => item === "昨日结存总数量");
    this.selfEndNumFlag = this.checkList.some((item) => item === "今日结存总数量");
    this.selfInNumFlag = this.checkList.some((item) => item === "今日入库总数量");
    this.selfOutNumFlag = this.checkList.some((item) => item === "今日出库总数量");
    this.selfPurchaseInNumFlag = this.checkList.some((item) => item === "采购入库");
    this.selfPurchaseReturnOutNumFlag = this.checkList.some((item) => item === "采购退货出库");
    this.selfSaleOutNumFlag = this.checkList.some((item) => item === "销售出库");
    this.selfSaleReturnInNumFlag = this.checkList.some((item) => item === "销售退货入库");
    this.selfAllocateInNumFlag = this.checkList.some((item) => item === "调拨入库");
    this.selfAllocateOutNumFlag = this.checkList.some((item) => item === "调拨出库");
  },
  methods: {
    async PrintNumPrintIncr() {
      setTimeout(() => {
        window.close();
      }, 1000);
    },
    async inventoryStatistics() {
      const { data } = await inventoryStatistics({
        page: this.page,
        pageSize: this.pageSize,
        end: this.endTime,
        start: this.startTime,
        warehouseId: this.warehouseId,
      });
      this.tableData = [];
      data.forEach((item) => {
        item.Details.forEach((itemD) => {
          this.tableData.push({
            materielCode: item.materielCode,
            materielId: item.materielId,
            materielName: item.materielName,
            warehouseId: item.warehouseId,
            ...itemD,
          });
        });
      });
    },
    async statistics() {
      const data = await statistics({
        page: this.page,
        pageSize: this.pageSize,
      });
      this.tableData = data.data;
    },
  },
};
</script>

<style scoped lang="scss">
.tem-row {
  padding: 20px 80px;
  position: relative;
}
.tem-img {
  width: 100%;
  /*padding: 20px 0;*/
}
.infomation {
  font-size: 14px;
}
.sign {
  font-size: 14px;
  margin-top: 6px;
}
.add_num {
  margin-right: 10px;
}
.print-tag {
  position: absolute;
  right: 190px;
  top: 0;
  img {
    width: 120px;
  }
}
.top_view {
  position: relative;
  height: 34px;
}
.time_view {
  position: absolute;
  left: 0;
  top: 0;
}
.name_view {
  text-align: center;
  font-size: 22px;
}
.num_view {
  position: absolute;
  right: 0;
  top: 0;
}
</style>
