<template>
  <div class="print-box">
    <el-row class="tem-row">
      <el-col :span="24">
        <div id="printTest" class="tem-img">
          <div class="top_view" style="font-size: 12px">
            <div class="time_view">
              <p>
                {{ no_time }}
              </p>
              <p>制单人:{{ userName }}</p>
            </div>
            <div class="name_view">
              <span> {{ myDate }}{{ id === "1" ? "库存" : id === "2" ? "销售" : id === "3" ? "财务" : "" }}日报 </span>
            </div>
            <!--            <div v-else-if="id === '2'" class="name_view">-->
            <!--              <span>{{ myDate }}销售日报</span>-->
            <!--            </div>-->
            <!--            <div v-else class="name_view">-->
            <!--              <span>{{ myDate }}财务日报</span>-->
            <!--            </div>-->
            <div class="num_view">
              <p>{{ enterprise_title }}提供技术支持</p>
            </div>
          </div>
          <table border="1" style="width: 100%; margin-top: 10px" class="goods-table">
            <tr>
              <th>应收总额</th>
              <th>实收总额</th>
              <th>欠款总金额</th>
              <th v-if="$accessCheck($Access.fianceBillMoney)">毛利总金额</th>
              <th>应付总额</th>
              <th>实付总额</th>
              <th v-for="(itemP, indexP) in tableData[0].todayreceivedMoney" :key="indexP">
                {{ itemP.accountName }}
              </th>
            </tr>
            <tr v-for="(item, index) in tableData" :key="index">
              <td>
                {{ item.getMoney }}
              </td>
              <td>
                {{ item.shouldGetMoney }}
              </td>
              <td>
                {{ item.oweMoney }}
              </td>
              <td v-if="$accessCheck($Access.fianceBillMoney)">
                {{ item.profitMoney }}
              </td>
              <td>
                {{ item.payMoney }}
              </td>
              <td>
                {{ item.shouldPayMoney }}
              </td>
              <td v-for="(itemP, indexP) in item.todayreceivedMoney" :key="indexP">
                {{ itemP.totalMoney }}
              </td>
              <!--              <td>-->
              <!--                {{ item.cashGetMoney }}-->
              <!--              </td>-->
              <!--              <td>-->
              <!--                {{ item.aliGetMoney }}-->
              <!--              </td>-->
            </tr>
          </table>
        </div>
      </el-col>
    </el-row>
    <div style="text-align: center">
      <el-button v-print="'#printTest'" type="primary" @click="PrintNumPrintIncr"> 确认打印 </el-button>
    </div>
  </div>
</template>

<script>
import { getTodayStatistics } from "@/api/Finance";
export default {
  name: "OutgoingPrinting",
  data() {
    return {
      no_time: "",
      tableData: [],
      endTime: "",
      startTime: "",
      id: "",
      warehouseId: "",
      printingNum: 0,
      pageSize: 10,
      page: 1,
      total: 1,
    };
  },
  computed: {
    myDate() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      return year + "年" + month + "月" + day + "日";
    },
  },
  created() {
    let start = new Date(new Date().toLocaleDateString());
    let end = new Date();
    this.endTime = parseInt(end.getTime() / 1000);
    this.startTime = parseInt(start.getTime() / 1000);
    this.no_time = this.$_common.formatDate(new Date().getTime());
    this.id = this.$route.query.id;
    this.getTodayStatistics();
  },
  methods: {
    async PrintNumPrintIncr() {
      setTimeout(() => {
        window.close();
      }, 1000);
    },
    async getTodayStatistics() {
      const data = await getTodayStatistics({
        page: this.page,
        pageSize: this.pageSize,
      });
      this.tableData.push(data.data);
    },
  },
};
</script>

<style scoped lang="scss">
.tem-row {
  padding: 20px 80px;
  position: relative;
}
.tem-img {
  width: 100%;
  /*padding: 20px 0;*/
}
.infomation {
  font-size: 14px;
}
.sign {
  font-size: 14px;
  margin-top: 6px;
}
.add_num {
  margin-right: 10px;
}
.print-tag {
  position: absolute;
  right: 190px;
  top: 0;
  img {
    width: 120px;
  }
}
.top_view {
  position: relative;
  height: 34px;
}
.time_view {
  position: absolute;
  left: 0;
  top: 0;
}
.name_view {
  text-align: center;
  font-size: 22px;
}
.num_view {
  position: absolute;
  right: 0;
  top: 0;
}
</style>
