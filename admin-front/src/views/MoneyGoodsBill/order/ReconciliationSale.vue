<template>
  <div class="print-box">
    <el-row class="tem-row">
      <el-col :span="24">
        <div id="printTest" class="tem-img">
          <div class="top_view" style="font-size: 12px">
            <div class="time_view">
              <p>
                {{ no_time }}
              </p>
              <p>制单人:{{ userName }}</p>
            </div>
            <div class="name_view">
              <span> {{ myDate }}{{ id === "1" ? "库存" : id === "2" ? "销售" : id === "3" ? "财务" : "" }}日报 </span>
            </div>
            <div class="num_view">
              <p>{{ enterprise_title }}提供技术支持</p>
            </div>
          </div>
          <table border="1" style="width: 100%; margin-top: 10px" class="goods-table">
            <tr>
              <th>商品名称</th>
              <th>商品规格</th>
              <th>销售数量</th>
              <th>本期退货数量</th>
              <th>上期退货数量</th>
              <th v-if="$accessCheck($Access.showSaleBillMoney)">销售总金额</th>
              <th v-if="$accessCheck($Access.showSaleBillMoney)">退货总金额</th>
              <th v-if="$accessCheck($Access.stockBillMoney)">毛利总金额</th>
            </tr>
            <tr v-for="(item, index) in tableData" :key="index">
              <td>
                {{ item.title }}
              </td>
              <td>
                <span>{{ item.unitName }}</span>
                <span v-if="item.specGroup">_{{ item.specGroup }}</span>
              </td>
              <td>
                {{ item.onum }}
              </td>
              <td>
                {{ item.thisRetNum }}
              </td>
              <td>
                {{ item.lastRetNum }}
              </td>
              <td v-if="$accessCheck($Access.showSaleBillMoney)">
                {{ item.totalMoney }}
              </td>
              <td v-if="$accessCheck($Access.showSaleBillMoney)">
                {{ item.returnTotalPrice }}
              </td>
              <td v-if="$accessCheck($Access.stockBillMoney)">
                {{ item.subProMoney }}
              </td>
            </tr>
            <tr>
              <td colspan="1" style="text-align: center">合计：</td>
              <td>--</td>
              <td>{{ totalNum }}</td>
              <td>{{ totalNum2 }}</td>
              <td>{{ totalNum2_one }}</td>
              <td v-if="$accessCheck($Access.showSaleBillMoney)">
                {{ totalNum3 }}
              </td>
              <td v-if="$accessCheck($Access.showSaleBillMoney)">
                {{ totalNum4 }}
              </td>
              <td v-if="$accessCheck($Access.stockBillMoney)">
                {{ totalNum5 }}
              </td>
            </tr>
          </table>
        </div>
      </el-col>
    </el-row>
    <div style="text-align: center">
      <el-button v-print="'#printTest'" type="primary" @click="PrintNumPrintIncr"> 确认打印 </el-button>
    </div>
  </div>
</template>

<script>
import { inventoryStatistics } from "@/api/Stock";
import { statistics } from "@/api/Order";
export default {
  name: "OutgoingPrinting",
  data() {
    return {
      no_time: "",
      tableData: [],
      endTime: "",
      startTime: "",
      id: "",
      warehouseId: "",
      printingNum: 0,
      pageSize: 10,
      page: 1,
      total: 1,
    };
  },
  computed: {
    totalNum() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return Number(this.tableData[0].onum);
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, Number(item.onum));
        }
        return sum;
      }
    },
    totalNum2() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return Number(this.tableData[0].thisRetNum);
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, Number(item.thisRetNum));
        }
        return sum;
      }
    },
    totalNum2_one() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return Number(this.tableData[0].lastRetNum);
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, Number(item.lastRetNum));
        }
        console.log(sum);
        return sum;
      }
    },
    totalNum3() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return Number(this.tableData[0].totalMoney);
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, Number(item.totalMoney));
        }
        return sum;
      }
    },
    totalNum4() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return Number(this.tableData[0].returnTotalPrice);
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, Number(item.returnTotalPrice));
        }
        return sum;
      }
    },
    totalNum5() {
      if (!this.tableData.length) {
        return 0;
      } else if (this.tableData.length === 1) {
        return Number(this.tableData[0].subProMoney);
      } else {
        let sum = 0;
        for (let i in this.tableData) {
          const item = this.tableData[i];
          sum = this.$NP.plus(sum, Number(item.subProMoney));
        }
        return sum;
      }
    },
    myDate() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      return year + "年" + month + "月" + day + "日";
    },
  },
  created() {
    let start = new Date(new Date().toLocaleDateString());
    let end = new Date();
    this.endTime = parseInt(end.getTime() / 1000);
    this.startTime = parseInt(start.getTime() / 1000);
    this.no_time = this.$_common.formatDate(new Date().getTime());
    this.id = this.$route.query.id;
    this.page = this.$route.query.page;
    this.pageSize = this.$route.query.pageSize;
    this.statistics();
  },
  methods: {
    async PrintNumPrintIncr() {
      setTimeout(() => {
        window.close();
      }, 1000);
    },
    async statistics() {
      const data = await statistics({
        page: this.page,
        pageSize: this.pageSize,
      });
      this.tableData = data.data;
    },
  },
};
</script>

<style scoped lang="scss">
.tem-row {
  padding: 20px 80px;
  position: relative;
}
.tem-img {
  width: 100%;
  /*padding: 20px 0;*/
}
.infomation {
  font-size: 14px;
}
.sign {
  font-size: 14px;
  margin-top: 6px;
}
.add_num {
  margin-right: 10px;
}
.print-tag {
  position: absolute;
  right: 190px;
  top: 0;
  img {
    width: 120px;
  }
}
.top_view {
  position: relative;
  height: 34px;
}
.time_view {
  position: absolute;
  left: 0;
  top: 0;
}
.name_view {
  text-align: center;
  font-size: 22px;
}
.num_view {
  position: absolute;
  right: 0;
  top: 0;
}
</style>
