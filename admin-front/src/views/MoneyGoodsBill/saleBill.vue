<template>
  <Container>
    <div slot="left" class="all-handel-btn">
      <el-date-picker
        v-model="timeValue"
        type="date"
        placeholder="选择日期"
        :picker-options="pickerOptions"
        :clearable="false"
        @change="jurisdiction"
      ></el-date-picker>
      <el-button type="primary" style="margin-left: 10px" size="small" @click="openUrl"> 打印 </el-button>
      <!--      <el-button type="primary" size="small">导出</el-button>-->
    </div>
    <div class="tip">{{ myDate }}销售日报</div>
    <el-table :data="marketList" :span-method="objectSpanMethod" :summary-method="getSummaries" show-summary>
      <el-table-column prop="title" label="商品名称" min-width="120"></el-table-column>
      <el-table-column prop="name" label="商品规格" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.unitName }}</span>
          <span v-if="scope.row.specGroup">_{{ scope.row.specGroup }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="onum" label="销售数量" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.onum) }}
        </template>
      </el-table-column>
      <el-table-column prop="thisRetNum" label="本期退货数量" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.thisRetNum) }}
        </template>
      </el-table-column>
      <el-table-column prop="lastRetNum" label="上期退货数量" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.lastRetNum) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="$accessCheck($Access.showSaleBillMoney)"
        prop="totalMoney"
        label="销售总金额"
        min-width="120"
      >
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.totalMoney) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="$accessCheck($Access.showSaleBillMoney)"
        prop="returnTotalPrice"
        label="退货总金额"
        min-width="120"
      >
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.returnTotalPrice) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="$accessCheck($Access.stockBillMoney)"
        prop="subProMoney"
        label="毛利总金额"
        min-width="120"
      >
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.subProMoney) }}
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { statistics } from "@/api/Order";
export default {
  name: "SaleBill",
  data() {
    return {
      marketList: [],
      total: 0,
      page: 1,
      pageSize: 10,
      spanArr: [],
      timeValue: "",
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      startTime: "",
      endTime: "",
      timeData: new Date(),
    };
  },
  computed: {
    myDate() {
      if (this.timeValue) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.timeData = new Date(this.timeValue);
      }
      let date = this.timeData;
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      return year + "年" + month + "月" + day + "日";
    },
  },
  created() {
    this.statistics();
  },
  activated() {
    if (this.$_isInit()) return;
    this.statistics();
  },
  methods: {
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (["商品名称"].includes(column.label)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    pageChange(val) {
      this.page = val;
      this.statistics();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    openUrl(row) {
      let routeData = this.$router.resolve({
        path: `/ReconciliationSale`,
        query: {
          id: 2,
          page: this.page,
          pageSize: this.pageSize,
        },
      });
      window.open(routeData.href, "_blank");
    },
    async statistics(value1) {
      const data = await statistics({
        page: this.page,
        pageSize: this.pageSize,
        start: this.startTime,
        end: this.endTime,
      });
      this.marketList = data.data;
      this.total = data.pageTotal;
      const getSpanArr = this.$_common.getSpanArr(this.marketList, "goodsId");
      this.spanArr = getSpanArr.spanArr;
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          // sums[index] += "";
          sums[index] = this.$_common.formatNub(sums[index], 2) + "";
        } else {
          sums[index] = "--";
        }
      });

      return sums;
    },
    async jurisdiction(val) {
      let start2 = new Date(this.timeValue);
      const value1 = parseInt(start2.getTime() / 1000);
      if (val) {
        this.startTime = value1;
        this.endTime = value1 + 86399;
      } else {
        this.startTime = "";
        this.endTime = "";
      }
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.tip {
  font-weight: bold;
  text-align: center;
  line-height: 50px;
}
</style>
