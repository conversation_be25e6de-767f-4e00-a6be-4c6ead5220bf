<template>
  <Container>
    <div slot="left" class="all-handel-btn">
      <el-date-picker
        v-model="timeValue"
        type="date"
        placeholder="选择日期"
        :picker-options="pickerOptions"
        :clearable="false"
        @change="jurisdiction"
      ></el-date-picker>
      <el-button type="primary" style="margin-left: 10px" size="small" @click="openUrl"> 打印 </el-button>
      <!--      <el-button type="primary" size="small">导出</el-button>-->
    </div>
    <div class="tip">{{ myDate }}财务日报</div>
    <el-table :data="financeData">
      <el-table-column prop="getMoney" label="应收总额" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.getMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="shouldGetMoney" label="实收总额" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.shouldGetMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="oweMoney" label="欠款总金额" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.oweMoney) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="$accessCheck($Access.fianceBillMoney)"
        prop="profitMoney"
        label="毛利总金额"
        min-width="120"
      >
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.profitMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="payMoney" label="应付总额" min-width="80">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.payMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="shouldPayMoney" label="实付总额" min-width="80">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.shouldPayMoney) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in todayreceivedMoney"
        :key="index"
        prop="totalMoney"
        :label="item.accountName"
        min-width="130"
      >
        <template>
          {{ $_common.formattedNumber(item.totalMoney) }}
        </template>
      </el-table-column>
    </el-table>
  </Container>
</template>

<script>
import { getTodayStatistics, getAllAccount } from "@/api/Finance";
export default {
  name: "FinanceBill",
  data() {
    return {
      financeData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      timeValue: "",
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      startTime: "",
      endTime: "",
      timeData: new Date(),
      todayreceivedMoney: [],
    };
  },
  computed: {
    myDate() {
      if (this.timeValue) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.timeData = new Date(this.timeValue);
      }
      let date = this.timeData;
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      return year + "年" + month + "月" + day + "日";
    },
  },
  created() {
    this.getTodayStatistics();
    // this.getAllAccount();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getTodayStatistics();
    // this.getAllAccount();
  },
  methods: {
    pageChange(val) {
      this.page = val;
      this.getTodayStatistics();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    openUrl(row) {
      let routeData = this.$router.resolve({
        path: `/ReconciliationFinance`,
        query: {
          id: 3,
        },
      });
      window.open(routeData.href, "_blank");
    },
    async getTodayStatistics() {
      this.todayreceivedMoney = [];
      const data = await getTodayStatistics({
        page: this.page,
        pageSize: this.pageSize,
        start: this.startTime,
        end: this.endTime,
      });
      this.financeData = [data.data];

      this.$nextTick(() => {
        this.todayreceivedMoney = data.data.todayreceivedMoney;
      });
      this.total = data.pageTotal;
    },
    async jurisdiction(val) {
      let start2 = new Date(this.timeValue);
      const value1 = parseInt(start2.getTime() / 1000);
      if (val) {
        this.startTime = value1;
        this.endTime = value1 + 86399;
      } else {
        this.startTime = "";
        this.endTime = "";
      }
      this.pageChange(1);
    },
    // //  获取列表
    // async getAllAccount() {
    //   const { data, pageTotal } = await getAllAccount({
    //     page: this.page,
    //     pageSize: this.pre_page,
    //     shopId: this.shopId,
    //     enableStatus: 5,
    //   });
    //
    //   this.tableData = data;
    //   this.total = pageTotal;
    // },
  },
};
</script>

<style scoped>
.tip {
  font-weight: bold;
  text-align: center;
  line-height: 50px;
}
</style>
