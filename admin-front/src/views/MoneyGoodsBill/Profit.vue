<template>
  <Container>
    <div slot="left" class="box-search-form">
      <el-date-picker
        v-model="timeValue"
        type="date"
        placeholder="选择日期"
        :picker-options="pickerOptions"
        :clearable="false"
        @change="jurisdiction"
      ></el-date-picker>
    </div>
    <!--    <div class="tip">{{ myDate }}利润表</div>-->
    <el-table :data="tableData" row-key="id" default-expand-all :tree-props="{ children: 'children' }">
      <el-table-column type="index" label="行次" width="50"></el-table-column>
      <el-table-column prop="date" label="项目"></el-table-column>
      <el-table-column prop="address" label="本月金额"></el-table-column>
      <el-table-column prop="address" label="本年累计金额"></el-table-column>
    </el-table>
  </Container>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        {
          id: 1,
          date: "销售统计",
          children: [
            {
              id: 11,
              date: "订单数",
            },
            {
              id: 12,
              date: "订单金额",
            },
            {
              id: 13,
              date: "毛利金额",
            },
            {
              id: 14,
              date: "退货订单",
            },
            {
              id: 15,
              date: "退货金额",
            },
            {
              id: 16,
              date: "实际毛利",
            },
          ],
        },
        {
          id: 2,
          date: "销售收款",
          children: [
            { id: 21, date: "应收金额" },
            { id: 22, date: "未收金额" },
            {
              id: 23,
              date: "实收金额",
              children: [
                { id: 231, date: "支付宝" },
                { id: 232, date: "微信" },
                { id: 233, date: "现金" },
              ],
            },
            { id: 24, date: "退货金额" },
            { id: 25, date: "未付退货" },
            {
              id: 26,
              date: "实退金额",
              children: [
                { id: 261, date: "支付宝" },
                { id: 262, date: "微信" },
                { id: 263, date: "现金" },
              ],
            },
          ],
        },
        {
          id: 3,
          date: "采购统计",
          children: [
            { id: 31, date: "采购订单" },
            { id: 32, date: "采购金额" },
            { id: 33, date: "采退订单" },
            { id: 34, date: "采退金额" },
            { id: 35, date: "实际毛利" },
          ],
        },
        {
          id: 4,
          date: "采购付款",
          children: [
            { id: 41, date: "应付金额" },
            { id: 42, date: "未付金额" },
            {
              id: 43,
              date: "实付金额",
              children: [
                { id: 431, date: "支付宝" },
                { id: 432, date: "微信" },
                { id: 433, date: "现金" },
              ],
            },
            { id: 44, date: "采退金额" },
            { id: 45, date: "未收退货" },
            {
              id: 46,
              date: "实收金额",
              children: [
                { id: 461, date: "支付宝" },
                { id: 462, date: "微信" },
                { id: 463, date: "现金" },
              ],
            },
          ],
        },
        {
          id: 5,
          date: "账户余额",
          children: [
            { id: 51, date: "银行A卡号" },
            { id: 52, date: "银行B卡号" },
          ],
        },
      ],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      timeValue: new Date(),
    };
  },
  computed: {
    myDate() {
      if (this.timeValue) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.timeData = new Date(this.timeValue);
      }
      let date = this.timeData;
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      return year + "年" + month + "月" + day + "日";
    },
  },
};
</script>

<style scoped lang="scss">
.tip {
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
}
</style>
