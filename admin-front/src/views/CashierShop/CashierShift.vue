<template>
  <div class="CashierShift">
    <CashierHeader></CashierHeader>
    <div class="shift_head">
      <div class="head_back">
        <i class="el-icon-back"></i>
        <span style="margin-left: 6px" @click="$router.push('/CashierShop')"> 返回 </span>
        <span class="head_line"></span>
        <span style="color: black">交班详情</span>
      </div>
      <el-row>
        <el-col :span="7">
          <div class="grid-content" style="border-right: 1px solid #f2f2f2">
            <div class="shift_content">
              <div class="shift_content_one clearfix">
                <span>收银员</span>
                <span style="float: right">{{ connect_detail.staffName }}</span>
              </div>
              <div class="shift_content_one clearfix">
                <span>上班时间</span>
                <span style="float: right">
                  {{ $_common.formatDate(connect_detail.atWorkTime) }}
                </span>
              </div>
              <div class="shift_content_one clearfix">
                <span>下班时间</span>
                <span style="float: right">
                  {{ $_common.formatDate(offDutyTime) }}
                </span>
              </div>
              <div class="shift_content_one clearfix">
                <span>工作时长</span>
                <span style="float: right">{{ offHours }}</span>
              </div>
            </div>
            <div class="shift_content_btn" style="text-align: right">
              <el-button type="primary" @click="editFn">交班记录</el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="17">
          <div class="grid-content">
            <ul class="shift_content">
              <li>
                <div class="shift_content_title">
                  <span>收款金额</span>
                  <span class="shift_content_price"> ￥{{ connect_detail.collectionMoney || "0.00" }} </span>
                </div>
                <div class="shift_content-main">
                  <div class="shift_content-item">
                    <i class="iconfont icon-yingyeejilu"></i>
                    <span class="shift_content_write">现金</span>
                    <p>￥{{ connect_detail.collectionData.cash || "0.00" }}</p>
                  </div>
                  <div class="shift_content-item">
                    <i class="iconfont icon-wx-pay"></i>
                    <span class="shift_content_write">微信</span>
                    <p>￥{{ connect_detail.collectionData.wechat || "0.00" }}</p>
                  </div>
                  <div class="shift_content-item">
                    <i class="iconfont icon-umidd17"></i>
                    <span class="shift_content_write">支付宝</span>
                    <p>￥{{ connect_detail.collectionData.alipay || "0.00" }}</p>
                  </div>
                  <div class="shift_content-item">
                    <i class="iconfont icon-yue"></i>
                    <span class="shift_content_write">余额</span>
                    <p>￥{{ connect_detail.collectionData.balance || "0.00" }}</p>
                  </div>
                  <div class="shift_content-item">
                    <i class="iconfont icon-qita"></i>
                    <span class="shift_content_write">其他</span>
                    <p>￥{{ connect_detail.collectionData.other || "0.00" }}</p>
                  </div>
                </div>
              </li>
              <li>
                <div class="shift_content_title">
                  <span>充值金额</span>
                  <span class="shift_content_price"> ￥{{ connect_detail.rechargeMoney || "0.00" }} </span>
                </div>
                <div class="shift_content-main">
                  <div class="shift_content-item">
                    <i class="iconfont icon-yingyeejilu"></i>
                    <span class="shift_content-item">现金</span>
                    <p>￥{{ connect_detail.refundData.cash || "0.00" }}</p>
                  </div>
                  <div class="shift_content-item">
                    <i class="iconfont icon-wx-pay"></i>
                    <span class="shift_content_write">微信</span>
                    <p>￥{{ connect_detail.refundData.wechat || "0.00" }}</p>
                  </div>
                  <div class="shift_content-item">
                    <i class="iconfont icon-umidd17"></i>
                    <span class="shift_content_write">支付宝</span>
                    <p>￥{{ connect_detail.refundData.alipay || "0.00" }}</p>
                  </div>
                  <div class="shift_content-item">
                    <i class="iconfont icon-qita"></i>
                    <span class="shift_content_write">其他</span>
                    <p>￥{{ connect_detail.refundData.other || "0.00" }}</p>
                  </div>
                </div>
              </li>
              <li>
                <div class="shift_content_title">
                  <span>退款金额</span>
                  <span class="shift_content_price"> ￥{{ connect_detail.refundMoney || "0.00" }} </span>
                </div>
                <div class="shift_content-main">
                  <div class="shift_content-item">
                    <i class="iconfont icon-yingyeejilu"></i>
                    <span class="shift_content_write">现金</span>
                    <p>￥{{ connect_detail.refundData.cash || "0.00" }}</p>
                  </div>
                  <div class="shift_content-item">
                    <i class="iconfont icon-wx-pay"></i>
                    <span class="shift_content_write">微信</span>
                    <p>￥{{ connect_detail.refundData.wechat || "0.00" }}</p>
                  </div>
                  <div class="shift_content-item">
                    <i class="iconfont icon-umidd17"></i>
                    <span class="shift_content_write">支付宝</span>
                    <p>￥{{ connect_detail.refundData.alipay || "0.00" }}</p>
                  </div>
                  <div class="shift_content-item">
                    <i class="iconfont icon-yue"></i>
                    <span class="shift_content_write">余额</span>
                    <p>￥{{ connect_detail.refundData.balance || "0.00" }}</p>
                  </div>
                </div>
              </li>
            </ul>
            <div class="clearfix shift_content_btn">
              <div class="float_left">
                <span>现金合计：</span>
                <span style="color: red">
                  ￥{{
                    $NP.plus(
                      connect_detail.collectionMoney || 0,
                      connect_detail.rechargeMoney || 0,
                      connect_detail.refundMoney || 0
                    )
                  }}
                </span>
              </div>
              <div class="float_right">
                <span style="margin-right: 24px"> 请仔细核对交班金额，确认无误后进行交班 </span>
                <el-button type="primary" @click="dialogVisible = true"> 交班 </el-button>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="交班确认"
      :visible.sync="dialogVisible"
      width="30%"
      @close="dialogVisible = false"
    >
      <p style="margin-bottom: 20px">
        <!--        交班后会退出登录，并可打印交班小票，确认要交班吗？-->
        交班后会退出登录，确认要交班吗？
      </p>
      <p>
        <el-input
          v-model="remark"
          type="textarea"
          :rows="4"
          maxlength="200"
          placeholder="请填写备注"
          show-word-limit
        ></el-input>
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmConnect">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CashierHeader from "./CashierHeader.vue";
import { connectDetails, confirmConnect } from "@/api/Cashier";

export default {
  name: "CashierShift",
  components: {
    CashierHeader,
  },
  data() {
    return {
      dialogVisible: false,
      remark: "",
      connect_detail: {},
      shopId: 1,
      offHours: "",
      offDutyTime: "",
    };
  },
  created() {
    this.shopId = this.$store.getters["MUser/cashierShop"].id;
    this.connectDetails();
  },
  methods: {
    editFn() {
      this.$router.push("/CashierRecord");
    },
    async connectDetails() {
      const data = await connectDetails({
        shopId: this.shopId,
      });

      this.connect_detail = data.data;
      this.offDutyTime = parseInt(new Date().getTime() / 1000);
      const s = this.offDutyTime - this.connect_detail.atWorkTime;
      this.offHours = this.$_common.second(s);
    },
    async confirmConnect() {
      const data = await confirmConnect(this.connect_detail.id, {
        offDutyTime: this.offDutyTime,
        remark: this.remark,
      });

      this.dialogVisible = false;
      sessionStorage.clear();
      setTimeout(() => {
        sessionStorage.setItem("isCashier", "1");
        this.$router.push("/CashierLogin");
      }, 200);
    },
  },
};
</script>

<style scoped>
.CashierShift {
  background-color: #f7f7f7;
  font-size: 14px;
  color: #333333;
}

.shift_head {
  margin-top: 20px;
}

.head_back {
  background-color: #fff;
  color: #ccc;
  font-size: 14px;
  cursor: pointer;
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #ededed;
  padding-left: 20px;
}

.head_line {
  height: 24px;
  width: 1px;
  background-color: #ededed;
  display: inline-block;
  margin: 6px 10px -8px 10px;
}

.grid-content {
  background-color: #fff;
  padding: 0;
}

.shift_content {
  height: calc(100vh - 200px);
  border-bottom: 1px solid #ededed;
}

.shift_content > li {
  padding: 0 40px;
  margin-bottom: 40px;
}

.shift_content_one {
  height: 60px;
  line-height: 60px;
  padding: 0 30px;
  border-bottom: 1px solid #ededed;
}

.shift_content_btn {
  background: #ffffff;
  padding: 8px;
  line-height: 48px;
}

.shift_content_title {
  line-height: 62px;
  font-size: 16px;
  font-weight: bold;
}

.shift_content_price {
  color: #fb6638;
  padding-left: 10px;
}

.shift_content-main {
  display: flex;
  flex-wrap: wrap;
}

.shift_content-item {
  width: 20%;
  max-width: 300px;
  line-height: 46px;
  font-size: 18px;
}
.shift_content-item > .iconfont {
  font-size: 24px;
  margin-right: 10px;
  vertical-align: middle;
}
.shift_content-item > .shift_content_write {
  font-size: 14px;
}
.shift_content-item > .icon-yingyeejilu {
  color: rgb(255, 97, 85);
}
.shift_content-item > .icon-wx-pay {
  color: rgb(89, 182, 76);
}
.shift_content-item > .icon-umidd17 {
  color: #009fe8;
}
.shift_content-item > .icon-yue {
  color: #f9b711;
}
.shift_content-item > .icon-qita {
  color: #725aa3;
}
</style>
