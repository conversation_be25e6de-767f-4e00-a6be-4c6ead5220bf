<template>
  <div class="CashierRecord">
    <CashierHeader></CashierHeader>
    <div class="shift_head">
      <div class="head_back">
        <span style="margin-left: 6px; color: #999999" @click="$router.push('/CashierShop')"> 返回收银首页 </span>
        <span class="head_line"></span>
        <span>我的交班记录</span>
      </div>
      <div>
        <el-row>
          <el-col :span="7" style="padding: 0">
            <div class="grid-content bg-purple record">
              <div style="height: calc(100vh - 140px)">
                <el-tabs v-model="activeName" class="el-tabs__item el_tabs--top" @tab-click="handleClick">
                  <el-tab-pane label="近7天" name="first"></el-tab-pane>
                  <el-tab-pane label="近30天" name="second"></el-tab-pane>
                  <el-tab-pane label="全部" name="third"></el-tab-pane>
                </el-tabs>
                <div v-if="!record_list.length" class="record_tip">暂无交班记录</div>
                <div v-for="(item, index) in record_list" :key="index" class="record_content" @click="recordFn(item)">
                  <p style="padding-bottom: 10px">时间:{{ $_common.formatDate(item.createTime) }}</p>
                  <div>
                    <span>收银员：{{ item.staffName }}</span>
                    <div style="float: right; padding-right: 20px">
                      <span>收款金额：</span>
                      <span style="color: red"> ￥{{ item.collectionMoney }} </span>
                    </div>
                  </div>
                </div>
                <FooterPage
                  :page-size="pageSize"
                  :total-page.sync="total"
                  :current-page.sync="page"
                  @pageChange="pageChange"
                  @sizeChange="sizeChange"
                ></FooterPage>
              </div>
            </div>
          </el-col>
          <el-col :span="17" style="padding: 0 0 0 1px">
            <div class="grid-content bg-purple">
              <div
                v-if="JSON.stringify(connect_detail) === '{}'"
                style="height: calc(100vh - 140px)"
                class="record_tip"
              >
                未选中任何交班记录
              </div>
              <div v-else class="grid-content">
                <div style="padding: 20px 0 20px 20px; font-size: 14px">
                  <span>收银员：{{ connect_detail.staffName }}</span>
                  <span style="padding-left: 20px"> 手机号：{{ connect_detail.mobile }} </span>
                  <span style="padding-left: 20px"> 时间：{{ $_common.formatDate(connect_detail.createTime) }} </span>
                  <span style="padding-left: 20px"> 上班时长：{{ connect_detail.hours || 0 }} </span>
                  <span style="padding-left: 20px"> 备注：{{ connect_detail.remark || "无" }} </span>
                </div>
                <ul class="shift_content">
                  <li>
                    <div class="shift_content_title">
                      <span>收款金额</span>
                      <span class="shift_content_price"> ￥{{ connect_detail.collectionMoney || "0.00" }} </span>
                    </div>
                    <div class="shift_content-main">
                      <div class="shift_content-item">
                        <i class="iconfont icon-yingyeejilu"></i>
                        <span class="shift_content_write">现金</span>
                        <p>￥{{ connect_detail.collectionData.cash || "0.00" }}</p>
                      </div>
                      <div class="shift_content-item">
                        <i class="iconfont icon-wx-pay"></i>
                        <span class="shift_content_write">微信</span>
                        <p>￥{{ connect_detail.collectionData.wechat || "0.00" }}</p>
                      </div>
                      <div class="shift_content-item">
                        <i class="iconfont icon-umidd17"></i>
                        <span class="shift_content_write">支付宝</span>
                        <p>￥{{ connect_detail.collectionData.alipay || "0.00" }}</p>
                      </div>
                      <div class="shift_content-item">
                        <i class="iconfont icon-yue"></i>
                        <span class="shift_content_write">余额</span>
                        <p>￥{{ connect_detail.collectionData.balance || "0.00" }}</p>
                      </div>
                      <div class="shift_content-item">
                        <i class="iconfont icon-qita"></i>
                        <span class="shift_content_write">其他</span>
                        <p>￥{{ connect_detail.collectionData.other || "0.00" }}</p>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="shift_content_title">
                      <span>充值金额</span>
                      <span class="shift_content_price"> ￥{{ connect_detail.rechargeMoney || "0.00" }} </span>
                    </div>
                    <div class="shift_content-main">
                      <div class="shift_content-item">
                        <i class="iconfont icon-yingyeejilu"></i>
                        <span class="shift_content-item">现金</span>
                        <p>￥{{ connect_detail.refundData.cash || "0.00" }}</p>
                      </div>
                      <div class="shift_content-item">
                        <i class="iconfont icon-wx-pay"></i>
                        <span class="shift_content_write">微信</span>
                        <p>￥{{ connect_detail.refundData.wechat || "0.00" }}</p>
                      </div>
                      <div class="shift_content-item">
                        <i class="iconfont icon-umidd17"></i>
                        <span class="shift_content_write">支付宝</span>
                        <p>￥{{ connect_detail.refundData.alipay || "0.00" }}</p>
                      </div>
                      <div class="shift_content-item">
                        <i class="iconfont icon-qita"></i>
                        <span class="shift_content_write">其他</span>
                        <p>￥{{ connect_detail.refundData.other || "0.00" }}</p>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="shift_content_title">
                      <span>退款金额</span>
                      <span class="shift_content_price"> ￥{{ connect_detail.refundMoney || "0.00" }} </span>
                    </div>
                    <div class="shift_content-main">
                      <div class="shift_content-item">
                        <i class="iconfont icon-yingyeejilu"></i>
                        <span class="shift_content_write">现金</span>
                        <p>￥{{ connect_detail.refundData.cash || "0.00" }}</p>
                      </div>
                      <div class="shift_content-item">
                        <i class="iconfont icon-wx-pay"></i>
                        <span class="shift_content_write">微信</span>
                        <p>￥{{ connect_detail.refundData.wechat || "0.00" }}</p>
                      </div>
                      <div class="shift_content-item">
                        <i class="iconfont icon-umidd17"></i>
                        <span class="shift_content_write">支付宝</span>
                        <p>￥{{ connect_detail.refundData.alipay || "0.00" }}</p>
                      </div>
                      <div class="shift_content-item">
                        <i class="iconfont icon-yue"></i>
                        <span class="shift_content_write">余额</span>
                        <p>￥{{ connect_detail.refundData.balance || "0.00" }}</p>
                      </div>
                    </div>
                  </li>
                </ul>
                <div class="clearfix shift_content_btn">
                  <div class="float_right">
                    <span>现金合计：</span>
                    <span style="color: red">
                      ￥{{
                        $NP.plus(
                          connect_detail.collectionMoney || 0,
                          connect_detail.rechargeMoney || 0,
                          connect_detail.refundMoney || 0
                        )
                      }}
                    </span>
                  </div>
                  <!--                  <div class="float_right">-->
                  <!--                    <el-button type="primary">-->
                  <!--                      打印交班小票-->
                  <!--                    </el-button>-->
                  <!--                  </div>-->
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import CashierHeader from "./CashierHeader.vue";
import { getRecordList, getRecordInfo } from "@/api/Cashier";
export default {
  name: "CashierRecord",
  components: {
    CashierHeader,
  },
  data() {
    return {
      activeName: "first",
      record_list: [],
      connect_detail: {},
      page: 1,
      pageSize: 10,
      total: 0,
      startTime: "",
      endTime: "",
    };
  },
  created() {
    this.startTime = parseInt(new Date().getTime() / 1000);
    this.endTime = this.$_common.dateToStamp(this.$_common.funDate(-7));
    this.getRecordList();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getRecordList();
  },
  methods: {
    async getRecordList() {
      const data = await getRecordList({
        page: this.page,
        pageSize: this.pageSize,
        startTime: this.startTime,
        endTime: this.endTime,
        userCenterId: "",
        isSelf: 5,
      });

      this.record_list = data.data;
      this.total = data.pageTotal;
    },
    handleClick(tab, event) {
      switch (this.activeName) {
        case "first":
          this.startTime = parseInt(new Date().getTime() / 1000);
          this.endTime = this.$_common.dateToStamp(this.$_common.funDate(-7));
          break;
        case "second":
          this.startTime = parseInt(new Date().getTime() / 1000);
          this.endTime = this.$_common.dateToStamp(this.$_common.funDate(-30));
          break;
        case "third":
          this.startTime = "";
          this.endTime = "";
          break;
      }
      this.page = 1;
      this.getRecordList();
    },
    pageChange(val) {
      this.page = val;
      this.getRecordList();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    async recordFn(item) {
      const data = await getRecordInfo(item.id);

      this.connect_detail = data.data;
    },
  },
};
</script>
<style>
.record .el-tabs__item {
  height: 60px !important;
  line-height: 60px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}
.record .el_tabs--top {
  width: 100%;
}
</style>
<style scoped>
.CashierRecord {
  background-color: #f7f7f7;
  height: 99vh;
  width: 100%;
}
.shift_head {
  width: 100%;
  margin-top: 20px;
}
.head_back {
  background-color: #fff;
  color: #2a75ed;
  font-size: 14px;
  cursor: pointer;
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #ededed;
  padding-left: 20px;
}

.head_line {
  height: 24px;
  width: 1px;
  background-color: #ededed;
  display: inline-block;
  margin: 6px 10px -8px 10px;
}

.grid-content {
  background-color: #fff;
  padding: 0;
}
.shift_content {
  height: calc(100vh - 200px);
  border-bottom: 1px solid #ededed;
}
.shift_content_title {
  line-height: 62px;
  font-size: 16px;
  font-weight: bold;
}
.shift_content_price {
  color: #ff4040;
  padding-left: 10px;
}
.record_content {
  font-size: 14px;
  border-bottom: 1px solid #ededed;
  color: #333333;
  padding: 10px 16px;
  cursor: pointer;
}
.record_tip {
  font-size: 12px;
  text-align: center;
  color: #b2b2b2;
  line-height: 600px;
}

.shift_content {
  height: calc(100vh - 260px);
  border-bottom: 1px solid #ededed;
}

.shift_content > li {
  padding: 0 40px;
  margin-bottom: 40px;
}

.shift_content_one {
  height: 60px;
  line-height: 60px;
  padding: 0 30px;
  border-bottom: 1px solid #ededed;
}

.shift_content_btn {
  background: #ffffff;
  padding: 8px;
  padding-right: 20px;
  line-height: 48px;
}

.shift_content_title {
  line-height: 62px;
  font-size: 16px;
  font-weight: bold;
}

.shift_content_price {
  color: #fb6638;
  padding-left: 10px;
}

.shift_content-main {
  display: flex;
  flex-wrap: wrap;
}

.shift_content-item {
  width: 20%;
  max-width: 300px;
  line-height: 46px;
  font-size: 18px;
}
.shift_content-item > .iconfont {
  font-size: 24px;
  margin-right: 10px;
  vertical-align: middle;
}
.shift_content-item > .shift_content_write {
  font-size: 14px;
}
.shift_content-item > .icon-yingyeejilu {
  color: rgb(255, 97, 85);
}
.shift_content-item > .icon-wx-pay {
  color: rgb(89, 182, 76);
}
.shift_content-item > .icon-umidd17 {
  color: #009fe8;
}
.shift_content-item > .icon-yue {
  color: #f9b711;
}
.shift_content-item > .icon-qita {
  color: #725aa3;
}
</style>
