<template>
  <div class="title_info clearfix">
    <div class="float_left" style="margin-left: 40px; font-weight: bold">
      <span>收银台</span>
      <span v-if="shopName">({{ shopName }})</span>
    </div>
    <div style="margin-right: 40px; float: right">
      <span style="padding-right: 20px; cursor: pointer">
        <full-screen :show-text="true"></full-screen>
      </span>
      <el-dropdown @command="handleCommand">
        <span class="el-dropdown-link">
          {{ userName || "未命名" }}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <!--          <el-dropdown-item command="userInfo">个人信息</el-dropdown-item>-->
          <el-dropdown-item command="editFn">收银交班</el-dropdown-item>
          <el-dropdown-item command="loginOut">退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
export default {
  name: "CashierHeader",
  props: {
    shopName: {
      type: String,
      default: "",
    },
  },
  methods: {
    ...mapActions({
      closeAllRoute: "closeAllRoute",
    }),
    handleCommand(command) {
      if (command === "userInfo") {
        // this.$router.push('/CashierShop/CashierShift')
      } else if (command === "editFn") {
        this.$router.push("/CashierShift");
      } else if (command === "loginOut") {
        this.loginOut();
      }
    },
    loginOut() {
      this.$confirm("确定要退出登录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        sessionStorage.clear();
        setTimeout(() => {
          sessionStorage.setItem("isCashier", "1");
          this.$router.push("/CashierLogin");
        }, 200);
        this.$router.push("/CashierLogin");
      });
    },
  },
};
</script>

<style scoped>
.el-dropdown-link {
  cursor: pointer;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.title_info {
  background-color: #fff;
  height: 62px;
  line-height: 62px;
  margin-bottom: 20px;
}
</style>
