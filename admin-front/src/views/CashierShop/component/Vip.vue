<template>
  <div>
    <div v-if="vip_login === false">
      <div class="tip">
        请输入会员手机号查询会员，或输入要办理会员的手机号进行办理
        <!--                    <p>(若会员未绑定手机号，可通过扫码枪扫描/输入会员码登录会员)</p>-->
      </div>
      <div class="keyboard">
        <div class="keyboard-input keyboard-input-wrapper">
          <input
            v-model="input"
            type="text"
            placeholder="输入会员手机号"
            autocomplete
            spellcheck="false"
            class="input-input input-large"
          />
        </div>
        <keyboard @click="numFn"></keyboard>
      </div>
    </div>
    <div v-if="vip_handle === false && vip_login === true" class="change-price">
      <div class="vip_title">
        <p class="color_size">
          <el-button type="text">
            {{ input }}
          </el-button>
          还不是会员，可快速办理新会员
        </p>
        <div>
          <span style="margin-right: 20px">会员姓名</span>
          <span>
            <el-input v-model="vip.name" clearable style="width: 26%"></el-input>
          </span>
        </div>
        <div>
          <span style="margin-right: 20px">会员生日</span>
          <span>
            <el-date-picker
              v-model="vip.birthday"
              style="width: 26%"
              default-value="1990-01-01"
              type="date"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="timestamp"
            ></el-date-picker>
          </span>
        </div>
        <div>
          <span style="margin-right: 20px">会员类型</span>
          <span>
            <el-select v-model="vip.type" style="width: 26%" placeholder="客户类型">
              <el-option
                v-for="item in customerType_list"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </span>
        </div>
      </div>
      <div class="btn-group" style="bottom: 40px; height: auto">
        <el-button @click="vip_login = false">取消</el-button>
        <el-button type="primary" @click="addCustomer">确认办理</el-button>
      </div>
    </div>
    <div v-if="vip_handle === true">
      <div>
        <div style="margin: 10px 0 0 10px; position: relative; color: #333333; height: 80px">
          <div style="display: inline-block">
            <el-row class="demo-avatar demo-basic">
              <el-col :span="12">
                <div class="demo-basic--circle">
                  <div class="block">
                    <el-avatar v-if="vip_info.avatar" :size="50" :src="vip_info.avatar"></el-avatar>
                    <el-avatar v-else icon="el-icon-user-solid"></el-avatar>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
          <div style="display: inline-block; margin-left: 20px; line-height: 20px; position: absolute; top: 6px">
            <div>
              <span>{{ vip_info.name }}</span>
              <span class="vip_name">默认会员</span>
            </div>
            <div>
              {{ vip_info.mobile }}
            </div>
          </div>
          <div style="float: right; margin-right: 20px">
            <el-button @click="changeUser">切换会员</el-button>
          </div>
        </div>
        <div class="property">
          <!--                      <div>-->
          <!--                        <p class="title">-->
          <!--                          余额（元）-->
          <!--                        </p>-->
          <!--                        <p class="price">-->
          <!--                          {{ vip_info.money||'0.00' }}-->
          <!--                          <span-->
          <!--                            class="price_text"-->
          <!--                            @click="reCharge=true"-->
          <!--                          >充值</span>-->
          <!--                        </p>-->
          <!--                      </div>-->
          <!--                      <div>-->
          <!--                        <p class="title">-->
          <!--                          积分-->
          <!--                        </p>-->
          <!--                        <p class="price">-->
          <!--                          0-->
          <!--                          <span-->
          <!--                            class="price_text"-->
          <!--                            @click="integral=true"-->
          <!--                          >充值</span>-->
          <!--                        </p>-->
          <!--                      </div>-->
          <!--                      <div>-->
          <!--                        <p class="title">-->
          <!--                          优惠券-->
          <!--                        </p>-->
          <!--                        <p class="price">-->
          <!--                          0-->
          <!--                        </p>-->
          <!--                      </div>-->
          <!--                      <div>-->
          <!--                        <p class="title">-->
          <!--                          会员卡-->
          <!--                        </p>-->
          <!--                        <p class="price">-->
          <!--                          无-->
          <!--                        </p>-->
          <!--                      </div>-->
          <div>
            <p class="title">消费金额（元）</p>
            <p class="price">
              {{ vip_info.totalPayMoney || "0.00" }}
            </p>
          </div>
          <div>
            <p class="title">订单数</p>
            <p class="price">
              {{ vip_info.orderNum || "0" }}
            </p>
          </div>
          <!--                      <div>-->
          <!--                        <p class="title">-->
          <!--                          维权订单数-->
          <!--                        </p>-->
          <!--                        <p class="price">-->
          <!--                          0-->
          <!--                        </p>-->
          <!--                      </div>-->
          <!--                      <div>-->
          <!--                        <p class="title">-->
          <!--                          退款金额（元）-->
          <!--                        </p>-->
          <!--                        <p class="price">-->
          <!--                          0-->
          <!--                        </p>-->
          <!--                      </div>-->
        </div>
        <div style="height: 62px; line-height: 62px; color: #333; font-weight: bold">
          <p>会员信息</p>
        </div>
        <div class="member_info">
          <el-form :model="vip_info" label-width="120px" size="small">
            <el-form-item label="会员注册时间：">
              {{ $_common.formatDate(vip_info.createTime) }}
            </el-form-item>
            <!--                        <el-form-item label="生日：">-->
            <!--                          {{ vip_info.birthday?$_common.formatDate(vip_info.birthday*1000,'yyyy-MM-dd'):'&#45;&#45;' }}-->
            <!--                        </el-form-item>-->
            <el-form-item v-if="vip_info.defaultAddress.area" label="默认收货地址：">
              {{ vip_info.defaultAddress.area.provinceName }}{{ vip_info.defaultAddress.area.cityName
              }}{{ vip_info.defaultAddress.area.districtName }}{{ vip_info.defaultAddress.address }}
            </el-form-item>
            <!--                        <el-form-item label="备注：">-->
            <!--                          {{ vip_info.remark||'-' }}-->
            <!--                        </el-form-item>-->
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Keyboard from "./Keyboard";
import { getCartByUser, searchCustomerDetails } from "@/api/Cashier";
import { getCustomerSourceList } from "@/api/System";
import { addCustomer } from "@/api/Customer";
export default {
  name: "Vip",
  components: {
    Keyboard,
  },
  props: {
    vipLogin: {
      type: Boolean,
      default: false,
    },
    vipHandle: {
      type: Boolean,
      default: false,
    },
    numList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    nowSelShop: {
      type: Object,
      default: () => {
        return {};
      },
    },
    reCharge: {
      type: Boolean,
      default: false,
    },
    collection: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      vip_login: false,
      vip_handle: false,
      input: "",
      customerId: "",
      customerName: "",
      customerType: "",
      userCenterId: "",
      vip_info: {
        createTime: "",
        birthday: "",
        price: "",
        integral: "",
        remarks: "",
        defaultAddress: {
          area: {
            provinceName: "",
            cityName: "",
            districtName: "",
            address: "",
          },
        },
        remark: "",
      },
      vip: {
        type: "",
        name: "",
        birthday: "",
      },
      customerType_list: [], // 客户类型列表
    };
  },
  created() {
    this.vip_handle = this.vipHandle;
    this.vip_login = this.vipLogin;
    this.re_charge = this.reCharge;
    this.getAllCustomerSource();
  },
  methods: {
    // 请输入会员手机号查询会员
    async numFn(e, num) {
      if (num !== "x" || num !== "清除" || num !== "确定") {
        this.numList.push(num);
      }
      if (num === "x") {
        this.input = this.input.substring(0, this.input.length - 1);
      } else if (num === "清除") {
        this.input = "";
        this.numList = [];
      } else if (num === "确定") {
        this.searchCustomerDetails();
      } else {
        this.input += num;
      }
    },
    changeUser() {
      this.collection = false;
      this.re_charge = false;
      this.vip_login = false;
      this.vip_handle = false;
      this.userCenterId = "";
      this.customerId = "";
      this.customerName = "";
      this.customerType = "";
      this.vip_info = {
        createTime: "",
        birthday: "",
        price: "",
        integral: "",
        remarks: "",
        defaultAddress: {
          area: {
            provinceName: "",
            cityName: "",
            districtName: "",
            address: "",
          },
        },
        remark: "",
      };
    },
    // 查询会员信息
    async searchCustomerDetails() {
      if (!this.input) {
        this.$message.warning("请输入会员手机号");
        return;
      }
      const re = /^1[3456789]\d{9}$/; // 正则表达式
      if (!re.test(this.input)) {
        this.$message.warning("手机号格式有误，请重新输入!");
        return;
      }
      const data = await searchCustomerDetails({
        mobile: this.input,
      });

      if (JSON.stringify(data.data) === "{}") {
        // this.$message.error('未查找到会员信息')
        this.vip_handle = false;
        this.vip_login = true;
        this.userCenterId = "";
        this.customerId = "";
        this.customerName = "";
        this.customerType = "";
        this.vip_info = {};
      } else {
        this.vip_handle = true;
        this.vip_login = true;
        this.vip_info = data.data;
        this.$emit("vipInfo", this.vip_info);
        this.userCenterId = data.data.userCenterId;
        this.customerId = data.data.customerId;
        this.customerName = data.data.name;
        this.customerType = data.data.customerType;
        this.$emit("getCartByUser", this.userCenterId);
      }
    },
    //  获取客户类型
    async getAllCustomerSource() {
      if (this.customerType_list.length) {
        return;
      }
      const data = await getCustomerSourceList();

      this.customerType_list = data.data;
      const defaultData = data.data.find((item) => item.defaultStatus === 5);
      if (defaultData) {
        this.vip.type = defaultData.id;
      } else {
        this.vip.type = data.data[0].id;
      }
    },
    // 新增会员
    async addCustomer() {
      const data = await addCustomer({
        birthday: parseInt(this.vip.birthday / 1000),
        shopId: this.nowSelShop.id,
        mobile: this.input,
        name: this.vip.name,
        type: this.vip.type,
      });

      this.searchCustomerDetails();
    },
  },
};
</script>

<style scoped>
.tip {
  font-size: 16px;
  color: #666;
  width: 580px;
  margin: 80px auto 20px;
  line-height: 20px;
}

.tip p {
  color: #999;
  font-size: 14px;
  margin: 5px 0;
}
.keyboard {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.keyboard-input {
  width: 590px;
  height: 60px;
  /*margin-left: 296px;*/
  position: relative;
  text-align: center;
}
.keyboard-input-wrapper {
  display: inline-block;
  vertical-align: middle;
  line-height: normal;
}
.input-input {
  height: 60px;
  font-size: 18px;
  padding: 6px 18px;
  display: inline-block;
  width: 590px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  color: #808695;
  cursor: text;
}
.change-price {
  position: relative;
  height: calc(99vh - 130px);
  overflow: auto;
}
.change-price .btn-group {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  text-align: right;
}
.vip_title {
  color: #333333;
  margin-top: 52px;
  text-align: center;
  font-size: 14px;
}
.color_size {
  font-size: 12px;
  color: #999;
}
.btn-group {
  padding: 10px 20px;
  border-top: 1px solid #f2f2f2;
  text-align: right;
  background-color: #ffffff;
}
.vip_name {
  margin-left: 12px;
  background: rgb(236, 245, 255);
  color: #409eff;
  padding: 2px 5px;
  font-size: 10px;
}
.property {
  height: 106px;
  background: #f5f6fa;
  border-radius: 4px;
  color: #333;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  line-height: 30px;
}

.property div {
  width: 25%;
  padding-left: 20px;
}

.title {
  font-size: 14px;
  margin-bottom: 2px;
}
.price {
  font-size: 26px;
  font-weight: 800;
}

.member_info {
  background: #f5f6fa;
  padding: 22px;
  font-size: 12px;
  color: #333;
}
</style>
