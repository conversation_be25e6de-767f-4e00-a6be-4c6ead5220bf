<template>
  <div>
    <div class="right-main">
      <p class="tip">输入收款金额，金额加入结算清单中进行结算。</p>
      <div class="input-box">
        <input
          v-model="input_two"
          type="text"
          placeholder="请输入收款金额"
          autocomplete="off"
          spellcheck="false"
          class="input-input"
        />
        <div class="input-unit" style="color: #333333">元</div>
      </div>
      <div class="input-box" style="margin-top: 10px">
        <input
          v-model="accountName"
          type="text"
          readonly
          placeholder="请选择结算账户"
          autocomplete="off"
          spellcheck="false"
          class="input-input"
        />
        <div class="input-unit" style="color: #666" @click="openAccountModel()">
          <i class="el-icon-circle-check"></i>
          点击选择
        </div>
      </div>
      <keyboard @click="numFnTwo($event)"></keyboard>
    </div>
    <AccountType
      v-if="account_show"
      :id="nowSelShop.id"
      :is-check="false"
      :is-show="account_show"
      @cancel="account_show = false"
      @confirm="accountsel"
    />
  </div>
</template>

<script>
import Keyboard from "./Keyboard";
import AccountType from "../../Finance/AccountType";
import { addReceived } from "@/api/Finance";
export default {
  name: "AccountPrice",
  components: {
    Keyboard,
    AccountType,
  },
  props: {
    nowSelShop: {
      type: Object,
      default: () => {
        return {};
      },
    },
    numList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    customerName: {
      type: String,
      default: "",
    },
    customerId: {
      type: [String, Number],
      default: 0,
    },
    money: {
      type: [String, Number],
      default: 0,
    },
    input: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      input_two: "",
      account_show: false,
      pay_account_num: "", // 收款结算账户
      pay_account_id: "", // 收款结算账户id
      pay_account_name: "", // 收款结算账户name
      account_num: "", // 结算账户
      accountName: "", // 结算账户name
      account_id: "", // 结算账户id
      activeName: "second",
      target_num: "",
    };
  },
  created() {
    this.num_list = this.numList;
  },
  methods: {
    // 直接收款输入键盘
    numFnTwo(e) {
      this.target_num = e.target.textContent;
      if (this.target_num !== "x" || this.target_num !== "清除" || this.target_num !== "确定") {
        this.num_list.push(this.target_num);
      }
      if (this.target_num === "x") {
        this.input_two = this.input_two.substring(0, this.input_two.length - 1);
      } else if (this.target_num === "清除") {
        this.input_two = "";
        this.num_list = [];
      } else if (this.target_num === "确定") {
        if (!parseInt(this.input_two)) {
          this.$message.warning("请输入收款金额");
          return;
        }
        this.addReceived();
      } else {
        this.input_two += this.target_num;
      }
    },
    // 打开结算账户
    openAccountModel(type) {
      this.pay_account_type = type;
      this.account_show = true;
    },

    // 选择结算账户
    accountsel(val) {
      if (this.pay_account_type) {
        this.pay_account_id = val[0].id;
        this.pay_account_num = val[0].accountNumber;
        this.pay_account_name = val[0].name;
      } else {
        this.account_id = val[0].id;
        this.account_num = val[0].accountNumber;
        this.accountName = val[0].name;
      }
    },
    // 直接收款
    addReceived() {
      if (!this.account_id) {
        this.$message.warning("请选择结算账户");
        return;
      }
      if (!this.userCenterId) {
        this.$confirm("订单未绑定会员，是否需要绑定会员进行结算?", "提示", {
          confirmButtonText: "会员登录",
          cancelButtonText: "跳过",
          type: "warning",
        })
          .then(() => {
            this.activeName = "second";
            this.$emit("activeName", this.activeName);
          })
          .catch(() => {
            this.addReceivedTwo();
          });
      } else {
        this.addReceivedTwo();
      }
    },
    async addReceivedTwo() {
      const accountList = [
        {
          accountId: this.account_id,
          accountNumber: this.account_num,
          accountName: this.accountName,
          money: this.input_two,
          discountMoney: 0,
          finalMoney: this.input_two,
          payWay: 4,
          remark: "",
        },
      ];
      const params = {
        customerId: this.customerId,
        customerName: this.customerName,
        sourceNo: "",
        sourceNoMoney: "",
        currentAccountName: this.userName,
        financeType: "销售收款",
        financeTypeId: 2,
        shopId: this.nowSelShop.id,
        money: this.money,
        shopName: this.nowSelShop.name,
        receiptTime: parseInt(new Date() / 1000),
        createTime: "",
        accountList: accountList,
      };

      const data = await addReceived(params);

      this.$message.success("收款成功");
      if (this.input) {
        this.$emit("searchCustomerDetails");
      }
      this.input_two = "";
    },
  },
};
</script>

<style scoped>
.right-main {
  height: calc(99vh - 166px);
  overflow: auto;
}
.tip {
  font-size: 16px;
  color: #666;
  width: 580px;
  margin: 80px auto 20px;
  line-height: 20px;
}

.tip p {
  color: #999;
  font-size: 14px;
  margin: 5px 0;
}
.input-box {
  width: 590px;
  margin: 0 auto;
  position: relative;
}
.input-input {
  height: 60px;
  font-size: 18px;
  padding: 6px 18px;
  display: inline-block;
  width: 590px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  color: #808695;
  cursor: text;
}
.input-unit {
  position: absolute;
  right: 24px;
  font-size: 18px;
  top: 50%;
  transform: translateY(-50%);
}
.input-box {
  width: 590px;
  margin: 0 auto;
  position: relative;
}
</style>
