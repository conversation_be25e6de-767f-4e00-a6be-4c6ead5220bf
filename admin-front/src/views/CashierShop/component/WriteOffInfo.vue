<template>
  <div>
    <div v-if="tipNum === 2">
      <div class="tip_info">
        <p style="margin: 0">订单详情</p>
      </div>
      <div>
        <div class="order-detail-box">
          <p v-if="!writeoffList.goodsData || !writeoffList.goodsData.length" class="tips">请输入核销码搜索核销订单</p>
          <div v-else>
            <div class="write-off-top">
              <div class="shop_time" style="width: 100%">
                <div v-for="(itemP, indexP) in writeoffList.goodsData" :key="indexP" class="group">
                  <p class="group_line">
                    {{ itemP.goodsName }}
                  </p>
                  <p class="color_size">
                    {{ itemP.unitName || "" }}
                    {{ itemP.specValueName || "" }}
                  </p>
                  <div class="clearfix">
                    <span class="float_left price-color"> ￥{{ itemP.price }} </span>
                    <span class="float_right">x{{ itemP.buyNum }}</span>
                  </div>
                </div>
              </div>
              <div class="o-price-info">
                <div>
                  <span class="op-label">商品总计：</span>
                  <span class="op-val">￥{{ writeoffList.totalMoney }}</span>
                </div>
                <div>
                  <span class="op-label">优惠：</span>
                  <span class="op-val"> -￥{{ writeoffList.orderPreferential }} </span>
                </div>
                <div>
                  <span class="op-label">实付：</span>
                  <span class="op-val">￥{{ writeoffList.payAmount }}</span>
                </div>
              </div>
            </div>
            <div class="comment_info">
              <p>订单编号：{{ writeoffList.no }}</p>
              <p>
                订单类型：
                {{ writeoffList.orderType === 1 ? "销售订单" : "其他" }}
              </p>
              <p>
                订单来源：{{
                  parseInt(writeoffList.source) === 1
                    ? "ios"
                    : parseInt(writeoffList.source) === 2
                    ? "安卓"
                    : parseInt(writeoffList.source) === 3
                    ? "小程序"
                    : parseInt(writeoffList.source) === 4
                    ? "后台创建"
                    : parseInt(writeoffList.source) === 5
                    ? "H5页面"
                    : parseInt(writeoffList.source) === 6
                    ? "pc页面"
                    : parseInt(writeoffList.source) === 8
                    ? "字节跳动小程序"
                    : ""
                }}
              </p>
              <p>
                支付方式：{{
                  parseInt(writeoffList.payType) === 1
                    ? "微信支付"
                    : parseInt(writeoffList.payType) === 2
                    ? "支付宝"
                    : parseInt(writeoffList.payType) === 3
                    ? "货到付款"
                    : parseInt(writeoffList.payType) === 4
                    ? "上门自提"
                    : parseInt(writeoffList.payType) === 5
                    ? "现金"
                    : parseInt(writeoffList.payType) === 6
                    ? "其他"
                    : ""
                }}
              </p>
              <p>下单时间：{{ $_common.formatDate(writeoffList.createTime) }}</p>
              <p>付款时间：{{ $_common.formatDate(writeoffList.payTime) }}</p>
              <p>买家姓名：{{ writeoffList.customerName }}</p>
              <p>手机号码：{{ writeoffList.customerMobile || "--" }}</p>
            </div>
          </div>
        </div>
        <div v-if="writeoffList.goodsData && writeoffList.goodsData.length" class="button-group">
          <el-button type="primary" @click="primaryOrder">确认核销</el-button>
        </div>
      </div>
      <!--            <div-->
      <!--              class="tips"-->
      <!--              v-else-->
      <!--            >-->
      <!--              未选中任何订单-->
      <!--            </div>-->
    </div>
  </div>
</template>

<script>
import { OrderVerification } from "@/api/Order";

export default {
  name: "WriteOffInfo",
  props: {
    tipNum: {
      type: [Number, String],
      default: 2,
    },
    writeoffList: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  methods: {
    async primaryOrder() {
      const data = await OrderVerification(this.writeoffList.id);
      this.$message.success("核销成功");
    },
  },
};
</script>

<style scoped>
.tip_info {
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2;
  height: 60px;
  line-height: 60px;
  padding-left: 16px;
}
.order-detail-box {
  height: calc(99vh - 210px);
  overflow: auto;
}
.tips {
  padding-top: 200px;
  text-align: center;
  color: #b2b2b2;
  font-size: 12px;
  font-weight: 500;
}
.write-off-top {
  border-bottom: 1px solid #f2f2f2;
  padding: 0 10px;
}
.shop_time {
  font-size: 14px;
  padding: 10px 0 0 10px;
  line-height: 20px;
  display: inline-block;
}
.group {
  width: 222px;
  height: 98px;
  background: #f5f6fa;
  border-radius: 4px;
  margin-right: 20px;
  padding: 16px;
}
.group_line {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.color_size {
  font-size: 12px;
  color: #999;
}
.price-color {
  color: #f56c6c;
}
.o-price-info {
  padding: 24px 20px;
}

.o-price-info > div {
  height: 32px;
  line-height: 32px;
  text-align: right;
}

.o-price-info > div .op-label {
  width: 150px;
}

.o-price-info > div .op-val {
  width: 150px;
}
.comment_info {
  padding: 20px;
  padding-bottom: 0;
}

.comment_info p {
  line-height: 36px;
}
.button-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 8px;
  border-top: 1px solid #f2f2f2;
}
</style>
