<template>
  <div v-if="tipNum === 3">
    <div class="tip_info" style="border-bottom: 0 none">
      <p style="margin: 0">订单详情</p>
    </div>
    <div v-if="JSON.stringify(orderDetail) === '{}'">
      <p class="tips">未选中任何订单</p>
    </div>
    <div v-else>
      <div class="order-detail-box">
        <p :class="[parseInt(orderDetail.payStatus) === 5 ? 'info-on' : '']" class="info_state">
          {{
            parseInt(orderDetail.payStatus) === 4 ? "等待付款" : parseInt(orderDetail.payStatus) === 5 ? "已支付" : ""
          }}
          <i
            class="pay-icon"
            :class="[parseInt(orderDetail.payStatus) === 5 ? 'el-icon-circle-check' : 'el-icon-time']"
          ></i>
        </p>
        <div class="order-detail-top">
          <div class="clearfix">
            <div class="float_left">买家：{{ orderDetail.customerName }}</div>
            <div class="float_right">手机号：{{ orderDetail.customerMobile || "--" }}</div>
          </div>
          <p>收银员：{{ orderDetail.cashierName }}</p>
          <p>导购员：{{ orderDetail.guideName }}</p>
        </div>
        <div class="o-goods-div">
          <ul class="o-goods-ul">
            <li v-for="(item, index) in orderDetail.goodsData" :key="index" class="o-goods-li clearfix">
              <div class="float_left o-goods-img">
                <img :src="item.images[0]" alt="" />
              </div>
              <div class="float_left o-goods-info">
                <div class="gi-div clearfix">
                  <span class="og-name float_left">
                    {{ item.goodsName }}
                  </span>
                  <span class="og-price float_right">¥{{ item.price }}</span>
                </div>
                <div class="gi-div clearfix">
                  <div class="og-name float_left sku-name">
                    {{ item.unitName }}
                    <span v-for="(itemS, indexS) in item.specGroup" :key="indexS">
                      <span v-if="index > 0">_</span>
                      {{ itemS.specValueName }}
                    </span>
                  </div>
                  <div class="og-price float_right">x{{ item.buyNum }}</div>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="order-info">
          <p>订单编号：{{ orderDetail.no }}</p>
          <p>下单时间：{{ $_common.formatDate(orderDetail.createTime) }}</p>
          <p>支付时间：{{ parseInt(orderDetail.payStatus) === 5 ? $_common.formatDate(orderDetail.payTime) : "--" }}</p>
          <p>
            支付方式：
            {{
              parseInt(orderDetail.payType) === 1
                ? "微信支付"
                : parseInt(orderDetail.payType) === 2
                ? "支付宝"
                : parseInt(orderDetail.payType) === 3
                ? "货到付款"
                : parseInt(orderDetail.payType) === 4
                ? "上门自提"
                : parseInt(orderDetail.payType) === 5
                ? "现金"
                : parseInt(orderDetail.payType) === 6
                ? "其他"
                : ""
            }}
          </p>
        </div>
        <div class="o-reamark">
          <p>备注：{{ orderDetail.remark || "无备注" }}</p>
        </div>
        <div class="o-price-info">
          <div>
            <span class="op-label">商品金额：</span>
            <span class="op-val">￥{{ orderDetail.totalMoney }}</span>
          </div>
          <div>
            <span class="op-label">改价：</span>
            <span class="op-val">￥{{ orderDetail.changeAmount }}</span>
          </div>
          <div>
            <span class="op-label">实付金额：</span>
            <span class="op-val">￥{{ orderDetail.payAmount }}</span>
          </div>
        </div>
      </div>
      <div class="button-group">
        <el-button type="primary" plain @click="toPrint">打印小票</el-button>
        <el-button type="primary" @click="diaLog">备注</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { toPrint } from "@/api/common";

export default {
  name: "WriteOffOrder",
  props: {
    tipNum: {
      type: [Number, String],
      default: 2,
    },
    nowOrderId: {
      type: [Number, String],
      default: 0,
    },
    orderDetail: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  methods: {
    async toPrint() {
      const data = await toPrint({
        objectId: this.nowOrderId,
        objectType: 1, // 销售单
      });

      this.$message({
        type: "success",
        message: "操作成功",
      });
    },
    diaLog() {
      this.$emit("dialog");
    },
  },
};
</script>

<style scoped>
.order-info > p {
  line-height: 38px;
  width: 50%;
}

.o-price-info > div {
  height: 32px;
  line-height: 32px;
  text-align: right;
}

.o-price-info > div .op-label {
  width: 150px;
}

.o-price-info > div .op-val {
  width: 150px;
}
.o-price-info {
  padding: 24px 20px;
}
.o-reamark {
  padding: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-bottom: 1px solid #f2f2f2;
  white-space: pre-wrap;
  word-break: break-all;
}
.order-info {
  padding: 12px 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  border-bottom: 1px solid #f2f2f2;
}

.sku-name {
  font-size: 12px;
  color: #999;
}
.tip_info {
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2;
  height: 60px;
  line-height: 60px;
  padding-left: 16px;
}
.order-detail-box {
  height: calc(99vh - 210px);
  overflow: auto;
}
.tips {
  padding-top: 200px;
  text-align: center;
  color: #b2b2b2;
  font-size: 12px;
  font-weight: 500;
}
.o-price-info {
  padding: 24px 20px;
}

.o-price-info > div {
  height: 32px;
  line-height: 32px;
  text-align: right;
}

.o-price-info > div .op-label {
  width: 150px;
}

.o-price-info > div .op-val {
  width: 150px;
}

.comment_info p {
  line-height: 36px;
}
.button-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 8px;
  border-top: 1px solid #f2f2f2;
}
.info_state {
  height: 80px;
  line-height: 80px;
  padding-left: 20px;
  font-size: 20px;
  font-weight: bold;
  background-color: #fee8ea;
  color: #f1495c;
  position: relative;
  overflow: hidden;
}
.pay-icon {
  position: absolute;
  top: -35px;
  right: -40px;
  font-size: 146px;
  opacity: 0.2;
}
.order-detail-top {
  line-height: 48px;
  padding: 12px 24px;
  border-bottom: 1px solid #f7f7f7;
}
.o-goods-li {
  padding: 16px 20px;
  border-bottom: 1px solid #f2f2f2;
}

.o-goods-img {
  width: 68px;
  height: 68px;
  margin-right: 12px;
  -o-object-fit: contain;
  object-fit: contain;
}

.o-goods-img img {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 4px;
}
.gi-div {
  margin-top: 9px;
}
.info-on {
  background-color: #eaeeff;
  color: #4476ff;
}
</style>
