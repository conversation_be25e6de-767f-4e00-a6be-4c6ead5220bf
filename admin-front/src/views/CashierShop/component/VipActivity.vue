<template>
  <div>
    <el-table :data="discountList" style="line-height: 30px" size="mini">
      <el-table-column label="选择" min-width="140">
        <template slot-scope="scope">
          <el-checkbox v-model="scope.row.checked" @change="couponChange($event, scope.row)"></el-checkbox>
        </template>
      </el-table-column>
      <el-table-column label="类型" min-width="140">
        <template>优惠券</template>
      </el-table-column>
      <el-table-column prop="name" label="活动" min-width="180"></el-table-column>
      <el-table-column prop="reducePrice" label="面值" min-width="120">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.reducePrice) }}
        </template>
      </el-table-column>
      <el-table-column prop="minPrice" label="使用门槛" min-width="100">
        <template slot-scope="scope">
          {{ Number(scope.row.minPrice) === 0 ? "无门槛" : scope.row.minPrice }}
        </template>
      </el-table-column>
      <el-table-column label="有效期" min-width="180">
        <template slot-scope="scope">
          <span v-if="scope.row.couponType === 20">领取后30天内有效</span>
          <span v-else>
            {{ $_common.formatDate(scope.row.startTime, "yyyy-MM-dd") }}
            至
            {{ $_common.formatDate(scope.row.endTime, "yyyy-MM-dd") }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "VipActivity",
  props: {
    discountList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  methods: {
    couponChange(val, row) {
      this.$emit("couponChange", val, row);
    },
  },
};
</script>

<style scoped></style>
