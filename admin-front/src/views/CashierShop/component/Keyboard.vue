<template>
  <div class="keyboard-content" @click="numFnOne($event)">
    <div class="keyboard-left">
      <div>7</div>
      <div>8</div>
      <div>9</div>
      <div>4</div>
      <div>5</div>
      <div>6</div>
      <div>1</div>
      <div>2</div>
      <div>3</div>
      <div>{{ !lbNum ? "" : lbNum }}</div>
      <div>0</div>
      <div>.</div>
    </div>
    <div class="keyboard-right">
      <div style="color: #333333">
        <span>清除</span>
      </div>
      <div class="del" style="border-bottom: 0">
        <span style="display: block; color: #333333">x</span>
      </div>
      <div class="flex1">
        <span>确定</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Keyboard",
  props: {
    tipNum: {
      type: [String, Number],
      default: "",
    },
    lbNum: {
      type: [String, Number, Boolean],
      default: "-",
    },
  },
  data() {
    return {
      keyboard_key: "",
      input: "",
      target_num: "",
      num_list: [],
      writeoff_list: [],
    };
  },
  created() {
    console.log(this.tipNum);
  },
  methods: {
    numFnOne(e) {
      const num = e.target.textContent;
      if (num === "") return;
      if (num.length >= 3) return;
      this.$emit("click", e, num);
    },
  },
};
</script>

<style scoped>
.keyboard-input_one {
  width: 480px;
  height: 60px;
  margin: 0 auto;
}
.keyboard-input {
  width: 590px;
  height: 60px;
  /*margin-left: 296px;*/
  position: relative;
  text-align: center;
}
.keyboard-input-wrapper {
  display: inline-block;
  vertical-align: middle;
  line-height: normal;
}
.keyboard-input-input {
  height: 60px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  font-size: 18px;
  color: #808695;
  padding: 6px 18px;
}
.input-input {
  height: 60px;
  font-size: 18px;
  padding: 6px 18px;
  display: inline-block;
  width: 590px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  color: #808695;
  cursor: text;
}
.keyboard-content {
  width: 596px;
  height: 264px;
  margin: 24px auto 0;
  background: #f5f6fa;
  border: 1px solid #dcdee2;
  border-right: 0;
  border-bottom: 0;
  text-align: center;
  line-height: 66px;
  display: flex;
  box-sizing: content-box;
  cursor: pointer;
  user-select: none;
}

.keyboard-left {
  flex: 1;
}
.keyboard {
  display: flex;
  align-items: center;
  flex-direction: column;
}
.keyboard-left div {
  display: inline-block;
  width: 150px;
  height: 66px;
  border: 1px solid #dcdee2;
  border-left: 0;
  border-top: 0;
  vertical-align: bottom;
  color: #333333;
}

.keyboard-left_one div {
  display: inline-block;
  width: 117px;
  height: 66px;
  border: 1px solid #dcdee2;
  border-left: 0;
  border-top: 0;
  vertical-align: bottom;
}

.keyboard-right {
  flex-direction: column;
}

.keyboard-right div {
  height: 66px;
  line-height: 66px;
  width: 146px;
  border: 1px solid #dcdee2;
  border-left: 0;
  border-top: 0;
}
.keyboard-right .flex1 {
  height: 133px;
  line-height: 133px;
  background: #409eff;
  border-color: #409eff;
  color: #fff;
  flex: 1;
}
.keyboard-right_one div {
  width: 117px;
  border: 1px solid #dcdee2;
  border-left: 0;
  border-top: 0;
}
</style>
