<template>
  <div>
    <el-dialog
      width="30%"
      title="选择导购"
      :visible.sync="shop_guide"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-table
        ref="multipleTable"
        :data="table_data"
        style="width: 100%"
        tooltip-effect="dark"
        size="mini"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="staffName" label="已关联导购"></el-table-column>
        <el-table-column prop="name" label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="deleteGuide(scope.row.index)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="guideFn">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllStaffStaff } from "@/api/Department";

export default {
  name: "ShopGuide",
  props: {
    shopGuide: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      table_data: [], // 已关联导购员列表
      choose_guide: [], // 列表勾选导购员
      guide: [],
      shop_guide: false,
    };
  },
  created() {
    this.shopGuideFn();
    this.shop_guide = this.shopGuide;
  },
  methods: {
    handleSelectionChange(val) {
      this.choose_guide = val;
    },
    close() {
      this.$emit("close");
    },
    // 删除导购员
    deleteGuide(index) {
      this.table_data.splice(index, 1);
    },

    // 选择导购员确定
    guideFn() {
      this.close();
      this.guide = this.choose_guide;
      this.$emit("shopGuideFn", this.guide);
    },
    // 导购员列表
    async shopGuideFn() {
      const data = await getAllStaffStaff({
        page: 1,
        pageSize: 10,
        deleteStatus: 5,
        keyword: "",
        signId: 2,
      });

      this.table_data = data.data;
    },
  },
};
</script>

<style scoped></style>
