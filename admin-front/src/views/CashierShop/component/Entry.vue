<template>
  <div>
    <p v-if="!entry_data.length" class="tips">暂无数据</p>
    <div v-else class="right-main">
      <div class="entry-ul">
        <div v-for="(item, index) in entry_data" :key="index" class="entry-li">
          <p class="entry-time">
            挂单时间：
            <span>{{ $_common.formatDate(item.createTime) }}</span>
          </p>
          <div v-for="(itemC, iC) in item.entryData" :key="iC" class="goods-group">
            <ul class="e-goods-ul">
              <li v-for="(itemCC, iCC) in itemC.shopGoodsData" :key="iCC" class="e-goods-li">
                <p class="e-goods-name">
                  {{ itemCC.goodsName }}
                </p>
                <p class="e-goods-sku">
                  {{ itemCC.unitName }};
                  <span v-for="(itemS, iS) in itemCC.specGroup" :key="iS"> {{ itemS.specValueName }}; </span>
                </p>
                <div class="e-goods-num">
                  <span class="price-color">￥{{ itemCC.price }}</span>
                  <span>x{{ itemCC.buyNum }}</span>
                </div>
              </li>
            </ul>
            <div class="clearfix">
              <div class="float_left" style="line-height: 32px">
                订单总额:
                <span class="price-color">￥{{ itemC.payMoney }}</span>
              </div>
              <div class="float_right">
                <el-button size="small" @click="delEntryData(item.id)"> 删除 </el-button>
                <el-button size="small" type="primary" @click="getEntryData(item.id)"> 取单 </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <FooterPage
        :page-size="10"
        layout="total, prev, pager, next"
        :total-page.sync="entry_page_total"
        :current-page.sync="entry_page"
        @pageChange="ePageChange"
      ></FooterPage>
    </div>
  </div>
</template>

<script>
import { addCartCashier, delEntryData, getAllEntryData, getEntryData } from "@/api/Cashier";

export default {
  name: "Entry",
  props: {
    nowSelShop: {
      type: Object,
      default: () => {
        return {};
      },
    },
    shopSpec: {
      type: Object,
      default: () => {
        return {};
      },
    },
    userCenterId: {
      type: [String, Number],
      default: 0,
    },
    resetList: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      entry_data: [], // 挂单列表
      entry_page_total: 0, // 挂单列表total
      entry_page: 0, // 挂单列表分页
      shop_spec: {
        subPrice: 0,
        rem_money: 0,
        goodsData: [],
      },
    };
  },
  watch: {
    nowSelShop(val) {
      this.getAllEntryData();
    },
    resetList() {
      this.getAllEntryData();
    },
  },
  created() {
    this.getAllEntryData();
    this.shop_spec = this.shopSpec;
  },
  methods: {
    // 挂单列表
    async getAllEntryData() {
      if (!this.nowSelShop.id) return;
      const data = await getAllEntryData({
        shopId: this.nowSelShop.id,
        page: this.entry_page,
        pageSize: 10,
      });

      this.entry_data = data.data;
      this.entry_page_total = data.pageTotal;
    },
    ePageChange(page) {
      this.entry_page = page;
      this.getAllEntryData();
    },
    // 删除挂单
    async delEntryData(id) {
      this.$confirm("您确认删除这个订单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delEntryData(id);

        this.$message.success("操作成功");
        this.getAllEntryData();
      });
    },
    // 取单
    async getEntryData(id) {
      this.$confirm("您确认取出这个订单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const { data } = await getEntryData(id);

        this.$message.success("操作成功");
        // 执行挂单后如果当前购物车有数据，把当前购物车的商品执行挂单
        if (this.shop_spec.goodsData && this.shop_spec.goodsData.length) {
          await this.$emit("saveEntryData");
        }
        // 执行取单后把当前挂单的订单重新加入购物车
        await this.EAddCart(data.entryData[0].shopGoodsData);
        // 获取挂单列表
        await this.getAllEntryData();
      });
    },
    // 取单后加入购物车
    async EAddCart(list) {
      const goodsData = list.map((item) => {
        return {
          skuId: item.skuId,
          goodsId: item.goodsId,
          buyNum: item.buyNum,
          shopId: item.shopId,
          source: item.source,
          goodsBasicId: item.goodsBasicId,
        };
      });
      const data = await addCartCashier({
        goodsData: goodsData,
        userCenterId: this.userCenterId,
      });
      this.$emit("getCartByUser");
    },
  },
};
</script>

<style scoped>
.tips {
  padding-top: 200px;
  text-align: center;
  color: #b2b2b2;
  font-size: 12px;
  font-weight: 500;
}

.right-main {
  height: calc(99vh - 166px);
  overflow: auto;
}
.entry-ul {
  color: #333333;
  line-height: 16px;
}

.entry-li {
  padding: 20px 0;
  border-bottom: 1px solid #f2f2f2;
}

.entry-time {
  font-size: 13px;
}

.e-goods-ul {
  flex-wrap: wrap;
  display: flex;
  margin-top: 20px;
}

.e-goods-li {
  width: 222px;
  background: #f5f6fa;
  border-radius: 4px;
  margin-bottom: 16px;
  margin-right: 20px;
  padding: 16px;
  position: relative;
}

.e-goods-name {
  margin-bottom: 6px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 34px;
}

.e-goods-num {
  display: flex;
  font-size: 12px;
  -webkit-box-pack: justify;
  justify-content: space-between;
}

.e-goods-sku {
  color: #666;
  font-size: 12px;
  margin-bottom: 6px;
}

.price-color {
  color: #f56c6c;
}
</style>
