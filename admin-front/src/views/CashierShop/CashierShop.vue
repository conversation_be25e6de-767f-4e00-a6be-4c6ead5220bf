<template>
  <div class="CashierShop">
    <CashierHeader :shop-name="now_sel_shop.name" />
    <el-row style="padding: 0 10px">
      <el-col :span="10">
        <div class="grid-content bg-purple">
          <div class="list_info">
            <p :class="tip_num === 1 ? 'list_info_p' : ''" @click="tip_num = 1">首页</p>
            <p :class="tip_num === 2 ? 'list_info_p' : ''" @click="tip_num = 2">核销</p>
            <p :class="tip_num === 3 ? 'list_info_p' : ''" @click="listFn">订单</p>
          </div>
          <div class="list_content clearfix">
            <span style="font-weight: bold; margin-left: 10px; font-size: 14px">
              <span v-if="tip_num === 1" :disabled="collection"> 结算清单（{{ shop_spec.goodsNum || "0" }}件） </span>
              <span v-if="tip_num === 2 && com_record === false">自提核销</span>
              <span v-if="tip_num === 3">订单管理</span>
            </span>
            <div style="float: right; margin-right: 10px; font-size: 14px">
              <span v-if="tip_num === 1">会员:</span>
              <span v-if="tip_num === 1" style="color: #409eff">
                {{ vip_info.mobile || "无" }}
              </span>
              <!--              <span-->
              <!--                style="color: #F56C6C;cursor: pointer"-->
              <!--                v-if="tip_num===2&&com_record===false"-->
              <!--                @click="com_record=true"-->
              <!--              >核销记录</span>-->
            </div>
            <div v-if="tip_num === 1">
              <div v-if="tip_num === 1 && !shop_spec.goodsData" class="tips" style="height: 63vh">未添加任何商品</div>
              <div
                v-if="tip_num === 1 && list_judge === false && shop_spec.goodsData"
                style="height: 60vh; overflow: auto"
              >
                <!--                结算清单 start-->
                <ul v-for="(item, index) in shop_spec.goodsData" :key="index">
                  <li v-for="(itemP, indexP) in item.shopGoodsData" :key="indexP" class="card-goods-li clearfix">
                    <div class="card-goods-img float_left">
                      <img :src="itemP.goodsImages" alt="" style="height: 60px; width: 60px" />
                    </div>
                    <div class="float_left card-goods-info">
                      <p class="c-goods-name">
                        {{ itemP.goodsName || "直接收款" }}
                      </p>
                      <div class="color_size clearfix">
                        <div class="float_left">
                          {{ itemP.unitName || "直接收款" }};
                          <span v-for="(itemPX, indexPX) in itemP.specGroup" :key="indexPX">
                            {{ itemPX.specValueName }}
                          </span>
                        </div>
                        <div class="float_right">
                          <el-button
                            v-if="!itemP.isprice"
                            type="text"
                            :disabled="collection"
                            size="mini"
                            @click="getCart(itemP)"
                          >
                            改价
                          </el-button>
                          <el-button type="text" :disabled="collection" size="mini" @click="delectShop(itemP.cartId)">
                            删除
                          </el-button>
                        </div>
                      </div>
                      <div class="c-goods-num">
                        <div class="price-color">￥{{ itemP.price || itemP.isprice }}</div>
                        <div style="text-align: center" @click="openKeyboard(indexP)">
                          <el-input
                            v-model="itemP.buyNum"
                            :disabled="collection"
                            style="width: 150px"
                            size="mini"
                            readonly
                          ></el-input>
                        </div>
                        <div class="price-color" style="text-align: right">
                          ￥{{ itemP.totalMoney || itemP.buyNum * num2 }}
                        </div>
                      </div>
                    </div>
                  </li>
                </ul>
                <!--                结算清单 END-->
              </div>
              <div v-if="tip_num === 1 && list_judge === false && shop_spec.goodsData">
                <div
                  class="clearfix"
                  style="border-bottom: 1px solid #f2f2f2; padding: 0 20px; border-top: 1px solid #f2f2f2"
                >
                  <div v-if="shop_spec.preferential !== 0" class="float_left">
                    <span class="price-color">
                      优惠：￥{{ $NP.plus(shop_spec.preferential || 0, shop_spec.subPrice || 0) }}
                    </span>
                    <span
                      style="color: #1881f7; padding-left: 20px; cursor: pointer"
                      @click="(detailed = true), (collection = false), (re_charge = false), (price_change = false)"
                    >
                      明细
                    </span>
                  </div>
                  <div class="float_right">
                    <span>应收金额：</span>
                    <span class="price-color"> ￥{{ shop_spec.payMoney || 0 }} </span>
                  </div>
                </div>
                <div style="margin-left: 20px; text-align: right">
                  <el-button :disabled="collection" @click="clearCart"> 取消 </el-button>
                  <el-button :disabled="collection" @click="dialogVisible = true"> 备注 </el-button>
                  <el-button :disabled="collection" @click="saveEntryData"> 挂单 </el-button>
                  <el-button
                    v-if="cashier_set.zero_set.status === 5 && cashier_set.zero_set.auto_zero === 4"
                    :disabled="collection"
                    @click="delZreo"
                  >
                    {{ shop_spec.rem_money - 0 !== 0 ? "取消抹零" : "抹零" }}
                  </el-button>
                  <el-button type="primary" :disabled="collection" @click="orderCashier"> 收款 </el-button>
                </div>
              </div>
            </div>

            <div v-if="tip_num === 2 && com_record === false" class="change-price">
              <div class="keyboard" style="padding-top: 50px">
                <div class="keyboard-input_one">
                  <input
                    v-model="keyboard_key"
                    type="text"
                    placeholder="请输入核销码"
                    autocomplete="off"
                    spellcheck="false"
                    class="keyboard-input-input"
                    style="width: 100%"
                  />
                </div>
                <keyboard :tip-num="tip_num" @click="numFnOne"></keyboard>
              </div>
            </div>
            <div v-if="tip_num === 2 && com_record === true" style="margin-top: -50px">
              <div class="head_back">
                <i class="el-icon-back"></i>
                <span style="margin-left: 6px" @click="com_record = false"> 返回 </span>
                <span class="head_line"></span>
                <span style="color: #333333">核销记录</span>
              </div>
              <div style="width: 90%; margin: 10px auto 0">
                <el-input
                  v-model="order_num"
                  size="medium"
                  placeholder="请输入订单编号查询订单"
                  class="input-with-select"
                >
                  <el-button slot="append" icon="el-icon-search"></el-button>
                </el-input>
              </div>
              <div class="tips">暂无数据</div>
            </div>
            <!--            订单管理 start-->
            <div v-if="tip_num === 3">
              <div class="order-input-view">
                <el-input v-model="order_no_key" placeholder="请输入订单编号查询订单" style="width: 100%">
                  <el-button slot="append" icon="el-icon-search" @click="serchOrder"></el-button>
                </el-input>
              </div>
              <ul class="order-ul">
                <li v-for="(item, index) in order_list" :key="index" class="order-item" @click="orderEvent(item)">
                  <div class="clearfix">
                    <span style="margin-left: 10px">订单号：{{ item.no }}</span>
                    <div style="float: right; margin-right: 10px; color: #1881f7">
                      <el-tag size="small" :type="item.payMsg === '已支付' ? 'success' : 'danger'">
                        {{ item.payMsg }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="clearfix">
                    <span style="margin-left: 10px"> 下单时间：{{ $_common.formatDate(item.createTime) }} </span>
                    <span style="float: right; margin-right: 10px"> 合计：￥{{ item.totalMoney }} </span>
                  </div>
                </li>
              </ul>
              <FooterPage
                layout="total, prev, pager, next"
                :page-size="o_pageSize"
                :total-page.sync="o_total"
                :current-page.sync="o_page"
                @pageChange="oPageChange"
              ></FooterPage>
            </div>
            <!--            订单管理 end-->
          </div>
        </div>
      </el-col>
      <el-col :span="14">
        <div class="grid-content bg-purple">
          <div v-if="tip_num === 1 && detailed === true">
            <div class="tip_info">
              <p style="margin: 0">优惠明细</p>
              <div
                style="float: right; padding-right: 20px; margin-top: -60px; cursor: pointer"
                @click="detailed = false"
              >
                <i class="el-icon-close"></i>
              </div>
            </div>
            <div>
              <div class="clearfix" style="padding: 10px; font-size: 14px">
                <div style="margin-bottom: 20px">
                  <span style="font-weight: bold">改价</span>
                  <span class="price-color float_right"> ￥{{ shop_spec.subPrice || 0 }} </span>
                </div>
                <div style="margin-bottom: 20px">
                  <span style="font-weight: bold">抹零</span>
                  <span class="price-color float_right"> ￥{{ shop_spec.rem_money || 0 }} </span>
                </div>
                <div v-if="shop_spec.useCoupon.reducePrice">
                  <span style="font-weight: bold">优惠活动</span>
                  <span class="price-color float_right"> ￥{{ shop_spec.useCoupon.reducePrice }} </span>
                </div>
              </div>
            </div>
          </div>
          <div v-if="tip_num === 1 && collection === true" class="pay-box">
            <div>
              <div
                v-if="userCenterId"
                style="padding: 10px 10px 10px 20px; position: relative; color: #333333; height: 100px"
                class="clearfix"
              >
                <div style="float: left; width: 300px">
                  <div style="display: inline-block; padding-top: 18px">
                    <el-row class="demo-avatar demo-basic">
                      <el-col :span="12">
                        <div class="demo-basic--circle">
                          <div class="block">
                            <el-avatar :size="50" :src="vip_info.avatar"></el-avatar>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                  <div
                    style="display: inline-block; margin-left: 10px; line-height: 20px; position: absolute; top: 34%"
                  >
                    <div>
                      <span>{{ vip_info.name }}</span>
                    </div>
                    <div>
                      {{ vip_info.mobile }}
                    </div>
                  </div>
                  <div style="display: inline-block; margin-left: 130px; position: absolute; top: 34%">
                    <el-button @click="changeUser">切换会员</el-button>
                  </div>
                </div>
                <!--                <div class="vip_inte">-->
                <!--                  <div>-->
                <!--                    会员积分-->
                <!--                  </div>-->
                <!--                  <div class="vip_price">-->
                <!--                    0-->
                <!--                  </div>-->
                <!--                </div>-->
                <!--                <div class="vip_inte">-->
                <!--                  <div>-->
                <!--                    会员余额-->
                <!--                  </div>-->
                <!--                  <div class="vip_price">-->
                <!--                    ￥0-->
                <!--                  </div>-->
                <!--                </div>-->
              </div>
              <div class="check_vip_price">
                收款金额：
                <span>￥{{ shop_spec.payMoney }}</span>
              </div>
              <div class="pay_type">
                <!--<el-button type="primary">
                  其他方式
                </el-button>-->
                <el-radio-group v-model="pay_type">
                  <!-- <el-radio-button
                    :label="2"
                    v-if="cashier_set.pay_set.alipay_payment_id===5"
                  >
                    支付宝
                  </el-radio-button>-->
                  <!--                  <el-radio-button-->
                  <!--                    :label="1"-->
                  <!--                    v-if="cashier_set.pay_set.wechat_payment_id===5"-->
                  <!--                  >-->
                  <!--                    微信支付-->
                  <!--                  </el-radio-button>-->
                  <el-radio-button v-if="cashier_set.pay_set.cash_pay === 5" :label="5"> 现金支付 </el-radio-button>
                  <!--<el-radio-button
                    :label="3"
                    v-if="cashier_set.pay_set.balance_pay===5&&userCenterId"
                  >
                    余额支付
                  </el-radio-button>-->
                  <el-radio-button :label="6">其他方式</el-radio-button>
                </el-radio-group>
              </div>
              <!--支付宝或者微信支付-->
              <div v-if="pay_type === 1 || pay_type === 2" class="pay-tip">请输入付款码数字</div>
              <!--现金支付-->
              <div v-else-if="pay_type === 5" style="margin-top: 20px">
                <div class="input-box">
                  <input
                    v-model="shop_spec.payMoney"
                    type="text"
                    placeholder="请输入需要的金额"
                    autocomplete="off"
                    spellcheck="false"
                    class="input-input"
                  />
                  <div class="input-unit" style="color: #333333">元</div>
                </div>
                <div class="input-box" style="margin-top: 10px">
                  <input
                    v-model="pay_account_name"
                    type="text"
                    readonly
                    placeholder="请选择结算账户"
                    autocomplete="off"
                    spellcheck="false"
                    class="input-input"
                  />
                  <div class="input-unit" style="color: #666" @click="openAccountModel('pay')">
                    <i class="el-icon-circle-check"></i>
                    点击选择
                  </div>
                </div>
                <keyboard @click="payCalculator($event)"></keyboard>
              </div>
              <!--余额支付-->
              <div v-else-if="pay_type === 3" class="pay-tip">
                <div class="balance">
                  <p>会员余额</p>
                  <p class="price">¥0</p>
                  <p class="tip">会员余额不足，请更换支付方式。</p>
                </div>
              </div>
              <!--其他支付方式-->
              <div v-else class="pay-tip">
                会员通过扫码等方式进行支付，确认会员支付成功后，点击确认付款即可完成付款操作。
                <div class="input-box" style="margin-top: 10px">
                  <input
                    v-model="pay_account_name"
                    type="text"
                    readonly
                    placeholder="请选择结算账户"
                    autocomplete="off"
                    spellcheck="false"
                    class="input-input"
                  />
                  <div class="input-unit" style="color: #666" @click="openAccountModel('pay')">
                    <i class="el-icon-circle-check"></i>
                    点击选择
                  </div>
                </div>
              </div>
            </div>
            <div class="right-bottom">
              <div style="padding: 20px" class="clearfix">
                <span class="float_left">
                  导购员：
                  <i v-if="!guide.length">无</i>
                  <span v-else>
                    <i v-for="(item, index) in guide" :key="index"> {{ item.staffName }}; </i>
                  </span>
                </span>
                <span class="float_right" @click="shopGuide = true">
                  <el-button type="text">编辑</el-button>
                </span>
              </div>
              <div class="btn-group">
                <el-button style="width: 100px" @click="collection = false"> 取消 </el-button>
                <el-button v-if="pay_type !== 3" type="primary" style="width: 100px" @click="paymentFn">
                  确定付款
                </el-button>
              </div>
            </div>
          </div>
          <div v-if="tip_num === 1 && payment === true" style="padding-top: 200px">
            <div style="height: 56vh; border-bottom: 1px solid #dcdee2">
              <p class="icon_check" style="text-align: center">
                <i class="el-icon-check"></i>
              </p>
              <p class="price-color" style="font-size: 20px; font-weight: bold; text-align: center">收款成功</p>
            </div>
            <div style="margin: 20px 20px 0 0; text-align: right">
              <el-button @click="toPrint">打印小票</el-button>
              <el-button type="primary" @click="payment = false"> 继续收款 </el-button>
            </div>
          </div>
          <div v-if="tip_num === 1 && re_charge === true" class="clearfix">
            <div style="height: 76vh; border-bottom: 1px solid #dcdee2">
              <div style="padding: 10px 10px 10px 20px; position: relative; color: #333333; height: 100px">
                <div style="float: left; width: 300px">
                  <div style="display: inline-block; padding-top: 18px">
                    <el-row class="demo-avatar demo-basic">
                      <el-col :span="12">
                        <div class="demo-basic--circle">
                          <div class="block">
                            <el-avatar :size="50" :src="vip_info.avatar"></el-avatar>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                  <div
                    style="display: inline-block; margin-left: 10px; line-height: 20px; position: absolute; top: 34%"
                  >
                    <div>
                      <span>{{ vip_info.name }}</span>
                    </div>
                    <div>
                      {{ vip_info.mobile }}
                    </div>
                  </div>
                  <div style="display: inline-block; margin-left: 130px; position: absolute; top: 34%">
                    <el-button @click="changeUser">切换会员</el-button>
                  </div>
                </div>
                <!--                <div class="vip_inte">-->
                <!--                  <div>-->
                <!--                    会员积分-->
                <!--                  </div>-->
                <!--                  <div class="vip_price">-->
                <!--                    0-->
                <!--                  </div>-->
                <!--                </div>-->
                <!--                <div class="vip_inte">-->
                <!--                  <div>-->
                <!--                    会员余额-->
                <!--                  </div>-->
                <!--                  <div class="vip_price">-->
                <!--                    ￥{{ vip_info.money||'0' }}-->
                <!--                  </div>-->
                <!--                </div>-->
              </div>
              <div class="check_vip_price">
                充值金额：
                <span>￥{{ vip_info.price }}</span>
              </div>
              <div class="pay_type">
                <el-button type="primary">其他方式</el-button>
              </div>
              <div style="margin-top: 50px; text-align: center">
                会员通过扫码等方式进行支付，确认会员支付成功后，点击确认付款即可完成付款操作。
              </div>
            </div>
            <div class="button_vip">
              <span>
                <el-button style="width: 100px" @click="re_charge = false"> 取消 </el-button>
              </span>
              <span style="width: 100px">
                <el-button type="primary" style="width: 100px"> 确定付款 </el-button>
              </span>
            </div>
          </div>
          <div
            v-if="
              tip_num === 1 &&
              price_change === false &&
              re_charge === false &&
              detailed === false &&
              collection === false &&
              payment === false
            "
            class="cashier"
          >
            <el-tabs
              v-model="activeName"
              class="el-tabs__item is-top el_tabs--top"
              style="position: relative"
              @tab-click="handleClick"
            >
              <el-tab-pane label="商品" name="first">
                <Goods
                  :shop-com="shop_com"
                  :all-shop="all_shop"
                  :user-center-id="userCenterId"
                  :num-list="num_list"
                  :now-sel-shop="now_sel_shop"
                  :user-coupon-id="userCouponId"
                  @goodsId="goodsIdFn"
                  @openKeyboard="openKeyboard"
                  @inputKey="inputKey"
                  @getCartByUser="getCartByUser"
                ></Goods>
              </el-tab-pane>
              <el-tab-pane label="会员" name="second">
                <Vip
                  :vip-login="vip_login"
                  :vip-handle="vip_handle"
                  :num-list="num_list"
                  :user-coupon-id="userCouponId"
                  :now-sel-shop="now_sel_shop"
                  :collection="collection"
                  :re-charge="re_charge"
                  @getCartByUser="getCartByUser"
                  @vipInfo="vipInfo"
                ></Vip>
              </el-tab-pane>
              <el-tab-pane label="优惠活动" name="third">
                <VipActivity :discount-list="discount_list" @couponChange="couponChange"></VipActivity>
              </el-tab-pane>
              <el-tab-pane label="取单" name="fourth">
                <Entry
                  :reset-list="rest_entry"
                  :now-sel-shop="now_sel_shop"
                  :shop-spec="shop_spec"
                  :user-center-id="userCenterId"
                  @saveEntryData="saveEntryData"
                  @getCartByUser="getCartByUser"
                ></Entry>
              </el-tab-pane>
              <el-tab-pane label="直接收款" name="fiver">
                <AccountPrice
                  :now-sel-shop="now_sel_shop"
                  :num-list="num_list"
                  :customer-id="customerId"
                  :customer-name="customerName"
                  :money="vip_info.money"
                  :input="input"
                  @activeName="activename"
                ></AccountPrice>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div v-if="tip_num === 1 && price_change === true" class="change-price">
            <div class="tip_info clearfix">
              <p style="margin: 0">改价</p>
              <div style="float: right; padding-right: 20px; margin-top: -60px; cursor: pointer" @click="deleteFn">
                <i class="el-icon-close"></i>
              </div>
            </div>
            <div class="shop_info clearfix">
              <div class="float_left">
                <img :src="cart_primary.goodsImages" alt="" />
              </div>
              <div class="float_right goods-info">
                <p>
                  {{ cart_primary.goodsName }}
                </p>
                <div>
                  <span class="price-color" style="margin-right: 10px"> ￥{{ cart_primary.originPrice }} </span>
                </div>
              </div>
            </div>
            <div style="margin: 40px 0 40px 40px">
              <span style="margin-right: 30px">现价</span>
              <span>￥{{ cart_primary.originPrice }}</span>
            </div>
            <div :inline="true" style="margin-left: 40px">
              <span style="margin-right: 20px">定价</span>
              <el-input v-model="price_num" placeholder="请输入内容" style="width: 20%">
                <template slot="append">元</template>
              </el-input>
            </div>
            <div class="btn-group">
              <el-button @click="deleteFn">取消</el-button>
              <el-button type="primary" @click="primaryCart">确定</el-button>
            </div>
          </div>
          <!--          核销 订单详情 start-->
          <WriteOffInfo :tip-num="tip_num" :writeoff-list="writeoff_list"></WriteOffInfo>
          <!--          核销 订单详情 end-->
          <!--            订单详情 start-->
          <WriteOffOrder
            :tip-num="tip_num"
            :order-detail="order_detail"
            :now-order-id="now_order_id"
            @dialog="diaLog"
          ></WriteOffOrder>
          <!--            订单详情 end-->
        </div>
      </el-col>
    </el-row>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <span>
        <el-input v-model="textarea" type="textarea" :rows="2" maxlength="200" placeholder="请填写备注"></el-input>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false"> 确 定 </el-button>
      </span>
    </el-dialog>
    <el-dialog
      :show-close="false"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="40%"
      title="选择店铺"
      :visible.sync="shop_visible"
    >
      <div style="height: 400px; overflow-y: auto; border-bottom: 1px solid #f7f7f7">
        <div
          v-for="(item, index) in shop_select"
          :key="index"
          class="shop"
          :class="[now_shop.id === item.id ? 'shop-on' : '']"
          @click="selShop(item)"
        >
          <span
            class="iconfont icon-002 select_icon"
            style="background-color: #f56c6c; color: #fff; text-align: center; line-height: 40px"
          ></span>
          <span>{{ item.name }}</span>
          <i v-show="now_shop.id === item.id" class="el-icon-check"></i>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="shop_visible = false">取消</el-button>
        <el-button type="primary" @click="shopConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="30%"
      title="会员充值"
      :visible.sync="reCharge"
      @close="close"
    >
      <el-form :model="vip_info" label-width="80px" size="small" :rules="rules">
        <el-form-item label="金额" prop="price">
          <el-input v-model="vip_info.price" placeholder="请填写金额">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="reChargeFn">去充值</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="30%"
      title="调整积分"
      :visible.sync="integral"
      @close="close"
    >
      <el-form :model="vip_info" label-width="80px" size="small" :rules="rules">
        <el-form-item label="积分" prop="integral">
          <el-input v-model="vip_info.integral" placeholder="请输入0以上的数字"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="vip_info.remarks" placeholder="充值说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary">确定</el-button>
      </div>
    </el-dialog>
    <!--    选择导购-->
    <ShopGuide v-if="shopGuide" :shop-guide="shopGuide" @close="close" @shopGuideFn="shopGuideFn"></ShopGuide>
    <!--    购物车键盘弹窗-->
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="640px"
      :visible.sync="keyboard_number"
    >
      <el-input v-model="input_key_num" readonly></el-input>
      <keyboard :lb-num="false" @click="keyboardConfirm"></keyboard>
    </el-dialog>
    <!--    选择结算账户-->
    <AccountType
      v-if="account_show"
      :id="now_sel_shop.id"
      :is-check="false"
      :is-show="account_show"
      @cancel="account_show = false"
      @confirm="accountsel"
    />
  </div>
</template>

<script>
import Keyboard from "./component/Keyboard";
import WriteOffInfo from "./component/WriteOffInfo";
import WriteOffOrder from "./component/WriteOffOrder";
import Goods from "@/views/CashierShop/component/Goods";
import Vip from "./component/Vip";
import CashierHeader from "./CashierHeader.vue";
import VipActivity from "./component/VipActivity";
import Entry from "./component/Entry";
import AccountPrice from "./component/AccountPrice";
import ShopGuide from "./component/shopGuide";
import { mapActions } from "vuex";
import {
  activityAll,
  saveEntryData,
  loggerConnect,
  getSettings,
  searchCustomerDetails,
  getCartByUser,
  updateBuyNumCashier,
  delCartCashier,
  clearCartCashier,
  GoodsByCategory,
  changePrice,
} from "@/api/Cashier";
import { getAllOrderOrder, getOrderInfoById, OrderVerification, getOrderByVerifyCode, cashierOrder } from "@/api/Order";
import { getAllStaffStaff } from "@/api/Department";
import { getAllShop } from "@/api/Shop";
import { toPrint } from "@/api/common";
import { addReceived } from "@/api/Finance";
import AccountType from "../Finance/AccountType";
export default {
  name: "CashierShop",
  components: {
    CashierHeader,
    AccountType,
    Keyboard,
    WriteOffInfo,
    WriteOffOrder,
    Goods,
    Vip,
    VipActivity,
    Entry,
    AccountPrice,
    ShopGuide,
  },
  data() {
    return {
      input_key_num: 0, // 键盘弹窗数字
      cg_index: 0, // 购物车商品选择下标
      keyboard_number: false, // 键盘弹窗
      activeName: "first",
      account_show: false, // 选择结算账户
      dialogVisible: false,
      shopGuide: false,
      shop_guide: [], // 导购员列表
      guide_name: "", // 导购员名称
      guide: [],
      collection: false,
      total: 0,
      page: 1,
      pageSize: 10,
      textarea: "",
      shop: "",
      num_list: [],
      target_num: "",
      input: "",
      keyboard_key: "", // 核销码
      input_two: "",
      tip_num: 1, // 左侧导航
      num1: 1,
      order_num: "",
      num2: "1",
      list_judge: false,
      list: "",
      no_time: "",
      price_num: "",
      price_change: false,
      order_list: [], // 订单列表
      order_no_key: "", // 订单输入框搜索关键字
      writeoff_list: {},
      all_shop: true,
      shop_com: false,
      com_record: false,
      spec_id_group: [],
      now_sku_data: {},
      shop_spec: {
        subPrice: 0,
        rem_money: 0,
        goodsData: [],
      },
      shop_select: [],
      vip_info: {
        createTime: "",
        birthday: "",
        price: "",
        integral: "",
        remarks: "",
        defaultAddress: {
          area: {
            provinceName: "",
            cityName: "",
            districtName: "",
            address: "",
          },
        },
        remark: "",
      },
      vip_handle: false,
      re_charge: false,
      integral: false,
      detailed: false,
      vip_login: false,
      confirm_vip: false,
      reCharge: false,
      vip_list: [
        {
          price: 10,
          price_num: 0.0,
        },
        {
          price: 300,
          price_num: 0.0,
        },
        {
          price: 15,
          price_num: 0.0,
        },
        {
          price: 121,
          price_num: 0.0,
        },
      ],
      rules: {
        price: [{ required: true, message: "请填写金额", trigger: "blur" }],
        integral: [{ required: true, message: "请填写积分", trigger: "blur" }],
        remarks: [{ required: true, message: "请填写备注", trigger: "blur" }],
      },
      shop_visible: false, // 选择店铺弹窗
      now_shop: {}, // 当前店铺信息
      now_sel_shop: {}, // 当前选中店铺信息
      classify_list: [], // 分类列表
      goodsId: "",
      userCenterId: "",
      goodsBasicId: "",
      order_detail: {}, // 订单详情
      o_pageSize: 10,
      o_total: 0,
      o_page: 1,
      cashier_staff: {}, // 收银员数据
      cart_primary: {},
      customerId: "",
      customerName: "",
      customerType: "",
      payment: false,
      cashier_set: {
        zero_set: {},
        pay_set: {},
      }, // 收银台设置
      pay_type: 6, // 收款方式
      discount_list: [], // 优惠活动
      pay_account_num: "", // 收款结算账户
      pay_account_id: "", // 收款结算账户id
      pay_account_name: "", // 收款结算账户name
      account_num: "", // 结算账户
      accountName: "", // 结算账户name
      account_id: "", // 结算账户id
      now_order_id: "", // 收款成功后存储当前订单id
      userCouponId: 0, // 勾选优惠券id
      weight_visible: false,
      goods_weight: 0, // 商品手动输入重量
      rest_entry: false, // 控制刷新取单列表
    };
  },
  async created() {
    // 获取收银台设置
    await this.getSettings();
    this.no_time = this.$_common.formatDate(new Date().getTime());
    if (this.$store.getters["MUser/cashierShop"].id) {
      this.now_sel_shop = this.$store.getters["MUser/cashierShop"];
      // 请求与该店铺相关数据
      this.loggerConnect();
    } else {
      // 获取商铺列表
      await this.getAllShop();
    }
  },
  methods: {
    ...mapActions({
      changeCashierShop: "MUser/changeCashierShop",
    }),
    goodsIdFn(goodsId) {
      this.goodsId = goodsId;
    },
    inputKey(input_key_num) {
      this.input_key_num = input_key_num;
      console.log(input_key_num);
    },
    vipInfo(vip_info) {
      this.vip_info = vip_info;
    },
    activename(activeName) {
      this.activeName = activeName;
    },
    shopGuideFn(guide) {
      this.guide = guide;
    },
    // 切换商铺后请求与该店铺相关数据
    async shopChange() {
      // 商品列表
      await this.GoodsByCategory();
      // 购物车详情
      this.getCartByUser();
    },
    // 获取收银台设置
    async getSettings() {
      const { data } = await getSettings();

      if (data.add_form) {
        this.cashier_set = data.add_form;
      }
    },
    async GoodsByCategory() {
      const data = await GoodsByCategory({
        page: this.page,
        pageSize: this.pageSize,
        categoryId: this.cate_id,
        keyword: this.shop,
        userCenterId: this.userCenterId,
        shopId: this.now_sel_shop.id,
      });

      this.shop_shop = data.data;
      this.total = data.pageTotal;
    },
    // 收银右边tab切换
    handleClick(tab, event) {
      switch (this.activeName) {
        // 商品
        case "first":
          break;
        // 会员
        case "second":
          // 获取客户类型
          break;
        // 优惠活动
        case "third":
          // this.activityAll()
          break;
        // 取单
        case "fourth":
          // 挂单列表
          break;
        // 直接收款
        case "fiver":
          break;
      }
    },
    // 修改购物车数量
    handleChange(value, cartId) {
      this.updateBuyNumCashier(value, cartId);
      console.log(cartId);
      console.log(value);
    },
    // 打开键盘
    openKeyboard(index) {
      this.keyboard_number = true;
      this.cg_index = index;
    },
    keyboardConfirm(e, num) {
      if (num === "x") {
        this.input_key_num = this.input_key_num.substring(0, this.input_key_num.length - 1);
      } else if (num === "清除") {
        this.input_key_num = "";
      } else if (num === "确定") {
        if (this.cg_index >= 0) {
          let goods = this.shop_spec.goodsData[0].shopGoodsData[this.cg_index];
          if (Number(this.input_key_num) > Number(goods.inventoryNum)) {
            this.$message.warning("库存不足");
            return;
          }
          goods.buyNum = Number(this.input_key_num);
          this.updateBuyNumCashier(Number(this.input_key_num), goods.cartId);
        } else {
          this.num2 = Number(this.input_key_num);
          this.addCart();
        }
        this.keyboard_number = false;
        this.input_key_num = 0;
      } else {
        this.input_key_num += num;
      }
    },
    // 修改购物车数量
    async updateBuyNumCashier(value, cartId) {
      const data = await updateBuyNumCashier(cartId, {
        userCenterId: this.userCenterId,
        buyNum: value,
      });

      this.getCartByUser();
    },
    // 查询会员信息
    async searchCustomerDetails() {
      if (!this.input) {
        this.$message.warning("请输入会员手机号");
        return;
      }
      const re = /^1[3456789]\d{9}$/; // 正则表达式
      if (!re.test(this.input)) {
        this.$message.warning("手机号格式有误，请重新输入!");
        return;
      }
      const data = await searchCustomerDetails({
        mobile: this.input,
      });

      if (JSON.stringify(data.data) === "{}") {
        // this.$message.error('未查找到会员信息')
        this.vip_handle = false;
        this.vip_login = true;
        this.userCenterId = "";
        this.customerId = "";
        this.customerName = "";
        this.customerType = "";
        this.vip_info = {};
      } else {
        this.vip_handle = true;
        this.vip_login = true;
        this.vip_info = data.data;
        this.userCenterId = data.data.userCenterId;
        this.customerId = data.data.customerId;
        this.customerName = data.data.name;
        this.customerType = data.data.customerType;
        this.getCartByUser();
      }
    },
    // 删除购物车
    delectShop(cartId) {
      // console.log(e.target.cartId)
      this.$confirm("您确认删除这个订单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const data = await delCartCashier({
            userCenterId: this.userCenterId,
            cartId: cartId,
          });

          this.shop_spec.goodsData[0].shopGoodsData.splice(cartId, 1);
          this.$message.success("操作成功");
          this.getCartByUser();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    async getCartByUser(userCenterId) {
      const data = await getCartByUser({
        userCenterId: userCenterId || this.userCenterId,
        userCouponId: this.userCouponId,
      });
      if (Array.isArray(data.data)) {
        this.shop_spec = {
          subPrice: 0,
          rem_money: 0,
          goodsData: [],
        };
        return;
      }
      this.shop_spec = data.data;
      if (data.data.coupon) {
        data.data.coupon.forEach((item) => {
          if (data.data.useCoupon.id) {
            item.checked = item.id === data.data.useCoupon.id;
          } else {
            item.checked = false;
          }
        });
        this.discount_list = data.data.coupon;
      }
    },
    // 清空购物车
    async clearCart() {
      const data = await clearCartCashier({
        userCenterId: this.userCenterId,
      });

      this.shop_spec.goodsData[0].shopGoodsData = [];
      this.getCartByUser();
    },
    // 核销码输入键盘
    async numFnOne(e, num) {
      if (!["x", "清除", "确定"].includes(num)) {
        this.num_list.push(num);
      }
      if (num === "x") {
        this.keyboard_key = this.keyboard_key.substring(0, this.keyboard_key.length - 1);
      } else if (num === "清除") {
        this.keyboard_key = "";
        this.num_list = [];
      } else if (num === "确定") {
        const data = await getOrderByVerifyCode({
          verifyCode: this.keyboard_key,
        });

        this.writeoff_list = data.data;
        this.keyboard_key = "";
      } else {
        this.keyboard_key += num;
      }
    },
    // 现金支付输入键盘
    payCalculator(e) {
      const targetNum = e.target.textContent;
      let num = this.shop_spec.totalMoney.toString();

      if (targetNum === "清除") {
        num = 0;
      } else if (targetNum === "确定") {
        this.paymentFn();
      } else if (targetNum === "x") {
        num = num.substring(0, num.length - 1);
      } else {
        num += targetNum;
      }
      this.shop_spec.totalMoney = num;
    },
    // 订单点击事件
    orderEvent(item) {
      this.getOrderInfoById(item.userCenterId, item.id);
    },
    diaLog() {
      this.dialogVisible = true;
    },
    deleteFn() {
      this.price_change = false;
    },

    listFn() {
      this.tip_num = 3;
      this.getAllOrderOrder();
    },

    // 订单列表分页切换
    oPageChange(page) {
      this.o_page = page;
      this.getAllOrderOrder();
    },
    // 订单搜索
    serchOrder() {
      this.oPageChange(1);
    },
    // 订单列表
    async getAllOrderOrder() {
      const data = await getAllOrderOrder({
        page: this.o_page,
        pageSize: this.o_pageSize,
        search: {
          orderType: 24,
          shopId: this.now_sel_shop.id,
          no: this.order_no_key,
        },
      });

      this.order_list = data.data;
      this.o_total = data.pageTotal;
    },
    // 创建订单
    orderCashier() {
      if (!this.userCenterId) {
        this.$confirm("订单未绑定会员，是否需要绑定会员进行结算?", "提示", {
          confirmButtonText: "会员登录",
          cancelButtonText: "跳过",
          type: "warning",
        })
          .then(() => {
            this.activeName = "second";
          })
          .catch(() => {
            this.collection = true;
            this.re_charge = false;
            this.price_change = false;
          });
      } else {
        this.collection = true;
        this.re_charge = false;
        this.price_change = false;
      }
    },
    async paymentFn() {
      if ([5, 6].includes(this.pay_type)) {
        if (!this.pay_account_num) {
          this.$message.warning("请选择结算账户");
          return;
        }
      }
      const guideUids = this.guide
        .map((item) => {
          return item.userCenterId;
        })
        .join(",");
      const accountList = [
        {
          accountId: this.pay_account_id,
          accountNumber: this.pay_account_num,
          accountName: this.pay_account_name,
          money: this.shop_spec.totalMoney,
          discountMoney: this.$NP.plus(this.shop_spec.preferential || 0, this.shop_spec.subPrice || 0),
          finalMoney: this.shop_spec.payMoney,
          payWay: 4,
          remark: "",
        },
      ];

      const data = await cashierOrder({
        accountList: accountList,
        currentAccountName: this.pay_account_name,
        payType: this.pay_type,
        totalMoney: this.shop_spec.totalMoney,
        guideUids: guideUids,
        payAmount: this.shop_spec.payMoney,
        customerId: this.customerId,
        userCenterId: this.userCenterId,
        customerName: this.customerName,
        customerType: this.customerType,
        source: 4,
        deliveryType: 2,
        isEnablePrint: 4,
        orderRemark: this.textarea,
        goodsData: this.shop_spec,
        operatorName: this.userName,
      });

      this.now_order_id = data.data;
      this.payment = true;
      this.collection = false;
      this.getCartByUser();
      if (this.input) {
        this.searchCustomerDetails();
      }
    },
    // 改价
    async getCart(itemP) {
      this.collection = false;
      this.price_change = true;
      this.re_charge = false;
      this.detailed = false;
      this.cart_primary = itemP;
    },
    async primaryCart() {
      const data = await changePrice({
        userCenterId: this.userCenterId,
        goodsId: this.cart_primary.goodsId,
        skuId: this.cart_primary.skuId,
        changePrice: this.price_num,
      });

      this.$message.success("操作成功");
      this.getCartByUser();
    },
    // 弹窗关闭
    close() {
      this.reCharge = false;
      this.integral = false;
      this.shopGuide = false;
    },
    // 充值
    reChargeFn() {
      this.reCharge = false;
      this.re_charge = true;
    },
    // 店铺列表
    async getAllShop() {
      const data = await getAllShop({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.shop_select = data.data;
      this.shop_visible = true;
    },
    // 收银台登录 交接登陆记录
    async loggerConnect() {
      const data = await loggerConnect({
        shopId: this.now_sel_shop.id,
      });

      this.cashier_staff = data.data;
      this.shopChange();
    },
    // 核销订单
    async primaryOrder() {
      const data = await OrderVerification(this.writeoff_list.id);
      this.$message.success("核销成功");
    },
    // 选择店铺
    selShop(row) {
      this.now_shop = row;
    },
    // 选择店铺 确认
    shopConfirm() {
      this.now_sel_shop = this.now_shop;
      this.shop_visible = false;
      // 交接登陆记录
      this.loggerConnect();
      this.changeCashierShop(this.now_sel_shop);
    },
    // 获取订单详情
    async getOrderInfoById(userCenterId, id) {
      const { data } = await getOrderInfoById(userCenterId, {
        orderId: id,
      });

      this.order_detail = data;
      this.now_order_id = id;
    },
    // 挂单
    async saveEntryData() {
      const data = await saveEntryData({
        shopId: this.now_sel_shop.id,
        entryData: this.shop_spec.goodsData,
      });

      this.$message.success("操作成功");
      // 执行挂单后，清空当前购物车
      this.clearCart();
      this.rest_entry = !this.rest_entry;
    },
    // 抹零
    async delZreo() {
      const data = await getCartByUser({
        userCenterId: this.userCenterId,
        isZero: Number(this.shop_spec.rem_money) !== 0 ? 4 : 5,
      });
      this.shop_spec = data.data;
    },
    // 优惠活动
    async activityAll() {
      const data = await activityAll({
        userCenterId: this.userCenterId,
        page: 1,
        pageSize: 30,
        shopId: this.now_sel_shop.id,
      });

      this.discount_list = data.data.map((item) => {
        return {
          ...item,
          checked: false,
        };
      });
    },
    // 打开结算账户
    openAccountModel(type) {
      this.pay_account_type = type;
      this.account_show = true;
    },

    // 选择结算账户
    accountsel(val) {
      if (this.pay_account_type) {
        this.pay_account_id = val[0].id;
        this.pay_account_num = val[0].accountNumber;
        this.pay_account_name = val[0].name;
      } else {
        this.account_id = val[0].id;
        this.account_num = val[0].accountNumber;
        this.accountName = val[0].name;
      }
    },
    // 直接收款
    addReceived() {
      if (!this.account_id) {
        this.$message.warning("请选择结算账户");
        return;
      }
      if (!this.userCenterId) {
        this.$confirm("订单未绑定会员，是否需要绑定会员进行结算?", "提示", {
          confirmButtonText: "会员登录",
          cancelButtonText: "跳过",
          type: "warning",
        })
          .then(() => {
            this.activeName = "second";
          })
          .catch(() => {
            this.addReceivedTwo();
          });
      } else {
        this.addReceivedTwo();
      }
    },
    async addReceivedTwo() {
      const accountList = [
        {
          accountId: this.account_id,
          accountNumber: this.account_num,
          accountName: this.accountName,
          money: this.input_two,
          discountMoney: 0,
          finalMoney: this.input_two,
          payWay: 4,
          remark: "",
        },
      ];
      const params = {
        customerId: this.customerId,
        customerName: this.customerName,
        sourceNo: "",
        sourceNoMoney: "",
        currentAccountName: this.userName,
        financeType: "销售收款",
        financeTypeId: 2,
        shopId: this.now_sel_shop.id,
        money: this.vip_info.money,
        shopName: this.now_sel_shop.name,
        receiptTime: parseInt(new Date() / 1000),
        createTime: "",
        accountList: accountList,
      };

      const data = await addReceived(params);

      this.$message.success("收款成功");
      if (this.input) {
        this.searchCustomerDetails();
      }
      this.input_two = "";
    },
    // 切换会员
    changeUser() {
      this.collection = false;
      this.re_charge = false;
      this.vip_login = false;
      this.vip_handle = false;
      this.userCenterId = "";
      this.customerId = "";
      this.customerName = "";
      this.customerType = "";
      this.vip_info = {
        createTime: "",
        birthday: "",
        price: "",
        integral: "",
        remarks: "",
        defaultAddress: {
          area: {
            provinceName: "",
            cityName: "",
            districtName: "",
            address: "",
          },
        },
        remark: "",
      };
    },
    // 单据小票打印
    async toPrint() {
      const data = await toPrint({
        objectId: this.now_order_id,
        objectType: 1, // 销售单
      });

      this.$message({
        type: "success",
        message: "操作成功",
      });
    },
    // 优惠券 勾选
    couponChange(val, row) {
      const target = this.$_common.deepClone(this.discount_list);
      target.forEach((item, index) => {
        if (val) {
          item.checked = item.id === row.id;
        } else {
          item.checked = false;
        }
      });
      this.discount_list = target;
      this.userCouponId = val ? row.id : "";
      this.getCartByUser();
    },
  },
};
</script>
<style>
.cashier .el-tabs__item {
  height: 60px !important;
  line-height: 60px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.cashier .el_tabs--top {
  width: 100%;
}

.member_info .el-form-item {
  margin-bottom: 0;
}

.member_info .el-form-item__label {
  color: #333;
}
</style>
<style scoped>
.CashierShop {
  background-color: #f7f7f7;
  height: 100vh;
  color: #333333;
  font-size: 14px;
}

.grid-content {
  background-color: #fff;
  height: calc(99vh - 90px);
  margin: 0 10px;
}

.list_info {
  width: 12%;
  height: calc(99vh - 90px);
  border-right: 1px solid #f7f7f7;
  float: left;
  font-size: 14px;
  cursor: pointer;
}

.list_info p {
  height: 68px;
  line-height: 68px;
  border-bottom: 1px solid #f7f7f7;
  text-align: center;
  margin: 0;
}

.list_content {
  border-bottom: 1px solid #f7f7f7;
  height: 60px;
  line-height: 60px;
  float: left;
  width: 86%;
}

.tips {
  padding-top: 200px;
  text-align: center;
  color: #b2b2b2;
  font-size: 12px;
  font-weight: 500;
}

.tip {
  font-size: 16px;
  color: #666;
  width: 580px;
  margin: 80px auto 20px;
  line-height: 20px;
}

.tip p {
  color: #999;
  font-size: 14px;
  margin: 5px 0;
}

.keyboard {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.keyboard-input_one {
  width: 596px;
  height: 60px;
  margin: 0 auto;
}

.keyboard-input-input {
  height: 60px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  font-size: 18px;
  color: #808695;
  padding: 6px 18px;
}

.input-box {
  width: 590px;
  margin: 0 auto;
  position: relative;
}

.input-unit {
  position: absolute;
  right: 24px;
  font-size: 18px;
  top: 50%;
  transform: translateY(-50%);
}

.input-input {
  height: 60px;
  font-size: 18px;
  padding: 6px 18px;
  display: inline-block;
  width: 590px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  color: #808695;
  cursor: text;
}

.tip_info {
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2;
  height: 60px;
  line-height: 60px;
  padding-left: 16px;
  margin: 0;
}

.color_size {
  font-size: 12px;
  color: #999;
}
.el-tabs__item {
  height: 60px;
  line-height: 60px;
}

.shop_info {
  display: inline-block;
  width: 276px;
  border-radius: 4px;
  border: 1px solid #ededed;
  margin: 20px 10px 15px 10px;
  padding: 12px 12px 12px 10px;
  cursor: pointer;
  color: #333333;
  line-height: 18px;
}

.shop_info img {
  border: 1px solid #f4f4f4;
  border-radius: 4px;
  width: 68px;
  height: 68px;
}

.shop_info .goods-info {
  width: calc(100% - 80px);
  margin-left: 10px;
}

.goods-info > p {
  padding-bottom: 5px;
  color: #333333;
  height: 36px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.head_back {
  color: #ccc;
  font-size: 14px;
  cursor: pointer;
  height: 50px;
  line-height: 50px;
  padding-left: 20px;
}

.head_back .head-back-tit {
  color: #333333;
}

.head_back .head_line {
  height: 20px;
  width: 1px;
  background-color: #ededed;
  display: inline-block;
  margin: 4px 10px -6px 10px;
}

.select_icon {
  height: 40px;
  width: 40px;
  border-radius: 50%;
  display: inline-block;
  margin: 10px 12px;
}

.shop {
  cursor: pointer;
  border: 1px solid #ededed;
  width: 248px;
  border-radius: 4px;
  margin-right: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  padding-right: 10px;
  position: relative;
  float: left;
}

.shop-on .el-icon-check {
  width: 18px;
  height: 18px;
  line-height: 18px;
  display: block;
  background: #f56c6c;
  border-radius: 50%;
  color: #fff;
  font-size: 12px;
  text-align: center;
  transform: scale(0.7);
  position: absolute;
  bottom: -9px;
  right: -9px;
}

.icon_check .el-icon-check {
  width: 100px;
  height: 100px;
  line-height: 100px;
  display: block;
  background: #fb6638;
  border-radius: 50%;
  color: #fff;
  font-size: 60px;
  font-weight: bold;
  text-align: center;
  transform: scale(0.7);
  margin: 0 auto;
}
.price {
  font-size: 26px;
  font-weight: 800;
}

.check_vip_price {
  font-size: 20px;
  font-weight: 800;
  color: #333;
  text-align: center;
  padding-top: 20px;
}

.pay_type {
  margin: 32px auto 0;
  text-align: center;
}

.button_vip {
  float: right;
  margin-right: 20px;
  margin-top: 10px;
}

.comment_info p {
  line-height: 36px;
}

.order-ul {
  margin-top: 10px;
  overflow-y: auto;
  height: calc(99vh - 280px);
}

.order-item {
  border-bottom: 1px solid #eeeeee;
  font-size: 14px;
  line-height: 38px;
  cursor: pointer;
}

.order-input-view {
  width: 92%;
  margin: 0 auto;
}

.o-goods-img img {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 4px;
}

.card-goods-li {
  border-bottom: 1px solid #f2f2f2;
  padding: 10px;
  line-height: 18px;
}

.card-goods-img {
  margin-right: 20px;
  padding-top: 12px;
}

.card-goods-img > img {
  border-radius: 4px;
  border: 1px solid #f2f2f2;
}

.card-goods-info {
  width: calc(100% - 80px);
}

.c-goods-name {
  padding-bottom: 10px;
}

.c-goods-num {
  font-size: 12px;
  display: flex;
}

.c-goods-num > div {
  flex: 3;
  line-height: 28px;
}

.price-color {
  color: #f56c6c;
}

.pay-tip {
  text-align: center;
  color: #999999;
  margin-top: 50px;
}

.pay-box {
  height: calc(99vh - 100px);
  overflow: auto;
  position: relative;
}

.right-bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
}

.btn-group {
  padding: 10px 20px;
  border-top: 1px solid #f2f2f2;
  text-align: right;
  background-color: #ffffff;
}
.pay-tip > .balance {
  text-align: left;
  width: 280px;
  padding: 17px 16px;
  background: #fef2ed;
  border-radius: 4px;
  color: #333;
  margin: 40px auto 0;
}
.pay-tip > .balance .price {
  font-size: 20px;
  font-weight: 800;
  color: #fb6638;
  margin-top: 8px;
}
.pay-tip > .balance .tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}
.change-price {
  position: relative;
  height: calc(99vh - 130px);
  overflow: auto;
}
.change-price .btn-group {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  text-align: right;
}
.list_info_p {
  background: rgb(236, 245, 255);
  color: #409eff;
}
</style>
