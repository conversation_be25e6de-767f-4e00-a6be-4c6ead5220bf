<template>
  <div class="loginContainer">
    <img src="@/assets/img/<EMAIL>" alt="" class="logoimg" />
    <div class="login-wrapper">
      <div class="login-bg">
        <img src="@/assets/img/login-bg.jpg" class="login-img" />
      </div>
      <div class="login-container">
        <div class="account-wrapper">
          <div class="account-title">
            <p
              class="account-title-item"
              :class="[tab_on === 'login_form' ? 'account-title-active' : '']"
              @click="tab_on = 'login_form'"
            >
              收银台登录
              <span class="account-title-line"></span>
            </p>
          </div>
        </div>
        <!--          登录-->
        <div v-if="tab_on === 'login_form'" class="account-login-wrapper">
          <div class="account-input-list">
            <div class="account-input-item">
              <div class="phoneWrapper">
                <p class="inputText" :class="[!!login_form.mobile ? 'inputTextFocus' : '']">手机号/帐号</p>
                <div class="inputBox" :class="[login_form_mobile ? 'alert' : '']">
                  <input
                    v-model="login_form.mobile"
                    type="text"
                    maxlength="50"
                    @blur="inputBlur($event, 'mobile')"
                    @focus="inputFocus($event, 'mobile')"
                  />
                  <p class="inputAlert hasTitle">请输入正确的帐号</p>
                </div>
              </div>
            </div>
            <div class="account-input-item">
              <div class="passwordWrapper">
                <p class="inputText" :class="[!!login_form.password ? 'inputTextFocus' : '']">密码</p>
                <div class="inputBox" :class="[login_form_password ? 'alert' : '']">
                  <input
                    v-model="login_form.password"
                    :type="login_form_look ? 'text' : 'password'"
                    maxlength="50"
                    @blur="inputBlur($event, 'password')"
                    @focus="inputFocus($event, 'password')"
                  />
                  <i
                    :class="[login_form_look ? 'icon-xianshimima' : 'icon-yincangmima']"
                    class="iconfont eyes"
                    @click="lookPwd"
                  ></i>
                  <p class="inputAlert hasTitle">请输入正确的密码</p>
                </div>
              </div>
            </div>
          </div>
          <div class="login-btn big-button" @click="loginSubmit">登录</div>
          <p class="find-pass-des" @click="tab_on = 'forgetPwd'">
            <a href="javascript:;">忘记密码</a>
          </p>
        </div>
      </div>
    </div>
    <div class="xiaoeDesc">
      <p class="xiaoe-des">
        <span class="xiaoe-line-left"></span>
        {{ enterprise_title }}
        <span class="dot"></span>
        {{ systemDesc }}
        <span class="xiaoe-line-right"></span>
      </p>
      <p class="xiaoe-records">
        Copyright © 2019-{{ fullYear }} {{ companyName }} 版权所有.
        <a target="_blank" href="http://www.beian.miit.gov.cn/" class="a_tag">
          {{ case_text }}
        </a>
        . {{ companyAddress }}
      </p>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import { mobileIsRegister, Register, login, forgetPassword, getAllEnterprisePhone } from "@/api/user";
import { companyName, icCase, companyAddress, systemDesc } from "@/config/settings";
import { sendMobileCode } from "@/api/common";
export default {
  name: "CashierLogin",
  data() {
    return {
      case_text: icCase,
      companyAddress: companyAddress,
      systemDesc: systemDesc,
      companyName: companyName,
      fullYear: new Date().getFullYear(),
      login_form_look: false,
      login_register_look: false,
      forgetPwd_look: false,
      enterprise_visible: false,
      tab_on: "login_form",
      login_form_mobile: false,
      login_form_password: false,
      login_form: {
        mobile: "",
        password: "",
      },
      login_register_mobile: false,
      login_register_password: false,
      login_register_smsCode: false,
      login_register: {
        mobile: "",
        smsCode: "",
        password: "",
      },
      enterprise_list: [],
      enterpriseName: "",
      forgetPwd_mobile: false,
      forgetPwd_password: false,
      forgetPwd_smsCode: false,
      forgetPwd_enterpriseId: false,
      forgetPwd: {
        mobile: "",
        smsCode: "",
        password: "",
        enterpriseId: "",
      },
      is_disF_yzm: false,
      is_dis_yzm: false,
      second_num: 60,
      secondF_num: 60,
    };
  },
  mounted() {},
  methods: {
    ...mapActions({
      changeUserName: "changeUserName",
      changeEnterpriseSalt: "changeEnterpriseSalt",
    }),
    inputBlur(e, prop) {
      this[this.tab_on + "_" + prop] = !this[this.tab_on][prop];
      if (prop === "mobile" && this.tab_on === "login_register") {
        this.mobileIsRegister();
      }
      if ((prop === "mobile" && this.tab_on === "forgetPwd") || (prop === "smsCode" && this.tab_on === "forgetPwd")) {
        this.getAllEnterprisePhone();
      }
    },
    inputFocus(e, prop) {
      this[this.tab_on + "_" + prop] = false;
    },
    // 判断手机号是否注册
    async mobileIsRegister() {
      const re = /^1[3456789]\d{9}$/;
      if (!re.test(this.login_register.mobile)) {
        return;
      }

      const { data } = await mobileIsRegister(this.login_register.mobile);

      if (data) {
        this.$message.warning("该手机号已被注册！");
        this.login_register.mobile = "";
      }
    },
    // 发送验证码
    async getYzm(mobile, source) {
      if (!mobile) {
        this.$message({
          type: "error",
          message: "请输入手机号",
        });
        return;
      }

      const data = await sendMobileCode({
        mobile: mobile,
        source: source,
      });

      this.$message({
        type: "success",
        message: "发送成功",
      });
      // 判断发送验证码是谁发送的
      if (source === "2") {
        this.timeToTime("is_disF_yzm", "secondF_num");
      } else {
        this.timeToTime("is_dis_yzm", "second_num");
      }
    },
    // 验证码倒计时
    timeToTime(is, num) {
      this[is] = true;
      this[num] = 60;
      const secondNumIn = setInterval(() => {
        this[num]--;
        if (this[num] === 0) {
          clearInterval(secondNumIn);
          this[is] = false;
        }
      }, 1000);
    },
    // 注册
    async Register() {
      if (!this.login_register.mobile) {
        this.login_register_mobile = true;
      }
      if (!this.login_register.smsCode) {
        this.login_register_smsCode = true;
      }
      if (!this.login_register.password) {
        this.login_register_password = true;
      }
      const isSub = Object.values(this.login_register).every((item) => !!item);
      if (!isSub) {
        return;
      }
      // 注册过的手机号不允许提交

      const data = await Register({
        mobile: this.login_register.mobile,
        password: this.login_register.password,
        repeatPassword: this.login_register.password,
        source: "6",
        smsCode: this.login_register.smsCode,
      });

      this.$message({
        type: "success",
        message: "注册成功",
      });
      this.tab_on = "login_form";
    },
    // 登录提交
    async loginSubmit() {
      if (!this.login_form.mobile) {
        this.login_form_mobile = true;
      }
      if (!this.login_form.password) {
        this.login_form_password = true;
      }
      const isSub = Object.values(this.login_form).every((item) => !!item);
      if (!isSub) {
        return;
      }
      this.changeUserName(this.login_form.mobile);

      const { data } = await login({
        mobile: this.login_form.mobile,
        password: this.login_form.password,
      });

      this.changeEnterpriseSalt(data.salt);
      sessionStorage.removeItem("isCashier");
      setTimeout(() => {
        sessionStorage.setItem(
          "user_info",
          JSON.stringify({
            ...data,
            phone: this.login_form.mobile,
          })
        );
        this.$router.push("/sIndex/1");
      }, 500);
    },
    // 忘记密码
    async forgetPwdSub() {
      if (!this.forgetPwd.mobile) {
        this.forgetPwd_mobile = true;
      }
      if (!this.forgetPwd.smsCode) {
        this.forgetPwd_smsCode = true;
      }
      if (!this.forgetPwd.enterpriseId) {
        this.forgetPwd_enterpriseId = true;
      }
      if (!this.forgetPwd.password) {
        this.forgetPwd_password = true;
      }
      const isSub = Object.values(this.forgetPwd).every((item) => !!item);
      if (!isSub) {
        return;
      }

      const data = await forgetPassword({
        mobile: this.forgetPwd.mobile,
        password: this.forgetPwd.password,
        repeatPassword: this.forgetPwd.password,
        smsCode: this.forgetPwd.smsCode,
        enterpriseId: this.forgetPwd.enterpriseId,
      });

      this.$message({
        type: "success",
        message: "密码修改成功",
      });
      this.tab_on = "login_form";
    },
    // 获取企业列表（依据手机号）
    async getAllEnterprisePhone() {
      if (!this.forgetPwd.mobile || !this.forgetPwd.smsCode) {
        return;
      }

      const { data } = await getAllEnterprisePhone({
        mobile: this.forgetPwd.mobile,
        smsCode: this.forgetPwd.smsCode,
      });

      this.enterprise_list = data;
    },
    selectEn(item) {
      this.enterprise_visible = false;
      this.forgetPwd.enterpriseId = item.id;
      this.enterpriseName = item.enterpriseName;
    },
    lookPwd() {
      this[this.tab_on + "_look"] = !this[this.tab_on + "_look"];
    },
  },
};
</script>

<style scoped>
.loginContainer {
  min-height: 730px;
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgb(244, 244, 248);
}
.logoimg {
  position: absolute;
  top: 3%;
  left: 2%;
  z-index: 1;
  width: 180px;
  cursor: pointer;
}
.login-wrapper {
  width: 1000px;
  height: 550px;
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.login-wrapper .login-bg {
  position: relative;
  width: 480px;
  height: 100%;
}
.login-wrapper .login-bg .login-img {
  width: 100%;
  height: 100%;
}
.login-wrapper .login-container {
  width: 520px;
  height: 100%;
  padding: 40px 70px;
  background: white;
  position: relative;
}
.account-wrapper .account-title {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 60px;
}
.account-wrapper .account-title .account-title-item {
  position: relative;
  margin: 0 29px;
  font-size: 18px;
  color: #999;
  font-weight: 500;
  cursor: pointer;
}
.account-wrapper .account-title .account-title-line {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  display: inline-block;
  width: 0;
  height: 2px;
  transition: all 0.2s linear;
}
.account-wrapper .account-title .account-title-active {
  color: #333;
}
.account-wrapper .account-title .account-title-active .account-title-line {
  width: 40px;
  background-color: #105cfb;
}
.picture-tag {
  position: absolute;
  right: 33px;
  top: 33px;
  display: flex;
  justify-content: flex-start;
}
.picture-tag .picture-tag-name {
  position: relative;
  margin: 0;
  margin-right: 8px;
  box-sizing: content-box;
  width: 87px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  font-size: 12px;
  background: #d9e5ff;
  border: 1px solid #bad0ff;
  color: #3778ff;
}
.picture-tag .picture-icon {
  position: relative;
  top: -4px;
  width: 42px;
  height: 42px;
  cursor: pointer;
}
.picture-tag .picture-tag-name::before {
  border: 6px solid transparent;
  border-left-color: #bad0ff;
  z-index: 1;
  content: " ";
  display: block;
  position: absolute;
  left: 100%;
  top: 50%;
  margin-top: -6px;
}
.picture-tag .picture-tag-name::after {
  content: " ";
  display: block;
  position: absolute;
  left: 100%;
  top: 50%;
  margin-top: -6px;
  border: 6px solid transparent;
  border-left-color: #d6e4f7;
  transform: translateX(0.5px);
  margin-left: -2px;
  z-index: 1;
}
.account-login-wrapper {
  margin-top: 50px;
}
.account-login-wrapper .account-input-item {
  margin-bottom: 45px;
}
.phoneWrapper {
  color: #353535;
  position: relative;
}
.inputText {
  color: #999;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  font-size: 16px;
  margin: 0;
  line-height: 35px;
  transform: translate3d(0, 28px, 0) scale(1);
  transform-origin: left top;
  position: absolute;
  bottom: 30px;
}
.inputTextFocus {
  transform: translateZ(0) scale(0.75);
}
.inputBox {
  position: relative;
  width: 100%;
  height: 36px;
}
.inputBox.alert input {
  border-color: #ff3366;
}
.inputBox input:focus {
  border-bottom: 1px solid #2a75ed;
}
.inputBox input {
  font-size: 16px;
  border: none;
  outline: none;
  box-shadow: none;
  width: 100%;
  line-height: 34px;
  height: 36px;
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  border-bottom: solid 1px #d4d9e2;
  background: transparent;
  color: #333;
  transition: all 0.2s linear;
}
.inputBox.alert .inputAlert.hasTitle {
  margin-left: 66px;
}
.inputBox .inputAlert {
  display: none;
}
.inputBox.alert .inputAlert {
  display: block;
  color: #ff3366;
  font-size: 12px;
  margin: 0;
  line-height: 12px;
  position: absolute;
  right: 0;
  bottom: -18px;
}
.passwordWrapper {
  color: #353535;
  position: relative;
}
.passwordWrapper .eyes {
  position: absolute;
  bottom: 5px;
  right: 0;
  color: #d4d9e2;
  cursor: pointer;
  font-size: 24px;
}
.account-login-wrapper .account-operate {
  position: absolute;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  width: 380px;
}
.account-login-wrapper .account-operate .remind-pwd {
  position: relative;
}
.account-login-wrapper a {
  color: #666;
  font-size: 12px;
}
.account-login-wrapper a:hover {
  color: #2a75ed;
}
.account-login-wrapper .login-btn {
  margin: 0 auto 20px;
  width: 380px;
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  text-align: center;
  cursor: pointer;
  color: #fff;
  background-color: #105cfb;
  outline: none;
  /*transition: all 100ms ease;*/
  letter-spacing: 2px;
}
.big-button {
  border-radius: 4px;
}
/*  底部*/
.xiaoeDesc {
  line-height: 20px;
  text-align: center;
  position: absolute;
  bottom: 3.6%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 0;
  min-width: 600px;
}
.xiaoeDesc .xiaoe-des {
  position: relative;
  margin: 0;
  font-size: 14px;
  letter-spacing: -0.2px;
  color: #666666;
}
.xiaoeDesc .xiaoe-des .xiaoe-line-left {
  position: relative;
  display: inline-block;
  border-bottom: 2px solid #888;
  width: 30px;
  top: -3px;
}
.xiaoeDesc .xiaoe-des .dot {
  display: inline-block;
  width: 3px;
  height: 3px;
  margin: 3px 5px;
  border-radius: 50%;
  background-color: #888888;
}
.xiaoeDesc .xiaoe-des .xiaoe-line-right {
  position: relative;
  display: inline-block;
  border-bottom: 2px solid #888;
  width: 30px;
  top: -3px;
}
.xiaoeDesc .xiaoe-records {
  margin: 15px 0 0;
  font-size: 12px;
  color: #999;
}
.a_tag {
  color: #999;
}
.a_tag:hover {
  text-decoration: underline;
}
/*  注册 */
.invalidPhoneWrapper .verify-code-box {
  position: relative;
  top: -10px;
  z-index: 1;
}
.verify-btn {
  background-color: transparent;
  color: #105cfb;
}
.verify-btn:hover {
  color: #ffffff;
  background: #3f7cfb;
}

.blueBtn {
  width: 104px;
  height: 36px;
  border-radius: 4px;
  border: solid 1px #2a75ed;
  line-height: 34px;
  font-size: 12px;
  display: inline-block;
  text-align: center;
  background: #fff;
  color: #2a75ed;
  cursor: pointer;
}
.register-wrapper .register-btn {
  margin-bottom: 20px;
  width: 380px;
  height: 56px;
  text-align: center;
  font-size: 16px;
  line-height: 56px;
  border: none;
  cursor: pointer;
  color: #fff;
  background-color: #105cfb;
  outline: none;
  letter-spacing: 2px;
}
.account-login-wrapper .find-pass-des {
  text-align: center;
  color: #666;
  font-size: 13px;
}
.account-login-wrapper .find-pass-des a {
  color: #105cfb;
  font-size: 13px;
}
.blueBtn.grey {
  color: #b2b2b2;
  border-color: #b2b2b2;
  cursor: auto;
}
.blueBtn.grey:hover {
  background-color: transparent;
  color: #b2b2b2;
}
/*  企业*/
.enterprise_li {
  line-height: 36px;
  padding: 0 20px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
}
.enterprise_li:hover {
  background-color: #f5f7fa;
}
.enterprise_on {
  color: #2a75ed;
  font-weight: bold;
}
</style>
