<template>
  <el-dialog
    title="设置"
    :visible.sync="isShow"
    width="50%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="close"
  >
    <el-form :inline="true" size="small" label-width="140px">
      <el-form-item label="价格模式:">
        <el-radio-group v-model="add_form.price_type">
          <el-radio :label="1">统一价格</el-radio>
          <el-radio :label="2">独立价格</el-radio>
        </el-radio-group>
        <p class="form-tip">统一价格:该商品在此门店中将按照总店铺的价格进行商品出售，会员权益及营销活动同步进行；</p>
        <p class="form-tip">独立价格:将按照此门店单独设置的价格进行出售，并且会员权益及营销活动不会生效。</p>
      </el-form-item>
      <el-form-item label="库存模式:">
        <el-radio-group v-model="add_form.stock_type">
          <el-radio :label="1">统一库存</el-radio>
          <el-radio :label="2">独立库存</el-radio>
        </el-radio-group>
        <p class="form-tip">统一库存:该商品在此门店中将按照总店铺库存进行商品出售，门店无法修改库存；</p>
        <p class="form-tip">独立库存:将按照此门店单独设置的库存进行出售，需要门店单独进行库存管理。</p>
      </el-form-item>
      <el-form-item label="经营权:">
        <el-radio-group v-model="add_form.management">
          <el-radio :label="1">禁售</el-radio>
          <el-radio :label="2">允许出售</el-radio>
        </el-radio-group>
        <p class="form-tip">此项门店无法自行设置，总后台统一管理，禁售时，门店对此商品不可见，无法上架售卖；</p>
        <p class="form-tip">允许出售时，门店可以对此商品进行设置项修改和上架售卖。</p>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "GoodsSetOnly",
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    goodsId: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      add_form: {
        price_type: 1,
        stock_type: 1,
        management: 1,
        goods_type: 1,
      },
    };
  },
  methods: {
    close() {
      this.$emit("close");
    },
    confirm() {
      this.close();
      this.$emit("confirm");
    },
  },
};
</script>

<style scoped></style>
