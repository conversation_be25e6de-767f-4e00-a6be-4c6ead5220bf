<template>
  <el-dialog
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :title="setTit"
    :visible.sync="isShow"
    width="30%"
    @close="close"
  >
    <el-form ref="set_form" :model="set_form" label-width="100px">
      <el-form-item v-if="setTit === '虚拟销量'" label="销量区间:">
        <el-input-number v-model="sale_num_form.minVal" :controls="false"></el-input-number>
        -
        <el-input-number v-model="sale_num_form.maxVal" :controls="false"></el-input-number>
        <p class="form-tip">虚拟总销量等于销量区间内产生的随机数</p>
      </el-form-item>
      <el-form-item v-if="setTit === '转移分类'" label="转移分类:">
        <GoodsCategory v-model="set_form.category" check-strictly />
      </el-form-item>
      <el-form-item v-if="setTit === '设置品牌'" label="设置品牌:">
        <el-input v-model="set_form.brandName" readonly style="width: 240px" placeholder="请选择品牌">
          <i slot="suffix" class="el-input__icon el-icon-search" @click="sel_brand = true"></i>
        </el-input>
      </el-form-item>
      <div v-if="setTit === '物流设置'">
        <el-form-item v-if="false" label="物流支持:" prop="deliverySupIds">
          <el-checkbox-group v-model="set_form.deliverySupIds">
            <el-checkbox label="1">快递</el-checkbox>
            <el-checkbox label="2">自提</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <br />
        <el-form-item label="快递运费:">
          <el-radio-group v-model="set_form.expressType">
            <el-radio :label="1">包邮</el-radio>
            <el-radio :label="2">运费模版</el-radio>
            <el-radio :label="3">统一运费</el-radio>
          </el-radio-group>
        </el-form-item>
        <br />
        <div v-if="set_form.expressType === 2">
          <el-form-item label="运费模板:" prop="ruleId">
            <el-select v-model="set_form.ruleId" placeholder="请选择">
              <el-option
                v-for="(item, index) in express_list"
                :key="index"
                :label="item.title"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div v-if="set_form.expressType === 3">
          <el-form-item label="统一运费:" prop="expressFee">
            <el-input-number v-model="set_form.expressFee" :controls="false"></el-input-number>
            <span>元</span>
          </el-form-item>
        </div>
        <el-form-item v-if="false" label="是否展示快递:">
          <el-radio-group v-model="set_form.showExpress">
            <el-radio :label="4">不展示</el-radio>
            <el-radio :label="5">展示</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div v-if="setTit === '价格模式'">
        <el-form-item label="价格模式:" prop="ruleId">
          <el-radio-group v-model="set_form.priceSet">
            <el-radio :label="5">统一价格</el-radio>
            <el-radio :label="4">独立价格</el-radio>
          </el-radio-group>
          <p class="form-tip">统一价格：该商品在此门店中将按照总店铺的价格进行商品出售，会员权益及营销活动同步进行；</p>
          <p class="form-tip">独立价格：将按照此门店单独设置的价格进行出售，并且会员权益及营销活动不会生效。</p>
        </el-form-item>
      </div>
      <div v-if="setTit === '库存模式'">
        <el-form-item label="价格模式:" prop="ruleId">
          <el-radio-group v-model="set_form.stockSet">
            <el-radio :label="5">统一库存</el-radio>
            <el-radio :label="4">独立库存</el-radio>
          </el-radio-group>
          <p class="form-tip">统一库存：该商品在此门店中将按照总店铺库存进行商品出售，门店无法修改库存；</p>
          <p class="form-tip">独立库存：将按照此门店单独设置的库存进行出售，需要门店单独进行库存管理。</p>
        </el-form-item>
      </div>
      <div v-if="setTit === '经营权'">
        <el-form-item label="经营权:" prop="ruleId">
          <el-radio-group v-model="set_form.saleSet">
            <el-radio :label="4">禁售</el-radio>
            <el-radio :label="5">允许出售</el-radio>
          </el-radio-group>
          <p class="form-tip">
            此项门店无法自行设置，总后台统一管理，禁售时，门店对此商品不可见，无法上架售卖；
            <br />
            允许出售时，门店可以对此商品进行设置项修改和上架售卖。
          </p>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button :loading="more_sub_btn" type="primary" @click="moreSubData"> 确 定 </el-button>
    </span>
  </el-dialog>
</template>

<script>
import GoodsCategory from "@/component/common/GoodsCategory.vue";
import { setSalesNumBatch, setBrand, updateCategory, batchGoodsExpress } from "@/api/goods";
import { getAllExpressRule } from "@/api/System";
export default {
  name: "GoodsSet",
  components: {
    GoodsCategory,
  },
  props: {
    chooseData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    isShow: {
      type: Boolean,
      default: false,
    },
    setTit: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      express_list: [],
      more_sub_btn: false,
      sale_num_form: {
        ids: [],
        minVal: "",
        maxVal: "",
      },
      set_form: {
        masterUnitId: "",
        shopName: "",
        brandName: "",
        category: [],
        branchUnit: [],
        deliverySupIds: ["1", "2"], // 物流支持 固定值 1 快递 2 自提 传参格式：1,2
        expressType: 1, // 快递运费 1 包邮 2 运费模版 3 固定费用
        expressFee: "", // 固定费用
        showExpress: 5, // 是否展示快递 5展示 4不展示
        ruleId: "", // 运费模版id
        priceSet: 5, // 价格模式
        stockSet: 5, // 库存模式
        saleSet: 5, // 经营权
      },
    };
  },
  created() {
    this.getAllExpressRule();
  },
  methods: {
    // 获取运费模版
    async getAllExpressRule() {
      const { data } = await getAllExpressRule();
      this.express_list = data;
    },
    // 批量提交
    moreSubData() {
      if (this.setTit === "转移分类") {
        this.updateCategory();
      }
      if (this.setTit === "设置品牌") {
        this.setBrand();
      }
      if (this.setTit === "虚拟销量") {
        this.setSalesNumBatch();
      }
      if (this.setTit === "物流设置") {
        this.batchGoodsExpress();
      }
    },
    // 转移分类
    async updateCategory() {
      if (!this.chooseData.length) {
        this.$message.warning("请选择要操作的商品");
        return;
      }
      if (!this.set_form.category) {
        this.$message.warning("请选择要转移的分类");
        return;
      }
      const idData = this.chooseData.map((item) => {
        return item.basicGoodsId;
      });
      this.more_sub_btn = true;
      const data = await updateCategory({
        id: idData, // 要移动的基础商品id
        categoryId: this.set_form.category[this.set_form.category.length - 1], // 移动至商品分类id
        categoryPath: this.set_form.category.join(","), // 新的商品分类路径
      });
      this.more_sub_btn = false;

      this.$message.success("操作成功");
      this.confirm();
    },
    //  设置品牌
    async setBrand() {
      if (!this.chooseData.length) {
        this.$message.warning("请选择要操作的商品");
        return;
      }
      if (!this.brandId) {
        this.$message.warning("请选择要操作的品牌");
        return;
      }
      const idData = this.chooseData.map((item) => {
        return item.basicGoodsId;
      });
      this.more_sub_btn = true;
      const data = await setBrand({
        id: idData,
        brandId: this.brandId,
      });
      this.more_sub_btn = false;

      this.$message.success("操作成功");
      this.confirm();
    },
    // 设置虚拟销量
    async setSalesNumBatch() {
      if (!this.chooseData.length) {
        this.$message.warning("请选择要操作的商品");
        return;
      }
      if (!this.sale_num_form.minVal) {
        this.$message.warning("虚拟销量区间最小值不能为0");
        return;
      }
      if (!this.sale_num_form.maxVal) {
        this.$message.warning("虚拟销量区间最大值不能为0");
        return;
      }
      if (this.sale_num_form.maxVal <= this.sale_num_form.minVal) {
        this.$message.warning("虚拟销量区间最大值必须大于最小值");
        return;
      }
      const idData = this.chooseData.map((item) => {
        return item.id;
      });
      this.more_sub_btn = true;
      const data = await setSalesNumBatch({
        ...this.sale_num_form,
        ids: idData,
      });
      this.more_sub_btn = false;

      this.$message.success("操作成功");
      this.confirm();
    },
    // 批量设置运费
    async batchGoodsExpress() {
      if (!this.chooseData.length) {
        this.$message.warning("请选择要操作的商品");
        return;
      }
      const idData = this.chooseData.map((item) => {
        return item.id;
      });
      this.more_sub_btn = true;
      const data = await batchGoodsExpress({
        expressType: this.set_form.expressType,
        ruleId: this.set_form.ruleId,
        expressFee: this.set_form.expressFee,
        ids: idData,
      });
      this.more_sub_btn = false;

      this.$message.success("操作成功");
      this.confirm();
    },
    close() {
      this.$emit("close");
    },
    confirm() {
      this.$emit("confirm");
      this.close();
    },
  },
};
</script>

<style scoped></style>
