<!--员工列表弹窗-->
<template>
  <el-dialog
    title="员工列表"
    :visible="isShow"
    width="50%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <div class="search-wrp">
      <el-input
        v-model="keywords"
        size="mini"
        style="width: 30%"
        clearable
        placeholder="请输入名称/手机号"
        @clear="searchClick"
      >
        <el-button slot="append" type="primary" icon="el-icon-search" @click="searchClick"></el-button>
      </el-input>
    </div>
    <el-table
      v-loading="loading"
      :data="tabelData"
      size="small"
      border
      @row-dblclick="dbSelect"
      @selection-change="handleSelectionChange"
    >
      <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
      <el-table-column prop="staffName" label="姓名"></el-table-column>
      <el-table-column prop="departmentName" label="部门"></el-table-column>
      <el-table-column prop="mobile" label="手机号"></el-table-column>
      <el-table-column label="选择" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" plain icon="el-icon-check" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="isCheck" slot="btn-div">
        <el-button size="small" type="primary" @click="confirm">确认</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { getStaffByShopId } from "@/api/Shop";
export default {
  name: "ClientListModal",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    isserch: {
      type: Boolean,
      default: true,
    },
    targetId: {
      type: [String, Number],
      default: 0,
    },
  },
  data() {
    return {
      search_data: [],
      choose_data: [],
      tabelData: [],
      pre_page: 10,
      page: 1,
      // modalShow: this.clientModalShow,
      selectedIndex: null,
      keywords: "",
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      selectedClient: {},
      row_id: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      const { data, pageTotal } = await getStaffByShopId({
        page: this.page,
        pageSize: this.pre_page,
        shopId: this.targetId,
      });
      this.loading = false;

      this.tabelData = data;
      this.total = pageTotal;
    },
    // 搜索前页数变1
    searchClick() {
      this.page = 1;
      this.getList();
    },
    // 双击选择
    dbSelect(row) {
      if (this.isserch) {
        this.$emit("confirm", [{ ...row }]);
        this.cancel();
      }
      this.row_id = row;
    },
    confirm() {
      this.$emit("confirm", this.choose_data);
      this.$emit("confirm", [{ ...this.row_id }]);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
    handleSelectionChange(val) {
      this.choose_data = val;
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
