<template>
  <div>
    <el-dialog title="员工" :visible.sync="add_staff" width="50%">
      <div style="height: 400px" class="page_div">
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="5" style="border-right: 1px solid #ededed; height: 364px">
            <div>
              <p class="staff_color">全部</p>
            </div>
          </el-col>
          <el-col :span="19">
            <div>
              <el-input
                v-model="keyword"
                placeholder="搜索员工名称/手机号"
                style="width: 40%"
                size="small"
                prefix-icon="el-icon-search"
                @clear="pageChange(1)"
              >
                <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
              </el-input>
              <div class="preview-content" @click="addStaffFn">
                <div class="preview-content-member">
                  <div class="block">
                    <el-avatar :size="50" :src="circleUrl"></el-avatar>
                  </div>
                  <div style="flex: 1; margin: 8px 0 0 10px">
                    <p>111</p>
                    <p class="subtitle">111</p>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="add_staff = false">取 消</el-button>
        <el-button type="primary" @click="add_staff = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getShopByStaff, getStaffByShopId } from "@/api/Shop";
export default {
  name: "AddStaff",
  props: {
    addStaff: {
      type: Boolean,
      default: false,
      total: 0,
      page: 1,
      pageSize: 10,
      circleUrl: "https://cube.elemecdn.com/3/7c/********************************.png",
    },
  },
  data() {
    return {
      add_staff: false,
      keyword: "",
      staff_info: false,
    };
  },
  created() {
    this.add_staff = this.addStaff;
  },
  methods: {
    pageChange(val) {
      this.page = val;
    },
    addStaffFn() {
      this.staff_info = true;
    },
  },
};
</script>

<style scoped>
.staff_color {
  background: rgb(236, 245, 255);
  color: #409eff;
  height: 36px;
  line-height: 36px;
  text-align: center;
}
.page_div {
  border-top: 1px solid #ededed;
  border-bottom: 1px solid #ededed;
}
.preview-content {
  overflow: auto;
  margin: 20px 0 0 10px;
}
.preview-content-member {
  display: flex;
  width: 244px;
  float: left;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  padding: 12px;
  position: relative;
  cursor: pointer;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #ededed;
}
.subtitle {
  color: #999;
}
</style>
