<template>
  <div style="background-color: #fff; padding: 20px">
    <div class="page-tip-div">
      <p>
        添加您已创建的员工账号，该员工账号在原有的权限基础上，新增对此门店的管理权限（多门店应用工具者拥有全部门店的操作权限，无法选择此类账号作为门店店长）；
      </p>
      <p>每个门店只能添加一个店长，同一个账号可以成为多个门店的店长，门店下的员工由店长在门店后台进行配置。</p>
    </div>
    <div>
      <el-button v-if="!tableData.length" type="primary" size="small" @click="addStaff(true)"> 添加员工 </el-button>
    </div>
    <el-table :data="tableData" style="width: 100%; margin-top: 10px" size="small">
      <el-table-column prop="staffName" label="姓名"></el-table-column>
      <el-table-column prop="mobile" label="手机号"></el-table-column>
      <el-table-column prop="address" label="账号"></el-table-column>
      <el-table-column prop="roleName" label="角色"></el-table-column>
      <el-table-column prop="createTime" label="创建时间">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="address" label="添加人"></el-table-column>
      <el-table-column prop="address" label="操作">
        <template>
          <el-button type="text" @click="delManager">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--        <AddStaff v-if="add_staff" :add-staff="add_staff"></AddStaff>-->
    <StaffList
      v-if="staff_show"
      :is-show="staff_show"
      :is-check="false"
      :isserch="isserch"
      :target-id="target_id"
      @cancel="staff_show = false"
      @confirm="staffSel"
    />
  </div>
</template>

<script>
import { setStaffType, getStaffInfo, delManager } from "@/api/Shop";
import StaffList from "@/component/common/staffListModal";
export default {
  name: "StoreShopowner",
  components: {
    StaffList,
  },
  data() {
    return {
      target_id: 0,
      tableData: [],
      add_staff: false,
      staff_show: false,
      isserch: true,
      salesManId: 0,
      total: 0,
      page: 1,
      pageSize: 10,
      managerId: 0,
      manager_id: 0,
    };
  },
  created() {
    this.target_id = this.$route.query.id;
    this.managerId = this.$route.query.managerId;
    if (this.managerId !== "null") {
      this.getStaffInfo();
    }
  },
  methods: {
    pageChange(val) {
      this.page = val;
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
    addStaff(isserch) {
      this.isserch = isserch;
      this.staff_show = true;
    },
    // 选择员工
    staffSel(val) {
      this.$confirm("确认添加该员工为店长吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        if (this.isserch) {
          this.salesManId = val[0].id;
          this.tableData = val;
          this.setStaffType();
        }
      });
    },
    // 添加店长
    async setStaffType() {
      const data = await setStaffType({
        id: this.salesManId,
        shopId: this.target_id,
      });
      this.$message.success("添加成功");
      this.getStaffInfo(data.data);
    },
    // 职工详情
    async getStaffInfo(id) {
      if (typeof id === "number") {
        this.managerId = id;
      }
      const data = await getStaffInfo({
        userCenterId: this.managerId,
      });
      if (!id) {
        this.tableData.push(data.data);
      }
    },
    // 删除店长
    async delManager(id) {
      this.$confirm("确认删除店长吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delManager({
          shopId: this.target_id,
        });
        this.$message({
          type: "success",
          message: "操作成功",
        });
        this.$router.push("./StoreList");
      });
    },
  },
};
</script>

<style scoped></style>
