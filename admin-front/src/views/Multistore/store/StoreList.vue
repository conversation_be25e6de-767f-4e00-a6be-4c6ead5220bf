<template>
  <Container>
    <div slot="left">
      <el-input
        v-model="keyword"
        style="width: 200px; margin-right: 10px"
        placeholder="搜索门店名称"
        size="small"
        clearable
        @keyup.enter.native="pageChange(1)"
        @clear="pageChange(1)"
      >
        <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
      </el-input>
      <el-button
        v-if="$accessCheck($Access.MultistoreList_AddStore)"
        size="small"
        type="primary"
        @click="$router.push('/Multistore/AddStore')"
      >
        创建店铺
      </el-button>
    </div>
    <div>
      <el-table :data="store_list">
        <el-table-column prop="name" label="门店名称" min-width="140" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="shopType" label="门店类型" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.shopType === 1 ? "联营商铺" : "直营商铺" }}
          </template>
        </el-table-column>
        <el-table-column prop="mobile" label="联系方式" min-width="120"></el-table-column>
        <el-table-column prop="date" label="地址" min-width="180" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span v-if="scope.row.area">
              {{ scope.row.area.provinceName || "" }}-{{ scope.row.area.cityName }}-{{ scope.row.area.districtName }}-{{
                scope.row.area.address
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="managerName" label="店长" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.managerName || "未设置" }}
          </template>
        </el-table-column>
        <el-table-column prop="managerName" label="仓库" :show-overflow-tooltip="true" min-width="140">
          <template slot-scope="scope">
            <span v-for="(item, index) in scope.row.warehouseData" :key="index"> {{ item.warehouseName }}; </span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="营业状态" min-width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 营业中 </span>
            <span v-else class="info-status">已打烊</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template slot-scope="scope">
            <el-button
              v-if="$accessCheck($Access.MultistoreList_shopGoodsManage)"
              type="text"
              @click="$router.push('./shopAdministration')"
            >
              商品管理
            </el-button>
            <el-button
              v-if="$accessCheck($Access.MultistoreList_editStore)"
              type="text"
              @click="editData(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
              v-if="$accessCheck($Access.MultistoreList_setMasterShop)"
              type="text"
              :disabled="scope.row.isMaster === 5"
              @click="setTopShop(scope.row.id)"
            >
              {{ scope.row.isMaster === 5 ? "总店" : "设为总店" }}
            </el-button>
            <el-dropdown @command="moreChange($event, scope.row)">
              <span class="el-dropdown-link">
                更多
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <!--                <el-dropdown-item command="a">同城配送</el-dropdown-item>-->
                <!--                <el-dropdown-item command="b">运费模版</el-dropdown-item>-->
                <el-dropdown-item v-if="$accessCheck($Access.MultistoreList_shopManager)" command="c">
                  门店店长
                </el-dropdown-item>
                <el-dropdown-item v-if="$accessCheck($Access.MultistoreList_shopSystem)" command="d">
                  门店后台
                </el-dropdown-item>
                <!--                <el-dropdown-item command="e">删除</el-dropdown-item>-->
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <FooterPage
        :page-size="pageSize"
        :total-page.sync="total"
        :current-page.sync="page"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      ></FooterPage>
      <el-dialog
        title="购买门店数量"
        :visible.sync="buy_show"
        width="30%"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        @close="buy_show = false"
      >
        <el-form size="small" label-width="80px" style="width: 50%; margin: 0 auto">
          <el-form-item label="购买:">
            <span class="buy-val">门店数量</span>
          </el-form-item>
          <el-form-item label="价格:">
            <span class="buy-val-price">0.00</span>
          </el-form-item>
          <el-form-item label="门店数:" style="position: relative">
            <el-radio-group v-model="buy_form.num" size="small">
              <el-radio :label="1" border>
                10个
                <i class="el-icon-success"></i>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="优惠码:">
            <el-input v-model="buy_form.discount_code" placeholder="请输入优惠码"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="buy_show = false"> 立即购买 </el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </Container>
</template>

<script>
import { mapActions } from "vuex";
import { getAllShop, setTopShop, updateShopStatus, search, changeManagerForShop, delShop } from "@/api/Shop";
export default {
  name: "StoreList",
  data() {
    return {
      buy_show: false,
      buy_form: {
        discount_code: "",
        num: 1,
      },
      keyword: "",
      store_list: [],
      total: 0,
      page: 1,
      pageSize: 10,
      warehouseData: [],
    };
  },
  created() {
    this.getAllShop();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    ...mapActions({
      changeSystemType: "MUser/changeSystemType",
      delAllVisitedRoutes: "tagsBar/delAllVisitedRoutes",
      changeStoreData: "MUser/changeStoreData",
    }),
    //  获取列表
    async getAllShop() {
      const data = await getAllShop({
        page: this.page,
        pageSize: this.pageSize,
      });
      let storeList = [];
      storeList = data.data.filter((item) => !item.merchantId);
      storeList.forEach((itemS, indexS) => {
        let warehouseData = [];
        for (let i in itemS.warehouseData) {
          warehouseData.push({
            ...itemS.warehouseData[i],
          });
        }
        itemS.warehouseData = warehouseData;
      });
      this.store_list = storeList;
      this.total = data.pageTotal;
    },
    // 点击搜索
    async searchList() {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        name: this.keyword,
      };

      const data = await search(params);
      this.store_list = data.data;
      this.total = data.pageTotal;
    },
    getData() {
      const obj = {
        name: this.keyword,
        // shopType: this.search_form.shopType,
        // enableStatus: this.search_form.enableStatus,
      };
      const isKey = this.$_common.isSerch(obj);
      if (isKey) {
        this.searchList();
      } else {
        this.getAllShop();
      }
    },
    editData(id) {
      this.$router.push(`/SystemSettings/liansuoguanli/EditShop/${id}`);
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 设置总店
    async setTopShop(id) {
      this.$confirm("确认设置该门店为总店吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await setTopShop(id);
        this.$message.success("设置成功");
        this.getData();
      });
    },
    moreChange(item, row) {
      switch (item) {
        case "a":
          break;
        case "b":
          break;
        case "c":
          this.$router.push("./StoreShopowner?id=" + row.id + "&managerId=" + row.managerId);
          break;
        case "d":
          this.delAllVisitedRoutes();
          this.changeSystemType(2);
          this.changeStoreData(row);
          let routeData = this.$router.resolve({
            path: `/SingleStore/goods/GoodsAdministration`,
          });
          window.open(routeData.href, "_blank");
          setTimeout(() => {
            this.$confirm("您已切换到其他门店，必须刷新页面才能继续操作?", "提示", {
              confirmButtonText: "确定",
              showCancelButton: false,
              type: "warning",
            }).then(() => {
              window.location.replace("/#/SingleStore/goods/GoodsAdministration");
              window.location.reload();
            });
          }, 500);
          break;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.left-tip {
  padding-left: 20px;
  font-size: 12px;
}
.buy-val {
  font-size: 16px;
}
.buy-val-price {
  font-size: 20px;
  color: #1c8fef;
  font-weight: bold;
}
.el-icon-success {
  color: #1c8fef;
  position: absolute;
  right: -6px;
  bottom: -7px;
}
</style>
