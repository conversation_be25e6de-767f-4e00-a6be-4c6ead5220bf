<template>
  <ContainerTit>
    <div slot="headr">
      <el-button type="primary">保存</el-button>
    </div>
    <el-form
      ref="add_form"
      :model="add_form"
      label-width="140px"
      :rules="base_rules"
      size="small"
      style="background-color: #fff; padding: 20px"
    >
      <el-form-item label="功能启用：">
        <el-switch v-model="add_form.function_true" active-color="#36B365" inactive-color="#ff4949"></el-switch>
        <p class="form-tip">
          注意：开启功能前，请将多门店的所有选项设置完成，否则可能会造成您的商城无法使用
          <!--          <el-button type="text" size="mini">使用教程</el-button>-->
          。
        </p>
      </el-form-item>
      <!--      <el-form-item label="可创建门店数量：">-->
      <!--        1/85-->
      <!--        <el-button type="text" size="mini">购买门店数量</el-button>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="功能到期时间：">2020-12-30</el-form-item>-->
      <el-form-item label="自动定位：">
        <el-radio-group v-model="add_form.region">
          <el-radio :label="1"> 所有用户每次进入商城自动定位至最近的门店 </el-radio>
          <el-radio :label="2"> 新用户自动定位，老用户进入上次购买过的门店 </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="门店切换：">
        <el-radio-group v-model="add_form.shop_select">
          <el-radio :label="1">允许用户手动切换门店</el-radio>
          <el-radio :label="2">禁止用户手动切换门店</el-radio>
        </el-radio-group>
        <p class="form-tip">禁止后，仅靠用户地址定位选择门店</p>
      </el-form-item>
    </el-form>
  </ContainerTit>
</template>

<script>
export default {
  name: "StoreSet",
  data() {
    return {
      add_form: {
        function_true: false,
        region: 1,
        shop_select: 1,
      },
      base_rules: {},
    };
  },
};
</script>

<style scoped>
.tip {
  font-size: 12px;
  color: #ccc;
}
</style>
