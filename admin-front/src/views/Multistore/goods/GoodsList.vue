<template>
  <Container>
    <div slot="right">
      <el-form size="small" inline>
        <el-form-item>
          <el-input v-model="search.keyword" size="small" placeholder="搜索商品名称">
            <el-button slot="append" icon="el-icon-search"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <GoodsCategory v-model="search.categoryPath" clearable size="small" @change="categoryChange" />
        </el-form-item>
      </el-form>
    </div>
    <el-table border :data="goods_list">
      <el-table-column prop="title" label="商品信息" min-width="220">
        <template slot-scope="scope">
          <div class="clearfix">
            <div class="float_left">
              <el-image
                fit="contain"
                style="width: 40px; height: 40px"
                :src="scope.row.images[0]"
                alt=""
                lazy
                scroll-container=".el-table__body-wrapper"
              >
                <div
                  slot="error"
                  style="
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    box-sizing: border-box;
                    border: 1px solid #eee;
                  "
                >
                  暂无图片
                </div>
              </el-image>
            </div>
            <div class="float_left goods-name-view" style="margin-left: 10px">
              <div class="goods-title">
                {{ scope.row.title }}
              </div>
              <div class="goods-no">
                {{ scope.row.code }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="goods" label="价格" min-width="120">
        <template slot-scope="scope">
          <el-popover placement="right" width="1000" trigger="click" @show="showSpec(scope.$index)">
            <el-table v-loading="spec_loading" :height="500" :data="scope.row.goods_sku_list" size="small" border>
              <el-table-column property="unitName" label="单位" min-width="60px"></el-table-column>
              <el-table-column property="specValueName" label="属性" min-width="120px"></el-table-column>
              <el-table-column property="enabledLadder" label="阶梯价" min-width="70px">
                <template slot-scope="props">
                  <el-tag v-if="props.row.enabledLadder === 1" type="success"> 是 </el-tag>
                  <el-tag v-else type="info">否</el-tag>
                </template>
              </el-table-column>
              <el-table-column property="salePrice" label="销售价(元)" min-width="160px">
                <template slot-scope="props">
                  <span v-if="props.row.enabledLadder === 0" style="color: #ff4040"> ¥{{ props.row.salePrice }} </span>
                  <div v-else>
                    <p v-for="(item, index) in props.row.ladderPrice" :key="index">
                      <span>
                        数量：
                        <span style="color: #ff4040">
                          {{ item.from }}-{{ index === props.row.ladderPrice.length - 1 ? "∞" : item.to }}
                        </span>
                        ，
                      </span>
                      <span>
                        价格：
                        <span style="color: #ff4040">¥{{ item.price }}</span>
                        ；
                      </span>
                    </p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column property="marketPrice" label="市场价(元)" min-width="100px"></el-table-column>
              <el-table-column property="setNum" label="起订数量" min-width="100px"></el-table-column>
              <el-table-column property="inventory" label="可用库存" min-width="100px">
                <template slot-scope="props">
                  {{ props.row.inventory - 0 }}
                </template>
              </el-table-column>
              <el-table-column property="salesNum" label="销量" min-width="100px"></el-table-column>
              <el-table-column property="barCode" label="条形码" min-width="100px"></el-table-column>
            </el-table>
            <el-button slot="reference" size="mini" plain type="primary">
              {{ scope.row.specTotal }}
              种规格
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="inventorTotal" label="总库存" min-width="130">
        <template slot-scope="scope">
          {{ Number(scope.row.inventorTotal) || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="100">
        <template>
          <el-button type="text" @click="$router.push('/Multistore/ShopAdministration')"> 编辑 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import GoodsCategory from "@/component/common/GoodsCategory.vue";
import { getAllGoods, searchGood, getGoodsInfo } from "@/api/goods";
import { mapGetters } from "vuex";
export default {
  name: "GoodsList",
  components: {
    GoodsCategory,
  },
  data() {
    return {
      merchantId: "",
      search: {
        keyword: "",
        categoryPath: [],
      },
      goods_list: [],
      spec_loading: false,
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  computed: {
    ...mapGetters({
      storeData: "MUser/storeData",
    }),
  },
  created() {
    if (this.systemType === 3) {
      this.merchantId = this.storeData.merchantData.id;
    }
    this.getAllGoods();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    categoryChange(val) {},
    // 切页
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    // 获取列表
    async getAllGoods() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        shopId: this.storeData.id,
        merchantId: this.merchantId,
      };
      const data = await getAllGoods(params);
      this.goods_list = data.data.map((item) => {
        return {
          ...item,
          sale_false_visible: false,
        };
      });
      this.total = data.pageTotal;
    },
    //  搜索商品 searchGood
    async searchGood() {
      let params = {
        keyword: this.search.keyword,
        categoryPath: this.search.categoryPath.join(","),
        page: this.page,
        pageSize: this.pageSize,
        merchantId: this.merchantId,
      };
      const data = await searchGood(params);

      this.goods_list = data.data.map((item) => {
        return {
          ...item,
          sale_false_visible: false,
        };
      });
      this.total = data.pageTotal;
    },
    // 判断当前使用方法为列表接口还是搜索引擎接口 获取列表数据
    getData() {
      // 搜索参数规整
      const obj = {
        keyword: this.search.keyword,
        categoryPath: this.search.categoryPath.join(","),
      };
      const isKey = this.$_common.isSerch(obj);
      if (isKey) {
        this.searchGood();
      } else {
        this.getAllGoods();
      }
    },
    async showSpec(index) {
      let target = this.$_common.deepClone(this.goods_list);
      if (!target[index].goods_sku_list) {
        this.spec_loading = true;
        const { data } = await getGoodsInfo(target[index].id);
        this.spec_loading = false;

        if (data.specType === 2) {
          this.goods_list[index].goods_sku_list = data.specMultiple.map((item) => {
            const specValueName = item.specGroup
              .map((itemS) => {
                return itemS.specValueName;
              })
              .join("_");
            return {
              ...item,
              specValueName: specValueName,
            };
          });
        } else if (data.specType === 1) {
          this.goods_list[index].goods_sku_list = data.specMultiple.map((item) => {
            return {
              ...item,
              specValueName: "无",
            };
          });
        }
      }
      // this.goods_data = target
    },
  },
};
</script>

<style scoped>
.goods-name-view {
  width: calc(100% - 76px);
}
</style>
