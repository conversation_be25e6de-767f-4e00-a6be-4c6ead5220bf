<template>
  <div>
    <div class="detail-tab-item">
      <p class="detail-tab-title">
        历史累计数据
        <span>(数据更新于2020-11-30 00:00:00,每日更新一次)</span>
      </p>
      <div class="detail-tab-main">
        <div class="data-wrapper">
          <div class="data-box">
            <p class="title">付款金额（元）</p>
            <p class="num">-</p>
            <div class="popover">
              <el-popover
                placement="right"
                title="付款金额（元）"
                width="200"
                trigger="hover"
                content="统计时间内，所有付款订单的实付款之和。（会员储值、会员卡不计算在内，退款金额不剔除；拼团订单在成团时计入付款金额）"
              >
                <span slot="reference"><i class="el-icon-question"></i></span>
              </el-popover>
            </div>
          </div>
          <div class="data-box">
            <p class="title">付款订单（笔）</p>
            <p class="num">-</p>
            <div class="popover">
              <el-popover
                placement="right"
                title="付款订单（笔）"
                width="200"
                trigger="hover"
                content="统计时间内，成功付款的订单数。（拼团订单成团后，计入付款订单）"
              >
                <span slot="reference"><i class="el-icon-question"></i></span>
              </el-popover>
            </div>
          </div>
          <div class="data-box">
            <p class="title">付款人数（人）</p>
            <p class="num">-</p>
            <div class="popover">
              <el-popover
                placement="right"
                title="付款人数（人）"
                width="200"
                trigger="hover"
                content="统计时间内，成功付款的会员数，一人多次付款记为一人。（拼团订单成团后，计入付款订单，不剔除退款订单）"
              >
                <span slot="reference"><i class="el-icon-question"></i></span>
              </el-popover>
            </div>
          </div>
          <div class="data-box">
            <p class="title">付款件数（件）</p>
            <p class="num">-</p>
            <div class="popover">
              <el-popover
                placement="right"
                title="付款件数（件）"
                width="200"
                trigger="hover"
                content="统计时间内，所有付款订单的商品件数之和。（退款订单不剔除）"
              >
                <span slot="reference"><i class="el-icon-question"></i></span>
              </el-popover>
            </div>
          </div>
          <div class="data-box">
            <p class="title">实际收入（元）</p>
            <p class="num">-</p>
            <div class="popover">
              <el-popover
                placement="right"
                title="实际收入（元）"
                width="200"
                trigger="hover"
                content="统计时间内，所有付款订单的实付款之和，剔除退款金额。（会员储值、会员卡不计算在内；拼团订单在成团时计入付款金额）"
              >
                <span slot="reference"><i class="el-icon-question"></i></span>
              </el-popover>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="detail-tab-item">
      <p class="detail-tab-title">
        实时概况
        <span>(数据更新于2020-11-30 00:00:00,每日更新一次)</span>
      </p>
      <div class="detail-tab-main" style="padding: 20px 40px">
        <el-date-picker
          v-model="time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="timeChange"
        ></el-date-picker>
        <el-radio-group v-model="data_select" style="margin-left: 20px" size="small">
          <el-radio-button label="天"></el-radio-button>
          <el-radio-button label="周"></el-radio-button>
          <el-radio-button label="日"></el-radio-button>
        </el-radio-group>
        <br />
        <el-radio-group v-model="type_select" style="margin-top: 20px" size="small">
          <el-radio-button label="付款金额"></el-radio-button>
          <el-radio-button label="付款订单"></el-radio-button>
          <el-radio-button label="付款人数"></el-radio-button>
          <el-radio-button label="付款件数"></el-radio-button>
          <el-radio-button label="下单人数"></el-radio-button>
          <el-radio-button label="下单件数"></el-radio-button>
          <el-radio-button label="实际收入"></el-radio-button>
        </el-radio-group>
        <div>
          <vab-chart style="width: 100%" :autoresize="true" :options="options" theme="vab-echarts-theme" />
        </div>
      </div>
    </div>
    <div class="detail-tab-item">
      <p class="detail-tab-title">数据明细</p>
      <div class="detail-tab-main">
        <el-table :data="tableData" style="width: 100%" border size="small">
          <el-table-column prop="date" label="日期" min-width="100"></el-table-column>
          <el-table-column prop="name" label="付款订单（笔）" min-width="180"></el-table-column>
        </el-table>
        <FooterPage
          :page-size="pageSize"
          :total-page.sync="total"
          :current-page.sync="page"
          @pageChange="pageChange"
          @sizeChange="sizeChange"
        ></FooterPage>
      </div>
    </div>
  </div>
</template>

<script>
import VabChart from "@/extra/vabCharts";
export default {
  name: "EditStoreData",
  components: {
    VabChart,
  },
  data() {
    return {
      time: [],
      start: "",
      end: "",
      data_select: "天",
      type_select: "付款金额",
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      chart_data: [],
      options: {
        xAxis: [
          {
            type: "category",
            data: [],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            data: [],
            type: "line",
            name: "付款金额",
            smooth: true,
          },
        ],
      },
    };
  },
  methods: {
    timeChange(val) {
      if (val && val.length) {
        this.start = val[0] / 1000;
        this.end = val[1] / 1000 + 86399;
      } else {
        this.start = "";
        this.end = "";
      }
      this.pageChange(1);
    },
    pageChange(val) {
      this.page = val;
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.data-wrapper {
  display: flex;
}
.data-box {
  flex: 1 1 20%;
  height: 150px;
  text-align: center;
  line-height: 130px;
  padding-top: 30px;
  position: relative;
}
.title {
  font-size: 16px;
  color: #3e4651;
  margin-bottom: 40px;
  line-height: 1;
}
.num {
  color: #3e4651;
  font-size: 40px;
  line-height: 30px;
}
.popover {
  position: absolute;
  top: 0;
  right: 20px;
  line-height: 1;
}
</style>
