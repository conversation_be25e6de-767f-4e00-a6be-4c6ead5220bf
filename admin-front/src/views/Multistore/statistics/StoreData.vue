<template>
  <Container>
    <div slot="right">
      <span style="margin-right: 10px">日期</span>
      <el-date-picker
        v-model="time"
        size="small"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      ></el-date-picker>
    </div>
    <el-table boder :data="goods_list" border>
      <el-table-column prop="goods" label="门店名称" min-width="200"></el-table-column>
      <el-table-column prop="goods" label="付款件数" min-width="120">
        <template slot="header">
          <span>付款件数</span>
          <el-popover
            placement="top-start"
            title="付款件数(件)"
            width="200"
            trigger="hover"
            content="统计时间内，所有付款订单的商品件数之和。（退款订单不剔除）"
          >
            <span slot="reference" class="iconfont icon-wenhao"></span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="goods" label="付款人数" min-width="130">
        <template slot="header">
          <span>付款人数</span>
          <el-popover
            placement="top-start"
            title="付款人数"
            width="200"
            trigger="hover"
            content="统计时间内，成功付款的会员数，一人多次付款记为一人。（拼团订单成团后，计入付款订单）"
          >
            <span slot="reference" class="iconfont icon-wenhao"></span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="goods" label="付款金额" min-width="130">
        <template slot="header">
          <span>付款金额</span>
          <el-popover
            placement="top-start"
            title="付款金额(元)"
            width="200"
            trigger="hover"
            content="统计时间内，所有付款订单的实付款之和。（会员储值、会员卡不计算在内，退款金额不剔除；拼团订单在成团时计入付款金额）"
          >
            <span slot="reference" class="iconfont icon-wenhao"></span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="goods" label="付款订单数" min-width="130">
        <template slot="header">
          <span>付款订单数</span>
          <el-popover
            placement="top-start"
            title="付款订单数(笔)"
            width="200"
            trigger="hover"
            content="统计时间内，成功付款的订单数。（拼团订单成团后，计入付款订单，不剔除退款订单）"
          >
            <span slot="reference" class="iconfont icon-wenhao"></span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="goods" label="实际收入" min-width="130">
        <template slot="header">
          <span>实际收入</span>
          <el-popover
            placement="top-start"
            title="实际收入(元)"
            width="200"
            trigger="hover"
            content="统计时间内，所有付款订单的实付款之和，剔除退款金额。（会员储值、会员卡不计算在内；拼团订单在成团时计入付款金额）"
          >
            <span slot="reference" class="iconfont icon-wenhao"></span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="更多数据" min-width="100">
        <template>
          <el-button type="text" @click="$router.push('./EditStoreData')"> 查看 </el-button>
        </template>
      </el-table-column>
    </el-table>
  </Container>
</template>

<script>
export default {
  name: "StoreData",
  data() {
    return {
      time: [],
      search: {
        keyword: "",
      },
      goods_list: [{}],
    };
  },
  methods: {
    categoryChange(val) {},
  },
};
</script>

<style scoped>
.icon-wenhao {
  font-size: 12px;
  color: #666666;
  margin-left: 10px;
}
</style>
