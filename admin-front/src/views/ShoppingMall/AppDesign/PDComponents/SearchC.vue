<template>
  <div class="search-box">
    <div
      class="search-in"
      :style="{
        textAlign: sForm.textStyle,
        borderRadius: sForm.boxStyle === 3 ? '40px' : sForm.boxStyle === 2 ? '5px' : '0',
      }"
    >
      <span class="iconfont icon-tubiaozhizuomoban_sousuo"></span>
      <span>{{ sForm.tipText }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "SearchC",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {
        tipText: "请输入关键词",
        boxStyle: "1",
        textStyle: "center",
      },
    };
  },
  watch: {
    setForm(val) {
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
};
</script>

<style scoped>
.search-box {
  /*background-color: #f5f9fc;*/
  padding: 10px 0;
}
.search-in {
  background: #fff;
  width: 350px;
  margin: 0 auto;
  height: 35px;
  line-height: 35px;
  font-size: 12px;
  color: #999;
  font-weight: 300;
  padding: 0 12px;
  border-radius: 5px;
}
.search-in .iconfont {
  vertical-align: middle;
  margin-right: 5px;
}
</style>
