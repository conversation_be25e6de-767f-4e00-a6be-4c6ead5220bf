<template>
  <div class="LimitedSeckill" :style="{ backgroundColor: sForm.bgColor }">
    <div class="top-div clearfix">
      <div class="float_left">
        <div class="icon-xsms">
          <i class="iconfont icon-shijian"></i>
          <span>限时</span>
          <span style="color: #000000">秒杀</span>
        </div>
        <ul class="time-ul clearfix">
          <li class="time-li float_left">
            <span>00</span>
          </li>
          <li class="icon-maohao float_left">:</li>
          <li class="time-li float_left">
            <span>04</span>
          </li>
          <li class="icon-maohao float_left">:</li>
          <li class="time-li float_left">
            <span>59</span>
          </li>
        </ul>
      </div>
      <div class="float_right more-btn">
        查看全部
        <i class="iconfont icon-jinru1"></i>
      </div>
    </div>
    <ul v-if="sForm.colNum === 2" class="goods-ul goods-ul-two">
      <li v-for="(item, index) in 2" :key="index" class="goods-li">
        <div class="goods-img">
          <img
            src="https://onlineimg.qianniao.vip/159826609288688/b8ca10e4ce35fded735c980403dfedf7/O1CN01HU5sWZ27Aktv3iVWn_!!0-item_pic.jpg"
            alt=""
          />
          <div class="goods-tag">
            <span>商品标签</span>
          </div>
        </div>
        <div class="goods-name ellipsis">商品名称商品名称商品名称商品名称商品名称商品名称商品名称商品名称</div>
        <div class="goods-price clearfix">
          <div class="float_left">¥214</div>
          <div class="float_right">
            <span class="rem-ic">¥</span>
            <span class="price-text">199.98</span>
            <div class="time-tag"></div>
            <span class="time-text">抢</span>
          </div>
        </div>
      </li>
    </ul>
    <ul v-if="sForm.colNum === 3" class="goods-ul">
      <li v-for="(item, index) in 3" :key="index" class="goods-li">
        <div class="goods-img">
          <img
            src="https://onlineimg.qianniao.vip/159826609288688/b8ca10e4ce35fded735c980403dfedf7/O1CN01HU5sWZ27Aktv3iVWn_!!0-item_pic.jpg"
            alt=""
          />
        </div>
        <div class="goods-name ellipsis">商品名称商品名称</div>
        <div class="goods-price clearfix">
          <div class="float_left">¥214</div>
          <div class="float_right">
            <span class="rem-ic">¥</span>
            <span class="price-text">199.98</span>
            <div class="time-tag"></div>
            <span class="time-text">抢</span>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: "LimitedSeckill",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {},
      goods_list: [
        {
          images: "",
          title: "商品名称",
          id: "001",
          skuId: "002",
        },
        {
          images: "",
          title: "商品名称",
          id: "005",
          skuId: "006",
        },
        {
          images: "",
          title: "商品名称",
          id: "004",
          skuId: "003",
        },
      ],
    };
  },
  watch: {
    setForm(val) {
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
};
</script>

<style scoped lang="scss">
.LimitedSeckill {
  .top-div {
    padding: 13px 16px;
    .icon-xsms {
      font-weight: bold;
      font-size: 16px;
      display: inline-block;
      vertical-align: middle;
      color: #fa6400;
      .icon-shijian {
        font-size: 14px;
        font-weight: normal;
        margin-right: 2px;
      }
    }
    .time-ul {
      font-size: 13px;
      margin-left: 10px;
      display: inline-block;
      vertical-align: middle;
      .icon-maohao {
        height: 20px;
        width: 10px;
        text-align: center;
        font-size: 16px;
        line-height: 20px;
        font-weight: bold;
        transform: translateY(-2px);
      }
      .time-li {
        height: 20px;
        font-family: "DINCond-Bold", "DINCond";
        background-color: #000000;
        color: #ffffff;
        width: 18px;
        text-align: center;
        font-size: 13px;
        line-height: 20px;
        border-radius: 4px;
        font-weight: bold;
        span {
          transform: scaleX(0.85);
          display: inline-block;
        }
      }
    }
    .more-btn {
      font-size: 14px;
      color: #9d9d9d;
      .icon-jinru1 {
        font-size: 13px;
        transform: translateY(-1px);
        display: inline-block;
      }
    }
  }
  .goods-ul {
    display: flex;
    padding: 0 11px;
    .goods-li {
      flex: 3;
      padding: 0 5px 20px;
      .goods-img {
        img {
          width: 110px;
          height: 110px;
          border-radius: 6px;
          display: block;
        }
      }
      .goods-name {
        margin: 0 5px;
        font-weight: 400;
        line-height: 20px;
        padding-top: 10px;
        color: rgba(0, 0, 0, 0.85);
      }
      .goods-price {
        margin: 0 5px;
        padding-top: 5px;
        .float_left {
          font-size: 12px;
          font-family: DINPro-Regular, DINPro;
          font-weight: 400;
          color: #9d9d9d;
          line-height: 16px;
          text-decoration: line-through;
        }
        .float_right {
          width: 66px;
          padding-left: 5px;
          height: 19px;
          background: linear-gradient(90deg, #fe923e 0%, #ff3724 100%);
          border-radius: 10px 4px 4px 10px;
          font-size: 12px;
          font-family: DINPro-Medium, DINPro;
          font-weight: 600;
          color: #ffffff;
          line-height: 19px;
          position: relative;
          overflow: hidden;
          .rem-ic {
            display: inline-block;
            transform: translateX(2px);
          }
          .price-text {
            transform: scaleX(0.85);
            display: inline-block;
          }
          .time-tag {
            position: absolute;
            right: -8px;
            transform: rotate(-60deg);
            top: 0;
            text-align: center;
            color: #ffffff;
            width: 24px;
            height: 24px;
            background: linear-gradient(90deg, #fe923e 0%, #ff3724 100%);
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.8);
          }
          .time-text {
            position: absolute;
            transform: scale(0.75, 0.75);
            right: 2px;
            top: 1px;
            font-size: 12px;
          }
        }
      }
    }
  }
  .goods-ul-two {
    .goods-li {
      flex: 2;
      .goods-img {
        position: relative;
        img {
          width: 130px;
          height: 130px;
          border-radius: 8px;
          display: block;
          margin: 0 auto;
        }
        .goods-tag {
          width: 52px;
          height: 16px;
          background: linear-gradient(139deg, #fd0000 0%, #fa6400 100%);
          border-radius: 4px;
          position: absolute;
          top: -7px;
          left: 7px;
          font-size: 12px;
          color: #ffffff;
          line-height: 15px;
          span {
            display: inline-block;
            transform: scale(0.85, 0.85);
          }
        }
      }
      .goods-name {
        -webkit-line-clamp: 2;
      }
      .goods-price {
        .float_right {
          width: 86px;
          padding-left: 10px;
          .rem-ic {
            transform: translateX(0);
          }
          .price-text {
            transform: scaleX(1);
            margin-left: 2px;
          }
        }
      }
    }
  }
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
</style>
