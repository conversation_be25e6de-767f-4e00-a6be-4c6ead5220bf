<template>
  <div class="Notice">
    <div
      class="Notice-view clearfix"
      :style="{
        color: sForm.textColor,
        backgroundColor: sForm.bgColor,
        margin: sForm.padding + 'px auto',
      }"
    >
      <div class="notice-icon float_left">
        <img :src="require('@/assets/img/pageComponents/noticeicon.png')" alt="" />
      </div>
      <div class="notice-main float_left" :style="{ width: sForm.iconSel === 3 ? '300px' : '310px' }">公告/通知栏</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Notice",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {},
    };
  },
  watch: {
    setForm(val) {
      this.sForm = val;
      if (!this.sForm.iconSel) {
        this.sForm.iconSel = 1;
      }
    },
  },
  created() {
    this.sForm = this.setForm;
    if (!this.sForm.iconSel) {
      this.sForm.iconSel = 1;
    }
  },
};
</script>

<style scoped lang="scss">
.Notice {
  /*background: #fafafa;*/
}
.Notice-view {
  width: 355px;
  padding-right: 12px;
  margin: 5px auto;
  background-color: #e9eff0;
  border-radius: 32px;
  .notice-icon {
    line-height: 32px;
    padding-left: 12px;
    img {
      width: 15px;
      display: inline-block;
      vertical-align: middle;
      transform: translateY(-2px);
    }
  }

  .notice-main {
    width: 310px;
    padding-left: 10px;
    font-size: 14px;
    line-height: 32px;
  }
}
</style>
