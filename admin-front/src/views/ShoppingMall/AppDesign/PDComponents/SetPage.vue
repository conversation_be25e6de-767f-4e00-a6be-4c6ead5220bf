<template>
  <div>
    <HeadFrom v-if="tag === '页面设置'" :tem-form="temForm" @confirm="confirm"></HeadFrom>
    <SwiperForm v-if="tag === '图片轮播'" :shop-id="shopId" :tem-form="temForm" @confirm="confirm"></SwiperForm>
    <MagicForm v-if="tag === '图片魔方'" :tem-form="temForm" :shop-id="shopId" @confirm="confirm"></MagicForm>
    <NoticeForm v-if="tag === '公告'" :tem-form="temForm" @confirm="confirm"></NoticeForm>
    <SearchForm v-if="tag === '搜索框'" :tem-form="temForm" @confirm="confirm"></SearchForm>
    <NavbarForm v-if="tag === '导航'" :shop-id="shopId" :tem-form="temForm" @confirm="confirm"></NavbarForm>
    <GoodsForm v-if="tag === '商品组'" :shop-id="shopId" :tem-form="temForm" @confirm="confirm"></GoodsForm>
    <LimitedSeckillForm v-if="tag === '秒杀商品'" :tem-form="temForm" @confirm="confirm"></LimitedSeckillForm>
    <CouponForm v-if="tag === '优惠券组'" :tem-form="temForm" @confirm="confirm"></CouponForm>
    <FloatBtnForm v-if="tag === '浮动按钮'" :shop-id="shopId" :tem-form="temForm" @confirm="confirm"></FloatBtnForm>
    <BlankForm v-if="tag === '辅助空白'" :tem-form="temForm" @confirm="confirm"></BlankForm>

    <LineForm v-if="tag === '辅助线'" :tem-form="temForm" @confirm="confirm"></LineForm>
    <WxServiceForm v-if="tag === '微信客服'" :tem-form="temForm" @confirm="confirm"></WxServiceForm>
    <ShopInfoForm v-if="tag === '商户信息'" :tem-form="temForm" @confirm="confirm"></ShopInfoForm>
    <AdvGroupForm v-if="tag === '图片广告'" :shop-id="shopId" :tem-form="temForm" @confirm="confirm"></AdvGroupForm>
    <IntegralGoods v-if="tag === '积分商品'" :tem-form="temForm" @confirm="confirm"></IntegralGoods>
  </div>
</template>

<script>
import WxServiceForm from "../PageFormCom/WxServiceForm.vue";
import HeadFrom from "../PageFormCom/HeadFrom.vue";
import SwiperForm from "../PageFormCom/SwiperForm.vue";
import BlankForm from "../PageFormCom/BlankForm.vue";
import LineForm from "../PageFormCom/LineForm.vue";
import MagicForm from "../PageFormCom/MagicForm.vue";
import NoticeForm from "../PageFormCom/NoticeForm.vue";
import NavbarForm from "../PageFormCom/NavbarForm.vue";
import GoodsForm from "../PageFormCom/GoodsForm.vue";
import SearchForm from "../PageFormCom/SearchForm.vue";
import CouponForm from "../PageFormCom/CouponForm.vue";
import LimitedSeckillForm from "../PageFormCom/LimitedSeckillForm.vue";
import FloatBtnForm from "../PageFormCom/FloatBtnForm.vue";
import ShopInfoForm from "../PageFormCom/ShopInfoForm.vue";
import IntegralGoods from "../PageFormCom/IntegralGoods.vue";
import AdvGroupForm from "../PageFormCom/AdvGroupForm.vue";
export default {
  name: "SetPage",
  components: {
    SwiperForm,
    HeadFrom,
    BlankForm,
    MagicForm,
    LineForm,
    NoticeForm,
    NavbarForm,
    SearchForm,
    LimitedSeckillForm,
    GoodsForm,
    FloatBtnForm,
    CouponForm,
    WxServiceForm,
    ShopInfoForm,
    AdvGroupForm,
    IntegralGoods,
  },
  props: {
    tag: {
      type: String,
      default: "",
    },
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    shopId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {};
  },
  methods: {
    confirm(form) {
      this.$emit("confirm", form);
    },
  },
};
</script>

<style>
.tem-box {
  /*padding: 12px;*/
  font-weight: 400;
}
.tem-tit {
  font-size: 14px;
  color: #333;
  font-weight: 400;
  padding: 10px;
  border-bottom: 4px solid #f5f9fc;
}
.tem-tit-span {
  /*line-height: 28px;*/
  font-weight: bold;
  font-size: 13px;
}
.el-form--label-top .el-form-item--small.el-form-item.PD-form-item {
  padding: 5px 16px;
  margin-bottom: 0;
  border-bottom: 1px solid #eeeeee;
}
.el-form--label-top .el-form-item--small.el-form-item.PD-form-item:last-child {
  border-bottom: 0;
}
</style>
