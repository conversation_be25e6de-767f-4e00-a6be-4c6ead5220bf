<template>
  <div class="ImgSwiper">
    <div v-if="sForm.style === 1">
      <img :src="sForm.bannerList[0].image.url || require('@/assets/img/replace-img.png')" alt="" class="banner-img" />
    </div>
    <div v-if="sForm.style === 2" class="car-img-view">
      <img
        :src="
          sForm.bannerList[2]
            ? sForm.bannerList[2].image.url || require('@/assets/img/replace-img.png')
            : require('@/assets/img/replace-img.png')
        "
        alt=""
        class="car-img"
      />
    </div>
    <ul v-if="sForm.indicatorDots" class="indicatorDot-ul clearfix">
      <li
        v-for="(item, index) in sForm.bannerList"
        :key="index"
        class="indicatorDot-li"
        :style="[
          index === 0 ? { backgroundColor: sForm.indicatorActiveColor } : { backgroundColor: sForm.indicatorColor },
        ]"
      ></li>
    </ul>
  </div>
</template>

<script>
export default {
  name: "ImgSwiper",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {},
    };
  },
  watch: {
    setForm(val) {
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
};
</script>

<style scoped>
.ImgSwiper {
  width: 100%;
  /*height: 180px;*/
  position: relative;
  overflow: hidden;
}
.car-img {
  width: 355px;
  height: 150px;
  object-fit: cover;
  border-radius: 12px;
  margin: 0 auto;
  display: block;
}
.banner-img {
  width: 100%;
  height: 175px;
  object-fit: cover;
  display: block;
}
.indicatorDot-ul {
  position: absolute;
  left: 50%;
  bottom: 10px;
  transform: translateX(-23px);
}
.indicatorDot-li {
  width: 10px;
  height: 10px;
  border-radius: 100%;
  margin-right: 5px;
  float: left;
}
.indicatorDot-on {
  opacity: 1;
}
</style>
