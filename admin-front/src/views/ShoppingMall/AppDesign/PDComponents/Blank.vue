<template>
  <!--  空白组件-->
  <div
    class="blank"
    :style="{
      height: sForm.height + 'px',
      backgroundColor: sForm.backgroundColor,
    }"
  ></div>
</template>

<script>
export default {
  name: "Blank",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {
          backgroundColor: "#f5f9fc",
          height: 20,
        };
      },
    },
  },
  data() {
    return {
      sForm: {},
    };
  },
  watch: {
    setForm(val) {
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
};
</script>

<style scoped></style>
