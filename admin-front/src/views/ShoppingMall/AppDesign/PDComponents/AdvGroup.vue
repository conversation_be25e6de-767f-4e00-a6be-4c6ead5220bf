<template>
  <div class="AdvGroup">
    <div v-if="sForm.style.styleId !== 3" class="group-one">
      <div class="one-li one-left">
        <div class="left-title-view">
          <span class="title" :style="{ color: sForm.adv_list[0].titleColor }">
            {{ sForm.adv_list[0].title }}
          </span>
          <span class="desc" :style="{ color: sForm.adv_list[0].descColor }">
            {{ sForm.adv_list[0].desc }}
          </span>
        </div>
        <div class="more-btn">
          <span>{{ sForm.adv_list[0].btnText }}</span>
          <span class="iconfont icon-jinru1"></span>
        </div>
        <div>
          <img class="adv-img" :src="sForm.adv_list[0].image.url || require('@/assets/img/replace-img.png')" />
        </div>
      </div>
      <div class="one-li one-right">
        <div class="right-li clearfix">
          <div class="float_left">
            <p class="title" :style="{ color: sForm.adv_list[1].titleColor }">
              {{ sForm.adv_list[1].title }}
            </p>
            <p class="desc" :style="{ color: sForm.adv_list[1].descColor }">
              {{ sForm.adv_list[1].desc }}
            </p>
            <div class="more-btn">
              <span>{{ sForm.adv_list[1].btnText }}</span>
              <span class="iconfont icon-jinru1"></span>
            </div>
          </div>
          <div class="float_right">
            <img class="adv-img" :src="sForm.adv_list[1].image.url || require('@/assets/img/replace-img.png')" />
          </div>
        </div>
        <div class="right-li clearfix">
          <div class="float_left">
            <p class="title" :style="{ color: sForm.adv_list[2].titleColor }">
              {{ sForm.adv_list[2].title }}
            </p>
            <p class="desc" :style="{ color: sForm.adv_list[2].descColor }">
              {{ sForm.adv_list[2].desc }}
            </p>
            <div class="more-btn">
              <span>{{ sForm.adv_list[2].btnText }}</span>
              <span class="iconfont icon-jinru1"></span>
            </div>
          </div>
          <div class="float_right">
            <img class="adv-img" :src="sForm.adv_list[2].image.url || require('@/assets/img/replace-img.png')" />
          </div>
        </div>
      </div>
    </div>
    <div v-if="sForm.style.styleId === 1" class="group-two">
      <div class="group-li">
        <div class="group-title">
          <span class="title" :style="{ color: sForm.adv_list[3].titleColor }">
            {{ sForm.adv_list[3].title }}
          </span>
          <span class="desc" :style="{ color: sForm.adv_list[3].descColor }">
            {{ sForm.adv_list[3].desc }}
          </span>
        </div>
        <div class="clearfix">
          <div class="more-btn float_left">
            <span>{{ sForm.adv_list[3].btnText }}</span>
            <span class="iconfont icon-jinru1"></span>
          </div>
          <div class="float_right">
            <img class="adv-img" :src="sForm.adv_list[3].image.url || require('@/assets/img/replace-img.png')" />
          </div>
        </div>
      </div>
      <div class="group-li">
        <div class="group-title">
          <span class="title" :style="{ color: sForm.adv_list[4].titleColor }">
            {{ sForm.adv_list[4].title }}
          </span>
          <span class="desc" :style="{ color: sForm.adv_list[4].descColor }">
            {{ sForm.adv_list[4].desc }}
          </span>
        </div>
        <div class="clearfix">
          <div class="more-btn float_left">
            <span>{{ sForm.adv_list[4].btnText }}</span>
            <span class="iconfont icon-jinru1"></span>
          </div>
          <div class="float_right">
            <img class="adv-img" :src="sForm.adv_list[4].image.url || require('@/assets/img/replace-img.png')" />
          </div>
        </div>
      </div>
    </div>
    <!--    样式3-->
    <div v-if="sForm.style.styleId === 3" class="group-two">
      <div class="group-li">
        <div class="group-title">
          <span class="title" :style="{ color: sForm.adv_list[0].titleColor }">
            {{ sForm.adv_list[0].title }}
          </span>
          <span class="desc" :style="{ color: sForm.adv_list[0].descColor }">
            {{ sForm.adv_list[0].desc }}
          </span>
        </div>
        <div class="clearfix">
          <div class="more-btn float_left">
            <span>{{ sForm.adv_list[0].btnText }}</span>
            <span class="iconfont icon-jinru1"></span>
          </div>
          <div class="float_right">
            <img class="adv-img" :src="sForm.adv_list[0].image.url || require('@/assets/img/replace-img.png')" />
          </div>
        </div>
      </div>
      <div class="group-li">
        <div class="group-title">
          <span class="title" :style="{ color: sForm.adv_list[1].titleColor }">
            {{ sForm.adv_list[1].title }}
          </span>
          <span class="desc" :style="{ color: sForm.adv_list[1].descColor }">
            {{ sForm.adv_list[1].desc }}
          </span>
        </div>
        <div class="clearfix">
          <div class="more-btn float_left">
            <span>{{ sForm.adv_list[1].btnText }}</span>
            <span class="iconfont icon-jinru1"></span>
          </div>
          <div class="float_right">
            <img class="adv-img" :src="sForm.adv_list[1].image.url || require('@/assets/img/replace-img.png')" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AdvGroup",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {},
    };
  },
  watch: {
    setForm(val) {
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
};
</script>

<style scoped lang="scss">
.AdvGroup {
  background-color: #ffffff;
  .more-btn {
    font-size: 10px;
    font-weight: 400;
    color: #cacaca;
    line-height: 14px;
    .iconfont {
      font-size: 10px;
    }
  }
  .group-one {
    display: flex;
    .one-li {
      flex: 2;
    }
    .one-left {
      padding-left: 16px;
      padding-top: 12px;
      padding-bottom: 12px;
      border-right: 1px solid #eeeeee;
      .adv-img {
        margin-top: 16px;
        width: 159px;
        height: 120px;
        border-radius: 8px;
      }
      .left-title-view {
        line-height: 23px;
        padding-bottom: 4px;
        .title {
          font-size: 16px;
          font-weight: 500;
          color: #2a2a2a;
        }
        .desc {
          margin-left: 4px;
          font-size: 12px;
          font-weight: 400;
          color: #f05064;
        }
      }
    }
    .one-right {
      padding-right: 16px;
      .right-li {
        padding: 12px 0 12px 12px;
        position: relative;
        .more-btn {
          position: absolute;
          left: 12px;
          bottom: 12px;
        }
        .title {
          font-size: 14px;
          font-weight: 500;
          color: #2a2a2a;
          line-height: 20px;
        }
        .desc {
          font-size: 12px;
          font-weight: 400;
          color: #fd6f02;
          line-height: 17px;
          padding-top: 4px;
        }
        &:first-child {
          border-bottom: 1px solid #eeeeee;
          .desc {
            color: #3cb4b6;
          }
        }
      }
      .adv-img {
        width: 67px;
        height: 76px;
        border-radius: 4px;
      }
    }
  }
}
.group-two {
  display: flex;
  border-top: 1px solid #eeeeee;
  .group-li {
    padding: 12px 0;
    flex: 2;
    &:first-child {
      padding-left: 16px;
      padding-right: 12px;
      border-right: 1px solid #eeeeee;
    }
    &:nth-child(2) {
      padding-right: 16px;
      padding-left: 12px;
    }
    .group-title {
      padding-bottom: 6px;
      line-height: 20px;
      .title {
        font-size: 14px;
        font-weight: 500;
        color: #2a2a2a;
      }
      .desc {
        margin-left: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #19906c;
      }
    }
    .adv-img {
      width: 97px;
      height: 54px;
      border-radius: 4px;
    }
  }
}
</style>
