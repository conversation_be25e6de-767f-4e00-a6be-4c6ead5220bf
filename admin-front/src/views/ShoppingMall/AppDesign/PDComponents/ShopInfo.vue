<template>
  <div
    class="clearfix shop-info"
    :style="{
      color: setForm.color,
      backgroundColor: setForm.bgColor,
    }"
  >
    <div class="float_left">
      <img
        class="shop-logo"
        :src="setForm.image.url || 'https://i.gtimg.cn/club/item/face/img/8/15918_100.gif'"
        alt=""
      />
    </div>
    <div class="float_left">
      <p class="shop-name">{{ storeData.name }}</p>
      <!--      <p class="shop-num">9999人关注</p>-->
    </div>
    <!--    <div class="float_right college-btn">-->
    <!--      <i class="el-icon-star-off"></i>-->
    <!--      <span style="margin-left: 3px">关注</span>-->
    <!--    </div>-->
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "ShopInfo",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {},
    };
  },
  computed: {
    ...mapGetters({
      storeData: "MUser/storeData",
    }),
  },
  watch: {
    setForm(val) {
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
};
</script>

<style scoped lang="scss">
.shop-info {
  padding: 10px 12px;
  .shop-logo {
    width: 44px;
    height: 44px;
    border-radius: 4px;
    display: block;
    margin-right: 10px;
  }
  .shop-name {
    line-height: 44px;
    font-weight: bold;
  }
  .shop-num {
    font-size: 12px;
    padding-top: 5px;
  }
  .college-btn {
    font-size: 12px;
    padding: 0 10px;
    line-height: 30px;
    background-color: #ff4400;
    color: #ffffff;
    border-radius: 4px;
    transform: translateY(7px);
  }
}
</style>
