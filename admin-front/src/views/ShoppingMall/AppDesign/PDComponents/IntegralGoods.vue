<template>
  <div class="goods-view" :style="{ backgroundColor: sForm.bgColor }">
    <div v-if="sForm.navStyle === 1">
      <!--    标题栏风格1-->
      <div
        v-if="sForm.titleStyle === 2"
        :style="{
          color: sForm.titleColor,
          backgroundColor: sForm.titleBgColor,
        }"
        class="title-view clearfix"
      >
        {{ sForm.title }}
        <span v-if="sForm.title_two" class="second-tit">
          {{ sForm.title_two }}
        </span>
      </div>
      <!--    标题栏风格2-->
      <div
        v-if="sForm.titleStyle === 1"
        class="tit-view-two clearfix"
        :style="{
          color: sForm.titleColor,
          backgroundColor: sForm.titleBgColor,
        }"
      >
        <span class="tit-view-two-line" :style="{ background: sForm.titleSetColor }"></span>
        <span class="tit-two-text">{{ sForm.title }}</span>
        <span v-if="sForm.title_two" class="tit-two-text-c">
          {{ sForm.title_two }}
        </span>
      </div>
      <!--    标题栏风格3-->
      <div
        v-if="sForm.titleStyle === 3"
        class="tit-view-three"
        :style="{
          color: sForm.titleColor,
          backgroundColor: sForm.titleBgColor,
        }"
      >
        <div class="tit-three-text">
          <div class="tit-view-style tit-view-style-left">
            <span class="three-style-big" :style="{ background: sForm.titleSetColor }"></span>
            <span class="three-style-small" :style="{ background: sForm.titleSetColor }"></span>
          </div>
          {{ sForm.title }}
          <div class="tit-view-style tit-view-style-right">
            <span class="three-style-small" :style="{ background: sForm.titleSetColor }"></span>
            <span class="three-style-big" :style="{ background: sForm.titleSetColor }"></span>
          </div>
        </div>

        <div v-if="sForm.title_two" class="tit-three-text-c">
          {{ sForm.title_two }}
        </div>
      </div>
      <!--    标题栏风格4 -->
      <div
        v-if="sForm.titleStyle === 4"
        class="tit-view-four"
        :style="{
          color: sForm.titleColor,
          backgroundColor: sForm.titleBgColor,
        }"
      >
        <div class="tit-four-text">
          {{ sForm.title }}
        </div>
        <div v-if="sForm.title_two" class="tit-four-text-c">
          {{ sForm.title_two }}
        </div>
      </div>
    </div>
    <!--    商品区 单列-->
    <ul v-if="sForm.colNum === 1" class="goods-one-ul">
      <li
        v-for="(item, index) in goodsList"
        :key="index"
        class="goods-one-li clearfix"
        :style="{ backgroundColor: sForm.itemBgColor }"
      >
        <div class="img-view float_left">
          <img class="goods-one-img" :src="item.images" />
        </div>
        <div class="goods-one-info float_left">
          <p v-if="sForm.goodsInfo.indexOf(1) > -1" class="ellipsis goods-one-name" :style="{ color: sForm.textColor }">
            {{ item.title }}
          </p>
          <p v-if="sForm.goodsInfo.indexOf(4) > -1" class="ellipsis goods-desc">
            商品卖点商品卖点商品卖点商品卖点商品卖点商品卖点商品卖点
          </p>

          <p v-if="sForm.goodsInfo.indexOf(5) > -1" class="sale-num">已售1000件</p>
          <div class="price-view clearfix">
            <div class="float_left">
              <span v-if="sForm.goodsInfo.indexOf(2) > -1" :style="{ color: sForm.priceColor }" class="sale-price">
                <i class="ic-rem">¥</i>
                <i v-html="splitPrice('34.5')"></i>
              </span>
            </div>
            <div class="float_right">
              <span v-if="sForm.btnStyle === 1" class="iconfont icon-xinzeng1"></span>
              <span v-else-if="sForm.btnStyle === 2" class="iconfont icon-gouwuche1"></span>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <!--    商品区 两列-->
    <ul v-if="sForm.colNum === 2" class="goods-two-ul" :style="{ flexWrap: sForm.type === 1 ? 'wrap' : 'nowrap' }">
      <li
        v-for="(item, index) in goodsList"
        :key="index"
        class="goods-two-li"
        :style="{ backgroundColor: sForm.itemBgColor }"
      >
        <div class="img-view">
          <img class="goods-two-img" :src="item.images" />
        </div>
        <div class="goods-two-info">
          <p v-if="sForm.goodsInfo.indexOf(1) > -1" class="ellipsis goods-two-name" :style="{ color: sForm.textColor }">
            {{ item.title }}
          </p>
          <div class="price-view clearfix">
            <div class="float_left">
              <span v-if="sForm.goodsInfo.indexOf(2) > -1" :style="{ color: sForm.priceColor }" class="sale-price">
                <i class="ic-rem">¥</i>
                <i v-html="splitPrice('34.5')"></i>
              </span>
            </div>
            <div class="float_right">
              <span v-if="sForm.btnStyle === 1" class="iconfont icon-xinzeng1"></span>
              <span v-else-if="sForm.btnStyle === 2" class="iconfont icon-gouwuche1"></span>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <!--    商品区 三列-->
    <ul v-if="sForm.colNum === 3" class="goods-three-ul" :style="{ flexWrap: sForm.type === 1 ? 'wrap' : 'nowrap' }">
      <li
        v-for="(item, index) in goodsList"
        :key="index"
        class="goods-three-li"
        :style="{ backgroundColor: sForm.itemBgColor }"
      >
        <div class="img-view">
          <img class="goods-three-img" :src="item.images" />
        </div>
        <div class="goods-three-info">
          <p
            v-if="sForm.goodsInfo.indexOf(1) > -1"
            class="ellipsis goods-three-name"
            :style="{ color: sForm.textColor }"
          >
            {{ item.title }}
          </p>
          <div class="price-view clearfix">
            <div class="float_left">
              <span v-if="sForm.goodsInfo.indexOf(2) > -1" :style="{ color: sForm.priceColor }" class="sale-price">
                <i class="ic-rem">¥</i>
                <i v-html="splitPrice('34.5')"></i>
              </span>
            </div>
            <div class="float_right">
              <span v-if="sForm.btnStyle === 1" class="iconfont icon-xinzeng1"></span>
              <span v-else-if="sForm.btnStyle === 2" class="iconfont icon-gouwuche1"></span>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: "GoodsGroup",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {},
      goods_list: [],
    };
  },
  computed: {
    goodsList() {
      if (this.sForm.navStyle === 2) {
        if (this.sForm.tabList[0].goodsFrom === 1) {
          let list = [];
          for (let i = 0; i < 6; i = i + 1) {
            list.push({
              title: "商品名称商品名称商品名称商品名称商品名称",
              brandName: "商品品牌",
              images: ["http://image.qianniao.vip/160886828880031/********************************/timg (1).jpeg"],
            });
          }
          return list;
        } else {
          return this.sForm.tabList[0].goods_list;
        }
      } else {
        if (this.sForm.goodsFrom === 1) {
          let list = [];
          for (let i = 0; i < 6; i = i + 1) {
            list.push({
              title: "商品名称商品名称商品名称商品名称商品名称",
              brandName: "商品品牌",
              images: ["http://image.qianniao.vip/160886828880031/********************************/timg (1).jpeg"],
            });
          }
          return list;
        } else {
          return this.sForm.goods_list;
        }
      }
    },
  },
  watch: {
    setForm(val) {
      this.sForm = val;
      this.goods_list = this.sForm.navStyle === 2 ? this.sForm.tabList[0].goods_list : this.sForm.goods_list;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
  methods: {
    splitPrice(price) {
      let arr = price.split(".");
      return `${arr[0]}<span style="font-size:12px;">.${arr[1]}</span>`;
    },
  },
};
</script>

<style scoped lang="scss">
.cate-nav-s {
  white-space: nowrap;
  line-height: 35px;
  width: 100%;
  overflow: hidden;
}
.cate-li {
  display: inline-block;
  font-size: 14px;
  color: #666;
  font-weight: 400;
  padding: 0 10px;
  text-align: center;
}
.cate-on:after {
  content: " ";
  display: block;
  width: 25px;
  margin: 0 auto;
  border-radius: 3px;
  height: 3px;
  background: linear-gradient(315deg, #fd463e 0%, #ff7f61 100%);
  transform: translateY(-3px);
}
.cate-on {
  color: #fd463e;
  font-size: 16px;
}
/*  标题栏风格1*/
.title-view {
  font-size: 18px;
  font-weight: 500;
  color: #2a2a2a;
  padding-top: 12px;
  padding-bottom: 12px;
  padding-left: 10px;
  .second-tit {
    margin-left: 8px;
    min-width: 46px;
    padding: 0 10px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background: linear-gradient(90deg, #ff9148 0%, #fa6400 100%);
    border-radius: 0px 12px 0px 12px;
    font-size: 10px;
    font-weight: 400;
    color: #ffffff;
    display: inline-block;
  }
}
/*  标题栏风格2*/
.tit-view-two {
  padding: 10px;
  font-size: 12px;
  border-bottom: 1px solid #eee;
  .tit-two-text {
    font-size: 16px;
  }
  .tit-view-two-line {
    content: "";
    display: inline-block;
    width: 3px;
    height: 16px;
    background: #fd463e;
    vertical-align: middle;
    transform: translateY(-2px);
    margin-right: 5px;
  }
  .tit-two-text-c {
    padding-left: 5px;
  }
}

/*  标题栏风格3*/
.tit-view-three {
  text-align: center;
  padding: 10px;
}
.tit-three-text {
  font-size: 16px;
  font-weight: 600;
  position: relative;
  display: inline-block;
  min-width: 64px;
  height: 22px;
}
.tit-view-style {
  position: absolute;
  line-height: 18px;
}
.tit-view-style-left {
  left: -20px;
  top: 2px;
  transform: rotate(50deg);
}
.tit-view-style-left .three-style-small {
  transform: translateX(2px);
}
.tit-view-style-right {
  right: -20px;
  bottom: 2px;
  transform: rotate(50deg);
}
.tit-view-style-right .three-style-small {
  transform: translateX(-2px);
}
.tit-four-text-c,
.tit-three-text-c {
  font-size: 12px;
  font-weight: 300;
  padding-top: 3px;
}
.three-style-big {
  display: inline-block;
  width: 4px;
  height: 18px;
  background: #fd463e;
  border-radius: 4px;
  vertical-align: middle;
}
.three-style-small {
  display: inline-block;
  width: 3px;
  height: 8px;
  background: #fd463e;
  opacity: 0.6;
  border-radius: 4px;
  vertical-align: middle;
}
/*  标题栏风格4*/
.tit-view-four {
  text-align: center;
  padding: 10px;
}
.tit-four-text {
  font-size: 16px;
  font-weight: 600;
}
.tit-four-text:before {
  display: inline-block;
  content: "";
  width: 30px;
  height: 1px;
  background: #333;
  vertical-align: middle;
  margin-right: 5px;
}
.tit-four-text:after {
  display: inline-block;
  content: "";
  width: 30px;
  height: 1px;
  background: #333;
  vertical-align: middle;
  margin-left: 5px;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.goods-two-ul {
  display: flex;
  flex-wrap: wrap;
  padding: 0 12px;
  overflow: hidden;
  .goods-two-li {
    padding: 19px 0 17px;
    display: inline-block;
    margin: 5px;
    border-radius: 8px;
    background-color: #ffffff;
    .img-view {
      position: relative;
      width: 166px;
      .goods-tag {
        position: absolute;
        top: -6px;
        left: 12px;
        width: 52px;
        height: 16px;
        background: linear-gradient(139deg, #fd0000 0%, #fa6400 100%);
        border-radius: 4px;
        color: #ffffff;
        font-size: 12px;
        line-height: 16px;
        text-align: center;
      }
      .goods-two-img {
        display: block;
        width: 130px;
        height: 130px;
        border-radius: 10px;
        margin: 0 auto;
      }
    }

    .goods-two-info {
      margin: 10px auto 0;
      width: 151px;
      .goods-two-name {
        font-size: 14px;
        font-weight: 500;
        color: #111111;
        -webkit-line-clamp: 2;
        margin-bottom: 4px;
      }
      .price-view {
        .sale-price {
          color: #fa6400;
          font-size: 20px;
          font-weight: 500;
          .ic-rem {
            font-size: 10px;
            font-weight: normal;
          }
        }
        .desc-price {
          font-size: 12px;
          font-weight: 400;
          color: #d8d8d8;
          text-decoration: line-through;
          margin-left: 4px;
        }
        .float_right {
          transform: translateY(4px);
          .iconfont {
            font-size: 20px;
            background-image: -webkit-linear-gradient(90deg, #fe923e 0%, #ff3724 100%);
            -webkit-background-clip: text;
            color: transparent;
          }
        }
      }
    }
  }
}
.goods-three-ul {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  padding: 0 12px;
  .goods-three-li {
    border-radius: 6px;
    overflow: hidden;
    background-color: #ffffff;
    display: inline-block;
    width: 109px;
    margin: 4px;
    .img-view {
      width: 109px;
      .goods-three-img {
        width: 109px;
        height: 109px;
        margin: 0 auto;
        display: block;
      }
    }
    .goods-three-info {
      padding: 8px;
      width: 109px;
      .goods-three-name {
        font-size: 14px;
        color: #111111;
        padding-bottom: 2px;
      }
      .price-view {
        .sale-price {
          color: #fa6400;
          font-size: 18px;
          font-weight: 500;
          .ic-rem {
            font-size: 12px;
          }
        }
        .desc-price {
          font-size: 12px;
          font-weight: 400;
          color: #d8d8d8;
          text-decoration: line-through;
          margin-left: 4px;
        }
        .float_right {
          transform: translateY(4px);
          .iconfont {
            font-size: 18px;
            background-image: -webkit-linear-gradient(90deg, #fe923e 0%, #ff3724 100%);
            -webkit-background-clip: text;
            color: transparent;
          }
        }
      }
    }
  }
}
.goods-one-ul {
  .goods-one-li {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 16px 12px 12px;
    width: 355px;
    margin: 10px auto;
    .img-view {
      position: relative;
      .goods-one-img {
        width: 130px;
        height: 130px;
        border-radius: 8px;
      }
      .tag-view {
        position: absolute;
        left: -4px;
        top: -4px;
        width: 52px;
        text-align: center;
        height: 16px;
        background: linear-gradient(139deg, #fd0000 0%, #fa6400 100%);
        border-radius: 4px;
        color: #ffffff;
        font-size: 12px;
        line-height: 16px;
        display: inline-block;
      }
    }
    .goods-one-info {
      width: 190px;
      padding-left: 10px;
      height: 130px;
      position: relative;
      .goods-one-name {
        font-size: 14px;
        font-weight: 500;
        color: #111111;
        -webkit-line-clamp: 2;
        margin-bottom: 12px;
      }
      .goods-desc {
        font-size: 12px;
        color: #fa6400;
        margin-bottom: 12px;
      }

      .sale-num {
        font-size: 13px;
        color: #9d9d9d;
      }
      .price-view {
        position: absolute;
        bottom: -4px;
        left: 10px;
        width: 100%;
        .sale-price {
          color: #fa6400;
          font-size: 20px;
          font-weight: 500;
          .ic-rem {
            font-size: 10px;
            font-weight: normal;
          }
        }
        .desc-price {
          font-size: 12px;
          font-weight: 400;
          color: #d8d8d8;
          text-decoration: line-through;
          margin-left: 4px;
        }
        .float_right {
          transform: translateY(4px);
          .iconfont {
            font-size: 20px;
            background-image: -webkit-linear-gradient(90deg, #fe923e 0%, #ff3724 100%);
            -webkit-background-clip: text;
            color: transparent;
          }
        }
      }
    }
  }
}
</style>
