<template>
  <div
    class="page-head"
    :class="[sForm.style === 3 ? 'style-three' : sForm.style === 2 ? 'style-two' : 'style-one']"
    :style="style"
  >
    <div v-if="sForm.style === 2" :style="{ background: setForm.topBgColor }" class="circular"></div>
    <div class="head-in">
      <div class="address-div ellipsis">
        <i class="el-icon-location"></i>
        <span>未央区</span>
      </div>
      <p class="page-tit">
        {{ sForm.name }}
      </p>
    </div>
  </div>
</template>

<script>
import { getBasicSetup } from "@/api/System";
export default {
  name: "NavigationBar",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {},
      themeStyle: {},
    };
  },
  computed: {
    style() {
      if (this.sForm.style === 3) {
        return {
          backgroundColor: this.setForm.topBgColor,
          color: this.setForm.textColor,
        };
      } else if (this.sForm.style === 1) {
        return {
          background: `linear-gradient(360deg,rgba(255, 255, 255, 0) 0%,${this.setForm.topBgColor} 100%)`,
          color: this.setForm.textColor,
        };
      } else {
        return {
          color: this.setForm.textColor,
        };
      }
    },
  },
  watch: {
    setForm(val) {
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
    this.getBasicSetup();
  },
  methods: {
    //  获取详情
    async getBasicSetup() {
      if (!this.$accessCheck(this.$Access.BaseSetGetBasicSetup)) {
        return;
      }
      const { data } = await getBasicSetup();
      this.themeStyle = data.basicData.themeStyle || {};
    },
  },
};
</script>
<style scoped lang="scss">
.page-head {
  cursor: pointer;
  height: 226px;
  width: 375px;
  color: #ffffff;
  .head-in {
    position: relative;
    width: 100%;
    height: 74px;
    z-index: 2;
    background-image: url("../../../../assets/img/pageComponents/preview_head.png");
    background-repeat: no-repeat;
    background-size: 100%;
    .address-div {
      position: absolute;
      top: 28px;
      padding-right: 2px;
      left: 12px;
      width: 90px;
      height: 28px;
      background: #ffffff;
      border-radius: 14px;
      line-height: 28px;
      text-align: center;
      font-size: 14px;
      font-weight: 500;
      border: 1px solid #f5f5f5;
      color: #111111;
      .el-icon-location {
        margin-right: 4px;
        color: #9fa8bd;
        font-size: 18px;
        margin-left: 8px;
      }
    }
    .page-tit {
      position: absolute;
      font-size: 14px;
      text-align: center;
      width: 100%;
      top: 20px;
      line-height: 44px;
      left: 0;
      height: 44px;
    }
  }
}
.style-one {
  background: linear-gradient(360deg, rgba(255, 255, 255, 0) 0%, #ec1c24 100%);
}
.style-two {
  position: relative;
  overflow: hidden;
  .circular {
    width: 140%;
    height: 200px;
    position: absolute;
    left: -20%;
    top: 0;
    border-radius: 0 0 50% 50%;
    background: #ec1c24;
  }
}
.style-three {
  background: #ec1c24;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
</style>
