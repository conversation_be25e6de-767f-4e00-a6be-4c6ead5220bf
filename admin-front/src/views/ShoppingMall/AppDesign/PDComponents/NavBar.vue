<template>
  <div class="NavBar clearfix" :style="{ backgroundColor: sForm.bgColor }">
    <el-row :gutter="10">
      <el-col
        v-for="(item, index) in sForm.navList"
        :key="index"
        class="nav-li"
        :class="[setForm.num === 5 ? 'span-4-8' : '']"
        :span="parseInt(24 / setForm.num)"
      >
        <el-image class="nav-icon" :src="item.image.url || require('@/assets/img/replace-img.png')"></el-image>
        <div :style="{ color: sForm.color }">
          {{ item.text || "文字标题" }}
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "NavBar",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {},
    };
  },
  watch: {
    setForm(val) {
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
};
</script>

<style scoped>
.NavBar {
  padding: 16px 0 4px;
}
.nav-icon {
  width: 44px;
  height: 44px;
  margin: 0 auto 6px;
  display: block;
  border-radius: 10px;
}
.nav-li {
  /*margin-right: 10px;*/
  font-size: 12px;
  text-align: center;
  margin-bottom: 12px;
}
.span-4-8 {
  width: 20%;
}
</style>
