<template>
  <div class="MagicImg">
    <div v-if="sForm.imgStyle !== 5" class="Magic-style">
      <el-row :gutter="sForm.paddingLR">
        <el-col
          v-for="(item, index) in sForm.imagesList"
          :key="index"
          :span="24 / sForm.imgStyle"
          :style="{
            paddingBottom: sForm.paddingTB + 'px',
            paddingTop: sForm.paddingTB + 'px',
          }"
        >
          <el-image
            :src="item.image.url || replaceImg"
            class="banner-img"
            :class="[sForm.imagesList === 1 || sForm.imgStyle === 1 ? 'one-img' : '']"
            :fit="sForm.imagesList === 1 || sForm.imgStyle === 1 ? '' : 'cover'"
          ></el-image>
        </el-col>
      </el-row>
    </div>
    <div v-if="sForm.imgStyle === 5">
      <!--    单张图-->
      <el-image
        v-if="sForm.imagesList.length === 1"
        :src="sForm.imagesList[0].image.url || replaceImg"
        class="banner-img"
        :class="[sForm.imagesList === 1 || sForm.imgStyle === 1 ? 'one-img' : '']"
        :fit="sForm.imagesList === 1 || sForm.imgStyle === 1 ? '' : 'cover'"
      ></el-image>
      <!--    两张-->
      <div
        v-if="sForm.imagesList.length === 2"
        class="Magic-view"
        :style="{
          paddingBottom: sForm.paddingTB + 'px',
          paddingTop: sForm.paddingTB + 'px',
        }"
      >
        <el-image
          v-for="(item, index) in sForm.imagesList"
          :key="index"
          :src="item.image.url || replaceImg"
          alt=""
          class="two-img"
          fit="cover"
          :style="{
            paddingRight: (index === 0 ? sForm.paddingLR / 2 : 0) + 'px',
            paddingLeft: (index === 1 ? sForm.paddingLR / 2 : 0) + 'px',
          }"
        ></el-image>
      </div>
      <!--      三张图片-->
      <div
        v-if="sForm.imagesList.length === 3"
        :style="{
          paddingBottom: sForm.paddingTB + 'px',
          paddingTop: sForm.paddingTB + 'px',
        }"
        class="Magic-view"
      >
        <el-image
          :src="sForm.imagesList[0].image.url || replaceImg"
          alt=""
          class="float_left tree-big-img"
          fit="cover"
          :style="{ paddingRight: sForm.paddingLR / 2 + 'px' }"
        ></el-image>
        <div class="float_left tree-small-view" :style="{ paddingLeft: sForm.paddingLR / 2 + 'px' }">
          <el-image
            :src="sForm.imagesList[1].image.url || replaceImg"
            alt=""
            class="tree-small-img"
            fit="cover"
            :style="{ paddingBottom: sForm.paddingTB / 2 + 'px' }"
          ></el-image>
          <el-image
            :src="sForm.imagesList[2].image.url || replaceImg"
            alt=""
            class="tree-small-img"
            fit="cover"
            :style="{ paddingTop: sForm.paddingTB / 2 + 'px' }"
          ></el-image>
        </div>
      </div>
      <!--      四张-->
      <div
        v-if="sForm.imagesList.length === 4"
        class="Magic-view"
        :style="{
          paddingBottom: sForm.paddingTB + 'px',
          paddingTop: sForm.paddingTB + 'px',
        }"
      >
        <el-image
          :src="sForm.imagesList[0].image.url || replaceImg"
          alt=""
          class="tree-big-img"
          fit="cover"
          :style="{ paddingRight: sForm.paddingLR / 2 + 'px' }"
        ></el-image>
        <div class="tree-small-view" :style="{ paddingLeft: sForm.paddingLR / 2 + 'px' }">
          <el-image
            :src="sForm.imagesList[1].image.url || replaceImg"
            alt=""
            class="tree-small-img"
            fit="cover"
            :style="{
              paddingBottom: sForm.paddingTB / 2 + 'px',
              height: 60 - sForm.paddingTB / 4 + 'px',
            }"
          ></el-image>
          <div class="four-small-view" :style="{ paddingTop: sForm.paddingTB / 2 + 'px' }">
            <el-image
              :src="sForm.imagesList[2].image.url || replaceImg"
              alt=""
              class="four-small-img"
              fit="cover"
              :style="{
                paddingRight: sForm.paddingLR / 2 + 'px',
                height: 60 - sForm.paddingTB / 4 + 'px',
              }"
            ></el-image>
            <el-image
              :src="sForm.imagesList[3].image.url || replaceImg"
              alt=""
              class="four-small-img"
              fit="cover"
              :style="{
                paddingLeft: sForm.paddingLR / 2 + 'px',
                height: 60 - sForm.paddingTB / 4 + 'px',
              }"
            ></el-image>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import replaceImg from "@/assets/img/replace-img.png";
export default {
  name: "MagicImg",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      replaceImg: replaceImg,
      sForm: {},
    };
  },
  watch: {
    setForm(val) {
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
};
</script>

<style scoped>
.MagicImg {
  margin: 0 auto;
  width: 364px;
}
.banner-img {
  width: 100%;
  height: 86px;
  display: block;
  border-radius: 4px;
}
.one-img {
  height: auto;
}
.two-img {
  width: 50%;
  height: 120px;
  border-radius: 4px;
}
.tree-big-img {
  width: 50%;
  border-radius: 4px;
  height: 120px;
}
.tree-small-view {
  width: 50%;
}
.tree-small-img {
  width: 100%;
  height: 60px;
  display: block;
  border-radius: 4px;
}
.four-small-view {
  width: 100%;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.four-small-img {
  width: 50%;
  height: 60px;
  border-radius: 4px;
}
.Magic-view {
  display: -webkit-flex;
  display: flex;
  width: 100%;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>
