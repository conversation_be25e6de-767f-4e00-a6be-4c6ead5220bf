<template>
  <div
    class="Line-view"
    :style="{
      backgroundColor: sForm.bgColor,
      paddingTop: sForm.padding + 'px',
      paddingBottom: sForm.padding + 'px',
    }"
  >
    <div
      class="line-in"
      :style="{
        borderTop: sForm.borderWidth + 'px ' + sForm.borderStyle + ' ' + sForm.borderColor,
      }"
    ></div>
  </div>
</template>

<script>
export default {
  name: "LineF",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {},
    };
  },
  watch: {
    setForm(val) {
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
};
</script>

<style scoped>
.line-in {
  border-top: 1px solid #000000;
}
</style>
