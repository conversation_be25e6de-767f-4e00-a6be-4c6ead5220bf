<template>
  <div class="coupon-box">
    <div v-if="sForm.title || sForm.titleDesc" class="coupon-title">
      <span class="title">{{ sForm.title }}</span>
      <span v-if="sForm.titleDesc" class="line">|</span>
      <span class="desc">{{ sForm.titleDesc }}</span>
    </div>
    <ul class="coupon-ul" :style="{ paddingTop: sForm.title || sForm.titleDesc ? '2px' : '23px' }">
      <li
        v-for="item in sForm.rowNum"
        :key="item"
        class="coupon-li"
        :class="[sForm.rowNum === 2 ? 'coupon-two-li' : '']"
      >
        <div class="coupon-in">
          <p class="num">¥200</p>
          <p class="coupon-desc">商品满1000可用</p>
          <div class="get-btn">
            <span>领取使用</span>
            <span class="iconfont icon-youjiantou"></span>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: "CouponGroup",
  props: {
    setForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      sForm: {},
    };
  },
  watch: {
    setForm(val) {
      console.log(val);
      this.sForm = val;
    },
  },
  created() {
    this.sForm = this.setForm;
  },
};
</script>

<style scoped lang="scss">
.coupon-box {
  border-radius: 16px;
  width: 355px;
  margin: 0 auto;
  background-image: url("../../../../assets/img/pageComponents/coupon_bck.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  .coupon-title {
    padding: 0 12px;
    line-height: 45px;
    height: 45px;
    color: #ffe6c6;
    font-size: 12px;
    .line {
      margin: 0 10px;
    }
    .title {
      font-size: 18px;
      font-weight: 600;
      color: #ffd7a4;
      text-shadow: 1px 2px 1px rgba(175, 19, 13, 0.51);
      background: linear-gradient(123deg, #fff7e5 0%, #f7c89a 100%);
      background-clip: text;
    }
  }
  .coupon-ul {
    padding-left: 4px;
    display: flex;
    padding-bottom: 23px;
    padding-top: 2px;
    .coupon-li {
      margin-left: 12px;
      width: 100px;
      height: 140px;
      background: linear-gradient(180deg, #ffe3cb 3%, #fff8f2 100%);
      border-radius: 5px;
      padding-top: 2px;
      .coupon-in {
        width: 96px;
        height: 136px;
        border-radius: 4px;
        border: 1px solid #a5837a;
        margin: 0 auto;
        text-align: center;
        .num {
          font-size: 24px;
          font-family: DIN-Medium, DIN;
          font-weight: 500;
          color: #8b3913;
          line-height: 29px;
          background: linear-gradient(180deg, #934331 0%, #c5785f 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-top: 26px;
        }
        .coupon-desc {
          font-size: 12px;
          font-weight: 400;
          color: #3c3c3c;
          line-height: 17px;
          margin-top: 8px;
        }
        .get-btn {
          width: 76px;
          height: 24px;
          line-height: 24px;
          background: #9e4722;
          border-radius: 20px;
          color: #ffffff;
          font-size: 10px;
          margin: 20px auto 0;
          .iconfont {
            font-size: 10px;
            margin-left: 5px;
          }
        }
      }
    }
    .coupon-two-li {
      width: 154px;
      .coupon-in {
        width: 150px;
        .get-btn {
          width: 86px;
        }
      }
    }
  }
}
</style>
