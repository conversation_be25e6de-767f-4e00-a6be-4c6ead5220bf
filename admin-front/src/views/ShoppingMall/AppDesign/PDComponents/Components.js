export const componentsList = [
  {
    title: "媒体组件",
    components: [
      {
        name: "图片轮播",
        comName: "ImgSwiper",
        icon: "icon-tupian",
        iconImg: require("@/assets/img/pageComponents/banner.png"),
        modelData: {
          indicatorDots: true,
          indicatorColor: "rgba(255, 255, 255, 0.4)",
          indicatorActiveColor: "rgba(255, 255, 255, 1)",
          autoplay: true,
          interval: 3000,
          style: 1,
          bannerList: [
            {
              image: {},
              img: "",
              url: "",
              switchTab: "",
            },
            {
              image: {},
              url: "",
              img: "",
              switchTab: "",
            },
            {
              image: {},
              url: "",
              img: "",
              switchTab: "",
            },
          ],
        },
      },
      {
        name: "图片广告",
        iconImg: require("@/assets/img/pageComponents/cube.png"),
        comName: "AdvGroup",
        icon: "icon-mofang",
        modelData: {
          style: {
            label: "上三下二",
            styleId: 1,
          },
          adv_list: [],
        },
      },
      {
        name: "图片魔方",
        iconImg: require("@/assets/img/pageComponents/cube.png"),
        comName: "MagicImg",
        icon: "icon-mofang",
        modelData: {
          imgStyle: 5,
          paddingLR: 2,
          paddingTB: 2,
          imagesList: [
            {
              image: {},
              url: "",
              switchTab: "",
            },
            {
              image: {},
              url: "",
              switchTab: "",
            },
            {
              image: {},
              url: "",
              switchTab: "",
            },
            {
              image: {},
              url: "",
              switchTab: "",
            },
          ],
        },
      },
      {
        name: "公告",
        iconImg: require("@/assets/img/pageComponents/notice.png"),
        icon: "icon-gonggao",
        comName: "Notice",
        modelData: {
          padding: 5,
          bgColor: "#ffffff",
          textColor: "#000000",
          iconSel: 1,
          iconColor: "#fd463e",
        },
      },
    ],
  },
  {
    title: "商城组件",
    components: [
      {
        name: "导航条",
        iconImg: require("@/assets/img/pageComponents/goods.png"),
        icon: "icon-tupian",
        comName: "NavigationBar",
        modelData: {
          name: "舜津科技",
          topBgColor: "#ffffff",
          textColor: "#000000",
          pageBgColor: "#f7f8fa",
          bgImage: "",
          style: 1,
        },
      },
      {
        name: "搜索框",
        iconImg: require("@/assets/img/pageComponents/search.png"),
        icon: "icon-sousuo",
        comName: "SearchC",
        modelData: {
          tipText: "请输入关键词",
          boxStyle: 1,
          textStyle: "center",
        },
      },
      {
        name: "导航",
        iconImg: require("@/assets/img/pageComponents/menu.png"),
        icon: "icon-kuaijierukou",
        comName: "NavBar",
        modelData: {
          color: "#000000",
          bgColor: "#ffffff",
          num: 4,
          navList: [
            {
              image: {},
              text: "",
              url: "",
              switchTab: "",
            },
            {
              image: {},
              url: "",
              text: "",
              switchTab: "",
            },
            {
              image: {},
              url: "",
              text: "",
              switchTab: "",
            },
            {
              image: {},
              url: "",
              text: "",
              switchTab: "",
            },
          ],
        },
      },
      {
        name: "商品组",
        iconImg: require("@/assets/img/pageComponents/goods.png"),
        icon: "icon-apps",
        comName: "GoodsGroup",
        modelData: {
          goodsFrom: 1,
          titleColor: "#333333",
          titleSetColor: "#fd463e",
          titleBgColor: "#ffffff",
          titleBgColor2: "#ffffff",
          titleStyle: 1,
          btnStyle: 1,
          navStyle: 1,
          title: "精选好物",
          title_two: "为您推荐",
          title_url: "",
          switchTab: "",
          categoryPath: [],
          goods_ids: [],
          goodsNum: 6,
          goodsSort: 1,
          bgColor: "#f5f9fc",
          textColor: "#333333",
          itemBgColor: "#ffffff",
          btnColor: "#fd463e",
          priceColor: "#fd463e",
          type: 1,
          colNum: 1,
          goodsInfo: [1, 2, 3, 4, 5, 6],
          tabList: [
            {
              goods_ids: [],
              tabName: "选项卡名称",
              goodsFrom: 1,
              categoryPath: [],
              goods_list: [
                {
                  images: "",
                  title: "商品名称",
                  id: "001",
                  skuId: "002",
                },
                {
                  images: "",
                  title: "商品名称",
                  id: "003",
                  skuId: "004",
                },
              ],
              goodsNum: 6,
              goodsSort: 1,
            },
          ],
          goods_list: [
            {
              images: "",
              title: "商品名称",
              id: "001",
              skuId: "002",
            },
            {
              images: "",
              title: "商品名称",
              id: "003",
              skuId: "004",
            },
          ],
        },
      },
      /*{
        name: "积分商品",
        iconImg: require("@/assets/img/pageComponents/goods.png"),
        icon: "icon-apps",
        comName: "IntegralGoods",
        modelData: {
          titleColor: "#333333",
          titleSetColor: "#fd463e",
          titleBgColor: "#ffffff",
          titleBgColor2: "#ffffff",
          titleStyle: 1,
          btnStyle: 1,
          navStyle: 1,
          title: "精选好物",
          title_two: "为您推荐",
          goodsNum: 6,
          bgColor: "#f5f9fc",
          textColor: "#333333",
          itemBgColor: "#ffffff",
          btnColor: "#fd463e",
          priceColor: "#fd463e",
          type: 1,
          colNum: 1,
          goodsInfo: [1, 2, 3, 4],
          goods_list: [
            {
              images:
                "http://image.qianniao.vip/160886828880146/35e97bca34dab476692d2ad8cc47954b/timg (2).jpeg",
              title: "商品名称",
              id: "001",
              skuId: "002",
            },
            {
              images:
                "http://image.qianniao.vip/160886828880146/35e97bca34dab476692d2ad8cc47954b/timg (2).jpeg",
              title: "商品名称",
              id: "003",
              skuId: "004",
            },
          ],
        },
      },*/
      {
        name: "优惠券组",
        iconImg: require("@/assets/img/pageComponents/coupon.png"),
        icon: "icon-youhuiquan",
        comName: "CouponGroup",
        modelData: {
          couponNum: 6,
          bgColor: "#f5f9fc",
          rowNum: 2,
          title: "优惠礼券",
          titleDesc: "先领券再购物",
        },
      },
      {
        name: "秒杀商品",
        iconImg: require("@/assets/img/pageComponents/seckill.png"),
        icon: "icon-miaosha",
        comName: "LimitedSeckill",
        modelData: {
          bgColor: "#ffffff",
          btnStyle: 1,
          seckill_id: "",
          seckill_title: "",
          goodsNum: 6,
          type: 1,
          colNum: 3,
          goodsInfo: [1, 2, 3],
        },
      },
    ],
  },
  {
    title: "工具组件",
    components: [
      {
        name: "微信客服",
        iconImg: require("@/assets/img/pageComponents/wxService.png"),
        comName: "wxService",
        icon: "icon-fudonganniu",
        modelData: {
          color: "#409EFF",
        },
      },
      {
        name: "浮动按钮",
        iconImg: require("@/assets/img/pageComponents/float.png"),
        comName: "FloatBth",
        icon: "icon-fudonganniu",
        modelData: {
          bgColor: "#ffffff",
          btnType: 1,
          phone: "",
          url: "",
          switchTab: "",
          image: "",
          btnStyle: 2,
        },
      },
      {
        name: "辅助空白",
        comName: "Blank",
        iconImg: require("@/assets/img/pageComponents/blank.png"),
        icon: "icon-kongbai",
        modelData: {
          backgroundColor: "",
          height: 5,
        },
      },
      {
        name: "辅助线",
        iconImg: require("@/assets/img/pageComponents/line.png"),
        comName: "LineF",
        icon: "icon-xian",
        modelData: {
          bgColor: "",
          borderColor: "#eeeeee",
          borderWidth: 1,
          borderStyle: "solid",
          padding: 5,
        },
      },
    ],
  },
];
