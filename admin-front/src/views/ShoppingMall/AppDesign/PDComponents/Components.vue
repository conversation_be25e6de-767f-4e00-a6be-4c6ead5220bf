<template>
  <div class="tem-box">
    <div class="tem-tit">
      <span class="tem-tit-span">组件库</span>
    </div>
    <div class="tem-main">
      <div v-for="(item, index) in componentsList" :key="index" class="com-ul-li">
        <div class="com-tit">
          {{ item.title }}
        </div>
        <ul class="com-ul clearfix">
          <li v-for="(itemC, indexC) in item.Components" :key="indexC" class="com-li">
            <i class="iconfont" :class="[itemC.icon]"></i>
            <p>{{ itemC.name }}</p>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Components",
  data() {
    return {
      componentsList: [
        {
          title: "媒体组件",
          components: [
            {
              name: "图片轮播",
              icon: "icon-tupian",
              componentName: "specialBanner",
              modelData: {
                bannerList: [],
                swiperConfig: {
                  indicatorDots: true,
                  indicatorColor: "rgba(255, 255, 255, 0.4)",
                  indicatorActiveColor: "rgba(255, 255, 255, 1)",
                  autoplay: false,
                  interval: 3000,
                  duration: 300,
                },
              },
            },
            {
              name: "单图组",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "图片魔方",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "头条快报",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
          ],
        },
        {
          title: "商城组件",
          components: [
            {
              name: "搜索框",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "公告组",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "导航",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "商品组",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "优惠券组",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "秒杀商品",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "拼团商品",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "砍价商品",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "线下门店",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
          ],
        },
        {
          title: "工具组件",
          components: [
            {
              name: "在线客服",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "富文本",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "辅助空白",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
            {
              name: "辅助线",
              icon: "icon-tupian",
              componentName: "specialBanner",
            },
          ],
        },
      ],
    };
  },
};
</script>

<style scoped>
.tem-box {
  padding: 12px;
  font-weight: 400;
}
.tem-tit {
  font-size: 14px;
  color: #333;
  font-weight: 400;
  padding-bottom: 10px;
  border-bottom: 1px solid #dddddd;
  padding-left: 10px;
}
.com-tit {
  font-size: 12px;
  color: #999;
  line-height: 40px;
}
.com-li {
  width: 74px;
  text-align: center;
  float: left;
  font-size: 12px;
  padding: 5px 0;
  margin: 5px;
  background: #f5f9fc;
  border: 1px solid #dddddd;
  cursor: pointer;
  transition: 0.3s;
  color: #424242;
}
.com-li p {
  padding-top: 5px;
}
.com-li:hover {
  background: #fff;
  border: 1px solid #00aeff;
}
</style>
