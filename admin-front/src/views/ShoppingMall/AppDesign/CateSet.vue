<template>
  <ContainerTit :is-footer="false">
    <el-row class="tem-row">
      <el-col :span="10">
        <img class="tem-img" :src="img_src" alt="" />
      </el-col>
      <el-col :span="14">
        <el-form>
          <el-form-item label="分类样式：" prop="style">
            <el-radio-group v-model="form.value" @change="styleChange">
              <el-radio v-for="(item, index) in tem_style" :key="index" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item style="text-align: center">
            <el-button v-if="$accessCheck($Access.CategorySetSetClassSetting)" type="primary" @click="setClassSetting">
              提交
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
  </ContainerTit>
</template>

<script>
import { getClassSettingInfo, setClassSetting } from "@/api/System";
export default {
  name: "CateSet",
  data() {
    return {
      form: {
        value: "",
      },
      img_tip: "宽750像素 高度不限",
      img_src: require("@/assets/img/10.jpg"),
      tem_style: [
        {
          label: "一级分类（大图）",
          value: 1,
          tip: "宽680px 高300px",
          img: require("@/assets/img/10.jpg"),
        },
        {
          label: "一级分类（小图）",
          value: 2,
          tip: "宽188px 高188px",
          img: require("@/assets/img/11.jpg"),
        },
        {
          label: "二级分类",
          value: 3,
          tip: "宽150px 高150px",
          img: require("@/assets/img/20.jpg"),
        },
        {
          label: "三级分类",
          value: 5,
          tip: "宽150px 高150px",
          img: require("@/assets/img/40.jpg"),
        },
        {
          label: "分类+商品",
          value: 4,
          img: require("@/assets/img/30.jpg"),
        },
      ],
    };
  },
  created() {
    // if (this.$accessCheck(this.$Access.CategorySetGetClassSettingInfo)) {
    //   this.getClassSettingInfo()
    // }
    this.getClassSettingInfo();
  },
  methods: {
    styleChange(val) {
      const obj = this.tem_style.find((item) => item.value === val);
      this.img_tip = obj.tip || "";
      this.img_src = obj.img;
    },
    // 获取分类
    async getClassSettingInfo() {
      const { data } = await getClassSettingInfo();

      this.form = data;
      const obj = this.tem_style.find((item) => item.value === this.form.value);
      this.img_tip = obj.tip || "";
      this.img_src = obj.img;
    },
    //  编辑
    async setClassSetting() {
      const data = await setClassSetting(this.form);

      this.$message({
        type: "success",
        message: "提交成功，小程序&App 下次进入后效果生效",
      });
      this.getClassSettingInfo();
    },
  },
};
</script>

<style scoped>
.tem-row {
  padding: 20px 80px;
}
.tem-img {
  box-shadow: 0 3px 10px #dcdcdc;
  width: 375px;
}
</style>
