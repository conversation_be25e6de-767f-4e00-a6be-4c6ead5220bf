<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">优惠券组</span>
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="显示数量" class="PD-form-item">
          <el-input-number v-model="form.couponNum" :max="6" @blur="confirm"></el-input-number>
          <span style="font-size: 12px; color: #666; font-weight: 300"> （最多6个） </span>
        </el-form-item>
        <el-form-item label="每行数量" class="PD-form-item">
          <el-radio-group v-model="form.rowNum" @change="confirm">
            <el-radio :label="2">两个</el-radio>
            <el-radio :label="3">三个</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="标题" class="PD-form-item">
          <el-input v-model="form.title"></el-input>
        </el-form-item>
        <el-form-item label="副标题" class="PD-form-item">
          <el-input v-model="form.titleDesc"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu";
export default {
  name: "CouponForm",
  components: {
    // UploadQiniu,
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {},
    };
  },
  watch: {
    temForm(val) {
      console.log("temForm::", val);
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
    uploadSuccess(val, res, file, fileList) {
      this.form.image = val;
      this.confirm();
    },
  },
};
</script>

<style scoped></style>
