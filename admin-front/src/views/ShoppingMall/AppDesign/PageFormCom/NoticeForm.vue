<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">公告</span>
      <!--<el-button
        class="float_right"
        type="primary"
        size="mini"
        @click="confirm"
      >
        完成
      </el-button>-->
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="背景颜色" class="PD-form-item">
          <el-color-picker v-model="form.bgColor" @change="confirm"></el-color-picker>
        </el-form-item>
        <el-form-item label="文字颜色" class="PD-form-item">
          <el-color-picker v-model="form.textColor" @change="confirm"></el-color-picker>
        </el-form-item>
        <el-form-item label="上下边距" class="PD-form-item">
          <el-slider v-model="form.padding" :max="50" @change="confirm"></el-slider>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
// import UploadQiniu from '@/component/common/UploadQiniu'

export default {
  name: "NoticeForm",
  components: {
    // UploadQiniu
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {},
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
    uploadSuccess(val, res, file) {
      this.form.icon = {
        name: file.name,
        url: val,
      };
      this.confirm();
    },
  },
};
</script>

<style scoped lang="scss">
.icon-fill {
  background-color: #fd463e;
  width: 18px;
  height: 18px;
  text-align: center;
  line-height: 18px;
  border-radius: 100%;
  display: inline-block;
  .icon-volume {
    color: #ffffff;
    font-size: 12px;
  }
}
.icon-volume1 {
  color: #fd463e;
}
.icon-text {
  font-size: 12px;
  background: #ffe1e1;
  color: #fd463e;
  border-radius: 9px;
  display: inline-block;
  width: 31px;
  height: 18px;
  line-height: 18px;
  text-align: center;
}
</style>
