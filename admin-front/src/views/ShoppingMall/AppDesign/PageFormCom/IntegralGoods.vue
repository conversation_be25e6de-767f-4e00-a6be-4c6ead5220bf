<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">商品组</span>
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item v-if="form.navStyle === 1" label="标题栏风格" class="PD-form-item">
          <el-radio-group v-model="form.titleStyle" @change="titleStyleChange">
            <el-radio :label="1" style="padding-bottom: 10px">风格1</el-radio>
            <el-radio :label="2">风格2</el-radio>
            <el-radio :label="3">风格3</el-radio>
            <el-radio :label="4">风格4</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.navStyle === 1" label="标题" class="PD-form-item">
          <el-input v-model="form.title" size="small" maxlength="5" show-word-limit placeholder="自定义标题"></el-input>
        </el-form-item>
        <el-form-item v-if="form.navStyle === 1" label="子标题" class="PD-form-item">
          <el-input
            v-model="form.title_two"
            size="small"
            maxlength="10"
            show-word-limit
            placeholder="自定义标题"
          ></el-input>
        </el-form-item>
        <el-form-item class="PD-form-item">
          <ul class="color-ul">
            <li>
              <p>标题字体色</p>
              <el-color-picker
                v-model="form.titleColor"
                :predefine="predefineColors"
                @change="confirm"
              ></el-color-picker>
            </li>
            <li>
              <p>标题栏背景</p>
              <el-color-picker
                v-model="form.titleBgColor"
                show-alpha
                :predefine="predefineColors"
                @change="titBgChange"
              ></el-color-picker>
            </li>
            <li>
              <p>标题装饰色</p>
              <el-color-picker
                v-model="form.titleSetColor"
                show-alpha
                :predefine="predefineColors"
                @change="confirm"
              ></el-color-picker>
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="显示数量" class="PD-form-item">
          <el-input-number v-model="form.goodsNum"></el-input-number>
        </el-form-item>
        <div class="common-form" style="border-top: 4px solid #f5f9fc">
          <el-form-item class="PD-form-item">
            <ul class="color-ul">
              <li>
                <p>总体背景</p>
                <el-color-picker
                  v-model="form.bgColor"
                  show-alpha
                  :predefine="predefineColors"
                  @change="confirm"
                ></el-color-picker>
              </li>
              <li>
                <p>商品背景</p>
                <el-color-picker
                  v-model="form.itemBgColor"
                  show-alpha
                  :predefine="predefineColors"
                  @change="confirm"
                ></el-color-picker>
              </li>
            </ul>
          </el-form-item>
          <el-form-item class="PD-form-item">
            <ul class="color-ul">
              <li>
                <p>字体色</p>
                <el-color-picker
                  v-model="form.textColor"
                  show-alpha
                  :predefine="predefineColors"
                  @change="confirm"
                ></el-color-picker>
              </li>
              <li>
                <p>价格颜色</p>
                <el-color-picker
                  v-model="form.priceColor"
                  show-alpha
                  :predefine="predefineColors"
                  @change="confirm"
                ></el-color-picker>
              </li>
            </ul>
          </el-form-item>
          <el-form-item class="PD-form-item">
            <ul class="color-ul">
              <li>
                <p>按钮样式</p>
                <el-radio-group v-model="form.btnStyle" @change="confirm">
                  <el-radio :label="1">
                    <i class="iconfont icon-xinzeng1"></i>
                  </el-radio>
                  <el-radio :label="2">
                    <i class="iconfont icon-gouwuche1"></i>
                  </el-radio>
                </el-radio-group>
              </li>
            </ul>
          </el-form-item>
          <el-form-item label="显示类型" class="PD-form-item">
            <el-radio-group v-model="form.type">
              <el-radio :label="1">列表平铺</el-radio>
              <el-radio :label="2" :disabled="form.colNum === 1"> 横向滑动 </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="分列数量" class="PD-form-item">
            <el-radio-group v-model="form.colNum">
              <el-radio :label="1" :disabled="form.type === 2">单列</el-radio>
              <el-radio :label="2">两列</el-radio>
              <el-radio :label="3">三列</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="显示内容" class="PD-form-item">
            <el-checkbox-group v-model="form.goodsInfo">
              <el-checkbox :label="1">商品名称</el-checkbox>
              <el-checkbox :label="2">商品价格</el-checkbox>
              <el-checkbox v-show="form.colNum === 1" :label="3"> 商品卖点 </el-checkbox>
              <el-checkbox v-show="form.colNum === 1" :label="4"> 商品销量 </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "IntegralGoods",
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {},
      tab_index: 0,
      predefineColors: [
        "#ffffff",
        "#000000",
        "#f7f8fa",
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "#c7158577",
      ],
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    titBgChange(val) {
      this.form.titleBgColor2 = this.$_common.colorRgba(val, 0.7);
      this.confirm();
    },
    titleStyleChange(val) {
      this.confirm();
    },
    linkConfirm(obj) {
      this.form.title_url = obj.links;
      this.form.switchTab = obj.switchTab ? "switchTab" : "";
      this.confirm();
    },
    confirm() {
      this.$emit("confirm", this.form);
    },
    // 导航风格切换
    styleChange() {
      this.form.title = "";
      this.form.goodsFrom = 1;
      this.form.categoryPath = [];
      this.form.goods_ids = [];
      this.confirm();
    },
  },
};
</script>

<style scoped lang="scss">
.icon-xinzeng1 {
  color: #fd463e;
}
.icon-gouwuche1 {
  font-size: 20px;
  border-radius: 100%;
  text-align: center;
  color: #fd463e;
}
.color-ul {
  display: flex;
  li {
    flex: 3;
    p {
      color: #666666;
    }
  }
}
</style>
