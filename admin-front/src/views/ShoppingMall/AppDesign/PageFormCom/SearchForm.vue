<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">搜索框</span>
    </div>
    <div class="tem-main">
      <el-form ref="form" label-position="top" :model="form" size="small">
        <el-form-item label="提示文字" class="PD-form-item">
          <el-input v-model="form.tipText"></el-input>
        </el-form-item>
        <el-form-item label="风格" class="PD-form-item">
          <el-radio-group v-model="form.boxStyle" @change="confirm">
            <el-radio :label="1">方形</el-radio>
            <el-radio :label="2">圆角</el-radio>
            <el-radio :label="3">圆弧</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文字对齐" class="PD-form-item">
          <el-radio-group v-model="form.textStyle" @change="confirm">
            <el-radio label="left">居左</el-radio>
            <el-radio label="center">居中</el-radio>
            <el-radio label="right">居右</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "SearchForm",
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {},
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
  },
};
</script>

<style scoped></style>
