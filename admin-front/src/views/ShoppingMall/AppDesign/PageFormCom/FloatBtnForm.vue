<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">浮动按钮</span>
      <!--<el-button
        class="float_right"
        type="primary"
        size="mini"
        @click="confirm"
      >
        完成
      </el-button>-->
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="按钮图标" class="PD-form-item">
          <UploadQiniu
            :width="50"
            :file-list="image_list"
            @uploadSuccess="uploadSuccess"
            @handleRemove="uploadRemove"
          ></UploadQiniu>
        </el-form-item>
        <el-form-item label="按钮风格" class="PD-form-item">
          <el-radio-group v-model="form.btnStyle" @change="confirm">
            <el-radio :label="1">空心</el-radio>
            <el-radio :label="2">实心</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.btnStyle === 2" label="背景颜色" class="PD-form-item">
          <el-color-picker v-model="form.bgColor" @change="confirm"></el-color-picker>
        </el-form-item>

        <el-form-item label="按钮类型" class="PD-form-item">
          <el-radio-group v-model="form.btnType" @change="confirm">
            <el-radio :label="1">链接</el-radio>
            <el-radio :label="2">拨打电话</el-radio>
            <el-radio :label="3">返回顶部</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.btnType === 1" label="链接" class="PD-form-item">
          <el-input v-model="form.url" placeholder="请选择跳转链接" readonly>
            <el-button slot="append" icon="el-icon-circle-check" @click="link_show = true"> 选择 </el-button>
          </el-input>
        </el-form-item>
        <el-form-item v-if="form.btnType === 2" label="电话号码" class="PD-form-item">
          <el-input v-model="form.phone" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <LinkSel
      v-if="link_show"
      :is-show="link_show"
      :shop-id="shopId"
      @confirm="linkConfirm"
      @cancel="link_show = false"
    />
  </div>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu";
import LinkSel from "../components/LinkSel.vue";

export default {
  name: "FloatBtnForm",
  components: {
    UploadQiniu,
    LinkSel,
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    shopId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      form: {},
      link_show: false,
      image_list: [],
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
      this.image_list = val.image ? [{ name: "", url: val.image }] : [];
    },
  },
  created() {
    this.form = this.temForm;
    this.image_list = this.temForm.image ? [{ name: "", url: this.temForm.image }] : [];
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
    uploadSuccess(val) {
      this.form.image = val;
      this.confirm();
    },
    uploadRemove() {
      this.form.image = "";
      this.confirm();
    },
    linkConfirm(obj) {
      this.form.url = obj.links;
      this.form.switchTab = obj.switchTab ? "switchTab" : "";
      this.confirm();
    },
  },
};
</script>

<style scoped></style>
