<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">页面设置</span>
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="页面标题" class="PD-form-item">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="顶部风格" class="PD-form-item">
          <el-radio-group v-model="form.style">
            <el-radio :label="1">风格1</el-radio>
            <el-radio :label="2">风格2</el-radio>
            <el-radio :label="3">风格3</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="顶部背景色" class="PD-form-item">
          <el-color-picker v-model="form.topBgColor" :predefine="predefineColors" show-alpha></el-color-picker>
        </el-form-item>
        <el-form-item label="标题颜色" class="PD-form-item">
          <el-color-picker v-model="form.textColor" :predefine="predefineColors"></el-color-picker>
        </el-form-item>
        <el-form-item label="页面背景色" class="PD-form-item">
          <el-color-picker v-model="form.pageBgColor" :predefine="predefineColors" show-alpha></el-color-picker>
        </el-form-item>
        <el-form-item label="背景图片" class="PD-form-item">
          <UploadQiniu
            :width="50"
            :file-list="image_list"
            @uploadSuccess="uploadSuccess"
            @handleRemove="uploadRemove"
          ></UploadQiniu>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import UploadQiniu from "@/component/common/UploadQiniu";
export default {
  name: "HeadFrom",
  components: {
    UploadQiniu,
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {},
      image_list: [],
      predefineColors: [
        "#ffffff",
        "#000000",
        "#f7f8fa",
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "#c7158577",
      ],
    };
  },
  computed: {
    ...mapGetters({
      enterprise: "MUser/enterprise",
      storeData: "MUser/storeData",
      systemType: "MUser/systemType",
    }),
  },
  watch: {
    temForm(val) {
      this.form = val;
      this.image_list = val.bgImage ? [{ name: "", url: val.bgImage }] : [];
    },
  },
  created() {
    this.form = this.temForm;
    this.image_list = this.temForm.bgImage ? [{ name: "", url: this.temForm.bgImage }] : [];
  },

  mounted() {
    // console.log(this.form);
    // if (!this.form.name) {
    //   if (this.systemType === 1) {
    //     this.form.name = this.enterprise.enterpriseName;
    //   } else {
    //     this.form.name = this.storeData.name;
    //   }
    // }
  },
  methods: {
    uploadSuccess(val) {
      this.form.bgImage = val;
      this.confirm();
    },
    uploadRemove() {
      this.form.bgImage = "";
      this.confirm();
    },
    confirm() {
      this.$emit("confirm", this.form);
    },
  },
};
</script>
