<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">商品组</span>
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="导航风格" class="PD-form-item">
          <el-radio-group v-model="form.navStyle" @change="styleChange">
            <el-radio :label="1">标题</el-radio>
            <el-radio :label="2">选项卡</el-radio>
            <el-radio :label="3">无</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.navStyle === 1" label="标题栏风格" class="PD-form-item">
          <el-radio-group v-model="form.titleStyle" @change="titleStyleChange">
            <el-radio :label="1" style="padding-bottom: 10px">风格1</el-radio>
            <el-radio :label="2">风格2</el-radio>
            <el-radio :label="3">风格3</el-radio>
            <el-radio :label="4">风格4</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.navStyle === 1" label="标题" class="PD-form-item">
          <el-input v-model="form.title" size="small" maxlength="5" show-word-limit placeholder="自定义标题"></el-input>
        </el-form-item>
        <el-form-item v-if="form.navStyle === 1" label="子标题" class="PD-form-item">
          <el-input
            v-model="form.title_two"
            size="small"
            maxlength="10"
            show-word-limit
            placeholder="自定义标题"
          ></el-input>
        </el-form-item>
        <el-form-item class="PD-form-item">
          <ul class="color-ul">
            <li>
              <p>标题字体色</p>
              <el-color-picker
                v-model="form.titleColor"
                :predefine="predefineColors"
                @change="confirm"
              ></el-color-picker>
            </li>
            <li>
              <p>标题栏背景</p>
              <el-color-picker
                v-model="form.titleBgColor"
                show-alpha
                :predefine="predefineColors"
                @change="titBgChange"
              ></el-color-picker>
            </li>
            <li>
              <p>标题装饰色</p>
              <el-color-picker
                v-model="form.titleSetColor"
                show-alpha
                :predefine="predefineColors"
                @change="confirm"
              ></el-color-picker>
            </li>
          </ul>
        </el-form-item>
        <div v-if="form.navStyle !== 2">
          <el-form-item label="选择商品" class="PD-form-item">
            <el-radio-group v-model="form.goodsFrom" @change="fromChange">
              <el-radio :label="1">自动获取</el-radio>
              <el-radio :label="2">手动选择</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="form.goodsFrom === 1" label="商品分类" class="PD-form-item">
            <GoodsCategory v-model="form.categoryPath" :check-strictly="true" width="240" @change="categoryChange" />
          </el-form-item>
          <el-form-item v-if="form.goodsFrom === 1" label="显示数量" class="PD-form-item">
            <el-input-number v-model="form.goodsNum"></el-input-number>
          </el-form-item>
          <el-form-item v-if="form.goodsFrom === 2" label-width="1" class="PD-form-item">
            <div class="goods-div clearfix" style="margin-bottom: 10px">
              <div v-for="(item, index) in form.goods_list" :key="index" class="goods-li float_left">
                <el-image :src="item.images[0] || require('@/assets/img/replace-img.png')" class="goods-img"></el-image>
                <i class="el-icon-error icon-del" @click="delGoods(index)"></i>
              </div>
            </div>
            <el-button type="primary" size="mini" plain @click="goods_show = true"> 添加商品 </el-button>
          </el-form-item>
        </div>
        <div v-if="form.navStyle === 2" class="tab-set">
          <el-form-item v-if="form.navStyle === 2" label="选项卡设置" class="PD-form-item">
            <div style="padding-bottom: 10px">
              <el-tag
                v-for="(item, index) in form.tabList"
                :key="index"
                v-dragging="{ item: item, list: form.tabList, group: 'tab' }"
                style="cursor: move"
                closable
                type="primary"
                :effect="tab_index === index ? 'dark' : 'plain'"
                @click="selTab(index)"
                @close="delTab(index)"
              >
                {{ item.tabName }}
              </el-tag>
              <el-button
                style="margin-left: 10px"
                type="primary"
                size="mini"
                plain
                icon="el-icon-plus"
                @click="addTab"
              ></el-button>
            </div>
            <el-input
              v-model="form.tabList[tab_index].tabName"
              size="small"
              maxlength="7"
              show-word-limit
              placeholder="选项卡名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="选择商品" class="PD-form-item">
            <el-radio-group v-model="form.tabList[tab_index].goodsFrom" @change="confirm">
              <el-radio :label="1">自动获取</el-radio>
              <el-radio :label="2">手动选择</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="form.tabList[tab_index].goodsFrom === 1" label="商品分类" class="PD-form-item">
            <GoodsCategory
              v-model="form.tabList[tab_index].categoryPath"
              :check-strictly="true"
              width="240"
              @change="categoryChange"
            />
          </el-form-item>
          <el-form-item v-if="form.tabList[tab_index].goodsFrom === 1" label="显示数量" class="PD-form-item">
            <el-input-number v-model="form.tabList[tab_index].goodsNum"></el-input-number>
          </el-form-item>
          <el-form-item v-if="form.tabList[tab_index].goodsFrom === 2" label-width="1" class="PD-form-item">
            <div class="goods-div clearfix">
              <div v-for="(item, index) in form.tabList[tab_index].goods_list" :key="index" class="goods-li float_left">
                <el-image :src="item.images[0] || require('@/assets/img/replace-img.png')" class="goods-img"></el-image>
                <i class="el-icon-error icon-del" @click="delGoods(index)"></i>
              </div>
            </div>
            <el-button
              style="width: 100%; border: 1px dashed #999"
              size="small"
              icon="el-icon-plus"
              @click="goods_show = true"
            >
              添加商品
            </el-button>
          </el-form-item>
        </div>
        <div class="common-form" style="border-top: 4px solid #f5f9fc">
          <el-form-item class="PD-form-item">
            <ul class="color-ul">
              <li>
                <p>总体背景</p>
                <el-color-picker
                  v-model="form.bgColor"
                  show-alpha
                  :predefine="predefineColors"
                  @change="confirm"
                ></el-color-picker>
              </li>
              <li>
                <p>商品背景</p>
                <el-color-picker
                  v-model="form.itemBgColor"
                  show-alpha
                  :predefine="predefineColors"
                  @change="confirm"
                ></el-color-picker>
              </li>
            </ul>
          </el-form-item>
          <el-form-item class="PD-form-item">
            <ul class="color-ul">
              <li>
                <p>字体色</p>
                <el-color-picker
                  v-model="form.textColor"
                  show-alpha
                  :predefine="predefineColors"
                  @change="confirm"
                ></el-color-picker>
              </li>
              <li>
                <p>价格颜色</p>
                <el-color-picker
                  v-model="form.priceColor"
                  show-alpha
                  :predefine="predefineColors"
                  @change="confirm"
                ></el-color-picker>
              </li>
            </ul>
          </el-form-item>
          <el-form-item class="PD-form-item">
            <ul class="color-ul">
              <li>
                <p>按钮样式</p>
                <el-radio-group v-model="form.btnStyle" @change="confirm">
                  <el-radio :label="1">
                    <i class="iconfont icon-xinzeng1"></i>
                  </el-radio>
                  <el-radio :label="2">
                    <i class="iconfont icon-gouwuche1"></i>
                  </el-radio>
                </el-radio-group>
              </li>
            </ul>
          </el-form-item>
          <el-form-item label="显示类型" class="PD-form-item">
            <el-radio-group v-model="form.type">
              <el-radio :label="1">列表平铺</el-radio>
              <el-radio :label="2" :disabled="form.colNum === 1"> 横向滑动 </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="分列数量" class="PD-form-item">
            <el-radio-group v-model="form.colNum">
              <el-radio :label="1" :disabled="form.type === 2">单列</el-radio>
              <el-radio :label="2">两列</el-radio>
              <el-radio :label="3">三列</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="显示内容" class="PD-form-item">
            <el-checkbox-group v-model="form.goodsInfo">
              <el-checkbox :label="1">商品名称</el-checkbox>
              <el-checkbox :label="2">商品价格</el-checkbox>
              <el-checkbox :label="3">划线价格</el-checkbox>
              <el-checkbox v-show="form.colNum === 1" :label="4"> 商品卖点 </el-checkbox>
              <el-checkbox v-show="form.colNum === 1" :label="5"> 商品销量 </el-checkbox>
              <el-checkbox v-show="form.colNum !== 3" :label="6"> 商品品牌 </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <SaleGoodsList
      v-if="goods_show"
      :show-sku="false"
      :is-show="goods_show"
      @cancel="goods_show = false"
      @confirm="selGoods"
    />
    <LinkSel
      v-if="link_show"
      :is-show="link_show"
      :shop-id="shopId"
      @confirm="linkConfirm"
      @cancel="link_show = false"
    />
  </div>
</template>

<script>
import LinkSel from "../components/LinkSel.vue";

import GoodsCategory from "@/component/common/GoodsCategory.vue";
import SaleGoodsList from "@/component/goods/SaleGoodsList";
export default {
  name: "GoodsForm",
  components: {
    LinkSel,
    GoodsCategory,
    SaleGoodsList,
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    shopId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      form: {},
      tab_index: 0,
      now_tab: {},
      goods_show: false,
      link_show: false,
      predefineColors: [
        "#ffffff",
        "#000000",
        "#f7f8fa",
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "#c7158577",
      ],
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    titBgChange(val) {
      this.form.titleBgColor2 = this.$_common.colorRgba(val, 0.7);
      this.confirm();
    },
    titleStyleChange(val) {
      this.confirm();
    },
    openUrl() {
      this.link_show = true;
    },
    linkConfirm(obj) {
      this.form.title_url = obj.links;
      this.form.switchTab = obj.switchTab ? "switchTab" : "";
      this.confirm();
    },
    confirm() {
      this.$emit("confirm", this.form);
    },
    // 商品来源切换
    fromChange(val) {
      if (this.form.navStyle === 2) {
        // 导航风格 选项卡
        this.form.tabList[this.tab_index].categoryPath = [];
        this.form.tabList[this.tab_index].goods_ids = [];
        this.form.tabList[this.tab_index].goods_list = [
          {
            images: "",
            title: "商品名称",
            id: "001",
            skuId: "002",
          },
          {
            images: "",
            title: "商品名称",
            id: "003",
            skuId: "004",
          },
        ];
      } else {
        // 其他
        this.form.categoryPath = [];
        this.form.goods_ids = [];
        this.form.goods_list = [
          {
            images: "",
            title: "商品名称",
            id: "001",
            skuId: "002",
          },
          {
            images: "",
            title: "商品名称",
            id: "003",
            skuId: "004",
          },
        ];
      }
      this.confirm();
    },
    categoryChange() {
      this.confirm();
    },
    addTab() {
      this.form.tabList.push({
        tabName: "选项卡名称",
        goodsFrom: 1,
        categoryPath: [],
        goods_list: [],
      });
    },
    selTab(index) {
      this.tab_index = index;
    },
    // 导航风格切换
    styleChange() {
      this.form.title = "";
      this.form.goodsFrom = 1;
      this.form.categoryPath = [];
      this.form.goods_ids = [];
      this.form.goods_list = [
        {
          images: "",
          title: "商品名称",
          id: "001",
          skuId: "002",
        },
        {
          images: "",
          title: "商品名称",
          id: "003",
          skuId: "004",
        },
      ];
      this.form.tabList = [
        {
          goods_ids: [],
          tabName: "选项卡名称",
          goodsFrom: 1,
          categoryPath: [],
          goods_list: [
            {
              images: "",
              title: "商品名称",
              id: "001",
              skuId: "002",
            },
            {
              images: "",
              title: "商品名称",
              id: "003",
              skuId: "004",
            },
          ],
          goodsNum: 6,
          goodsSort: 1,
        },
      ];
      this.confirm();
    },
    // 选择商品确定
    selGoods(goods) {
      console.log(goods);
      const goodsData = goods;
      if (this.form.navStyle === 2) {
        // 导航风格 选项卡
        if (this.form.tabList[this.tab_index].goods_ids && this.form.tabList[this.tab_index].goods_ids.length) {
          this.form.tabList[this.tab_index].goods_list = this.$_common.unique(
            this.form.tabList[this.tab_index].goods_list.concat(goodsData),
            ["id", "skuId"]
          );
        } else {
          this.form.tabList[this.tab_index].goods_list = goodsData;
        }
        this.form.tabList[this.tab_index].goods_ids = this.form.tabList[this.tab_index].goods_list.map((item) => {
          return item.id;
        });
      } else {
        // 其他
        if (this.form.goods_ids.length) {
          this.form.goods_list = this.$_common.unique(this.form.goods_list.concat(goodsData), ["id", "skuId"]);
        } else {
          this.form.goods_list = goodsData;
        }
        this.form.goods_ids = this.form.goods_list.map((item) => {
          return item.id;
        });
      }
      this.confirm();
    },
    delGoods(index) {
      if (this.form.navStyle === 2) {
        if (this.form.tabList[this.tab_index].goods_list.length === 1) {
          this.$message.warning("至少保留一个商品");
          return;
        }
        this.form.tabList[this.tab_index].goods_list.splice(index, 1);
      } else {
        if (this.form.goods_list.length === 1) {
          this.$message.warning("至少保留一个商品");
          return;
        }
        this.form.goods_list.splice(index, 1);
      }
      this.form.goods_ids = this.form.goods_list.map((item) => {
        return item.id;
      });
    },
    delTab(index) {
      if (this.form.tabList.length === 1) {
        this.$message.warning("至少保留一个选项");
        return;
      }
      this.form.tabList.splice(index, 1);
    },
  },
};
</script>

<style scoped lang="scss">
.tab-ul {
  font-size: 14px;
  color: #666666;
  line-height: 30px;
  width: calc(100% - 100px);
  margin: 10px auto;
  /*border: 1px solid #dddddd;*/
}
.tag-li {
  border: 1px solid #dddddd;
  padding: 0 30px;
  cursor: pointer;
}
.tag-li:last-child {
  border-bottom: 0 none;
}
.tag-li .el-icon-delete {
  color: #ff4040;
  font-size: 16px;
}
.goods-div {
  border: 1px solid #eee;
  padding-top: 10px;
  margin-bottom: 10px;
}
.goods-img {
  width: 60px;
  height: 60px;
}

.goods-li {
  margin-left: 10px;
  margin-bottom: 10px;
  position: relative;
  width: 62px;
  height: 62px;
  border: 1px solid #eee;
}
.goods-li .icon-del {
  position: absolute;
  right: -5px;
  top: -10px;
  color: #ff4040;
  cursor: pointer;
}
.url-div {
  background: #fff;
  font-size: 12px;
  color: #666;
  line-height: 32px;
  padding: 0 10px;
  cursor: pointer;
  border: 1px solid #ddd;
}
.icon-xinzeng1 {
  color: #fd463e;
}
.icon-gouwuche1 {
  font-size: 20px;
  border-radius: 100%;
  text-align: center;
  color: #fd463e;
}
.color-ul {
  display: flex;
  li {
    flex: 3;
    p {
      color: #666666;
    }
  }
}
</style>
