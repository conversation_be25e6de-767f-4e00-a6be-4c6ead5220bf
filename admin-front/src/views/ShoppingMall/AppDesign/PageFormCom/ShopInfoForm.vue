<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">辅助线</span>
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="背景颜色" class="PD-form-item">
          <el-color-picker
            v-model="form.bgColor"
            show-alpha
            :predefine="predefineColors"
            @change="confirm"
          ></el-color-picker>
        </el-form-item>
        <el-form-item label="文字颜色" class="PD-form-item">
          <el-color-picker v-model="form.color" :predefine="predefineColors" @change="confirm"></el-color-picker>
        </el-form-item>
        <el-form-item label="logo" class="PD-form-item">
          <div>
            <UploadQiniu
              :width="50"
              is-btn="picture"
              :file-list="form.image.url ? [form.image] : []"
              @uploadSuccess="uploadSuccess"
            >
              <div slot="upbtn">
                <el-button size="small" type="text">点击上传</el-button>
              </div>
            </UploadQiniu>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu";
export default {
  name: "LineForm",
  components: {
    UploadQiniu,
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {},
      predefineColors: [
        "#ffffff",
        "#000000",
        "#f7f8fa",
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "#c7158577",
      ],
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
    uploadSuccess(val, res, file, fileList) {
      this.form.image = {
        name: file.name,
        url: val,
      };
      this.confirm();
    },
  },
};
</script>

<style scoped></style>
