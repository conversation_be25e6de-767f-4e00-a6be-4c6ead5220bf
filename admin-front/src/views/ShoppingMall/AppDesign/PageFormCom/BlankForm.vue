<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">辅助空白</span>
      <!--      <el-button-->
      <!--        class="float_right"-->
      <!--        type="primary"-->
      <!--        size="mini"-->
      <!--        @click="confirm"-->
      <!--      >-->
      <!--        完成-->
      <!--      </el-button>-->
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="背景颜色" class="PD-form-item">
          <el-color-picker
            v-model="form.backgroundColor"
            show-alpha
            :predefine="predefineColors"
            @change="confirm"
          ></el-color-picker>
        </el-form-item>
        <el-form-item label="高度" class="PD-form-item">
          <el-slider v-model="form.height" show-input @change="confirm"></el-slider>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "BlankForm",
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {},
      predefineColors: [
        "#ffffff",
        "#000000",
        "#f7f8fa",
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "#c7158577",
      ],
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
  },
};
</script>

<style scoped></style>
