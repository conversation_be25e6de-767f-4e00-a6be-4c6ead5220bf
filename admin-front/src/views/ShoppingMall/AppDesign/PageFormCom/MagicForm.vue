<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">图片魔方</span>
      <!--<el-button
        class="float_right"
        type="primary"
        size="mini"
        @click="confirm"
      >
        完成
      </el-button>-->
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="布局风格" class="PD-form-item">
          <el-radio-group v-model="form.imgStyle" @change="styleChange">
            <el-radio :label="1" style="padding-bottom: 10px">单列</el-radio>
            <el-radio :label="2">两列</el-radio>
            <el-radio :label="3">三列</el-radio>
            <el-radio :label="4">四列</el-radio>
            <el-radio :label="5">魔方</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="左右间距" class="PD-form-item">
          <el-slider v-model="form.paddingLR" :max="30" @change="confirm"></el-slider>
        </el-form-item>
        <el-form-item label="上下间距" class="PD-form-item">
          <el-slider v-model="form.paddingTB" :max="30" @change="confirm"></el-slider>
        </el-form-item>
        <el-form-item label="图片" class="PD-form-item">
          <p v-if="form.imgStyle === 1" class="input-tip">
            建议图片尺寸
            <span class="px-span">宽度720，高度200px-950px</span>
          </p>
          <p v-else-if="form.imgStyle === 2" class="input-tip">
            建议图片尺寸
            <span class="px-span">348px*172px</span>
            ，图片高度须完全一致
          </p>
          <p v-else-if="form.imgStyle === 3" class="input-tip">
            建议图片尺寸
            <span class="px-span">238px*172px</span>
            ，图片高度须完全一致
          </p>
          <p v-else-if="form.imgStyle === 4" class="input-tip">
            建议图片尺寸
            <span class="px-span">178px*172px</span>
            ，图片高度须完全一致
          </p>
          <div v-else-if="form.imgStyle === 5">
            <p v-if="form.imagesList.length === 1" class="input-tip">
              建议图片尺寸
              <span class="px-span">宽度720，高度200px-950px</span>
            </p>
            <img v-if="form.imagesList.length === 2" class="cube-img" :src="require('@/assets/img/cube_two.png')" />
            <img v-if="form.imagesList.length === 3" class="cube-img" :src="require('@/assets/img/cube_three.png')" />
            <img v-if="form.imagesList.length === 4" class="cube-img" :src="require('@/assets/img/cube_four.png')" />
          </div>

          <ul class="up-img-ul">
            <li v-for="(item, index) in form.imagesList" :key="index" class="clearfix up-img-li">
              <div>
                <UploadQiniu
                  :width="50"
                  is-btn="picture"
                  :file-list="item.image.url ? [item.image] : []"
                  @uploadSuccess="uploadSuccess"
                  @beforeUpload="beforeUpload(index)"
                >
                  <div slot="upbtn">
                    <el-button size="small" type="text">点击上传</el-button>
                    <!--                                  <i class="el-icon-plus"></i>-->
                  </div>
                </UploadQiniu>
              </div>
              <div class="up-url" @click="openUrl(index)">
                <i class="el-icon-link"></i>
                <span>{{ item.url || "请选择链接" }}</span>
              </div>
              <div v-if="form.imagesList.length > 1" class="img-del-icon" @click="delImg(index)">
                <i class="el-icon-close"></i>
              </div>
            </li>
          </ul>
          <div style="padding-top: 10px">
            <el-button
              style="width: 100%; border: 1px dashed #999"
              size="small"
              icon="el-icon-plus"
              :disabled="form.imagesList.length === (form.imgStyle === 5 ? 4 : 12)"
              @click="addImg"
            >
              {{ form.imagesList.length }}/{{ form.imgStyle === 5 ? "4" : "12" }}
              添加一个
            </el-button>
          </div>
        </el-form-item>
        <!--        <el-form-item label="图片间隙："></el-form-item>-->
      </el-form>
    </div>
    <LinkSel
      v-if="link_show"
      :is-show="link_show"
      :shop-id="shopId"
      @confirm="linkConfirm"
      @cancel="link_show = false"
    />
  </div>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu";
import LinkSel from "../components/LinkSel.vue";

export default {
  name: "MagicForm",
  components: {
    UploadQiniu,
    LinkSel,
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    shopId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      link_show: false,
      img_index: 0,
      form: {},
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    styleChange(val) {
      if (val === 5 && this.form.imagesList.length > 4) {
        this.form.imagesList.splice(4);
      }
      this.confirm();
    },
    confirm() {
      this.$emit("confirm", this.form);
    },
    delImg(index) {
      this.form.imagesList.splice(index, 1);
      this.confirm();
    },
    uploadSuccess(val, res, file, fileList) {
      this.form.imagesList[this.img_index].image = {
        name: file.name,
        url: val,
      };
      this.confirm();
    },
    beforeUpload(index) {
      this.img_index = index;
    },
    linkConfirm(obj) {
      this.form.imagesList[this.img_index].url = obj.links;
      this.form.imagesList[this.img_index].switchTab = obj.switchTab ? "switchTab" : "";
      this.confirm();
    },
    addImg() {
      this.form.imagesList.push({
        image: "",
        url: "",
        switchTab: "",
      });
      this.confirm();
    },
    openUrl(index) {
      this.img_index = index;
      this.link_show = true;
    },
  },
};
</script>

<style scoped>
.cube-img {
  width: 250px;
  margin: 0 auto;
  display: block;
}
.px-span {
  color: #fb6638;
}
</style>
