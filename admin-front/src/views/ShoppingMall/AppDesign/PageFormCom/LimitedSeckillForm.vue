<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">秒杀商品</span>
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="背景" class="PD-form-item">
          <el-color-picker v-model="form.bgColor" show-alpha :predefine="predefineColors"></el-color-picker>
        </el-form-item>
        <el-form-item label="商品数量" class="PD-form-item">
          <el-input-number v-model="form.goodsNum"></el-input-number>
        </el-form-item>
        <el-form-item label="选择秒杀" class="PD-form-item">
          <el-input v-model="form.seckill_title" style="width: 240px" readonly placeholder="请选择一组秒杀">
            <el-button slot="append" icon="el-icon-search" @click="seckill_show = true"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="显示类型" class="PD-form-item">
          <el-radio-group v-model="form.type" @change="confirm">
            <el-radio :label="1">列表平铺</el-radio>
            <el-radio :label="2" :disabled="form.colNum === 1"> 横向滑动 </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="分列数量" class="PD-form-item">
          <el-radio-group v-model="form.colNum" @change="confirm">
            <el-radio :label="2">两列</el-radio>
            <el-radio :label="3">三列</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="显示内容" class="PD-form-item">
          <el-checkbox-group v-model="form.goodsInfo" @change="confirm">
            <el-checkbox :label="1">商品名称</el-checkbox>
            <el-checkbox :label="2">秒杀价格</el-checkbox>
            <el-checkbox :label="3">商品原价</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="按钮样式" class="PD-form-item">
          <el-radio-group v-model="form.btnStyle" @change="confirm">
            <el-radio :label="1">
              <i class="iconfont icon-xinzeng1"></i>
            </el-radio>
            <el-radio :label="2">
              <i class="iconfont icon-gouwuche"></i>
            </el-radio>
          </el-radio-group>
        </el-form-item>-->
      </el-form>
    </div>
    <SeckillList
      v-if="seckill_show"
      :is-show="seckill_show"
      :is-check="false"
      @cancel="seckill_show = false"
      @confirm="seckillSel"
    />
  </div>
</template>

<script>
import SeckillList from "@/component/common/SeckillList";
export default {
  name: "LimitedSeckillForm",
  components: {
    SeckillList,
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {},
      predefineColors: [
        "#ffffff",
        "#000000",
        "#f7f8fa",
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "#c7158577",
      ],
      seckill_show: false,
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
    seckillSel(val) {
      this.form.seckill_title = val[0].title;
      this.form.seckill_id = val[0].id;
    },
  },
};
</script>

<style scoped>
.icon-xinzeng1 {
  color: #fd463e;
}
.icon-gouwuche {
  font-size: 12px;
  width: 23px;
  height: 23px;
  border-radius: 100%;
  line-height: 24px;
  text-align: center;
  color: #fff;
  background-color: #fd463e;
  display: inline-block;
}
</style>
