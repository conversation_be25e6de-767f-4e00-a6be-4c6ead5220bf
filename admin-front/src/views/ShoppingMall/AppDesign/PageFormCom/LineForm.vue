<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">辅助线</span>
      <!--<el-button
        class="float_right"
        type="primary"
        size="mini"
        @click="confirm"
      >
        完成
      </el-button>-->
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="背景颜色" class="PD-form-item">
          <el-color-picker v-model="form.bgColor" @change="confirm"></el-color-picker>
        </el-form-item>
        <el-form-item label="线条样式" class="PD-form-item">
          <el-radio-group v-model="form.borderStyle" @change="confirm">
            <el-radio label="solid">实线</el-radio>
            <el-radio label="dotted">点状</el-radio>
            <el-radio label="dashed">虚线</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="线条颜色" class="PD-form-item">
          <el-color-picker v-model="form.borderColor" @change="confirm"></el-color-picker>
        </el-form-item>
        <el-form-item label="线条高度" class="PD-form-item">
          <el-slider v-model="form.borderWidth" :max="20" @change="confirm"></el-slider>
        </el-form-item>
        <el-form-item label="上下边距" class="PD-form-item">
          <el-slider v-model="form.padding" :max="50" @change="confirm"></el-slider>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "LineForm",
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {},
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
  },
};
</script>

<style scoped></style>
