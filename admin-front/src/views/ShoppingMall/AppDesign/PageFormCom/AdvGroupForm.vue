<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">图片广告</span>
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="选择风格" class="PD-form-item">
          <div class="clearfix" style="cursor: pointer" @click="style_visible = true">
            <span class="float_left">{{ form.style.label }}</span>
            <span class="float_right">
              <i class="iconfont icon-jinru1"></i>
            </span>
          </div>
          <div class="style-size">
            <img v-if="form.style.styleId === 1" :src="require('@/assets/img/pageComponents/adv-size-01.jpg')" />
            <img v-if="form.style.styleId === 2" :src="require('@/assets/img/pageComponents/adv-size-02.jpg')" />
            <img v-if="form.style.styleId === 3" :src="require('@/assets/img/pageComponents/adv-size-03.jpg')" />
          </div>
        </el-form-item>
        <el-form-item label="内容设置" class="PD-form-item">
          <ul class="up-img-ul">
            <li v-for="(item, index) in form.adv_list" :key="index" class="clearfix up-img-li">
              <div>
                <UploadQiniu
                  is-btn="picture"
                  :file-list="item.image.url ? [item.image] : []"
                  :width="50"
                  @uploadSuccess="uploadSuccess"
                  @beforeUpload="beforeUpload(index)"
                ></UploadQiniu>
              </div>
              <div class="up-url">
                <i>标题：</i>
                <input v-model="item.title" type="text" />
              </div>
              <div class="up-url">
                <i>标题颜色：</i>
                <el-color-picker
                  v-model="item.titleColor"
                  :predefine="predefineColors"
                  @change="confirm"
                ></el-color-picker>
              </div>
              <div class="up-url">
                <i>副标题：</i>
                <input v-model="item.desc" type="text" />
              </div>
              <div class="up-url">
                <i>副标题颜色：</i>
                <el-color-picker
                  v-model="item.descColor"
                  :predefine="predefineColors"
                  @change="confirm"
                ></el-color-picker>
              </div>
              <div class="up-url">
                <i>按钮文字：</i>
                <input v-model="item.btnText" type="text" />
              </div>
              <div class="up-url" @click="openUrl(index)">
                <i>按钮指向：</i>
                <span>{{ item.url || "请选择链接" }}</span>
              </div>
            </li>
          </ul>
        </el-form-item>
      </el-form>
    </div>
    <el-dialog title="选择风格" :visible.sync="style_visible" width="60%">
      <div class="style-ul">
        <div
          v-for="(item, index) in style_list"
          :key="index"
          class="style-item"
          :class="[form.style.styleId === item.styleId ? 'style-on' : '']"
          @click="changeStyle(item)"
        >
          <div class="style-img">
            <img :src="item.img" />
          </div>
          <p>{{ item.label }}</p>
        </div>
      </div>
    </el-dialog>
    <LinkSel
      v-if="link_show"
      :is-show="link_show"
      :shop-id="shopId"
      @confirm="linkConfirm"
      @cancel="link_show = false"
    />
  </div>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu";
import LinkSel from "../components/LinkSel.vue";
const advArr = [
  {
    title: "热销榜单",
    titleColor: "#000000",
    desc: "发现爆款好商品",
    descColor: "#F05064",
    btnText: "立即抢购",
    image: {},
    url: "",
    switchTab: "",
  },
  {
    title: "当季新品",
    titleColor: "#3CB4B6",
    desc: "发现爆款好商品",
    descColor: "#F05064",
    btnText: "查看全部",
    image: {},
    url: "",
    switchTab: "",
  },
  {
    title: "优质店铺",
    titleColor: "#000000",
    desc: "发现好店",
    descColor: "#FD6F02",
    btnText: "查看全部",
    image: {},
    url: "",
    switchTab: "",
  },
  {
    title: "品质好货",
    titleColor: "#000000",
    desc: "好评如潮",
    descColor: "#19906C",
    btnText: "查看全部",
    image: {},
    url: "",
    switchTab: "",
  },
  {
    title: "爆品推荐",
    titleColor: "#000000",
    desc: "质量好销量高",
    descColor: "#19906C",
    btnText: "查看全部",
    image: {},
    url: "",
    switchTab: "",
  },
];
export default {
  name: "AdvGroupForm",
  components: {
    UploadQiniu,
    LinkSel,
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    shopId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      link_show: false,
      style_visible: false,
      form: {},
      style_list: [
        {
          label: "上三下二",
          styleId: 1,
          img: require("@/assets/img/pageComponents/adv-style-03.png"),
        },
        {
          label: "一左两右",
          styleId: 2,
          img: require("@/assets/img/pageComponents/adv-style-02.png"),
        },
        {
          label: "一左一右",
          styleId: 3,
          img: require("@/assets/img/pageComponents/adv-style-01.png"),
        },
      ],
      img_index: 0,
      predefineColors: ["#000000", "#F05064", "#FD6F02", "#ffd700", "#19906C", "#3CB4B6", "#1e90ff", "#c71585"],
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
      if (!this.form.adv_list.length) {
        if (this.form.style.styleId === 1) {
          this.form.adv_list = this.$_common.deepClone(advArr);
        }
      }
    },
  },
  created() {
    this.form = this.temForm;
    if (!this.form.adv_list.length) {
      if (this.form.style.styleId === 1) {
        this.form.adv_list = this.$_common.deepClone(advArr);
      }
    }
  },
  methods: {
    linkConfirm(obj) {
      this.form.adv_list[this.img_index].url = obj.links;
      this.form.adv_list[this.img_index].switchTab = obj.switchTab ? "switchTab" : "";
      this.confirm();
    },
    confirm() {
      this.$emit("confirm", this.form);
    },
    openUrl(index) {
      this.img_index = index;
      this.link_show = true;
    },
    changeStyle(style) {
      this.style_visible = false;
      this.form.style = {
        label: style.label,
        styleId: style.styleId,
      };
      switch (style.styleId) {
        case 1:
          this.form.adv_list = this.$_common.deepClone(advArr);
          break;
        case 2:
          this.form.adv_list = this.$_common.deepClone(advArr).slice(0, 3);
          break;
        case 3:
          this.form.adv_list = this.$_common.deepClone(advArr).slice(0, 2);
          break;
      }
    },
    uploadSuccess(val, res, file, fileList) {
      this.form.adv_list[this.img_index].image = {
        name: file.name,
        url: val,
      };
      this.confirm();
    },
    beforeUpload(index) {
      this.img_index = index;
    },
  },
};
</script>

<style scoped lang="scss">
.style-ul {
  display: flex;
  flex-wrap: wrap;
  .style-item {
    text-align: center;
    margin-right: 13px;
    margin-bottom: 15px;
    cursor: pointer;
    border: 1px solid #ededed;
    background: #f7f8fa;
    p {
      line-height: 30px;
      font-weight: 600;
    }
    .style-img {
      height: 224px;
      line-height: 224px;
      img {
        width: 280px;
        vertical-align: middle;
      }
    }
  }
  .style-on {
    border-color: #ff6a00;
  }
}
.style-size {
  width: 300px;
  border: 1px solid #eeeeee;
  margin: 10px auto;
  img {
    width: 100%;
    display: block;
  }
}
.up-url {
  margin-bottom: 5px;
  input {
    font-size: 12px;
  }
}
</style>
