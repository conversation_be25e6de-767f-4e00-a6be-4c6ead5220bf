<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">微信客服</span>
    </div>
    <div class="tem-main">
      <p class="form-tip">温馨提示：该功能仅限微信小程序商城使用</p>
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="按钮颜色" class="PD-form-item">
          <el-color-picker v-model="form.color" @change="confirm"></el-color-picker>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "WxServiceForm",
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {},
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
  },
};
</script>
