<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">导航</span>
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="背景颜色" class="PD-form-item">
          <el-color-picker
            v-model="form.bgColor"
            show-alpha
            :predefine="predefineColors"
            @change="confirm"
          ></el-color-picker>
        </el-form-item>
        <el-form-item label="文字颜色" class="PD-form-item">
          <el-color-picker v-model="form.color" @change="confirm"></el-color-picker>
        </el-form-item>
        <el-form-item label="每行数量" class="PD-form-item">
          <el-radio-group v-model="form.num" @change="confirm">
            <el-radio :label="3">3个</el-radio>
            <el-radio :label="4">4个</el-radio>
            <el-radio :label="5">5个</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="导航" class="PD-form-item">
          <p class="input-tip">建议图片尺寸44px*44px，图片高度须完全一致</p>

          <ul class="up-img-ul">
            <li v-for="(item, index) in form.navList" :key="index" class="clearfix up-img-li">
              <div>
                <UploadQiniu
                  :width="50"
                  is-btn="picture"
                  :file-list="item.image.url ? [item.image] : []"
                  @uploadSuccess="uploadSuccess"
                  @beforeUpload="beforeUpload(index)"
                >
                  <div slot="upbtn">
                    <el-button size="small" type="text">点击上传</el-button>
                    <!--                                  <i class="el-icon-plus"></i>-->
                  </div>
                </UploadQiniu>
              </div>
              <el-input v-model="item.text" size="small" placeholder="按钮文字" class="nav-input"></el-input>
              <div class="up-url" @click="openUrl(index)">
                <i class="el-icon-link"></i>
                <span>{{ item.url || "请选择链接" }}</span>
              </div>
              <div v-if="form.navList.length > 1" class="img-del-icon" @click="delImg(index)">
                <i class="el-icon-close"></i>
              </div>
            </li>
          </ul>
          <div style="padding-top: 10px">
            <el-button style="width: 100%; border: 1px dashed #999" size="small" icon="el-icon-plus" @click="addImg">
              添加一个
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <LinkSel
      v-if="link_show"
      :is-show="link_show"
      :shop-id="shopId"
      @confirm="linkConfirm"
      @cancel="link_show = false"
    />
  </div>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu";
import LinkSel from "../components/LinkSel.vue";
export default {
  name: "NavbarForm",
  components: {
    UploadQiniu,
    LinkSel,
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    shopId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      form: {},
      img_index: 0,
      link_show: false,
      predefineColors: [
        "#ffffff",
        "#000000",
        "#f7f8fa",
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "#c7158577",
      ],
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
    linkConfirm(obj) {
      this.form.navList[this.img_index].url = obj.links;
      this.form.navList[this.img_index].switchTab = obj.switchTab ? "switchTab" : "";
      this.confirm();
    },
    uploadSuccess(val, res, file, fileList) {
      this.form.navList[this.img_index].image = {
        name: file.name,
        url: val,
      };
      this.$emit("confirm", this.form);
    },
    beforeUpload(index) {
      this.img_index = index;
    },
    openUrl(index) {
      this.img_index = index;
      this.link_show = true;
    },
    delImg(index) {
      this.form.navList.splice(index, 1);
      this.confirm();
    },
    addImg() {
      this.form.navList.push({
        image: {},
        text: "",
        url: "",
        switchTab: "",
      });
      this.confirm();
    },
  },
};
</script>

<style scoped>
.nav-input {
  margin-bottom: 10px;
}
</style>
