<template>
  <div class="tem-box">
    <div class="tem-tit clearfix">
      <span class="tem-tit-span float_left">图片轮播</span>
      <!--<el-button
        class="float_right"
        type="primary"
        size="mini"
        @click="confirm"
      >
        完成
      </el-button>-->
    </div>
    <div class="tem-main">
      <el-form ref="form" :model="form" size="small" label-position="top">
        <el-form-item label="轮播图风格" class="PD-form-item">
          <el-radio-group v-model="form.style">
            <el-radio :label="1">标准</el-radio>
            <el-radio :label="2">卡牌</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="指示点显示" class="PD-form-item">
          <el-switch
            v-model="form.indicatorDots"
            active-color="#36B365"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
            @change="confirm"
          ></el-switch>
        </el-form-item>
        <el-form-item label="指示点颜色" class="PD-form-item">
          <el-color-picker v-model="form.indicatorActiveColor" @change="colorChange"></el-color-picker>
        </el-form-item>
        <el-form-item label="自动切换" class="PD-form-item">
          <el-switch
            v-model="form.autoplay"
            active-color="#36B365"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
            @change="confirm"
          ></el-switch>
        </el-form-item>
        <el-form-item label="切换时间" class="PD-form-item">
          <el-input-number v-model="form.interval" :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item label="轮播图片" class="PD-form-item">
          <p v-if="form.style === 1" class="input-tip">建议图片尺寸750px*420px，图片高度须完全一致</p>
          <p v-else class="input-tip">建议图片尺寸634px*302px，图片高度须完全一致</p>

          <ul class="up-img-ul">
            <li v-for="(item, index) in form.bannerList" :key="index" class="clearfix up-img-li">
              <div>
                <UploadQiniu
                  is-btn="picture"
                  :file-list="item.image.url ? [item.image] : []"
                  :width="50"
                  @uploadSuccess="uploadSuccess"
                  @beforeUpload="beforeUpload(index)"
                ></UploadQiniu>
              </div>
              <div class="up-url" @click="openUrl(index)">
                <i class="el-icon-link"></i>
                <span>{{ item.url || "请选择链接" }}</span>
              </div>
              <div v-if="form.bannerList.length > 1" class="img-del-icon" @click="delImg(index)">
                <i class="el-icon-close"></i>
              </div>
            </li>
          </ul>
          <div style="padding-top: 10px">
            <el-button
              style="width: 100%; border: 1px dashed #999"
              size="small"
              icon="el-icon-plus"
              :disabled="form.bannerList.length === 10"
              @click="addImg"
            >
              {{ form.bannerList.length }}/10 添加一个
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <LinkSel
      v-if="link_show"
      :is-show="link_show"
      :shop-id="shopId"
      @confirm="linkConfirm"
      @cancel="link_show = false"
    />
  </div>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu";
import LinkSel from "../components/LinkSel.vue";

export default {
  name: "SwiperForm",
  components: {
    UploadQiniu,
    LinkSel,
  },
  props: {
    temForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    shopId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      file_list: [],
      link_show: false,
      img_index: 0,
      form: {},
    };
  },
  watch: {
    temForm(val) {
      this.form = val;
    },
  },
  created() {
    this.form = this.temForm;
  },
  methods: {
    confirm() {
      this.$emit("confirm", this.form);
    },
    colorChange(val) {
      this.form.indicatorColor = this.$_common.colorRgba(val, 0.4);
      this.confirm();
    },
    addImg() {
      this.form.bannerList.push({
        image: {},
        url: "",
        switchTab: "",
      });
      this.confirm();
    },
    delImg(index) {
      this.form.bannerList.splice(index, 1);
      this.confirm();
    },
    uploadSuccess(val, res, file, fileList) {
      this.form.bannerList[this.img_index].image = {
        name: file.name,
        url: val,
      };
      this.form.bannerList[this.img_index].img = val;
      this.confirm();
    },
    beforeUpload(index) {
      this.img_index = index;
    },
    linkConfirm(obj) {
      this.form.bannerList[this.img_index].url = obj.links;
      this.form.bannerList[this.img_index].switchTab = obj.switchTab ? "switchTab" : "";
      this.confirm();
    },
    openUrl(index) {
      this.img_index = index;
      this.link_show = true;
    },
  },
};
</script>
