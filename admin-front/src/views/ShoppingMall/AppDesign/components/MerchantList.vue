<template>
  <div>
    <el-table border :data="merchant_list" size="small">
      <el-table-column prop="id" width="70" label="ID"></el-table-column>
      <el-table-column prop="name" label="商户名称"></el-table-column>
      <el-table-column label="选择" align="center">
        <template slot-scope="scope">
          <el-button size="mini" icon="el-icon-check" @click="selStore(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </div>
</template>

<script>
import { getAllMerchant } from "@/api/Merchants";
export default {
  name: "MerchantList",
  data() {
    return {
      merchant_list: [],
      pageSize: 1,
      total: 0,
      page: 1,
      search_form: {
        search: "",
        status: 1,
        service: 1,
        start: "",
        end: "",
        delStart: "",
        delEnd: "",
      },
    };
  },
  mounted() {
    this.getAllMerchant();
  },
  methods: {
    async getAllMerchant() {
      const data = await getAllMerchant({
        auditStatus: 2,
        search: this.search_form.search,
        page: this.page,
        pageSize: this.pageSize,
        starAuditTime: this.search_form.start,
        endAuditTime: this.search_form.end,
        starExpireTime: this.search_form.delStart,
        endExpireTime: this.search_form.delEnd,
      });
      this.merchant_list = data.data;
      this.total = data.pageTotal;
    },
    selStore(row) {
      this.$emit("change", row);
    },
    pageChange(page) {
      this.page = page;
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
