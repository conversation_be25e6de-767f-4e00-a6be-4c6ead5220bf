<template>
  <el-dialog
    title="链接选择"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible="isShow"
    width="60%"
    @close="cancel"
  >
    <div style="min-height: 400px">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="商城模块" name="first">
          <div class="pageul-box">
            <h3>商城页面</h3>
            <ul class="clearfix">
              <li v-for="(item, index) in link_list" :key="index" class="float_left item-li" @click="selLink(item)">
                {{ item.name }}
              </li>
            </ul>
            <el-button style="position: absolute; bottom: 0; right: 10px" @click="clear"> 清空 </el-button>
          </div>
          <div v-if="special_list.length > 0" class="pageul-box">
            <h3>专题模块</h3>
            <ul class="clearfix">
              <li v-for="(item, index) in special_list" :key="index" class="float_left item-li" @click="selLink(item)">
                {{ item.name }}
              </li>
            </ul>
          </div>
        </el-tab-pane>
        <el-tab-pane label="商品" name="second">
          <div class="clearfix" style="padding-bottom: 10px">
            <el-input v-model="keyword" style="width: 240px" size="mini" placeholder="请输入商品名称">
              <el-button slot="append" @click="searchGoods">
                <i class="el-icon-search"></i>
              </el-button>
            </el-input>
          </div>
          <el-table ref="goods_list" border :data="goods_list" size="small">
            <el-table-column prop="title" label="商品名称" align="center"></el-table-column>
            <el-table-column label="商品图片" align="center">
              <template slot-scope="scope">
                <el-image fit="contain" style="width: 60px; height: 60px" :src="scope.row.images[0]">
                  <div
                    slot="error"
                    style="
                      height: 100%;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      box-sizing: border-box;
                      border: 1px solid #eee;
                    "
                  >
                    暂无图片
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column prop="code" label="商品编码" align="center"></el-table-column>
            <el-table-column prop="categoryName" label="商品分类" align="center"></el-table-column>
            <el-table-column prop="inventorTotal" label="总库存" align="center">
              <template slot-scope="scope">
                {{ scope.row.inventorTotal - 0 }}
              </template>
            </el-table-column>
            <el-table-column label="选择商品" align="center">
              <template slot-scope="scope">
                <el-button size="mini" icon="el-icon-check" @click="selGoods(scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>
          <FooterPage
            :page-size="pre_page"
            :total-page.sync="total"
            :current-page.sync="page"
            @pageChange="pageChange"
            @sizeChange="sizeChange"
          ></FooterPage>
        </el-tab-pane>
        <el-tab-pane label="分类" name="third">
          <GoodsCategory
            v-model="category"
            width="600"
            is-type="panel"
            :check-strictly="true"
            clearables
            @change="categoryChange"
          />
        </el-tab-pane>
        <el-tab-pane v-if="systemType === 1" label="商户列表" name="fourth">
          <MerchantList @change="selStore"></MerchantList>
        </el-tab-pane>
        <el-tab-pane v-if="systemType === 1" label="商品分组" name="fiveth">
          <ul class="clearfix">
            <li v-for="(item, index) in goods_groups" :key="index" class="float_left item-li" @click="selGroup(item)">
              {{ item.name }}
            </li>
          </ul>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script>
import { getSpecial } from "@/api/System";
import { getAllGoods, getAllGoodsGroups, searchGood } from "@/api/goods";
import GoodsCategory from "@/component/common/GoodsCategory.vue";
import { mapGetters } from "vuex";
import MerchantList from "./MerchantList.vue";
export default {
  name: "LinkSel",
  components: {
    GoodsCategory,
    MerchantList,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    shopId: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      goods_list: [],
      link_list: [
        {
          links: "/pages/index/index",
          switchTab: true,
          name: "首页",
        },
        {
          links: "/pages/cart/cart",
          switchTab: true,
          name: "购物车",
        },
        {
          links: "/pages/classification/cate",
          switchTab: true,
          name: "分类列表",
        },
        {
          links: "/pagesT/user/GetCoupon",
          switchTab: false,
          name: "领取优惠券",
        },
        {
          links: "/pagesT/user/DiscountCoupon",
          switchTab: false,
          name: "我的优惠券",
        },
        {
          links: "/pagesT/seckill/Seckill",
          switchTab: false,
          name: "秒杀抢购",
        },
        {
          links: "/pagesT/order/order",
          switchTab: false,
          name: "我的订单",
        },
        {
          links: "/pagesT/user/VipList",
          switchTab: false,
          name: "会员卡中心",
        },
        {
          links: "/pagesT/user/Collection",
          switchTab: false,
          name: "常购清单",
        },
        {
          links: "/pagesT/pointsMall/index",
          switchTab: false,
          name: "积分商城",
        },
        {
          links: "/pagesT/money/Balance",
          switchTab: false,
          name: "我的余额",
        },
      ],
      special_list: [],
      category: [],
      activeName: "first",
      keyword: "",
      total: 0,
      pre_page: 10,
      page: 1,
      pageLayout: "total, prev, pager, next",
      goods_groups: [],
    };
  },
  computed: {
    ...mapGetters({
      systemType: "MUser/systemType",
    }),
  },
  created() {
    this.getList();
    this.getSpecial();
    this.getAllGoodsGroups();
  },
  mounted() {
    if (this.systemType === 1) {
      this.link_list.push({
        links: "/pagesT/store/Apply",
        switchTab: false,
        name: "商户入驻",
      });
      this.link_list.push({
        links: "/pagesT/store/StoreHome",
        switchTab: false,
        name: "商户列表",
      });
    }
  },
  methods: {
    // 获取商品服务
    async getAllGoodsGroups() {
      const res = await getAllGoodsGroups({
        page: 1,
        pageSize: 100,
      });
      if (Array.isArray(res.data)) {
        this.goods_groups = res.data;
      } else {
        this.goods_groups = [];
      }
    },
    // 获取专题活动
    async getSpecial() {
      const { data } = await getSpecial({
        shopId: this.shopId,
      });

      this.special_list = data.map((item) => {
        return {
          ...item,
          links: "/pagesT/activity/Activity?id=" + item.id,
          switchTab: false,
          name: item.pageName,
        };
      });
    },
    selLink(item) {
      this.cancel();
      this.$emit("confirm", item);
    },
    selGroup(item) {
      this.cancel();
      this.$emit("confirm", {
        ...item,
        links: `/pagesT/productDetail/groupList?id=${item.id}&name=${item.name}`,
        switchTab: false,
        name: "商品分组",
      });
    },
    skuChange(index) {
      const target = this.$_common.deepClone(this.goods_list);
      const skuItem = target[index].skuData.find((item) => item.skuId === target[index].skuId);
      target[index] = {
        ...target[index],
        salePrice: skuItem.salePrice,
        unitName: skuItem.unitName,
        inventory: skuItem.inventory,
      };
      this.goods_list = target;
    },
    //  搜索
    async searchGoods() {
      const { data, pageTotal } = await searchGood({
        keyword: this.keyword,
        page: this.page,
        pageSize: this.pre_page,
      });

      this.total = pageTotal;
      this.goods_list = data;
      this.total = pageTotal;
    },
    // 获取列表数据
    async getList() {
      const { data, pageTotal } = await getAllGoods({
        page: this.page,
        pageSize: this.pre_page,
      });

      this.goods_list = data;
      this.total = pageTotal;
    },
    pageChange(page) {
      this.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.pre_page = size;
      this.pageChange(1);
    },
    selGoods(row) {
      this.cancel();
      this.$emit("confirm", {
        ...row,
        links: `/pagesT/product/product?id=${row.id}`,
        switchTab: false,
        name: "商品详情",
      });
    },
    selStore(row) {
      this.cancel();
      this.$emit("confirm", {
        ...row,
        links: `/pagesT/store/StoreHome?id=${row.id}&shopName=${row.name}`,
        switchTab: false,
        name: "商户主页",
      });
    },
    categoryChange(val, cateArr) {
      const id = val[val.length - 1];
      const name = cateArr[cateArr.length - 1].title;
      this.$emit("confirm", {
        links: `/pagesT/productDetail/productDetail?id=${id}&name=${name}`,
        switchTab: false,
        name: "分类列表",
      });
      this.cancel();
    },
    cancel() {
      this.$emit("cancel");
    },
    clear() {
      this.cancel();
      this.$emit("confirm", "");
    },
  },
};
</script>

<style scoped>
.item-li {
  font-size: 12px;
  color: #666;
  border: 1px solid #eee;
  padding: 5px 10px;
  cursor: pointer;
  margin-right: 10px;
}
.pageul-box {
  padding-bottom: 30px;
  height: 300px;
}
.pageul-box h3 {
  padding-bottom: 10px;
}
</style>
