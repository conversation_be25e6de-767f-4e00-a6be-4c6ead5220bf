<template>
  <Container>
    <div slot="left">
      <el-button
        v-if="$accessCheck($Access.PageSetSave)"
        size="small"
        type="primary"
        @click="$router.push('/ShoppingMall/AppDesign/PageDesign')"
      >
        新增模版
      </el-button>
    </div>
    <el-table :data="tableData">
      <el-table-column prop="pageName" label="模版名称"></el-table-column>
      <el-table-column prop="pageType" label="页面类型">
        <template slot-scope="scope">
          {{ scope.row.pageType === 1 ? "首页" : scope.row.pageType === 2 ? "专题活动" : "" }}
        </template>
      </el-table-column>
      <el-table-column prop="shopName" label="所属商铺"></el-table-column>
      <el-table-column prop="enableStatus" label="状态">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.PageSetUpdateEnableStatus)"
            v-model="scope.row.enableStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="updatePage($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left">
        <template slot-scope="scope">
          <el-button v-if="$accessCheck($Access.PageSetSave)" type="text" @click="editData(scope.row.id)">
            编辑
          </el-button>
          <el-button v-if="$accessCheck($Access.PageSetDel)" type="text" @click="delData(scope.row.id)">
            删除
          </el-button>
          <el-button v-else-if="$accessCheck($Access.PageSetGetPageInfo)" type="text" @click="editData(scope.row.id)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </Container>
</template>

<script>
import { PageDel, PageGetAll, PageUpdateEnableStatus } from "@/api/System";

export default {
  name: "PageDesignList",
  data() {
    return {
      page: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
    };
  },
  created() {
    this.getList();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getList();
  },
  methods: {
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    pageChange(page) {
      this.page = page;
      this.getList();
    },
    async getList() {
      const data = await PageGetAll({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.tableData = data.data;
      this.total = data.pageTotal;
    },
    editData(id) {
      this.$router.push("/ShoppingMall/AppDesign/PageDesignEdit/" + id);
    },
    delData(id) {
      this.$confirm("确定删除该模版吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await PageDel(id);

        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.getList();
      });
    },
    async updatePage(val, row) {
      try {
        const data = await PageUpdateEnableStatus({
          pageType: 1,
          id: row.id,
          enableStatus: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.getList();
      } catch (e) {
        this.getList();
      }
    },
  },
};
</script>

<style scoped>
.template-list {
  padding: 50px;
}
.flex-box {
  height: 470px;
  width: 270px;
  margin-left: 32px;
  margin-bottom: 32px;
  padding: 0 12px 12px;
}
.add-template {
  border: 1px dashed #fb6638;
  border-radius: 4px;
  background-color: #fff4f0;
  text-align: center;
  cursor: pointer;
}
.add-template p {
  display: inline-block;
  line-height: 470px;
}
.add-template p .icon-plus {
  color: #fb6638;
  font-weight: bold;
}
</style>
