<!--启动页-->
<template>
  <ContainerTit>
    <Container></Container>
    <div slot="headr">
      <el-button v-if="$accessCheck($Access.StartPageSetting)" type="primary" :loading="loading" @click="setSubmit">
        提交保存
      </el-button>
    </div>
    <el-row :gutter="20">
      <!--              style="background-color: #f2f4f7; height: calc(100vh - 138px)"
-->
      <el-col :span="12">
        <el-card shadow="never">
          <div class="div-phone">
            <img
              class="start-show-img"
              :src="basicData.startUpPage.img || require('@/assets/img/replace-img.png')"
              alt=""
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="never">
          <div class="form-div">
            <el-form label-width="100px">
              <el-form-item label="是否启用：">
                <el-switch
                  v-model="basicData.startUpPage.isEnable"
                  active-color="#36B365"
                  inactive-color="#ff4949"
                  active-text="启用"
                  inactive-text="禁用"
                ></el-switch>
              </el-form-item>
              <el-form-item label="定时关闭：">
                <el-radio-group v-model="basicData.startUpPage.time">
                  <el-radio :label="3">3秒</el-radio>
                  <el-radio :label="4">4秒</el-radio>
                  <el-radio :label="5">5秒</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="启动页图片：">
                <UploadQiniu :file-list="img_list" @uploadSuccess="uploadSuccess" @handleRemove="uploadRemove" />
                <p class="form-tip">建议图片尺寸：750px*1334px</p>
              </el-form-item>
              <el-form-item label="链接：">
                <el-input v-model="basicData.startUpPage.url" style="width: 400px" placeholder="请选择链接" readonly>
                  <el-button slot="append" icon="el-icon-search" @click="link_show = true"></el-button>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <LinkSel
      v-if="link_show"
      :is-show="link_show"
      :shop-id="shopId"
      @confirm="linkConfirm"
      @cancel="link_show = false"
    />
  </ContainerTit>
</template>

<script>
import { getBasicSetup, setting } from "@/api/System";
import { mapActions } from "vuex";
import UploadQiniu from "../../../component/common/UploadQiniu";
import LinkSel from "./components/LinkSel.vue";

export default {
  name: "StartUpPage",
  components: {
    UploadQiniu,
    LinkSel,
  },
  props: {
    shopId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      loading: false,
      link_show: false,
      img_list: [],
      basicData: {
        goodsNum: "", // 库存警告
        images: [],
        stockDisplay: 4,
        goodsPrice: 5,
        allowReturnDay: 0, // 设置天数
        personnelReview: 4,
        shop: "",
        description: "",
        phone: "",
        company: "",
        region: [],
        address: "",
        shelfLifeSetUp: 4,
        recommend: [],
        wxPay: 4,
        aliPay: 4,
        cashPay: 4,
        themeStyle: {},
        startUpPage: {
          isEnable: true,
          time: 5,
          img: "",
          url: "",
          switchTab: "",
        },
      },
    };
  },
  async created() {
    this.getBasicSetup();
  },
  methods: {
    ...mapActions({
      changeShelfLifeSetUp: "MUser/changeShelfLifeSetUp",
    }),
    linkConfirm(obj) {
      if (obj.links.indexOf("?") === -1 && !obj.switchTab) {
        this.basicData.startUpPage.url = obj.links + "?pageName=startUp";
      } else if (!obj.switchTab) {
        this.basicData.startUpPage.url = obj.links + "&pageName=startUp";
      } else {
        this.basicData.startUpPage.url = obj.links;
      }

      this.basicData.startUpPage.switchTab = obj.switchTab ? "switchTab" : "";
    },
    uploadSuccess(url, list) {
      this.basicData.startUpPage.img = url;
    },
    uploadRemove() {
      this.basicData.startUpPage.img = "";
    },
    changeStyle(row) {
      this.basicData.themeStyle = row;
      this.img_list = row.img;
    },
    //  提交
    async setSubmit() {
      const data = await setting({
        basicData: this.basicData,
      });

      this.$message({
        message: "提交成功",
        type: "success",
      });
      this.changeShelfLifeSetUp(this.basicData.shelfLifeSetUp);
      this.getBasicSetup();
    },
    //  获取详情
    async getBasicSetup() {
      const { data } = await getBasicSetup();

      this.basicData = { ...this.basicData, ...data.basicData };
      if (data.basicData.startUpPage) {
        if (data.basicData.startUpPage.img) {
          this.img_list = [
            {
              name: "",
              url: data.basicData.startUpPage.img,
            },
          ];
        }
      }
    },
  },
};
</script>

<style scoped>
.div-phone {
  width: 401px;
  height: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fafafa;
  padding: 20px 12px;
  margin: 0 auto;
}
.start-show-img {
  height: 650px;
  width: 375px;
  border: 1px solid #ddd;
  border-radius: 4px;
  object-fit: cover;
}
</style>
