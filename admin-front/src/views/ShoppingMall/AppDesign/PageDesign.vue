<template>
  <ContainerTit>
    <div slot="headr">
      <el-button v-if="$accessCheck($Access.PageSetSave)" type="primary" @click="submit"> 保存页面 </el-button>
    </div>
    <div class="page-div">
      <div style="padding-bottom: 10px">
        <el-form :inline="true" size="small">
          <el-form-item style="margin-bottom: 0">
            <span class="f-label">页面名称：</span>
            <el-input v-model="pageName" style="width: 194px" placeholder="请输入页面名称"></el-input>
          </el-form-item>
          <el-form-item style="margin-bottom: 0">
            <span class="f-label">页面类型：</span>
            <el-select v-model="pageType" placeholder="请选择页面类型">
              <el-option label="首页" :value="1"></el-option>
              <el-option label="专题活动" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="systemType === 3 || systemType === 1" style="margin-bottom: 0">
            <span class="f-label">店铺：</span>
            <SelectShop
              v-model="shopId"
              width="150"
              :disabled="!!$route.params.id"
              placeholder="选择店铺"
              @change="shopConfirm"
              @clear="delShop"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="clearfix tem-out">
        <!--    组件库-->
        <div class="tem-view com-view">
          <div v-for="(item, index) in componentsList" :key="index" class="com-box">
            <div class="com-tit">
              {{ item.title }}
            </div>
            <ul class="com-ul clearfix">
              <li v-for="(itemC, indexC) in item.components" :key="indexC">
                <div
                  v-if="itemC.comName !== 'NavigationBar'"
                  class="com-li"
                  :style="{ backgroundImage: `url(${itemC.iconImg})` }"
                  @click="selComponent(itemC)"
                >
                  <p>{{ itemC.name }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <!--页面展示-->
        <div class="tem-view page-view">
          <div class="page-show-box">
            <!--            浮动按钮-->
            <div class="page-show" :style="pageStyle">
              <div
                v-if="modelList.findIndex((item) => item.comName === 'FloatBth') > -1"
                class="float-bth"
                :class="[tem_tag === '浮动按钮' ? 'float-bth-on' : '']"
                @click="
                  temChange(
                    '浮动按钮',
                    modelList.findIndex((item) => item.comName === 'FloatBth')
                  )
                "
              >
                <div
                  class="float-bth-in"
                  :style="{
                    backgroundColor: floatForm.btnStyle === 2 ? floatForm.bgColor : 'transparent',
                    borderColor: floatForm.btnStyle === 2 ? floatForm.bgColor : '#999999',
                  }"
                >
                  <img :src="floatForm.image || require('@/assets/img/replace-img.png')" alt="" class="float-img" />
                </div>
                <el-popover v-model="FloatBth_visible" placement="bottom" width="160">
                  <p>确定删除吗？</p>
                  <div style="text-align: right; margin: 0">
                    <el-button size="mini" type="text" @click="FloatBth_visible = false"> 取消 </el-button>
                    <el-button type="primary" size="mini" @click="delPageModel('FloatBth')"> 确定 </el-button>
                  </div>
                  <div slot="reference" class="del-ic">
                    <i class="el-icon-error"></i>
                  </div>
                </el-popover>
              </div>
              <!--              微信客服-->
              <div
                v-if="modelList.findIndex((item) => item.comName === 'wxService') > -1"
                class="float-bth wxBtn"
                :class="[tem_tag === '微信客服' ? 'float-bth-on' : '']"
                @click="
                  temChange(
                    '微信客服',
                    modelList.findIndex((item) => item.comName === 'wxService')
                  )
                "
              >
                <div class="float-bth-in">
                  <i class="iconfont icon-kefu" :style="{ color: wxForm.color }"></i>
                </div>
                <el-popover v-model="wxService_visible" placement="bottom" width="160">
                  <p>确定删除吗？</p>
                  <div style="text-align: right; margin: 0">
                    <el-button size="mini" type="text" @click="wxService_visible = false"> 取消 </el-button>
                    <el-button type="primary" size="mini" @click="delPageModel('wxService')"> 确定 </el-button>
                  </div>
                  <div slot="reference" class="del-ic">
                    <i class="el-icon-error"></i>
                  </div>
                </el-popover>
              </div>
              <!--              其他组件-->
              <div
                v-for="(li, index) in modelList"
                :key="index"
                v-dragging="{ item: li, list: modelList, group: 'li' }"
                class="page-model"
                :class="[index === 0 ? 'head-absolute' : '']"
                @click="temChange(li.name, index)"
              >
                <div
                  :is="li.comName"
                  v-if="li.comName !== 'FloatBth' && li.comName !== 'wxService'"
                  class="page-com"
                  :class="[model_index === index ? 'page-on' : '']"
                  :set-form="li.modelData"
                ></div>
                <el-popover v-if="index > 0" v-model="li.visible" placement="bottom" width="160">
                  <p>确定删除吗？</p>
                  <div style="text-align: right; margin: 0">
                    <el-button size="mini" type="text" @click="li.visible = false"> 取消 </el-button>
                    <el-button type="primary" size="mini" @click="delPageModel(index)"> 确定 </el-button>
                  </div>
                  <div slot="reference" class="del-page-model">
                    <i class="el-icon-close"></i>
                  </div>
                </el-popover>
              </div>
            </div>
          </div>
        </div>
        <!--组件表单-->
        <div class="tem-view set-view">
          <SetPage :tag="tem_tag" :tem-form="tem_form" :shop-id="shopId" @confirm="setConfirm" />
        </div>
      </div>
    </div>
  </ContainerTit>
</template>

<script>
import { getPageInfo, PageSave } from "@/api/System";
import SetPage from "./PDComponents/SetPage.vue";
import { componentsList } from "./PDComponents/Components.js";
import { componentsListStore } from "./PDComponents/ComponentsStore.js";
import ImgSwiper from "./PDComponents/ImgSwiper";
import MagicImg from "./PDComponents/MagicImg";
import Blank from "./PDComponents/Blank";
import LineF from "./PDComponents/Line";
import Notice from "./PDComponents/Notice";
import GoodsGroup from "./PDComponents/GoodsGroup.vue";
import NavBar from "./PDComponents/NavBar";
import SearchC from "./PDComponents/SearchC";
import LimitedSeckill from "./PDComponents/LimitedSeckill";
import CouponGroup from "./PDComponents/CouponGroup";
import NavigationBar from "./PDComponents/NavigationBar";
import ShopInfo from "./PDComponents/ShopInfo";
import IntegralGoods from "./PDComponents/IntegralGoods";
import AdvGroup from "./PDComponents/AdvGroup";
import { title } from "@/config/settings.js";
import SelectShop from "@/component/goods/SelectShop.vue";
const NavigationBarForm = {
  name: "页面设置",
  comName: "NavigationBar",
  modelData: {
    name: title,
    topBgColor: "#ec1c24",
    textColor: "#ffffff",
    pageBgColor: "#f7f8fa",
    bgImage: "",
    style: 1,
  },
};
export default {
  name: "PageDesign",
  components: {
    SelectShop,
    SetPage,
    ImgSwiper,
    MagicImg,
    Blank,
    LineF,
    Notice,
    NavBar,
    GoodsGroup,
    // FloatBth,
    LimitedSeckill,
    CouponGroup,
    SearchC,
    NavigationBar,
    ShopInfo,
    AdvGroup,
    IntegralGoods,
  },
  data() {
    return {
      wxService_visible: false,
      FloatBth_visible: false,
      active_collapse: 0,
      pageName: "",
      pageType: 1,
      tem_form: {}, // 当前页面组件的form内容
      // componentsList: componentsList, // 组件库列表
      model_index: 0, // 页面内组件 index 下标
      set_form: {},
      floatForm: {},
      wxForm: {},
      modelList: [NavigationBarForm],
      tem_tag: "", // 组件标示
      isStore: false,
      shopId: 0,
    };
  },
  computed: {
    pageStyle() {
      const obj = this.modelList[0].modelData;
      return {
        backgroundColor: obj.pageBgColor,
        backgroundImage: "url(" + obj.bgImage + ")",
        backgroundPosition: obj.topBgColor ? "0 74px" : "0 0",
      };
    },
    // 组件库列表
    componentsList() {
      if (this.systemType === 3) {
        return componentsListStore;
      } else {
        return componentsList;
      }
    },
  },
  watch: {
    pageName(val) {
      if (this.pageType === 2) {
        this.modelList[0].modelData.titleText = val;
      }
    },
    pageType(val) {
      if (val === 2) {
        this.modelList[0].modelData.titleText = this.pageName;
      }
    },
  },
  created() {
    if (this.$route.params.id) {
      this.getDetail();
    }
    if (this.systemType === 3) {
      this.isStore = true;
    }
  },
  mounted() {
    // 页面内组件 拖拽事件
    this.$dragging.$on("dragged", (res) => {
      this.model_index = this.modelList.findIndex((item) => item.name === res.draged.name);
    });
  },
  methods: {
    // 组件库事件，点击选择组件
    selComponent(rowd) {
      const row = this.$_common.deepClone(rowd);
      if (rowd.name === "浮动按钮") {
        const isFlot = this.modelList.find((item) => item.name === "浮动按钮");
        if (isFlot) {
          this.$message.warning("只能添加一个浮动按钮哦～～");
          return;
        }
      }
      if (rowd.name === "微信客服") {
        const isFlot = this.modelList.find((item) => item.name === "微信客服");
        if (isFlot) {
          this.$message.warning("只能添加一个微信客服按钮哦～～");
          return;
        }
      }
      if (rowd.name === "商户信息") {
        const isFlot = this.modelList.find((item) => item.name === "商户信息");
        if (isFlot) {
          this.$message.warning("只能添加一个哦～～");
          return;
        }
      }
      this.tem_tag = row.name;
      // 在当前位置之后插入一个组件
      this.model_index = this.model_index + 1;
      this.modelList.splice(this.model_index, 0, row);
      this.tem_form = row.modelData;
      if (rowd.name === "浮动按钮") {
        this.floatForm = this.tem_form;
      }
      if (rowd.name === "微信客服") {
        this.wxForm = this.tem_form;
      }
    },
    // 页面显示事件，点击选择页面内组件
    temChange(tag, index) {
      this.tem_tag = tag;
      this.model_index = index;

      if (tag === "浮动按钮") {
        this.floatForm = this.modelList[index].modelData;
        this.tem_form = this.floatForm;
      } else if (tag === "微信客服") {
        this.wxForm = this.modelList[index].modelData;
        this.tem_form = this.wxForm;
      } else {
        this.tem_form = this.modelList[index].modelData;
      }
    },
    // 页面设计form表单完成事件
    setConfirm(obj) {
      this.modelList[this.model_index].modelData = obj;
      this.set_form = obj;
    },
    // 删除页面内组件
    delPageModel(index) {
      this.tem_tag = "";
      if (index === "FloatBth") {
        this.FloatBth_visible = false;
        const indexF = this.modelList.findIndex((item) => item.comName === "FloatBth");
        this.modelList.splice(indexF, 1);
        this.temChange(this.modelList[1].name, 1);
      } else if (index === "wxService") {
        this.wxService_visible = false;
        const indexF = this.modelList.findIndex((item) => item.comName === "wxService");
        this.modelList.splice(indexF, 1);
        this.temChange(this.modelList[1].name, 1);
      } else {
        this.modelList[index].visible = false;
        this.temChange(this.modelList[this.model_index - 1].name, this.model_index - 1);
        this.modelList.splice(index, 1);
      }
    },
    // 获取详情
    async getDetail() {
      if (!this.$accessCheck(this.$Access.PageSetGetPageInfo)) {
        return;
      }
      const { data } = await getPageInfo(this.$route.params.id);

      this.pageName = data.pageName;
      this.pageType = data.pageType;
      this.modelList = data.pageData;
      this.shopId = data.shopId;
      const navBar = this.modelList.findIndex((item) => item.comName === "NavigationBar");
      const fData = this.modelList.find((item) => item.name === "浮动按钮");
      const wData = this.modelList.find((item) => item.name === "微信客服");
      if (navBar === -1) {
        this.modelList.unshift(NavigationBarForm);
      } else {
        if (this.modelList[navBar].name !== "页面设置") {
          this.modelList[navBar] = NavigationBarForm;
        }
      }
      if (fData) {
        this.floatForm = fData.modelData;
      }
      if (wData) {
        this.wxForm = wData.modelData;
      }
      this.temChange(this.modelList[0].name, 0);
    },
    async submit() {
      // return
      if (!this.pageName) {
        this.$message.warning("请输入页面名称");
        return;
      }
      let params = {
        pageName: this.pageName,
        pageType: this.pageType,
        pageData: this.modelList,
        isStore: this.isStore,
        shopId: this.shopId,
      };
      if (this.$route.params.id) {
        params.id = this.$route.params.id;
      }
      const data = await PageSave(params);
      this.$message.success("提交成功");
      if (!this.$route.params.id) {
        this.$closeCurrentGoEdit("/ShoppingMall/AppDesign/PageDesignList");
      }
      // this.$closeCurrentGoEdit("/ShoppingMall/AppDesign/PageDesignList");
    },
    // 选择商铺
    shopConfirm(val, row) {
      this.shopId = row[0].id;
      // this.form.shop = row[0].name;
    },
    delShop() {
      this.shopId = "";
    },
  },
};
</script>

<style scoped>
.com-view {
  width: 300px;
  float: left;
}
.page-view {
  width: calc(100% - 680px);
  /*padding: 0 14px ;*/
  background-color: #f2f4f7;
  float: left;
}
.page-show-box {
  margin: 20px auto;
  width: 410px;
  padding: 13px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #dddddd;
}
.page-show {
  width: 380px;
  border: 1px solid #dddddd;
  position: relative;
  min-height: 648px;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: 0 74px;
}
.set-view {
  width: 380px;
  float: right;
}
.tem-view {
  height: calc(100vh - 132px);
  overflow-y: auto;
}
/*  组件库*/

.com-box {
  font-weight: 400;
  margin: 10px;
  border: 1px solid #eee;
}
.com-tit {
  font-size: 13px;
  color: #666;
  line-height: 40px;
  background-color: #f8f8fa;
  padding-left: 10px;
  border-bottom: 1px solid #eee;
}
.com-ul {
  padding: 5px;
}
.com-li {
  width: 76px;
  height: 76px;
  text-align: center;
  float: left;
  font-size: 12px;
  padding-top: 10px;
  margin: 5px;
  background: #f8f8fa;
  border: 1px solid #eee;
  cursor: pointer;
  transition: 0.3s;
  color: #333;
  font-weight: 400;
}
.com-li p {
  padding-top: 42px;
}
.com-li .iconfont {
  font-size: 28px;
  color: #666;
}
.com-li:hover {
  background: #fff;
  border: 1px solid #fb6638;
}
/*  pageShow*/
.page-show {
  min-height: 648px;
  padding-top: 70px;
}
.page-model {
  cursor: move;
  /*border: 2px dashed transparent ;*/
  position: relative;
}
.head-absolute {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
}
.del-page-model {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  font-size: 12px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  background: #fb6638;
  color: #fff;
  cursor: pointer;
  display: none;
  border-bottom-left-radius: 12px;
}

.page-com {
  border: 1px solid transparent;
}
.page-com:hover {
  border: 1px solid #fb6638;
}
.page-model:hover .del-page-model {
  display: block;
}
.page-on {
  border: 1px solid #fb6638;
}
/* 浮动按钮 */
.float-bth {
  width: 40px;
  height: 40px;
  text-align: center;
  /*border-radius: 100%;*/
  position: absolute;
  right: 10px;
  bottom: 150px;
  border: 2px dashed transparent;
  cursor: pointer;
  z-index: 999;
}
.wxBtn {
  background-color: #ffffff;
  bottom: 200px;
}
.float-bth-in {
  width: 100%;
  height: 100%;
  border-radius: 100%;
  border: 1px solid #999;
  box-shadow: 0 3px 10px #dcdcdc;
}
.wxBtn .float-bth-in {
  border: 0 none;
  text-align: center;
  line-height: 40px;
}
.wxBtn .float-bth-in .iconfont {
  font-size: 26px;
  height: 40px;
  display: inline-block;
  transform: translateY(-2px);
}
.float-img {
  width: 20px;
  height: 20px;
  display: inline-block;
  margin-top: 8px;
}
.del-ic {
  position: absolute;
  top: -10px;
  right: -10px;
  color: #ff4040;
  cursor: pointer;
  display: none;
}
.float-bth:hover {
  border: 1px solid #fb6638;
}
.float-bth:hover .del-ic {
  display: block;
}
.f-label {
  font-weight: 400;
}
.float-bth-on {
  border: 1px solid #fb6638;
}
</style>
<style>
.up-img-li .el-upload--picture-card {
  width: 90px;
  height: 40px;
  border-color: transparent;
}
.up-img-li .el-upload--picture-card:hover {
  border-color: transparent;
}
/*  图片上传*/
.input-tip {
  font-size: 12px;
  color: #777;
  line-height: 22px;
  background-color: #e5f0fe;
  padding: 8px 16px;
  border-radius: 6px;
}
.up-img-li {
  border: 1px solid #ddd;
  background: #f5f9fc;
  margin-top: 10px;
  padding: 8px;
  position: relative;
}
.up-url {
  /*width: calc(100% - 120px);*/
  background: #fff;
  font-size: 12px;
  color: #666;
  padding: 0 10px;
  cursor: pointer;
}
.img-del-icon {
  position: absolute;
  right: 0;
  top: 0;
  color: #fb6638;
  width: 20px;
  height: 20px;
  z-index: 1;
  display: none;
  cursor: pointer;
}
.up-img-li:hover .img-del-icon {
  display: block;
}
</style>
