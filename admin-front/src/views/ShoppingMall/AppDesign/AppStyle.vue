<template>
  <ContainerTit>
    <Container></Container>
    <div slot="headr">
      <el-button v-if="$accessCheck($Access.StyleSetSetting)" type="primary" :loading="loading" @click="setSubmit">
        提交保存
      </el-button>
    </div>
    <el-card shadow="never">
      <ul class="style-ul clearfix">
        <li
          v-for="(item, index) in style_list"
          :key="index"
          class="style-li float_left"
          :class="[item.label === basicData.themeStyle.label ? 'style-on' : '']"
          @click="changeStyle(item)"
        >
          <span
            class="color-span"
            :style="{
              background: 'linear-gradient(' + item.color_o + ',' + item.color_t + ')',
            }"
          ></span>
          <span class="color-text">{{ item.label }}</span>
        </li>
      </ul>
    </el-card>
    <el-card shadow="never">
      <img v-for="(item, index) in img_list" :key="index" class="app-page-img" :src="item" alt="" />
    </el-card>
  </ContainerTit>
</template>

<script>
import { mapActions } from "vuex";
import { setting, getBasicSetup } from "@/api/System";

export default {
  name: "AppStyle",
  data() {
    return {
      loading: false,
      style_list: [
        {
          label: "热情红",
          theme: "red",
          color_t: "#ff3883",
          color_o: "#ff2d2d",
          img: [require("@/assets/img/style/red-o.jpg"), require("@/assets/img/style/red-u.jpg")],
        },
        {
          label: "活力橙",
          theme: "orange",
          color_t: "#ff9320",
          color_o: "#ff6c24",
          img: [require("@/assets/img/style/orange-o.jpg"), require("@/assets/img/style/orange-u.jpg")],
        },
        {
          label: "格调金",
          theme: "gold",
          color_t: "#f5cc94",
          color_o: "#d8a96c",
          img: [require("@/assets/img/style/gold-o.jpg"), require("@/assets/img/style/gold-u.jpg")],
        },
        {
          label: "雅致粉",
          theme: "pink",
          color_t: "#fe3f94",
          color_o: "#ec0f67",
          img: [require("@/assets/img/style/pink-o.jpg"), require("@/assets/img/style/pink-u.jpg")],
        },
        {
          label: "纯净绿",
          theme: "green",
          color_t: "#91E055",
          color_o: "#5FAB2A",
          img: [require("@/assets/img/style/green-o.jpg"), require("@/assets/img/style/green-u.jpg")],
        },
        {
          label: "商务蓝",
          theme: "blue",
          color_t: "#0da0fe",
          color_o: "#2072f4",
          img: [require("@/assets/img/style/blue-o.jpg"), require("@/assets/img/style/blue-u.jpg")],
        },
      ],
      basicData: {
        goodsNum: "", // 库存警告
        images: [],
        stockDisplay: 4,
        goodsPrice: 5,
        allowReturnDay: 0, // 设置天数
        personnelReview: 4,
        shop: "",
        description: "",
        phone: "",
        company: "",
        region: [],
        address: "",
        shelfLifeSetUp: 4,
        recommend: [],
        wxPay: 4,
        aliPay: 4,
        cashPay: 4,
        themeStyle: {
          label: "热情红",
          color_o: "#ff3883",
          color_t: "#ff2d2d",
        },
      },
      img_list: [require("@/assets/img/style/red-o.jpg"), require("@/assets/img/style/red-u.jpg")],
    };
  },
  async created() {
    this.getBasicSetup();
  },
  methods: {
    ...mapActions({
      changeShelfLifeSetUp: "MUser/changeShelfLifeSetUp",
    }),
    changeStyle(row) {
      this.basicData.themeStyle = {
        label: row.label,
        theme: row.theme,
        color_t: row.color_t,
        color_o: row.color_o,
      };
      this.img_list = row.img;
    },
    //  提交
    async setSubmit() {
      const data = await setting({
        basicData: this.basicData,
      });

      this.$message({
        message: "提交成功",
        type: "success",
      });
      this.changeShelfLifeSetUp(this.basicData.shelfLifeSetUp);
      this.getBasicSetup();
    },
    //  获取详情
    async getBasicSetup() {
      const { data } = await getBasicSetup();

      this.basicData = { ...this.basicData, ...data.basicData };
      if (this.basicData.themeStyle) {
        this.img_list = this.style_list.find((item) => item.label === this.basicData.themeStyle.label).img;
      }
    },
  },
};
</script>

<style scoped>
.style-ul {
  padding-top: 20px;
}
.style-li {
  width: 142px;
  height: 68px;
  line-height: 68px;
  -webkit-box-shadow: 4px 4px 6px #f5f9fc;
  box-shadow: 4px 4px 6px #f5f9fc;
  text-align: center;
  border-radius: 4px;
  font-size: 14px;
  border: 1px solid #eee;
  margin-left: 20px;
  cursor: pointer;
}
.style-on {
  border-color: #ff4400;
}
.color-span {
  display: inline-block;
  vertical-align: middle;
  width: 32px;
  height: 32px;
  border-radius: 100%;
  margin-right: 10px;
}
.app-page-img {
  width: 300px;
  margin-left: 164px;
  margin-top: 40px;
  box-shadow: 0px 0px 6px 3px #f5f9fc;
}
</style>
