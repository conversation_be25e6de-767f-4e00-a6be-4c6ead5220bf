<template>
  <ContainerTit :is-footer="false">
    <Container></Container>
    <div>
      <div v-if="parseInt(enterprise.enterpriseId) === 4" class="page-tip-div">
        温馨提示：当前企业为舜津科技演示企业，不允许操作小程序发布
      </div>
      <el-timeline>
        <el-timeline-item timestamp="步骤一" placement="top">
          <el-card class="step-card" shadow="never">
            <div class="step-left">
              <h3>设置微信小程序必要信息与支付方式</h3>
              <p class="step-des">
                请提前完成
                <el-button
                  v-if="$accessCheck($Access.WxCodeSet)"
                  type="text"
                  @click="$router.push('/ShoppingMall/WxCode/WxCodeSet')"
                >
                  微信小程序设置
                </el-button>
                <span v-else>微信小程序设置</span>
                并且保证信息填写与微信小程序后台信息一致。
              </p>
              <p class="step-des">
                微信小程序
                <el-button
                  v-if="$accessCheck($Access.PayList)"
                  type="text"
                  @click="$router.push('/SystemSettings/jiaoyiset/PayList')"
                >
                  支付设置
                </el-button>
                <span v-else>支付设置</span>
                ：选择微信支付
              </p>
              <p class="step-des">
                <el-button
                  v-if="$accessCheck($Access.DeliverySet)"
                  type="text"
                  @click="$router.push('/SystemSettings/jiaoyiset/Delivery')"
                >
                  配送方式设置
                </el-button>
                <span v-else>配送方式设置</span>
                ：可以启用适合您商铺的配送方式
              </p>
            </div>

            <el-button
              v-if="$accessCheck($Access.WxCodeSet)"
              class="step-btn"
              type="primary"
              @click="$router.push('/ShoppingMall/WxCode/WxCodeSet')"
            >
              前去设置
            </el-button>
          </el-card>
        </el-timeline-item>
        <el-timeline-item timestamp="步骤二" placement="top">
          <el-card class="step-card" shadow="never">
            <div class="step-left">
              <h3>已注册微信小程序，立即授权发布</h3>
              <p class="step-des">
                使用微信小程序管理员帐号扫码进行授权，授权过程中请勾选所有权限以确保小程序功能完整性。如未注册小程序，可以从公众号后台免微信认证创建小程序或直接前往“微信公众平台”注册企业主体的小程序帐号。
              </p>
              <el-button type="text" @click="goPage">官方注册小程序</el-button>
            </div>
            <el-button
              v-if="$accessCheck($Access.wxPushpreAuthCode)"
              class="step-btn"
              :disabled="parseInt(enterprise.enterpriseId) === 4"
              type="primary"
              @click="wxOpen"
            >
              小程序授权
            </el-button>
          </el-card>
        </el-timeline-item>
        <el-timeline-item timestamp="步骤三" placement="top">
          <el-card shadow="never">
            <h3>选择小程序模版，提交代码</h3>
            <ul v-if="template_list.length" class="clearfix temp-ul">
              <li class="temp-item float_left">
                <img class="tem-img" :src="tembg1" alt="" />
                <p class="tem-tit">
                  {{ template_list[0].title }}
                </p>
                <p>
                  <span style="padding-right: 10px">
                    <el-popover v-if="template_list[0].auditStatus !== 6" placement="right" width="230" trigger="hover">
                      <el-image style="width: 200px; height: 200px" :src="template_list[0].qrcodeImg" fit="contain" />
                      <el-button slot="reference" size="small" type="primary"> 预览 </el-button>
                    </el-popover>
                    <!--              @click="useTem(template_list[0])"-->
                    <el-button
                      v-if="![0, 1, 5].includes(template_list[0].auditStatus)"
                      slot="reference"
                      size="small"
                      type="primary"
                      :disabled="parseInt(enterprise.enterpriseId) === 4"
                      style="margin: 0 10px"
                      @click="subUpdate(template_list[0])"
                    >
                      {{
                        template_list[0].auditStatus === 2
                          ? "查询审核状态"
                          : template_list[0].auditStatus === 3
                          ? "已撤回"
                          : template_list[0].auditStatus === 4
                          ? "版本更新重新提交代码"
                          : template_list[0].auditStatus === 6
                          ? "提交代码"
                          : template_list[0].auditStatus === 7
                          ? "正在使用"
                          : ""
                      }}
                    </el-button>
                    <el-button
                      v-if="template_list[0].auditStatus === 5 || template_list[0].auditStatus === 7"
                      slot="reference"
                      size="small"
                      style="margin: 0 10px"
                      type="primary"
                      :disabled="parseInt(enterprise.enterpriseId) === 4"
                      @click="subUpdate(template_list[0])"
                    >
                      重新提交代码
                    </el-button>
                    <el-popover
                      v-if="template_list[0].auditStatus === 1"
                      placement="top-start"
                      title="提示"
                      width="200"
                      trigger="hover"
                      :content="template_list[0].reason.reason"
                    >
                      <el-button
                        slot="reference"
                        :disabled="parseInt(enterprise.enterpriseId) === 4"
                        size="small"
                        type="primary"
                        @click="subUpdate(template_list[0])"
                      >
                        审核拒绝,重新提交代码
                      </el-button>
                    </el-popover>
                  </span>
                  <!--                  $router.push('/ShoppingMall/mendianset/TemplateEdit/'+template_list[0].id)-->
                  <el-button
                    size="small"
                    type="primary"
                    :disabled="parseInt(enterprise.enterpriseId) === 4"
                    @click="setModel(index, template_list[0].id)"
                  >
                    设置
                  </el-button>
                </p>
              </li>
            </ul>
          </el-card>
        </el-timeline-item>
        <el-timeline-item timestamp="步骤四" placement="top">
          <el-card class="step-card" shadow="never">
            <div class="step-left">
              <h3>完成所有准备，提交审核并发布小程序</h3>
              <p class="step-des">提交微信审核（最长14个工作日），审核通过后即可立即发布版本</p>
            </div>

            <el-button
              v-if="auditStatus === 5"
              class="step-btn"
              type="primary"
              :disabled="parseInt(enterprise.enterpriseId) === 4"
              @click="submitAudit(wx_status)"
            >
              提交审核
            </el-button>
            <el-button v-if="auditStatus === 0" class="step-btn" type="primary" @click="release(wx_status)">
              审核通过，发布小程序
            </el-button>
            <el-button
              v-if="auditStatus === 2 || auditStatus === 7"
              disabled
              class="step-btn"
              :type="auditStatus === 2 ? 'primary' : auditStatus === 7 ? 'success' : ''"
              plain
            >
              {{ auditStatus === 2 ? "审核中" : auditStatus === 7 ? "发布成功" : "" }}
            </el-button>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
  </ContainerTit>
</template>

<script>
import tembg1 from "@/assets/img/template1.png";
import tembg2 from "@/assets/img/template2.png";
import {
  getWxStatus,
  preAuthCode,
  submitAudit,
  getAllTemplate,
  submitCode,
  bindTemplate,
  getAuditStatus,
  release,
} from "@/api/System";
import { mapGetters } from "vuex";
export default {
  name: "TemplateSet",
  data() {
    return {
      template_list: [],
      reason: "",
      tembg1: tembg1,
      tembg2: tembg2,
      auditStatus: "",
      wx_status: {},
    };
  },
  computed: {
    ...mapGetters({
      enterprise: "MUser/enterprise",
    }),
  },
  created() {
    this.getData();
    this.getWxStatus();
  },
  methods: {
    // 设置按钮事件
    setModel(index, id) {
      // if (index === 0) {
      this.$router.push("/ShoppingMall/AppDesign/PageDesignList");
      // }
    },
    // 获取提交代码后小程序状态
    async getWxStatus() {
      const { data } = await getWxStatus();

      this.auditStatus = data.auditStatus;
      this.wx_status = data;
    },
    // 确定要提交审核吗
    submitAudit(row) {
      if (!this.$accessCheck(this.$Access.wxPushsubmitAudit)) {
        this.$message.warning("抱歉，您没有当前操作权限");
        return;
      }
      this.$confirm("确定要提交审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await submitAudit({
          templateId: row.templateId,
        });

        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.getData();
        this.getWxStatus();
      });
    },
    //  审核subUpdate
    async subUpdate(row) {
      if ([6, 1, 5, 7].includes(parseInt(row.auditStatus))) {
        if (!this.$accessCheck(this.$Access.wxPushsubmitCode)) {
          this.$message.warning("没有操作权限");
          return;
        }
        // 提交代码
        this.$confirm("确定要提交代码?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          const data = await submitCode(row.id, {
            weixinTemplateId: row.weixinTemplateId,
            version: row.version,
          });

          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getData();
          this.getWxStatus();
        });
      } else if (parseInt(row.auditStatus) === 4) {
        //  企业使用模版启用/停用
        if (!this.$accessCheck(this.$Access.TemplateSetBindTemplate)) {
          this.$message.warning("抱歉，您没有当前操作权限");
          return;
        }
        this.$confirm("确定要使用该模版吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          const data = await bindTemplate(row.id, {
            enableStatus: 5,
            weixinTemplateId: row.weixinTemplateId,
            version: row.version,
          });

          this.$message({
            type: "success",
            message: "模版启用成功!",
          });
          this.getData();
        });
      } else if (parseInt(row.auditStatus) === 2) {
        //  查询审核状态
        this.getAuditStatus(row);
      }
    },
    // 查询审核状态
    async getAuditStatus(row) {
      if (!this.$accessCheck(this.$Access.wxPushpreAuthCode)) {
        this.$message.warning("抱歉，您没有当前操作权限");
        return;
      }
      const { data } = await getAuditStatus({
        templateId: row.id,
        auditId: row.auditId,
      });

      this.$message.success(data.message);
    },
    // 发布通过审核的小程序 】
    async release(row) {
      if (!this.$accessCheck(this.$Access.wxPushrelease)) {
        this.$message.warning("抱歉，您没有当前操作权限");
        return;
      }
      const data = await release({
        templateId: row.templateId,
      });

      this.$message.success("操作成功");
      this.getData();
      this.getWxStatus();
    },
    async getData() {
      const { data } = await getAllTemplate();

      this.template_list = data;
    },
    goPage() {
      window.open("https://mp.weixin.qq.com/");
    },
    //  小程序授权
    async wxOpen() {
      if (!this.$accessCheck(this.$Access.wxPushpreAuthCode)) {
        this.$message.warning("抱歉，您没有当前操作权限");
        return;
      }
      const { data } = await preAuthCode();

      const wxUrl = data;
      //  打开空白页面
      window.open(wxUrl);
    },
  },
};
</script>

<style scoped>
.temp-item {
  background: #f5f9fc;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  margin-right: 20px;
}
.tem-img {
  width: 160px;
  border-radius: 20px;
}
.temp-ul {
  padding: 20px;
}
.tem-tit {
  font-size: 14px;
  padding: 10px 0;
}
.step-left {
  width: 84%;
}
.step-left h3 {
  padding-bottom: 10px;
}
.step-des {
  color: #666;
}
.step-card {
  position: relative;
}
.step-btn {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-20px);
}
</style>
