<template>
  <ContainerTit :is-footer="false">
    <Container>
      <el-tabs v-model="activeTabName" type="card">
        <el-tab-pane label="商城" name="mall">
          <WxCodeSetForm :form-data="mallForm" @submit="(form) => updateSystemSettings(1, form)" />
        </el-tab-pane>
        <el-tab-pane label="移动送货" name="movableMall">
          <WxCodeSetForm :form-data="movableMallForm" @submit="(form) => updateSystemSettings(4, form)" />
        </el-tab-pane>
      </el-tabs>
    </Container>
  </ContainerTit>
</template>

<script>
import { getSystemSettingsInfo, updateSystemSettings } from "@/api/System";
import WxCodeSetForm from "@/views/ShoppingMall/WxCode/components/WxCodeSetForm.vue";

export default {
  name: "BaseSetAdd",
  components: {
    WxCodeSetForm,
  },
  data() {
    return {
      activeTabName: "mall",
      img_list: [],
      mallForm: {
        name: "",
        account: "",
        img: "",
        originalID: "",
        APPscrect: "",
        appid: "",
      },
      movableMallForm: {
        name: "",
        account: "",
        img: "",
        originalID: "",
        APPscrect: "",
        appid: "",
      },
    };
  },
  created() {
    this.getSystemSettingsInfo(1);
    this.getSystemSettingsInfo(4);
  },
  methods: {
    // 获取详情
    async getSystemSettingsInfo(type) {
      if (!this.$accessCheck(this.$Access.WxCodeSetGetSystemSettingsInfo)) {
        return;
      }

      const { data } = await getSystemSettingsInfo(type);
      if (type === 1) {
        this.mallForm = {
          ...data,
          ...this.mallForm,
          ...data.content,
        };
      } else if (type === 4) {
        this.movableMallForm = {
          ...data,
          ...this.movableMallForm,
          ...data.content,
        };
      }
    },
    // 提交
    async updateSystemSettings(type, form) {
      let id;
      if (type === 1) {
        console.log(this.mallForm);
        id = this.mallForm.id;
      } else if (type === 4) {
        id = this.movableMallForm.id;
      }
      const params = {
        type,
        content: {
          ...form,
        },
      };
      const data = await updateSystemSettings(id, {
        ...params,
      });

      this.$message.success("提交成功");
      await this.getSystemSettingsInfo(type);
    },
  },
};
</script>

<style scoped>
.page-tit {
  font-size: 14px;
  font-weight: bold;
  padding-left: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
}
</style>
