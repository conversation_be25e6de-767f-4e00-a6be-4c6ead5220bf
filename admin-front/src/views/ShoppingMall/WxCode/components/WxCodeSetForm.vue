<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="200px">
    <div class="detail-tab-item">
      <p class="detail-tab-title">基本信息</p>
      <div class="detail-tab-main">
        <div v-if="parseInt(enterprise.enterpriseId) === 4" class="page-tip-div">
          温馨提示：当前企业为舜津科技演示企业，不允许操作小程序配置
        </div>
        <el-form-item prop="name" label="小程序名称：">
          <el-input v-model="form.name" style="width: 350px" placeholder="小程序名称"></el-input>
        </el-form-item>
        <el-form-item prop="account" label="小程序账号：">
          <el-input v-model="form.account" style="width: 350px" placeholder="小程序账号"></el-input>
        </el-form-item>
        <el-form-item prop="img" label="小程序二维码：">
          <UploadQiniu :file-list="img_list" @uploadSuccess="uploadSuccess" @handleRemove="uploadRemove" />
        </el-form-item>
        <el-form-item prop="originalID" label="小程序原始ID：">
          <el-input v-model="form.originalID" style="width: 350px" placeholder="小程序原始ID"></el-input>
        </el-form-item>
      </div>
    </div>

    <div class="detail-tab-item">
      <p class="detail-tab-title">开发者设置</p>
      <div class="detail-tab-main">
        <el-form-item prop="appid" label="AppId：">
          <el-input v-model="form.appid" style="width: 350px" placeholder="小程序AppId"></el-input>
        </el-form-item>
        <el-form-item label="AppScrect：" prop="APPscrect">
          <el-input v-model="form.APPscrect" style="width: 350px" placeholder="AppScrect(小程序密钥)"></el-input>
        </el-form-item>
      </div>
    </div>
    <div>
      <el-button type="primary" @click="submitForm">保存</el-button>
    </div>
  </el-form>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu.vue";
import { mapGetters } from "vuex";

export default {
  components: { UploadQiniu },
  props: {
    formData: {
      type: Object,
      default: () => {
        return {
          name: "",
          account: "",
          img: "",
          originalID: "",
          appid: "",
          APPscrect: "",
        };
      },
    },
  },
  data() {
    return {
      img_list: [],
      rules: {
        name: [{ required: true, message: "请输入小程序名称", trigger: "blur" }],
        account: [{ required: true, message: "请输入小程序账号", trigger: "blur" }],
        img: [
          {
            required: true,
            message: "请上传小程序二维码",
            trigger: "change",
          },
        ],
        originalID: [{ required: true, message: "请输入小程序原始ID", trigger: "blur" }],
        appid: [{ required: true, message: "请输入小程序APPID", trigger: "blur" }],
        APPscrect: [
          {
            required: true,
            message: "请输入小程序APPscrect",
            trigger: "blur",
          },
        ],
      },
      form: {
        name: "",
        account: "",
        img: "",
        originalID: "",
        appid: "",
        APPscrect: "",
      },
    };
  },
  computed: {
    ...mapGetters({
      enterprise: "MUser/enterprise",
    }),
  },
  watch: {
    formData: {
      handler(val) {
        this.form = val;
        console.log(this.form);
        this.img_list = val.img ? [{ url: val.img }] : [];
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    uploadSuccess(val, res, file, fileList) {
      this.form.img = val;
    },
    uploadRemove() {
      this.form.img = "";
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("submit", this.form);
        }
      });
    },
  },
};
</script>

<style scoped></style>
