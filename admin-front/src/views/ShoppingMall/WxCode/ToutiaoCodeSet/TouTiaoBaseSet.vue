<template>
  <ContainerTit>
    <el-card class="box-card" style="height: calc(100vh - 200px); overflow-y: scroll">
      <el-form
        ref="basicData"
        :model="basicData"
        :rules="rules"
        :label-position="labelPosition"
        style="padding: 10px"
        class="formitem"
      >
        <el-row>
          <el-col :span="16">
            <div>
              <el-form-item :label="basicData.name_AppID" prop="message_AppID">
                <el-input v-model="basicData.message_AppID" style="width: 100%" placeholder="请输入内容"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item :label="basicData.name_AppSecret" prop="message_AppSecret">
                <el-input v-model="basicData.message_AppSecret" style="width: 100%" placeholder="请输入内容"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item :label="basicData.name_shanghuhao" prop="message_shanghuhao">
                <el-input
                  v-model="basicData.message_shanghuhao"
                  style="width: 100%"
                  placeholder="请输入内容"
                ></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item :label="basicData.name_zhifuset" prop="message_zhifuset">
                <el-input v-model="basicData.message_zhifuset" style="width: 100%" placeholder="请输入内容"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item :label="basicData.name_secretset" prop="message_secretset">
                <el-input v-model="basicData.message_secretset" style="width: 100%" placeholder="请输入内容"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item :label="basicData.name_app_id" prop="message_app_id">
                <el-input v-model="basicData.message_app_id" style="width: 100%" placeholder="请输入内容"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item :label="basicData.name_alipay_public_key" prop="message_alipay_public_key">
                <el-input
                  v-model="basicData.message_alipay_public_key"
                  type="textarea"
                  autosize
                  placeholder="请输入内容"
                  style="width: 100%"
                ></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item :label="basicData.name_alipay_private_key" prop="message_alipay_private_key">
                <el-input
                  v-model="basicData.message_alipay_private_key"
                  type="textarea"
                  autosize
                  placeholder="请输入内容"
                  style="width: 100%"
                ></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item>
                <el-button type="primary" @click="saveSetting">保存</el-button>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </ContainerTit>
</template>

<script>
import { getSystemSettingsInfo2, saveByteDanceSetting } from "@/api/System";
export default {
  name: "TouTiaoBaseSet",
  data() {
    return {
      labelPosition: "top",
      id: 0,
      basicData: {
        name_AppID: "AppID(小程序Key)",
        name_AppSecret: "AppSecret(小程序Key)",
        name_shanghuhao: "商户号(支付设置)",
        name_zhifuset: "App ID(支付设置)",
        name_secretset: "支付secret(支付设置)",
        name_app_id: "支付宝应用app_id",
        name_alipay_public_key: "支付宝公钥alipay_public_key",
        name_alipay_private_key: "支付宝应用私钥alipay_private_key",
        message_AppID: "",
        message_AppSecret: "",
        message_shanghuhao: "",
        message_zhifuset: "",
        message_secretset: "",
        message_app_id: "",
        message_alipay_public_key: "",
        message_alipay_private_key: "",
      },
      rules: {
        message_AppID: [{ required: true, message: "请输入信息", trigger: "blur" }],
        message_AppSecret: [{ required: true, message: "请输入信息", trigger: "blur" }],
        message_shanghuhao: [{ required: true, message: "请输入信息", trigger: "blur" }],
        message_zhifuset: [{ required: true, message: "请输入信息", trigger: "blur" }],
        message_secretset: [{ required: true, message: "请输入信息", trigger: "blur" }],
        message_app_id: [{ required: true, message: "请输入信息", trigger: "blur" }],
        message_alipay_public_key: [{ required: true, message: "请输入信息", trigger: "blur" }],
        message_alipay_private_key: [{ required: true, message: "请输入信息", trigger: "blur" }],
      },
    };
  },
  mounted() {
    this.getSystemSettingsInfo2();
  },
  methods: {
    async getSystemSettingsInfo2() {
      const data = await getSystemSettingsInfo2();

      console.log(data);
      this.id = data.data.id;
    },
    async saveByteDanceSetting() {
      const data = await saveByteDanceSetting(this.id, {
        type: 4,
        content: {
          wxpay: {
            micro_appid: this.message_AppID,
            micro_app_secret: this.message_AppSecret,
            appid: this.message_shanghuhao,
            merchant_id: this.message_zhifuset,
            app_secret: this.message_secretset,
          },
          alipay: {},
        },
      });

      console.log(data);
    },
    saveSetting() {
      this.saveByteDanceSetting();
    },
  },
};
</script>

<style lang="scss" scoped>
.formitem .el-form-item__label {
  padding: 0 !important;
}
</style>
