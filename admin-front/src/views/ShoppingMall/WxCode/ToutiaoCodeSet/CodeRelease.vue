<template>
  <ContainerTit>
    <!-- 标题右侧按钮 -->
    <div slot="headr">
      <el-button type="primary" @click="dialogFormVisible = true"> 可跳转小程序设置 </el-button>
    </div>
    <!-- 右侧按钮弹出框 -->
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="可跳转小程序设置"
      :visible.sync="dialogFormVisible"
    >
      <p class="el-dialog__message">最多可配置10个，超出无效</p>
      <el-form
        ref="dynamicValidateForm"
        :model="dynamicValidateForm"
        label-width="100px"
        class="demo-dynamic dialog-form"
      >
        <el-form-item v-for="domain in dynamicValidateForm.domains" :key="domain.key">
          <el-input v-model="domain.value" class="dialog-input"></el-input>
          <div class="button_delete">
            <el-button @click.prevent="removeDomain(domain)">删除</el-button>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button @click="addDomain">新增</el-button>
          <el-button type="primary" class="dialog-tijiao" @click="submitForm('dynamicValidateForm')"> 提交 </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 页面主题内容 -->
    <el-card class="box-card" style="height: calc(100vh - 200px); overflow-y: scroll">
      <!-- 步骤条 -->
      <div class="block">
        <el-timeline class="timeline-title">
          <!-- 步骤条一 -->
          <el-timeline-item timestamp="第一步：" placement="top" type="success" color="#409EFF" size="large">
            <p class="timeline-step">下载并安装字节跳动开发者工具，如果已经安装可跳过这一步。</p>
            <el-button @click="downloadZijie">下载字节跳动开发者工具</el-button>
          </el-timeline-item>
          <!-- 步骤条二 -->
          <el-timeline-item timestamp="第二步：" placement="top" type="success" color="#409EFF" size="large">
            <p class="timeline-step">下载小程序代码包，并解压。</p>
            <el-button @click="downloadCode">下载小程序代码包</el-button>
          </el-timeline-item>
          <!-- 步骤条三 -->
          <el-timeline-item timestamp="第三步：" placement="top" type="success" color="#409EFF" size="large">
            <p class="timeline-step">运行字节跳动开发者工具，选择打开项目，打开解压出来的小程序代码包，点击上传。</p>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </ContainerTit>
</template>

<script>
export default {
  name: "CodeRelease",
  data() {
    return {
      devtoolsUrl:
        "https://developer.toutiao.com/dev/cn/mini-app/develop/developer-instrument/developer-instrument-update-and-download",
      codeUrl: "https://sale.infokp.cn/web/index.php?r=plugin%2Fttapp%2Findex%2Fpackage-download",
      dialogFormVisible: false,
      dynamicValidateForm: {
        domains: [
          {
            value: "1234567",
          },
        ],
      },
    };
  },
  methods: {
    downloadZijie() {
      // 跳转下载
      window.open(this.devtoolsUrl);
    },
    downloadCode() {
      // 跳转下载
      window.open(this.codeUrl);
    },
    submitForm(formName) {
      // 弹出框提交
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    removeDomain(item) {
      // 弹出框删除
      var index = this.dynamicValidateForm.domains.indexOf(item);
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1);
      }
    },
    addDomain() {
      // 弹出框新增
      this.dynamicValidateForm.domains.push({
        value: "1234567",
        key: Date.now(),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.block {
  padding: 10px;
}
.timeline-title .el-timeline-item__timestamp.is-top {
  padding: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #303133;
}
.timeline-title .el-timeline-item__tail {
  background-color: #409eff;
  border-color: #409eff;
}
.timeline-step {
  font-size: 16px;
  margin-bottom: 16px;
}
.el-dialog__message {
  margin-bottom: 20px;
}
.dialog-form .el-form-item__content {
  margin-left: 0 !important;
}
.el-dialog__wrapper .el-dialog {
  width: 40% !important;
}
.dialog-input {
  width: 80% !important;
  float: left;
}
.dialog-tijiao {
  float: right;
}
.button_delete {
  float: right;
  width: 18%;
  .el-button {
    width: 100%;
    padding: 12px 0;
    text-align: center;
  }
}
</style>
