<template>
  <!--  基础设置-->
  <Container>
    <ContainerTit>
      <div slot="headr">
        <el-button type="primary" @click="setSubmit">提交 </el-button>
      </div>
      <div class="detail-tab-item" style="padding: 20px">
        <div v-if="page_tabs !== '5'" class="page-tip-div" style="margin: 0">
          <i class="el-icon-info"></i>
          温馨提示：基本设置重新提交后，其他账户需重新选择企业进入后，设置方能生效
        </div>
        <div v-else class="page-tip-div" style="margin: 0">
          <i class="el-icon-info"></i>
          温馨提示：小票打印机使用的是佳博打印机，下面有打印机的购买链接和使用帮助，也可自行淘宝购买打印机
        </div>
        <el-tabs v-model="page_tabs" type="card">
          <el-tab-pane label="商城设置" name="1"></el-tab-pane>
          <el-tab-pane label="交易设置" name="2"></el-tab-pane>
          <el-tab-pane label="商品设置" name="3"></el-tab-pane>
          <el-tab-pane label="客户设置" name="4"></el-tab-pane>
          <el-tab-pane label="打印机设置" name="5"></el-tab-pane>
          <el-tab-pane label="库存设置" name="6"></el-tab-pane>
        </el-tabs>
        <el-form
          ref="basicData"
          style="padding-top: 10px"
          :model="basicData"
          :rules="rules"
          size="small"
          label-width="180px"
        >
          <div v-if="page_tabs === '1'" class="desc-div">
            <!--          v-if="activeName === 'BaseSet'"-->
            <div>
              <el-form-item label="商城LOGO：" prop="images">
                <UploadQiniu
                  :limit="1"
                  :file-list="img_list"
                  @uploadSuccess="uploadSuccess"
                  @handleRemove="uploadRemove"
                />
              </el-form-item>
              <el-form-item prop="shop" label="商城：">
                <el-input v-model="basicData.shop" style="width: 350px" placeholder="商城"></el-input>
              </el-form-item>
              <el-form-item prop="description" label="商城简介：">
                <el-input v-model="basicData.description" style="width: 350px" placeholder="请输入商城简介"></el-input>
              </el-form-item>
              <el-form-item prop="phone" label="客服电话：">
                <el-input v-model="basicData.phone" style="width: 350px" placeholder="请输入客服电话"></el-input>
              </el-form-item>
              <el-form-item prop="company" label="公司名字：">
                <el-input v-model="basicData.company" style="width: 350px" placeholder="请输入公司名字"></el-input>
              </el-form-item>
              <el-form-item prop="region" label="公司地址：">
                <span>
                  <RegionSelect v-model="basicData.region" size="small" style="width: 350px" @change="regionChange" />
                </span>
              </el-form-item>
              <el-form-item prop="address" label="详细地址：">
                <el-input v-model="basicData.address" style="width: 350px" placeholder="请输入公司详细地址"></el-input>
              </el-form-item>
            </div>
          </div>
          <div v-if="page_tabs === '2'" class="desc-div">
            <!--          v-if="activeName === 'TradeSet'"-->
            <div>
              <!--              订单自动审核-->
              <div v-if="false">
                <el-form-item label="微信支付：">
                  <el-switch
                    v-model="autoAuditOrder.wxPay"
                    active-color="#36B365"
                    inactive-color="#ff4949"
                    :active-value="5"
                    :inactive-value="4"
                    active-text="启用"
                    inactive-text="禁用"
                  ></el-switch>
                  <p class="form-tip">开启后：该种支付方式的订单支持自动审核下推到销售出库单</p>
                </el-form-item>
                <el-form-item label="支付宝支付：">
                  <el-switch
                    v-model="autoAuditOrder.aliPay"
                    active-color="#36B365"
                    inactive-color="#ff4949"
                    :active-value="5"
                    :inactive-value="4"
                    active-text="启用"
                    inactive-text="禁用"
                  ></el-switch>
                  <p class="form-tip">开启后：该种支付方式的订单支持自动审核下推到销售出库单</p>
                </el-form-item>
                <el-form-item label="货到付款：">
                  <el-switch
                    v-model="autoAuditOrder.cashPay"
                    active-color="#36B365"
                    inactive-color="#ff4949"
                    :active-value="5"
                    :inactive-value="4"
                    active-text="启用"
                    inactive-text="禁用"
                  ></el-switch>
                  <p class="form-tip">开启后：该种支付方式的订单支持自动审核下推到销售出库单</p>
                </el-form-item>
              </div>
              <el-form-item prop="address" label="订单打印标示展示：">
                <el-radio-group v-model="basicData.printTag">
                  <el-radio :label="5">展示</el-radio>
                  <el-radio :label="4">不展示</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="自动取消订单：">
                <el-input-number v-model="orderAutoCloseSec" :controls="false"></el-input-number>
                <span style="padding-left: 10px">小时</span>
                <p class="form-tip">货到付款的订单下单后未审核，{{ orderAutoCloseSec || "未设置" }}小时内会自动取消</p>
              </el-form-item>
              <el-form-item label="自动收货订单：">
                <el-input-number v-model="orderAutoFinishSec" :controls="false"></el-input-number>
                <span style="padding-left: 10px">小时</span>
                <p class="form-tip">订单出库后用户未确认收货，{{ orderAutoFinishSec || "未设置" }}小时内会自动收货</p>
              </el-form-item>
              <el-form-item prop="returnWay" label="退款金额返回方式:">
                <el-radio-group v-model="basicData.returnWay">
                  <el-radio :label="5">原路返回</el-radio>
                  <el-radio :label="4">退回余额</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="cancelOrder" label="取消订单节点:">
                <el-radio-group v-model="basicData.cancelOrder">
                  <el-radio :label="5">启用</el-radio>
                  <el-radio :label="4">禁用</el-radio>
                </el-radio-group>
                <p class="form-tip">
                  开启：订单在出库前客户可以点击取消订单按钮；
                  <br />
                  禁用：只有待审核的订单客户才能操作取消订单按钮；
                </p>
              </el-form-item>
              <el-form-item prop="cancelOrderAudit" label="取消订单审核:">
                <el-radio-group v-model="basicData.cancelOrderAudit">
                  <el-radio :label="5">启用</el-radio>
                  <el-radio :label="4">禁用</el-radio>
                </el-radio-group>
                <p class="form-tip">
                  开启：客户取消订单需要系统进行审核
                  <br />
                  禁用：客户可直接进行取消订单，不需要系统审核
                </p>
              </el-form-item>
            </div>
          </div>
          <div v-if="page_tabs === '3'" class="desc-div">
            <!--          v-if="activeName === 'GoodsSet'"-->
            <div>
              <el-form-item prop="startDeliveryPrice" label="起订价：">
                <el-input-number v-model="basicData.startDeliveryPrice" :controls="false"></el-input-number>
                <p class="form-tip">当订单总额低于设置价格时，商城APP&微信小程序不允许客户提交创建订单，单位：元</p>
              </el-form-item>

              <el-form-item prop="freeExpressPrice" label="配送：">
                订单满
                <el-input-number v-model="basicData.freeExpressPrice" :controls="false"></el-input-number>
                元包邮
                <p class="form-tip">当订单总额大于等于设置价格时，订单可进行包邮，单位：元</p>
              </el-form-item>
              <el-form-item prop="calculateExpressType" label="运费规则：">
                <el-radio-group v-model="basicData.calculateExpressType">
                  <el-radio :label="4">累加运费</el-radio>
                  <el-radio :label="5">组合运费</el-radio>
                </el-radio-group>
                <el-tooltip placement="right">
                  <div slot="content">
                    累加运费:所有商品运费合计金额,作为整单运费金额
                    <br />
                    组合运费:取订单中运费最高的商品运费,作为整单运费金额
                  </div>
                  <i
                    style="position: absolute; top: 30%; left: 200px; color: rgb(192, 197, 207)"
                    class="el-icon-question"
                  ></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item prop="costPriceTimes" label="商品销售价：">
                <el-input-number v-model="basicData.costPriceTimes" :controls="false"></el-input-number>
                倍成本价
              </el-form-item>
              <el-form-item prop="salePriceTimes" label="商品市场价：">
                <el-input-number v-model="basicData.salePriceTimes" :controls="false"></el-input-number>
                倍销售价
              </el-form-item>
              <el-form-item prop="memberPriceTimes" label="商品会员价：">
                <el-input-number v-model="basicData.memberPriceTimes" :controls="false"></el-input-number>
                倍销售价
              </el-form-item>

              <el-form-item prop="goodsPrice" label="商品价格：">
                <el-radio-group v-model="basicData.goodsPrice">
                  <el-radio :label="5">允许游客查看</el-radio>
                  <el-radio :label="4">不允许游客查看</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="goodsPrice" label="价格区域保护：">
                <el-radio-group v-model="basicData.goodsPriceAreaProtection">
                  <el-radio :label="5">是</el-radio>
                  <el-radio :label="4">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="stockDisplay" label="库存展示：">
                <el-radio-group v-model="basicData.stockDisplay">
                  <el-radio :label="4">不显示库存</el-radio>
                  <el-radio :label="5">显示有/无货</el-radio>
                  <el-radio :label="6">显示库存数量</el-radio>
                </el-radio-group>
              </el-form-item>
              <!--              <el-form-item prop="phone" label="是否支持负库存销售：">-->
              <!--                <el-switch-->
              <!--                  v-model="basicData.preSale"-->
              <!--                  active-color="#36B365"-->
              <!--                  inactive-color="#ff4949"-->
              <!--                  :active-value="5"-->
              <!--                  :inactive-value="4"-->
              <!--                  active-text="启用"-->
              <!--                  inactive-text="禁用"-->
              <!--                  @change="changePre"-->
              <!--                ></el-switch>-->
              <!--              </el-form-item>-->
              <el-form-item prop="limitLevel" label="限购级别：">
                <el-radio-group v-model="basicData.limitLevel">
                  <el-radio :label="5">单笔限购</el-radio>
                  <el-radio :label="4">活动时间范围限购</el-radio>
                </el-radio-group>
                <p class="form-tip">活动商品用户购买数量</p>
              </el-form-item>
              <el-form-item prop="isSalesNum" label="销量展示：">
                <el-radio-group v-model="basicData.isSalesNum">
                  <el-radio :label="4">不显示销量</el-radio>
                  <el-radio :label="5">显示销量</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="false" prop="allowReturnDay" label="退货设置：">
                订单完成
                <el-input
                  v-model="basicData.allowReturnDay"
                  size="small"
                  style="width: 50px"
                  placeholder="请输入允许退货天数"
                ></el-input>
                天后不会允许退货
              </el-form-item>

              <!--              <el-form-item prop="phone" label="库存预警：">-->
              <!--                商品低于-->
              <!--                <el-input-number-->
              <!--                  v-model="basicData.goodsNum"-->
              <!--                  :controls="false"-->
              <!--                  size="small"-->
              <!--                  style="width: 100px"-->
              <!--                  placeholder=""-->
              <!--                ></el-input-number>-->
              <!--                时请通知我-->
              <!--                <p class="form-tip">-->
              <!--                  当库存数量不足时，库存预警能够帮助店铺及时处理库存商品-->
              <!--                </p>-->
              <!--              </el-form-item>-->
              <!--              <el-form-item-->
              <!--                prop="phone"-->
              <!--                label="自动上下架："-->
              <!--              >-->
              <!--                <el-switch-->
              <!--                  v-model="basicData.autoRemoveGoods"-->
              <!--                  active-color="#36B365"-->
              <!--                  inactive-color="#ff4949"-->
              <!--                  :active-value="5"-->
              <!--                  :inactive-value="4"-->
              <!--                  active-text="启用"-->
              <!--                  inactive-text="禁用"-->
              <!--                >-->
              <!--                </el-switch>-->
              <!--                <p class="form-tip">-->
              <!--                  开启后，商品库存为空后，自动下架该商品，否则反之-->
              <!--                </p>-->
              <!--              </el-form-item>-->
              <!--              <el-form-item prop="phone" label="开启保质期：">-->
              <!--                <el-switch-->
              <!--                  v-model="basicData.shelfLifeSetUp"-->
              <!--                  active-color="#36B365"-->
              <!--                  inactive-color="#ff4949"-->
              <!--                  :active-value="5"-->
              <!--                  :inactive-value="4"-->
              <!--                  active-text="启用"-->
              <!--                  inactive-text="禁用"-->
              <!--                ></el-switch>-->
              <!--                <p class="form-tip">-->
              <!--                  开启后，商品基础资料必填保质期,入库单必填批次生产日期,菜单展示保质期查询，否则反之-->
              <!--                </p>-->
              <!--              </el-form-item>-->
              <el-form-item prop="recommend" label="搜索推荐设置：">
                <el-tag
                  v-for="(tag, index) in basicData.recommend"
                  :key="index"
                  closable
                  :disable-transitions="false"
                  @close="handleClose(index)"
                >
                  {{ tag.name }}
                </el-tag>
                <el-input
                  v-if="inputVisible"
                  ref="saveTagInput"
                  v-model="tagVal"
                  class="input-new-tag"
                  size="small"
                  @keyup.enter.native="handleInputConfirm"
                  @blur="handleInputConfirm"
                ></el-input>
                <el-button v-else class="button-new-tag" size="small" @click="showInput"> + 新增 </el-button>
              </el-form-item>
            </div>
          </div>
          <div v-if="page_tabs === '4'" class="desc-div">
            <!--          v-if="activeName === 'CustomerSet'"-->
            <div>
              <el-form-item prop="personnelReview" label="客户审核：">
                <el-switch
                  v-model="basicData.personnelReview"
                  active-color="#36B365"
                  inactive-color="#ff4949"
                  :active-value="5"
                  :inactive-value="4"
                  active-text="启用"
                  inactive-text="禁用"
                ></el-switch>
                <p class="form-tip">开启后：必须审核过后才可以进行下单购买；禁用后:客户注册成功即代表审核通过</p>
              </el-form-item>
              <el-form-item prop="personnelReview" label="完善资料：">
                <el-switch
                  v-model="basicData.finishData"
                  active-color="#36B365"
                  inactive-color="#ff4949"
                  :active-value="5"
                  :inactive-value="4"
                  active-text="启用"
                  inactive-text="禁用"
                ></el-switch>
                <p>
                  <el-checkbox v-model="basicData.finishDataGo"> 立即去完善 </el-checkbox>
                </p>
                <p class="form-tip">开启后：客户在小程序授权完成登陆以后必须完善资料才可以进行使用</p>
              </el-form-item>
              <el-form-item label="资料内容:">
                <el-checkbox-group v-model="basicData.customerData" @change="customerDataChange">
                  <el-checkbox
                    v-for="(item, index) in customer_data_list"
                    :key="index"
                    :label="item"
                    :disabled="item === '真实姓名'"
                  >
                    {{ item }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>
          </div>
          <div v-if="page_tabs === '5'" class="desc-div">
            <el-tabs v-model="printer_tabs" type="border-card">
              <el-tab-pane label="佳博" name="gainshca">
                <el-form-item prop="printerConfig.gainshca.memberCode" label="商户编码：">
                  <el-input
                    v-model="basicData.printerConfig.gainshca.memberCode"
                    style="width: 350px"
                    placeholder="请输入商户编码"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.gainshca.deviceID" label="终端编号：">
                  <el-input
                    v-model="basicData.printerConfig.gainshca.deviceID"
                    style="width: 350px"
                    placeholder="请输入终端编号"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.gainshca.apiKey" label="商户秘钥：">
                  <el-input
                    v-model="basicData.printerConfig.gainshca.apiKey"
                    style="width: 350px"
                    placeholder="请输入商户秘钥"
                  ></el-input>
                </el-form-item>
                <el-form-item label="打印机帮助：">
                  <a href="https://www.kancloud.cn/qianniaoyunshang/houtaishiyongliucheng/1806010" target="_blank">
                    https://www.kancloud.cn/qianniaoyunshang/houtaishiyongliucheng/1806010
                  </a>
                </el-form-item>
                <el-form-item label="打印机购买：">
                  <a href="https://item.jd.com/62582739647.html" target="_blank">
                    https://item.jd.com/62582739647.html
                  </a>
                </el-form-item>
              </el-tab-pane>
              <el-tab-pane label="飞鹅" name="feie">
                <el-form-item prop="printerConfig.feie.feieUser" label="用户账号：">
                  <el-input
                    v-model="basicData.printerConfig.feie.feieUser"
                    style="width: 350px"
                    placeholder="请输入飞鹅用户账号"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.feie.feieUserKey" label="用户密钥：">
                  <el-input
                    v-model="basicData.printerConfig.feie.feieUserKey"
                    style="width: 350px"
                    placeholder="请输入用户密钥"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.feie.feieSn" label="打印机编号：">
                  <el-input
                    v-model="basicData.printerConfig.feie.feieSn"
                    style="width: 350px"
                    placeholder="请输入打印机编号"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.feie.feieKey" label="打印机密钥：">
                  <el-input
                    v-model="basicData.printerConfig.feie.feieKey"
                    style="width: 350px"
                    placeholder="请输入打印机密钥"
                  ></el-input>
                </el-form-item>
                <el-form-item label="打印机帮助：">
                  <a href="http://help.feieyun.com/" target="_blank"> http://help.feieyun.com/ </a>
                </el-form-item>
              </el-tab-pane>
              <el-tab-pane label="易联云W1" name="yilianyunW1">
                <el-form-item label="绑定状态：">
                  <el-tag :type="yilianyunBindStatus ? 'success' : 'info'">
                    {{ yilianyunBindStatus ? "已绑定" : "未绑定" }}
                  </el-tag>
                  <span v-if="configChanged" style="margin-left: 10px; color: #f56c6c; font-size: 12px">
                    <i class="el-icon-warning"></i> 配置已修改，请先保存配置
                  </span>
                </el-form-item>
                <el-form-item prop="printerConfig.yilianyunW1.client_id" label="应用ID：">
                  <el-input
                    v-model="basicData.printerConfig.yilianyunW1.client_id"
                    style="width: 350px"
                    placeholder="请输入应用ID"
                    @input="checkConfigChange"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.yilianyunW1.client_secret" label="调用凭证：">
                  <el-input
                    v-model="basicData.printerConfig.yilianyunW1.client_secret"
                    style="width: 350px"
                    placeholder="请输入调用凭证"
                    @input="checkConfigChange"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.yilianyunW1.machine_code" label="终端号：">
                  <el-input
                    v-model="basicData.printerConfig.yilianyunW1.machine_code"
                    style="width: 350px"
                    placeholder="请输入终端号"
                    @input="checkConfigChange"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.yilianyunW1.msign" label="终端密钥：">
                  <el-input
                    v-model="basicData.printerConfig.yilianyunW1.msign"
                    style="width: 350px"
                    placeholder="请输入终端密钥"
                    @input="checkConfigChange"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.yilianyunW1.qr_key" label="终端临时密钥：">
                  <el-input
                    v-model="basicData.printerConfig.yilianyunW1.qr_key"
                    style="width: 350px"
                    placeholder="请输入终端临时密钥（可选）"
                    @input="checkConfigChange"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.yilianyunW1.phone" label="流量卡号：">
                  <el-input
                    v-model="basicData.printerConfig.yilianyunW1.phone"
                    style="width: 350px"
                    placeholder="请输入流量卡号（可选）"
                    @input="checkConfigChange"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="printerConfig.yilianyunW1.print_name" label="终端昵称：">
                  <el-input
                    v-model="basicData.printerConfig.yilianyunW1.print_name"
                    style="width: 350px"
                    placeholder="请输入终端昵称（可选）"
                    @input="checkConfigChange"
                  ></el-input>
                </el-form-item>
                <el-form-item label="打印机操作：">
                  <el-button
                    v-if="!yilianyunBindStatus"
                    type="primary"
                    size="small"
                    :loading="bindLoading"
                    :disabled="configChanged"
                    style="margin-right: 10px"
                    @click="handleBindYilianyun"
                  >
                    {{ bindLoading ? "绑定中..." : "绑定" }}
                  </el-button>
                  <el-button
                    v-if="yilianyunBindStatus"
                    type="danger"
                    size="small"
                    :loading="unbindLoading"
                    :disabled="configChanged"
                    @click="handleUnbindYilianyun"
                  >
                    {{ unbindLoading ? "解绑中..." : "解绑" }}
                  </el-button>
                  <el-tooltip v-if="configChanged" content="请先保存配置后再进行绑定操作" placement="top">
                    <el-button type="info" size="small" disabled style="margin-left: 10px"> 请先保存配置 </el-button>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="打印机帮助：">
                  <a href="https://doc.yilianyun.net/" target="_blank"> https://doc.yilianyun.net/ </a>
                </el-form-item>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div v-if="page_tabs === '6'" class="desc-div">
            <el-form-item prop="enableLocationManagement" label="是否开启库区库位：">
              <el-switch
                v-model="basicData.enableLocationManagement"
                active-color="#36B365"
                inactive-color="#ff4949"
                :active-value="5"
                :inactive-value="4"
                active-text="启用"
                inactive-text="禁用"
              ></el-switch>
            </el-form-item>
            <el-form-item prop="goodsNum" label="库存预警：">
              商品低于
              <el-input-number
                v-model="basicData.goodsNum"
                :controls="false"
                size="small"
                style="width: 100px"
                placeholder=""
              ></el-input-number>
              时请通知我
              <p class="form-tip">当库存数量不足时，库存预警能够帮助店铺及时处理库存商品</p>
            </el-form-item>
            <el-form-item prop="shelfLifeSetUp" label="开启保质期：">
              <el-switch
                v-model="basicData.shelfLifeSetUp"
                active-color="#36B365"
                inactive-color="#ff4949"
                :active-value="5"
                :inactive-value="4"
                active-text="启用"
                inactive-text="禁用"
              ></el-switch>
              <p class="form-tip">开启后，商品基础资料必填保质期,入库单必填批次生产日期,菜单展示保质期查询，否则反之</p>
            </el-form-item>
            <!--            <el-form-item prop="phone" label="是否允许负库存出库：">-->
            <!--              <el-switch-->
            <!--                v-model="basicData.distributionExamine"-->
            <!--                active-color="#36B365"-->
            <!--                inactive-color="#ff4949"-->
            <!--                :active-value="5"-->
            <!--                :inactive-value="4"-->
            <!--                active-text="启用"-->
            <!--                inactive-text="禁用"-->
            <!--              ></el-switch>-->
            <!--            </el-form-item>-->
          </div>
        </el-form>
      </div>
    </ContainerTit>
  </Container>
</template>

<script>
import { getBasicSetup, setting } from "@/api/System";
import { bindYilianyunW1, getYilianyunW1BindStatus, unbindYilianyunW1 } from "@/api/common";
import RegionSelect from "@/component/common/RegionSelectJSON";
import { mapActions } from "vuex";
import UploadQiniu from "../../../component/common/UploadQiniu";

export default {
  name: "BaseSetAdd",
  components: {
    RegionSelect,
    UploadQiniu,
  },
  data() {
    return {
      customer_data_list: ["真实姓名", "电话", "地址", "客户类型", "营业执照", "备注"],
      activeName: "",
      img_list: [], // 上传公司照片
      rules: {},
      inputVisible: false,
      tagVal: "",
      autoAuditOrder: {
        wxPay: 4,
        aliPay: 4,
        cashPay: 4,
      },
      orderAutoCloseSec: "",
      orderAutoFinishSec: "",
      bindLoading: false, // 绑定按钮loading状态
      unbindLoading: false, // 解绑按钮loading状态
      yilianyunBindStatus: false, // 易联云绑定状态
      originalYilianyunConfig: null, // 原始易联云配置，用于检测变更
      configChanged: false, // 配置是否已变更
      basicData: {
        orderAutoCloseSec: "",
        isSalesNum: 5,
        orderAutoFinishSec: "",
        startDeliveryPrice: 0, // 起送价
        calculateExpressType: 5, // 运费规则 4累加 5组合
        freeExpressPrice: 0, // 包邮价
        goodsNum: "", // 库存警告
        images: [],
        costPriceTimes: 1.06,
        salePriceTimes: 1.2,
        memberPriceTimes: 1.0,
        stockDisplay: 4,
        goodsPrice: 5,
        goodsPriceAreaProtection: 5, // 价格区域保护 4不保护 ，5保护
        printTag: 4, // 订单打印标示是否展示 4不展示 ，5 展示
        returnWay: 5, //退款金额返回方式 5原路返回 ，4退回余额
        cancelOrder: 5, // 取消订单 5启用，4禁用
        cancelOrderAudit: 4, // 取消订单审核 5启用，4禁用
        allowReturnDay: 0, // 设置天数
        personnelReview: 4, // 客户审核 4禁用 5启用
        finishData: 4, // 完善资料 4禁用 5启用
        customerData: ["真实姓名", "电话", "地址", "客户类型", "营业执照", "备注"], // 自定义完善资料
        autoRemoveGoods: 4,
        shop: "",
        description: "",
        phone: "",
        company: "",
        region: [],
        address: "",
        shelfLifeSetUp: 4,
        enableLocationManagement: 4,
        preSale: 4,
        recommend: [],
        // 兼容旧版本的单一打印机配置
        memberCode: "",
        deviceID: "",
        apiKey: "",
        // 新的多打印机配置结构
        printerConfig: {
          gainshca: {
            memberCode: "",
            deviceID: "",
            apiKey: "",
          },
          feie: {
            feieUser: "",
            feieUserKey: "",
            feieSn: "",
            feieKey: "",
          },
          yilianyunW1: {
            client_id: "",
            client_secret: "",
            machine_code: "",
            msign: "",
            qr_key: "",
            phone: "",
            print_name: "",
          },
        },
        limitLevel: 4,
        // distributionExamine: 4,
        finishDataGo: false, // 完善资料登录后立即去完善
      },
      page_tabs: "1",
      printer_tabs: "gainshca", // 默认显示佳博打印机标签页
    };
  },
  async created() {
    await this.getBasicSetup();
    this.activeName = this.$route.name;
  },
  activated() {
    if (this.$_isInit()) return;
    this.activeName = this.$route.name;
    this.getBasicSetup();
  },
  methods: {
    ...mapActions({
      changeShelfLifeSetUp: "MUser/changeShelfLifeSetUp",
      changeEnableLocationManagement: "MUser/changeEnableLocationManagement",
      changePrintTag: "MUser/changePrintTag",
      changeCostPriceTimes: "MUser/changeCostPriceTimes",
      changeSalePriceTimes: "MUser/changeSalePriceTimes",
      changeMemberPriceTimes: "MUser/changeMemberPriceTimes",
      changeBaseSetting: "MUser/changeBaseSetting",
    }),
    changePre(val) {
      if (val === 5) {
        this.basicData.stockDisplay = 4;
      }
    },
    // 打开收银台
    openUrl(url) {
      let routeData = this.$router.resolve({
        path: url,
      });
      window.open(routeData.href, "_blank");
    },
    // 添加照片
    uploadSuccess(val, res, file, fileList) {
      this.basicData.images = val;
    },
    uploadRemove(file, fileList) {
      this.basicData.images = "";
    }, // 移出照片
    handleClose(index) {
      this.basicData.recommend.splice(index, 1);
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      let inputValue = this.tagVal;
      if (inputValue) {
        this.basicData.recommend.push({ name: inputValue });
      }
      this.inputVisible = false;
      this.tagVal = "";
    },
    //  获取详情
    async getBasicSetup() {
      const { data } = await getBasicSetup();

      this.basicData = { ...this.basicData, ...data.basicData };
      this.autoAuditOrder = {
        ...this.autoAuditOrder,
        ...this.basicData.autoAuditOrder,
      };
      this.orderAutoFinishSec = data.basicData.orderAutoFinishSec
        ? this.$NP.divide(data.basicData.orderAutoFinishSec, 3600)
        : "";
      this.orderAutoCloseSec = data.basicData.orderAutoCloseSec
        ? this.$NP.divide(data.basicData.orderAutoCloseSec, 3600)
        : "";
      if (this.basicData.images) {
        this.img_list = [
          {
            name: "",
            url: this.basicData.images,
          },
        ];
      } else {
        this.img_list = [];
      }
      this.changePrintTag(this.basicData.printTag || 4);
      this.changeCostPriceTimes(this.basicData.costPriceTimes || 1.06);
      this.changeSalePriceTimes(this.basicData.salePriceTimes || 1.2);
      this.changeMemberPriceTimes(this.basicData.memberPriceTimes || 1.0);

      // 保存原始易联云配置用于变更检测
      this.saveOriginalYilianyunConfig();
      // 加载易联云绑定状态
      this.loadYilianyunBindStatus();
    },
    //  提交
    async setSubmit() {
      const orderAutoFinishSec = this.$NP.times(this.orderAutoFinishSec, 3600);
      const orderAutoCloseSec = this.$NP.times(this.orderAutoCloseSec, 3600);

      // 确保向后兼容：同步新配置到旧字段
      this.syncPrinterConfigForCompatibility();

      this.basicData = {
        ...this.basicData,
        autoAuditOrder: this.autoAuditOrder,
        orderAutoCloseSec: orderAutoCloseSec,
        orderAutoFinishSec: orderAutoFinishSec,
      };

      await setting({
        basicData: this.basicData,
      });

      this.$message({
        message: "提交成功，基本设置重新提交后，其他账户需重新选择企业进入后，设置方能生效",
        type: "success",
      });
      this.changeShelfLifeSetUp(this.basicData.shelfLifeSetUp);
      this.changeEnableLocationManagement(this.basicData.enableLocationManagement);
      this.changeBaseSetting(this.basicData);

      // 重置配置变更状态
      this.configChanged = false;
      this.saveOriginalYilianyunConfig();

      await this.getBasicSetup();
    },
    // 同步打印机配置以保持向后兼容
    syncPrinterConfigForCompatibility() {
      if (this.basicData.printerConfig && this.basicData.printerConfig.gainshca) {
        // 将新的佳博配置同步到旧字段，保持向后兼容
        this.basicData.memberCode = this.basicData.printerConfig.gainshca.memberCode;
        this.basicData.deviceID = this.basicData.printerConfig.gainshca.deviceID;
        this.basicData.apiKey = this.basicData.printerConfig.gainshca.apiKey;
      }
    },
    regionChange(row) {
      this.basicData.region = row;
    },
    // 自定义客户资料
    customerDataChange() {},
    // 绑定易联云打印机
    async handleBindYilianyun() {
      // 验证必填字段
      const config = this.basicData.printerConfig.yilianyunW1;
      if (!config.client_id || !config.machine_code || !config.msign) {
        this.$message.error("请先填写完整的易联云配置信息（应用ID、终端号、终端密钥为必填项）");
        return;
      }

      // 检查配置是否已保存
      if (this.configChanged) {
        this.$message.error("配置已修改，请先保存配置后再进行绑定操作");
        return;
      }

      this.bindLoading = true;
      try {
        // 传递设备配置参数
        const requestData = {
          client_id: config.client_id,
          machine_code: config.machine_code,
          msign: config.msign,
          qr_key: config.qr_key || "",
          phone: config.phone || "",
          print_name: config.print_name || "",
        };

        const response = await bindYilianyunW1(requestData);

        // 统一处理响应格式
        if (response.errorcode === 0 || response.state === true) {
          const message = response.data?.message || response.message || "易联云打印机绑定成功";
          this.$message.success(message);
          // 刷新绑定状态
          await this.loadYilianyunBindStatus();
        } else {
          const errorMsg = response.data || response.message || "绑定失败";
          this.$message.error(errorMsg);
        }
      } catch (error) {
        console.error("绑定易联云打印机失败:", error);
        let errorMessage = "绑定失败，请检查网络连接或配置信息";

        // 尝试解析错误信息
        if (error.response && error.response.data) {
          if (typeof error.response.data === "string") {
            errorMessage = error.response.data;
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message;
          } else if (error.response.data.data) {
            errorMessage = error.response.data.data;
          }
        }

        this.$message.error(errorMessage);
      } finally {
        this.bindLoading = false;
      }
    },
    // 解绑易联云打印机
    async handleUnbindYilianyun() {
      // 验证必填字段
      const config = this.basicData.printerConfig.yilianyunW1;
      if (!config.client_id || !config.machine_code || !config.msign) {
        this.$message.error("请先填写完整的易联云配置信息（应用ID、终端号、终端密钥为必填项）");
        return;
      }

      // 检查配置是否已保存
      if (this.configChanged) {
        this.$message.error("配置已修改，请先保存配置后再进行解绑操作");
        return;
      }

      // 确认操作
      try {
        await this.$confirm("确定要解绑易联云打印机吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
      } catch {
        return; // 用户取消操作
      }

      this.unbindLoading = true;
      try {
        // 传递设备配置参数
        const requestData = {
          client_id: config.client_id,
          machine_code: config.machine_code,
          msign: config.msign,
          qr_key: config.qr_key || "",
          phone: config.phone || "",
          print_name: config.print_name || "",
        };

        const response = await unbindYilianyunW1(requestData);

        // 统一处理响应格式
        if (response.errorcode === 0 || response.state === true) {
          const message = response.data?.message || response.message || "易联云打印机解绑成功";
          this.$message.success(message);
          // 刷新绑定状态
          await this.loadYilianyunBindStatus();
        } else {
          const errorMsg = response.data || response.message || "解绑失败";
          this.$message.error(errorMsg);
        }
      } catch (error) {
        console.error("解绑易联云打印机失败:", error);
        let errorMessage = "解绑失败，请检查网络连接或配置信息";

        // 尝试解析错误信息
        if (error.response && error.response.data) {
          if (typeof error.response.data === "string") {
            errorMessage = error.response.data;
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message;
          } else if (error.response.data.data) {
            errorMessage = error.response.data.data;
          }
        }

        this.$message.error(errorMessage);
      } finally {
        this.unbindLoading = false;
      }
    },
    // 加载易联云绑定状态
    async loadYilianyunBindStatus() {
      try {
        const response = await getYilianyunW1BindStatus();
        if (response.errorcode === 0) {
          // 兼容不同的响应字段名
          this.yilianyunBindStatus = response.data.bindStatus || response.data.bind_status || false;
        } else {
          console.warn("获取易联云绑定状态失败:", response);
          // 如果获取状态失败，默认为未绑定
          this.yilianyunBindStatus = false;
        }
      } catch (error) {
        console.error("获取易联云绑定状态失败:", error);
        // 网络错误时默认为未绑定
        this.yilianyunBindStatus = false;
      }
    },
    // 保存原始易联云配置
    saveOriginalYilianyunConfig() {
      if (this.basicData.printerConfig && this.basicData.printerConfig.yilianyunW1) {
        this.originalYilianyunConfig = JSON.parse(JSON.stringify(this.basicData.printerConfig.yilianyunW1));
      }
    },
    // 检查配置是否有变更
    checkConfigChange() {
      if (!this.originalYilianyunConfig) {
        this.configChanged = false;
        return;
      }

      const currentConfig = this.basicData.printerConfig.yilianyunW1;
      this.configChanged = JSON.stringify(currentConfig) !== JSON.stringify(this.originalYilianyunConfig);
    },
  },
};
</script>

<style scoped>
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

.index-view {
  display: inline-block;
  font-size: 12px;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border: 1px solid #ddd;
  border-radius: 100%;
  text-align: center;
  margin-right: 10px;
  color: #666;
}

.desc-div {
  font-size: 12px;
  padding: 10px 20px;
  line-height: 32px;
  color: #666666;
}
</style>
