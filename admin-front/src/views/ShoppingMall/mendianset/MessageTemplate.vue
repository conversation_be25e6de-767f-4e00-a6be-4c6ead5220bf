<template>
  <Container>
    <div slot="right">
      <el-button size="small" type="primary" @click="updateSystemSettings"> 提交 </el-button>
    </div>
    <div slot="left" style="margin-bottom: 10px">
      <el-alert
        title="温馨提示"
        description="获取前请先确认您已获得模版消息的使用权限，并且模版消息中没有任何数据。获取后请不要到微信公众号后台删除相应的模版消息，否则会影响模版消息正常使用。"
        type="info"
        show-icon
        :closable="false"
      ></el-alert>
    </div>
    <el-table border :data="msg_data">
      <el-table-column prop="name" label="标题" align="center"></el-table-column>
      <el-table-column prop="templateID" label="模版ID" align="center">
        <template slot-scope="scope">
          <el-input v-model="scope.row.templateID" size="small" placeholder="请输入模版ID"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="appsecret" label="操作" align="center">
        <template>
          <el-button size="mini" @click="show_template = true"> 查看模版示例 </el-button>
          <el-button size="mini">发送测试</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="模板消息格式"
      :visible.sync="show_template"
      width="50%"
    >
      <ul class="msg-model">
        <li>
          <span class="de_label">标题：</span>
          <span class="de_val">新订单通知</span>
        </li>
        <li>
          <span class="de_label">行业：</span>
          <span class="de_val">IT科技-互联网|电子商务</span>
        </li>
        <li class="clearfix">
          <span class="de_label float_left">详细内容：</span>
          <div class="de_val float_left">
            <p style="padding-bottom: 10px">{ { first.DATA } }</p>
            <p>提交时间：{ { tradDateTime.DATA } }</p>
            <p>订单类型：{ { orderType.DATA } }</p>
            <p>客户信息：{ { customerInfo.DATA } }</p>
            <p>{ { orderItemName.DATA } }：{ { orderItemData.DATA } }</p>
            <p style="color: #999; padding-top: 10px">
              在发送时，需要将内容中的参数（ { { .DATA } } 内为参数 ）赋值替换为需要的信息
            </p>
          </div>
        </li>
        <li class="clearfix">
          <span class="de_label float_left">内容示例：</span>
          <div class="de_val float_left">
            <p style="padding-bottom: 10px">您有一笔新订单，请及时处理</p>
            <p>提交时间：2019-09-09 12:09:09</p>
            <p>订单类型：商城订单</p>
            <p>客户信息：张三</p>
            <p>商品信息：测试商品1</p>
          </div>
        </li>
      </ul>
      <span slot="footer" class="dialog-footer">
        <el-button @click="show_template = false">关 闭</el-button>
      </span>
    </el-dialog>
  </Container>
</template>

<script>
import { getSystemSettingsInfo, updateSystemSettings } from "@/api/System";
export default {
  name: "MessageTemplate",
  data() {
    return {
      show_template: false,
      dataid: 0,
      msg_data: [
        {
          name: "新订单通知",
          templateID: "",
        },
        {
          name: "发货通知",
          templateID: "",
        },
        {
          name: "订单取消通知",
          templateID: "",
        },
        {
          name: "订单售后申请",
          templateID: "",
        },
        {
          name: "退货通知",
          templateID: "",
        },
      ],
    };
  },
  created() {
    this.getSystemSettingsInfo();
  },
  methods: {
    // 获取详情
    async getSystemSettingsInfo() {
      const { data } = await getSystemSettingsInfo(2);

      if (JSON.stringify(data) !== "{}") {
        this.dataid = data.id;
        const msgData = [];
        for (let i in data.content) {
          let item = data.content[i];
          msgData.push({
            name: i,
            templateID: item,
          });
        }
        this.msg_data = msgData;
      }
    },
    // 提交
    async updateSystemSettings() {
      let content = {};
      this.msg_data.forEach((item) => {
        content[item.name] = item.templateID;
      });
      const params = {
        type: "2",
        content: content,
      };

      const data = await updateSystemSettings(this.dataid, {
        ...params,
      });

      this.$message.success("提交成功");
      this.getSystemSettingsInfo();
    },
  },
};
</script>

<style scoped>
.msg-model > li {
  padding: 10px 0;
  border-bottom: 1px solid #ddd;
}
.msg-model > li .de_label {
  width: 100px;
  display: inline-block;
}
.msg-model > li .de_val {
  padding-left: 30px;
}
</style>
