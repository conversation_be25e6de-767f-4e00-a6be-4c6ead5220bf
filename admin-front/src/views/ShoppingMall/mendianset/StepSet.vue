<template>
  <ContainerTit>
    <Container></Container>
    <div v-if="$accessCheck($Access.StepSetSet)" slot="headr">
      <el-button type="primary" :loading="loading" @click="subDate"> 提交保存 </el-button>
    </div>
    <div class="detail-tab-item" style="margin-top: 0">
      <div class="detail-tab-title">流程设置提示</div>
      <div class="detail-li-main">
        <ul class="set-tip">
          <li class="set-tip-li">
            <span class="set-tag"></span>
            <span>表示已开启此步骤</span>
          </li>
          <li class="set-tip-li">
            <span class="set-tag oval-def"></span>
            <span>表示已关闭此步骤</span>
          </li>
          <li class="set-tip-li">
            <span class="set-tag">
              <i class="el-icon-lock"></i>
            </span>
            <span>表示此步骤默认开启，不允许调整</span>
          </li>
        </ul>
      </div>
    </div>
    <div v-if="order_step.length" class="detail-tab-item">
      <div class="detail-tab-title">销售订单设置</div>
      <div class="detail-li-main">
        <div class="step-ul clearfix">
          <div
            class="step-tag"
            :class="[
              order_step[0].content.submit.enableStatus !== 5 ? 'step-tag-deff' : '',
              order_step[0].content.submit.lock ? 'pointer' : '',
            ]"
            @click="
              order_step[0].content.submit.enableStatus === 5
                ? (order_step[0].content.submit.enableStatus = 4)
                : (order_step[0].content.submit.enableStatus = 5)
            "
          >
            {{ order_step[0].content.submit.name }}
            <i v-if="order_step[0].content.submit.lock" class="el-icon-lock step-lock"></i>
          </div>
          <div class="arrow-flow"></div>
          <div
            class="step-tag"
            :class="[
              order_step[0].content.audit.enableStatus !== 5 ? 'step-tag-deff' : '',
              order_step[0].content.audit.lock ? 'pointer' : '',
            ]"
            @click="
              order_step[0].content.audit.enableStatus === 5
                ? (order_step[0].content.audit.enableStatus = 4)
                : (order_step[0].content.audit.enableStatus = 5)
            "
          >
            {{ order_step[0].content.audit.name }}
            <i v-if="order_step[0].content.audit.lock" class="el-icon-lock step-lock"></i>
          </div>
          <div class="arrow-flow"></div>
          <div
            class="step-tag"
            :class="[
              order_step[0].content.out.enableStatus !== 5 ? 'step-tag-deff' : '',
              order_step[0].content.out.lock ? 'pointer' : '',
            ]"
            @click="
              order_step[0].content.out.enableStatus === 5
                ? (order_step[0].content.out.enableStatus = 4)
                : (order_step[0].content.out.enableStatus = 5)
            "
          >
            {{ order_step[0].content.out.name }}
            <i v-if="order_step[0].content.out.lock" class="el-icon-lock step-lock"></i>

            <el-tooltip
              effect="dark"
              content="商品库存在出库流程完成后扣减。如需进行库存管理需要开启此节点。"
              placement="bottom"
            >
              <i class="el-icon-question tips-box"></i>
            </el-tooltip>
          </div>
          <div class="arrow-flow"></div>
          <!--          <span>-->
          <!--            <div-->
          <!--              class="step-tag"-->
          <!--              :class="[-->
          <!--                order_step[0].content.receivd.enableStatus !== 5-->
          <!--                  ? 'step-tag-deff'-->
          <!--                  : '',-->
          <!--                order_step[0].content.receivd.lock ? 'pointer' : '',-->
          <!--              ]"-->
          <!--              @click="-->
          <!--                order_step[0].content.receivd.enableStatus === 5-->
          <!--                  ? (order_step[0].content.receivd.enableStatus = 4)-->
          <!--                  : (order_step[0].content.receivd.enableStatus = 5)-->
          <!--              "-->
          <!--            >-->
          <!--              {{ order_step[0].content.receivd.name }}-->
          <!--              <i-->
          <!--                v-if="order_step[0].content.receivd.lock"-->
          <!--                class="el-icon-lock step-lock"-->
          <!--              ></i>-->
          <!--            </div>-->
          <!--            <div class="arrow-flow"></div>-->
          <!--          </span>-->
          <div
            class="step-tag"
            :class="[
              order_step[0].content.finish.enableStatus !== 5 ? 'step-tag-deff' : '',
              order_step[0].content.finish.lock ? 'pointer' : '',
            ]"
            @click="
              order_step[0].content.finish.enableStatus === 5
                ? (order_step[0].content.finish.enableStatus = 4)
                : (order_step[0].content.finish.enableStatus = 5)
            "
          >
            {{ order_step[0].content.finish.name }}
            <i v-if="order_step[0].content.finish.lock" class="el-icon-lock step-lock"></i>
          </div>
        </div>
      </div>
    </div>
    <div v-if="false" class="detail-tab-item">
      <div class="detail-tab-title">退货单设置</div>
      <div class="detail-li-main">
        <div class="step-ul clearfix">
          <div
            class="step-tag"
            :class="[
              order_step[1].content.submit.enableStatus !== 5 ? 'step-tag-deff' : '',
              order_step[1].content.submit.lock ? 'pointer' : '',
            ]"
            @click="
              order_step[1].content.submit.enableStatus === 5
                ? (order_step[1].content.submit.enableStatus = 4)
                : (order_step[1].content.submit.enableStatus = 5)
            "
          >
            {{ order_step[1].content.submit.name }}
            <i v-if="order_step[1].content.submit.lock" class="el-icon-lock step-lock"></i>
          </div>
          <div class="arrow-flow"></div>
          <div
            class="step-tag"
            :class="[
              order_step[1].content.audit.enableStatus !== 5 ? 'step-tag-deff' : '',
              order_step[1].content.audit.lock ? 'pointer' : '',
            ]"
            @click="
              order_step[1].content.audit.enableStatus === 5
                ? (order_step[1].content.audit.enableStatus = 4)
                : (order_step[1].content.audit.enableStatus = 5)
            "
          >
            {{ order_step[1].content.audit.name }}
            <i v-if="order_step[1].content.audit.lock" class="el-icon-lock step-lock"></i>
          </div>
          <div class="arrow-flow"></div>
          <div
            class="step-tag"
            :class="[
              order_step[1].content.in.enableStatus !== 5 ? 'step-tag-deff' : '',
              order_step[1].content.in.lock ? 'pointer' : '',
            ]"
            @click="
              order_step[1].content.in.enableStatus === 5
                ? (order_step[1].content.in.enableStatus = 4)
                : (order_step[1].content.in.enableStatus = 5)
            "
          >
            {{ order_step[1].content.in.name }}
            <i v-if="order_step[1].content.in.lock" class="el-icon-lock step-lock"></i>
          </div>
          <div class="arrow-flow"></div>
          <div
            class="step-tag"
            :class="[
              order_step[1].content.finish.enableStatus !== 5 ? 'step-tag-deff' : '',
              order_step[1].content.finish.lock ? 'pointer' : '',
            ]"
            @click="
              order_step[1].content.finish.enableStatus === 5
                ? (order_step[1].content.finish.enableStatus = 4)
                : (order_step[1].content.finish.enableStatus = 5)
            "
          >
            {{ order_step[1].content.finish.name }}
            <i v-if="order_step[1].content.finish.lock" class="el-icon-lock step-lock"></i>
          </div>
        </div>
      </div>
    </div>
  </ContainerTit>
</template>

<script>
import { getAllProcessSetting, setAllProcessSetting } from "@/api/System";
export default {
  name: "StepSet",
  data() {
    return {
      loading: false,
      order_step: [],
    };
  },
  created() {
    this.getAllProcessSetting();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getAllProcessSetting();
  },
  methods: {
    async getAllProcessSetting() {
      if (!this.$accessCheck(this.$Access.StepSetGetAll)) {
        return;
      }
      const data = await getAllProcessSetting();
      this.order_step = data.data;
    },

    async subDate() {
      this.loading = true;
      const data = await setAllProcessSetting({
        ...this.order_step,
      });
      this.loading = false;
      this.$message.success("保存成功");
      this.getAllProcessSetting();
    },
  },
};
</script>
<style>
.StepSet .el-alert__content {
  display: block;
  padding: 0;
  width: 100%;
}
</style>
<style scoped>
.StepSet {
  width: 98%;
  margin: 0 auto 20px;
}

.set-tip {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
}

.set-tip-li {
  width: 24%;
  color: #333333;
  line-height: 24px;
}
.set-tag {
  display: inline-block;
  text-align: center;
  line-height: 24px;
  width: 24px;
  height: 24px;
  margin-bottom: 0;
  border-radius: 50%;
  background-color: #20ade5;
  color: #ffffff;
  margin-right: 10px;
  vertical-align: middle;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.18), inset 0 3px 1px 0 rgba(73, 203, 255, 0.8);
}

.oval-def {
  background-color: #ffffff;
  border: 2px solid #20ade5;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.18);
}

.arrow-flow {
  display: inline-flex;
  vertical-align: middle;
  width: 6%;
  height: 8px;
  margin: 0 10px 0;
  justify-content: flex-start;
  align-content: center;
  align-items: center;
}

.arrow-flow:before {
  content: "";
  flex: 1;
  height: 0;
  border-bottom: 1px dashed #20ade5;
}

.arrow-flow:after {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 8px solid #20ade5;
  border-top: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.step-tag {
  display: inline-block;
  position: relative;
  width: 66px;
  height: 66px;
  margin-bottom: 12px;
  border-radius: 50%;
  background-color: #20ade5;
  color: #ffffff;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.18), inset 0 3px 1px 0 rgba(73, 203, 255, 0.8);
  line-height: 64px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  border: 2px solid #20ade5;
}

.step-ul .tips-box {
  position: absolute;
  bottom: -26px;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  line-height: 1;
  font-size: 16px;
  color: #999999;
}

.detail-li-main {
  padding: 10px 16px;
}

.step-lock {
  position: absolute;
  bottom: 10px;
  font-weight: bold;
  left: 50%;
  transform: translateX(-50%);
}

.step-tag-deff {
  background-color: #ffffff;
  border: 2px solid #20ade5;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.18);
  color: #20ade5;
}

.detail-tab-item {
  margin-top: 20px;
}
.pointer {
  pointer-events: none;
  cursor: not-allowed;
}
</style>
