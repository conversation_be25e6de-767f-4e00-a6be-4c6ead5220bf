<template>
  <Container>
    <div v-if="$accessCheck($Access.AnnouncementAddAnnouncement)" slot="left">
      <el-button size="small" type="primary" @click="openModel(false)"> 新增公告 </el-button>
    </div>

    <el-table :data="notice_list">
      <el-table-column prop="title" label="公告名称"></el-table-column>
      <el-table-column prop="content" label="公告详情" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="type" label="公告类型">
        <template slot-scope="scope">
          <span v-if="scope.row.type === 5">商城公告</span>
          <span v-else>企业公告</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.AnnouncementOnAnnouncement)"
            v-model="scope.row.status"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="statusSet($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.status === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="是否弹出">
        <template slot-scope="scope">
          <el-switch
            v-if="$accessCheck($Access.AnnouncementOnAnnouncement)"
            v-model="scope.row.upStatus"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            @change="upAnnouncement($event, scope.row)"
          ></el-switch>
          <div v-else>
            <span v-if="scope.row.status === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            v-if="$accessCheck($Access.AnnouncementEditAnnouncement)"
            type="text"
            @click="openModel(true, scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="$accessCheck($Access.AnnouncementDelAnnouncement)"
            type="text"
            @click="delData(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :title="(is_edit ? '编辑' : '新增') + '公告'"
      :visible.sync="show_model"
      width="40%"
    >
      <el-form ref="add_form" :model="add_form" size="small" label-width="100px">
        <el-form-item label="公告类型：">
          <el-select v-model="add_form.type" placeholder="公告类型" style="width: 150px" clearable>
            <el-option label="商城通知" :value="5"></el-option>
            <el-option label="企业公告" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公告名称：" prop="title">
          <el-input v-model="add_form.title" placeholder="请输入公告名称" show-word-limit maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="公告内容：" prop="content">
          <el-input
            v-model="add_form.content"
            type="textarea"
            placeholder="请输入内容"
            maxlength="120"
            :rows="8"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="是否启用：">
          <el-radio-group v-model="add_form.status">
            <el-radio :label="5">是</el-radio>
            <el-radio :label="4">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否弹出：">
          <el-radio-group v-model="add_form.upStatus">
            <el-radio :label="5">是</el-radio>
            <el-radio :label="4">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="show_model = false">取 消</el-button>
        <el-button type="primary" size="small" @click="addData"> 确 定 </el-button>
      </span>
    </el-dialog>
  </Container>
</template>

<script>
import {
  getAllAnnouncement,
  addAnnouncement,
  editAnnouncement,
  delAnnouncement,
  onAnnouncement,
  upAnnouncement,
} from "@/api/System";
export default {
  name: "NoticeLsit",
  data() {
    return {
      pageSize: 10,
      page: 1,
      total: 0,
      show_model: false,
      is_edit: false,
      add_form: {
        title: "",
        content: "",
        status: 5,
        upStatus: 5,
        type: "",
      },
      notice_id: 0,
      notice_list: [],
    };
  },
  created() {
    this.getData();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    async getData() {
      const data = await getAllAnnouncement({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.notice_list = data.data;
      this.total = data.pageTotal;
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    pageChange(page) {
      this.page = page;
      this.getData();
    },
    openModel(isEdit, row) {
      this.add_form = {
        type: "",
        title: "",
        content: "",
        status: 5,
        upStatus: 5,
      };
      this.show_model = true;
      this.is_edit = isEdit;
      if (row) {
        this.notice_id = row.id;
        this.add_form = {
          type: row.type,
          title: row.title,
          content: row.content,
          status: row.status,
          upStatus: row.upStatus,
        };
      }
    },
    async addData() {
      if (!this.add_form.type || !this.add_form.title.trim() || !this.add_form.content.trim()) {
        this.$message.warning("所有项不能为空");
        return;
      }
      let target = {};
      if (!this.is_edit) {
        target = await addAnnouncement({
          ...this.add_form,
        });
      } else {
        target = await editAnnouncement(this.notice_id, {
          ...this.add_form,
        });
      }
      const data = target;

      this.show_model = false;
      this.$message({
        type: "success",
        message: "提交成功!",
      });
      this.pageChange(1);
    },
    delData(id) {
      this.$confirm("确定要删除此公告吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const data = await delAnnouncement(id);
        await this.getData();
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
    async statusSet(val, row) {
      try {
        const data = await onAnnouncement(row.id);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        await this.getData();
      }
    },
    async upAnnouncement(val, row) {
      try {
        const data = await upAnnouncement(row.id);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        await this.getData();
      }
    },
  },
};
</script>

<style scoped></style>
