<template>
  <Container>
    <div slot="left">
      <el-button type="primary" size="small" @click="configure"> 一键配置 </el-button>
      <el-button type="primary" size="small" @click="subMessage"> 保存 </el-button>
    </div>
    <div class="page-tip-div" style="margin-top: 0">
      <p>
        温馨提示：1.获取前请先确认您已获得订阅消息的实用权限，并且订阅消息中没有任何数据。获取后请不要到微信小程序后台
        删除相应的订阅消息，否则会影响订阅消息正常使用。
      </p>
      <p>　2.请在小程序后台添加类目:"生活服务---->百货/线下超市/便利店"</p>
    </div>
    <el-table :data="tableData" style="width: 100%; padding: 0 10px">
      <el-table-column prop="title" label="模板名" min-width="80px"></el-table-column>
      <el-table-column prop="tmplId" label="模板ID" min-width="120px">
        <template slot-scope="scope">
          <el-input v-model="scope.row.tmplId" :controls="false" size="small" readonly></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" min-width="100px">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="editNews(scope.row)"> 预览 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--    <FooterPage-->
    <!--      :page-size="pageSize"-->
    <!--      :total-page.sync="total"-->
    <!--      :current-page.sync="page"-->
    <!--      @pageChange="pageChange"-->
    <!--      @sizeChange="sizeChange"-->
    <!--    ></FooterPage>-->
    <el-dialog title="下单成功通知" :visible.sync="dialogVisible" width="40%">
      <span>
        <el-form
          ref="add_form"
          :model="add_form"
          label-width="100px"
          size="small"
          style="align-content: center; display: flex"
        >
          <div style="display: inline-block">
            <el-form-item>
              <img :src="add_form.img" alt="" style="width: 240px; height: 240px" />
            </el-form-item>
          </div>
          <div style="display: inline-block">
            <el-form-item label="模板名称:">
              {{ add_form.title }}
            </el-form-item>
            <el-form-item label="场景说明:">
              {{ add_form.sceneDesc }}
            </el-form-item>
            <el-form-item label="模板编号:">
              {{ add_form.tid }}
            </el-form-item>
          </div>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false"> 确定 </el-button>
      </span>
    </el-dialog>
  </Container>
</template>

<script>
import { saveTemplateMessage, getTemplateMessage, autoSystemTemplateMessage } from "@/api/System";
export default {
  name: "NewsTemplate",
  data() {
    return {
      search_form: {
        status: "",
        name: "",
      },
      add_form: {
        title: "",
        sceneDesc: "",
        tid: "",
      },
      new_status: true,
      dialogVisible: false,
      pageSize: 10,
      page: 1,
      total: 0,
      target_id: "",
      tableData: [{}],
      newa_status: [
        {
          value: 1,
          label: "开启",
        },
        {
          value: 2,
          label: "关闭",
        },
      ],
    };
  },
  created() {
    this.getTemplateMessage();
  },
  methods: {
    pageChange(page) {
      this.page = page;
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    editNews(row) {
      this.dialogVisible = true;
      this.add_form = row;
      console.log(this.add_form);
    },
    // 一键配置
    async configure() {
      const data = await autoSystemTemplateMessage();
      this.$message.success("配置成功");
      this.getTemplateMessage();
    },
    // 获取详情
    async getTemplateMessage() {
      const data = await getTemplateMessage();
      this.tableData = data.data;
    },
    // 保存
    async subMessage() {
      let obj = {};
      this.tableData.forEach((item) => {
        obj[item.id] = item.tmplId;
      });
      const data = await saveTemplateMessage(obj);
      this.$message.success("保存成功");
      this.getTemplateMessage();
    },
  },
};
</script>

<style scoped></style>
