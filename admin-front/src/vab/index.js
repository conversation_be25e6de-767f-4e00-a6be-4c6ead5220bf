import Vue from "vue";

// 加载图标
import "@/icon";
// 加载样式
import "./styles/vab.scss";
// 权限
import { checkActionAccess } from "@/access/check";
import * as nodes from "@/access/node";
// 分页
import FooterPage from "@/component/common/FooterPage";
//页面布局
import ContainerTit from "@/component/layout/ContainerTit";
import Container from "@/component/layout/Container";
import ContainerQuery from "@/component/layout/ContainerQuery";

// 数字精度计算
import NP from "number-precision";
// 混入常用字段
import mixinP from "@/mixin";
// vue 打印
import Print from "vue-print-nb";
// vue 拖拽组件
import VueDND from "awe-dnd";
//WebSocket封装方法
// import * as socketApi from "@/utils/socket";
// Vue.prototype.socketApi = socketApi;
import "xe-utils";
Vue.use(VueDND);
Vue.use(Print);
Vue.prototype.$NP = NP; // 高精度数学计算
Vue.prototype.$accessCheck = checkActionAccess;
Vue.prototype.$Access = nodes;
Vue.component("FooterPage", FooterPage);
Vue.component("ContainerTit", ContainerTit);
Vue.component("Container", Container);
Vue.component("ContainerQuery", ContainerQuery);

Vue.mixin(mixinP);
// 关闭当前路由跳转编辑路由
Vue.prototype.$closeCurrentGoEdit = function (goPath = "/") {
  if (typeof goPath !== "string") return;
  goPath = goPath.trim();
  goPath = goPath.charAt(0) === "/" ? goPath : `/${goPath}`;
  const path = this.$route.path;
  this.$store.dispatch("tagsBar/delVisitedRoute", path);
  setTimeout(() => {
    // console.log(goPath, path)
    if (goPath === path) {
      // 关闭当前页面再打开当前页面
      this.$router.back();
    } else {
      // 关闭当前页面打开其他页面
      this.$router.push({ path: goPath }, () => {});
    }
  }, 1);
};

// 加载组件
const requireComponent = require.context("./components", true, /\.vue$/);
requireComponent.keys().forEach((fileName) => {
  const componentConfig = requireComponent(fileName);
  const componentName = componentConfig.default.name;
  Vue.component(componentName, componentConfig.default || componentConfig);
});
// 混入初始化方法，判断当前进入页面是否是首次进入，首次进入不执行 activated
Vue.mixin({
  methods: {
    $_isInit() {
      if (!this.$___init) {
        this.$___init = true;
        return true;
      }
    },
  },
});
// 加载插件
const requirePlugin = require.context("./plugins", true, /\.js$/);
requirePlugin.keys().forEach((fileName) => {
  requirePlugin(fileName);
});
