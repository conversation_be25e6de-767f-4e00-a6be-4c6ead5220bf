/**

 * @description 全局样式
 */

@import "./normalize.scss";
@import "./transition.scss";
@import "./loading.scss";
@import "./themes/glory.scss";
@import "./themes/green.scss";
@import "./themes/ocean.scss";
@import "./themes/white.scss";

$base: ".vab";

@mixin base-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border: 3px solid transparent;
    border-radius: 7px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144, 147, 153, 0.5);
  }
}
@font-face {
  font-family: DIN-Medium;
  src: url("./DIN-Medium-2.otf");
}
html {
  body {
    position: relative;
    box-sizing: border-box;
    height: 100vh;
    padding: 0;
    margin: 0;
    /*"DIN-Medium", DIN, "Noto Sans SC",*/
    font-family: DIN-Medium,SourceHanSansCN-Normal, SourceHanSansCN,"Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", Arial, sans-serif;
    font-size: $base-font-size-default;
    color: #2D405E;
    background: #f6f8f9;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    @include base-scrollbar;

    * {
      box-sizing: border-box;
      @include base-scrollbar;
    }

    /*a标签 */
    a {
      color: $base-color-blue;
      text-decoration: none;
    }

    ul,
    p,
    pre {
      margin: 0;
      padding: 0;
    }

    /*图片 */
    img {
      object-fit: cover;
    }

    /*图标 */
    svg,
    i {
      &:hover {
        opacity: 0.9;
      }
    }

    /*el-button按钮 */
    .el-button {
      margin-left: 10px;
      border-radius: 4px;
    }
    span + span,
    a + a,
    span + a {
      .el-button {
        margin-left: 10px;
      }
    }
    .ui-btn{
      display: inline-block;
      vertical-align: middle;
      padding: 0 10px;
      text-align: center;
      height: 22px;
      line-height: 22px;
      background: #F5F5F5;
      border-radius: 4px;
      border: 1px solid #CAD0D7;
      font-size: 12px;
      color: #2D405E;
    }
    /*el-scrollbar滚动条 */
    .el-scrollbar {
      height: 100%;
    }

    /*el-form表单 */
    .el-form--label-top {
      .el-form-item__label {
        padding: 0;
      }
    }

    /*  el-badge */
    .el-badge__content {
      border: 0;
    }

    /* el-tag */
    .el-tag + .el-tag {
      margin-left: 10px;
    }

    /* el-alert */
    .el-alert {
      margin: 0 0 $base-padding 0;
    }

    /* markdown编辑器*/
    .editor-toolbar {
      .no-mobile,
      .fa-question-circle {
        display: none;
      }
    }

    /* el-divider间隔线 */
    .el-divider--horizontal {
      margin: 8px 0 $base-padding + 8px 0;

      .el-divider__text {
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }

    /* el-image-viewer表格大图展示*/
    .el-image-viewer {
      &__close {
        .el-icon-circle-close {
          color: $base-color-white;
        }
      }
    }

    .vue-admin-beautiful-wrapper {
      .app-main-container {
        @include base-scrollbar;

        > [class*="-container"] {
          padding: $base-padding;
          background: $base-color-white;
        }
      }
    }

    /* nprogress进度条 */
    #nprogress {
      position: fixed;
      z-index: $base-z-index;

      .bar {
        background: $base-color-blue !important;
      }

      .peg {
        box-shadow: 0 0 10px $base-color-blue, 0 0 5px $base-color-blue !important;
      }
    }
    .el-button-group{
      border-radius: 6px;
      overflow: hidden;
    }
    .vxe-table--render-default{
      font-family: DIN-Medium, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", Arial, sans-serif;
    }
    /* 修改开关的样式 */
    .switchStyle{
      .el-switch__label {
        position: absolute;
        display: none;
        color: #fff !important;
      }
      .el-switch__label--left {
        z-index: 1;
        left: 6px;
        margin-right: 0 !important;
        margin-left: 12px !important;

      }
      .el-switch__label--right {
        z-index: 1;
        left: -14px;
        margin-right: 0 !important;
        transform: translateX(8px);
      }
      .el-switch__label {
        &.is-active{
          display: block;
        }
      }
      &.el-switch{
        .el-switch__label,.el-switch__core{
          width: 50px !important;
        }
        .el-switch__label *{
          font-size: 12px !important;
        }
      }
    }
    /* el-table表格 */
    .el-table {
      .el-table__body-wrapper {
        @include base-scrollbar;
      }
      td,
      th {
        position: relative;
        box-sizing: border-box;
        padding: 12px 0;

        .cell {
          font-size: $base-font-size-default;
          //font-size: 12px;
          //font-weight: normal;
          color: #2D405E;
          .el-dropdown-link {
            color: $base-color-blue;
            margin-left: 10px;
            cursor: pointer;
          }
          .el-image {
            width: 34px;
            height: 34px;
            border-radius: $base-border-radius;
            display: block;
          }
          .goods-name-view {
            width: calc(100% - 56px);
            margin-left: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
          }
          p {
            margin: 0;
          }
          .el-button--text{
            padding: 0 8px;
            border-right: 1px solid rgba(0, 0, 0, 0.09);
            margin-left: 0;
            font-size: 14px;
            &:first-child{
              padding-left: 0;
            }
            &:last-child{
              border-right: 0;
            }
          }
        }
      }
      th {
        background: #ECF0F7;
        .cell{
          font-weight: 500;
        }
      }
    }
    /* vxe-table表格 */
    .vxe-table {
      .vxe-table--body-wrapper {
        @include base-scrollbar;
      }
      td,
      th {
        position: relative;
        box-sizing: border-box;
        padding: 13px 0 !important;

        .vxe-cell {
          font-size: $base-font-size-default;
          //font-size: 14px;
          //font-weight: normal;
          color: #2D405E;
          .el-dropdown-link {
            color: $base-color-blue;
            margin-left: 10px;
            cursor: pointer;
          }
          .el-image {
            width: 34px;
            height: 34px;
            border-radius: $base-border-radius;
            display: block;
          }
          .goods-name-view {
            width: calc(100% - 56px);
            margin-left: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
          }
          p {
            margin: 0;
          }
          .el-button--text{
            padding: 0 8px;
            border-right: 1px solid rgba(0, 0, 0, 0.09);
            margin-left: 0;
            font-size: 14px;
            &:first-child{
              padding-left: 0;
            }
            &:last-child{
              border-right: 0;
            }
          }
          /* 修改列表里面开关的样式 */
          .switchStyle{
            .el-switch__label {
              position: absolute;
              display: none;
              color: #fff !important;
            }
            .el-switch__label--left {
              z-index: 1;
              left: 6px;
              margin-right: 0 !important;
              margin-left: 12px !important;

            }
            .el-switch__label--right {
              z-index: 1;
              left: -14px;
              margin-right: 0 !important;
              transform: translateX(8px);
            }
            .el-switch__label {
              &.is-active{
                display: block;
              }
            }
            &.el-switch{
              .el-switch__label,.el-switch__core{
                width: 50px !important;
              }
              .el-switch__label *{
                font-size: 12px !important;
              }
            }
          }
        }
      }
      th {
        background: #ECF0F7;
        .cell{
          font-weight: 500;
        }
      }
    }
    /* el-pagination分页 */
    .el-pagination {
      padding: 2px 5px;
      margin: $base-padding 0 0 0;
      font-weight: normal;
      color: $base-color-black;
      text-align: center;
    }

    /* el-menu菜单开始 */
    .el-menu {
      user-select: none;
    }

    /* el-dialog 弹窗 */
    @media (max-width: 576px) {
      .el-dialog {
        width: auto !important;
        margin: 10px;
      }
    }

    .el-dialog,
    .el-message-box {
      &__footer {
        padding: $base-padding;
        text-align: right;
      }

      &__content {
        padding: $base-padding;
      }
    }

    /* el-card卡片 */
    .el-card {
      margin-bottom: $base-padding;

      &__body {
        padding: $base-padding;
      }
    }
    /* el-tab */
    .contmain{
      .el-tabs--card > .el-tabs__header .el-tabs__nav{
        border: 0 none;
        padding-top: 12px;
        .el-tabs__item{
          padding: 0 16px;
          height: 36px;
          line-height: 36px;
          color: #4F5E7B;
          background: #ECF0F7;
          margin-left: 8px;
          border: 1px solid #DEE2EE;
          border-radius: 2px 2px 0px 0px;
          font-weight: 400;
          &.is-active{
            background: transparent;
            color: #2D405E;
            border-bottom-color: #ffffff;
            font-weight: 500;
          }
        }
      }

    }
    /* 状态颜色 */
    .success-status{
      &:before{
        content: ' ';
        display: inline-block;
        width: 8px;
        height: 8px;
        background: $base-color-green;
        border-radius: 100%;
        margin-right: 7px;
      }
    }
    .danger-status{
      &:before{
        content: ' ';
        display: inline-block;
        width: 8px;
        height: 8px;
        background: $base-color-red;
        border-radius: 100%;
        margin-right: 7px;
      }
    }
    .info-status{
      &:before{
        content: ' ';
        display: inline-block;
        width: 8px;
        height: 8px;
        background: $base-color-info;
        border-radius: 100%;
        margin-right: 7px;
      }
    }
    .warning-status{
      &:before{
        content: ' ';
        display: inline-block;
        width: 8px;
        height: 8px;
        background: $base-color-orange;
        border-radius: 100%;
        margin-right: 7px;
      }
    }
    .primary-status{
      &:before{
        content: ' ';
        display: inline-block;
        width: 8px;
        height: 8px;
        background: $base-color-blue;
        border-radius: 100%;
        margin-right: 7px;
      }
    }
    .el-dialog{
      text-align: left !important;
    }
    .el-input__inner{
      border-radius: 4px !important;
    }
    .el-input-number .el-input__inner{
      text-align: left !important;
    }
  }
}
