/*----------初始化文件----------*/
@charset "utf-8";
/* CSS BillTemplate */
*, :after, :before {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

/*全局变灰*/
/*html {*/
/*  -webkit-filter: grayscale(100%);*/
/*  -moz-filter: grayscale(100%);*/
/*  -ms-filter: grayscale(100%);*/
/*  -o-filter: grayscale(100%);*/
/*  filter: grayscale(100%);*/
/*  filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);*/
/*}*/

a:focus, a:hover {
  text-decoration: none;
}

button {
  outline: none;
}

table {
  border-collapse: collapse; /*让表格边框细线*/
  border-spacing: 0; /*清除边框间距*/
}

fieldset, img {
  border: 0 none; /*有些浏览器默认这些标签有边框，所以要清除默认边框*/
  display: block;
}

address, caption, cite, code, dfn, em, i, u, b, strong, th, var {
  font-style: normal;
  font-weight: normal;
  /*清除标签默认文本样式和加粗*/
}

input[type='submit'] {
  outline: 0 none; /*去掉文本框的默认轮廓线*/
  border: 0 none;
}

input, textarea {
  outline: 0 none; /*去掉文本框的默认轮廓线*/
  border: 0 none;
}

ol, ul {
  list-style: none; /*清除列表默认样式*/
}

caption, th {
  text-align: left; /*清除标签默认文本居中对齐*/
}

a {
  text-decoration: none; /*大部分页面中的链接没有下划线*/
}

img {
  display: inline-block;
}

.clearfix:after {
  display: block;
  clear: both;
  content: "";
  visibility: hidden;
  height: 0;
}

.clearfix {
  zoom: 1;
}

.float_left {
  float: left
}

.float_right {
  float: right
}

.wid200 {
  width: 200px !important;
}

/*详情布局样式*/
.detail-tab-item {
  width: 100%;
  /*border: 1px solid #eeeeee;*/
  padding-bottom: 20px;
  margin: 0 auto 12px;
  background-color: #ffffff;
  border-radius: 3px;
}

.detail-tab-title {
  color: #333333;
  font-weight: bold;
  padding: 8px 20px;
  line-height: 30px;
  font-size: 14px;
  margin-bottom: 10px;
  border-bottom: 1px solid #ECF0F7;
  /*background-color: #f7f7f7;*/
}

.detail-tab-main {
  padding: 0 12px;
}
.dropdown-div {
  width: 100%;
}
/*提示*/
.page-tip-div {
  background-color: rgb(236, 245, 255);
  color: rgba(0,0,0,.85);
  font-size: 13px;
  padding: 8px 16px;
  margin-bottom: 10px;
  border-radius: 3px;
  border: 1px solid rgb(102, 177, 255);
}
.page-tip-div .el-icon-info{
  color: #1890ff;
  margin-right: 5px;
}
.form-tip {
  font-size: 12px;
  color: #777777;
  line-height: 20px;
}

/*列表点击*/
.click-div {
  cursor: pointer;
  text-decoration: underline;
}

.click-div:hover {
  opacity: 0.7;
  /*text-decoration: underline;*/
}

.order-info-p{
  padding-bottom: 4px;
  height: 34px;
}
.order-info-label{
  color: #888;
}
.de_label{
  font-size: 13px;
  color: #6a6a6a;
  margin-right: 8px;
}
/*列表批量按钮*/
.foot-btn-div .el-button{
  margin-left: 10px !important;
}
/*列表展示图片*/
.list-img{
  width: 50px; height: 50px; background-color: #f4f4f4;border-radius: 4px
}
.page-div{
  padding: 20px;
  background-color: #ffffff;
  border-radius: 3px;
}
/*抽屉*/
.el-drawer__body{
  overflow-y: auto !important;
}
.dis-inline{
  display: inline-block;
}
/*表格*/
.tree-table [class*="el-table__row--level"] .el-table__expand-icon {
  color: #000;
  font-size: 16px;
}



.el-table .success-row {
  background: #f0f9eb;
}
.el-table .warning-row {
  background: oldlace;
}
.el-table .info-row {
  background: #dcdfe6;
}
.el-table .danger-row {
  background: rgb(253, 226, 226);
}
.el-table .print-row {
  background: #36B365;
}
.el-dialog__body{
  padding: 5px 20px !important;
}
/*打印*/
.print-box {
  background-color: #ffffff;
  color: #000000;
  padding-bottom: 20px;
}
.print-box .goods-table {
  border-color: #000000;
  color: #000000;
}
.print-box .goods-table th {
  font-size: 14px;
  text-align: center;
  border-color: #000000;
}
.print-box .goods-table td {
  text-align: center;
  font-size: 15px;
  border-color: #000000;
}
.print-box .goods-table .remarks {
  text-align: left;
  padding-left: 5px;
  font-size: 14px;
}
/*空白页面*/
.empty-view {
  text-align: center;
  margin-bottom: 20px;
}
.empty-img {
  font-size: 14px;
  padding: 40px 0;
}
.code-text{
  color: #666666;
  font-size: 12px;
}

.inventory-text{
  color: #000000;
  font-weight: bold;
}
.success-color{
  color: #36B365;
}
.danger-color{
  color: #F54966;
}
.warning-color{
  color: #F7BD1B;
}
.primary-color{
  color: #1890ff;
}


.blank-view{
  background-color: #ffffff; padding: 20px
}
