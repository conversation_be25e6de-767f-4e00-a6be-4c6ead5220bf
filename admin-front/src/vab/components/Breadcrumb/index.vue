<template>
  <el-breadcrumb class="breadcrumb-container" separator="|">
    <el-breadcrumb-item v-for="item in levelList" :key="item.path">
      <a @click.prevent="handleLink(item.redirect)">
        <!--        <vab-remix-icon-->
        <!--          v-if="item.meta && item.meta.remixIcon"-->
        <!--          :icon-class="item.meta.remixIcon"-->
        <!--        ></vab-remix-icon>-->
        <span v-if="item.meta.title === '商户采购单'">
          {{ systemType === 3 ? "商户入库单" : item.meta.title }}
        </span>
        <span v-else>{{ item.meta.title }}</span>
      </a>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script>
export default {
  name: "Breadcrumb",
  data() {
    return {
      levelList: this.getBreadcrumb(),
    };
  },
  watch: {
    $route() {
      this.levelList = this.getBreadcrumb();
    },
  },
  methods: {
    getBreadcrumb() {
      return this.$route.matched.filter((item) => item.meta.title);
      // return this.$route.matched.filter(
      //   (item) => item.name && item.meta.title
      // );
    },
    handleLink(redirect) {
      this.$router.push(redirect);
    },
  },
};
</script>

<style lang="scss" scoped>
.breadcrumb-container {
  /*height: $base-nav-bar-height;
    font-size: $base-font-size-default;
    line-height: $base-nav-bar-height;*/
  line-height: 36px;
  ::v-deep {
    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        a {
          display: flex;
          float: left;
          font-weight: normal;
          color: #2d405e !important;

          i {
            margin-right: 3px;
          }
        }
      }
      .el-breadcrumb__separator {
        font-weight: 300;
        color: #dee2ee;
      }

      &:first-child {
        .el-breadcrumb__inner {
          a {
            color: #2d405e;
            font-size: 24px;
            font-weight: bold;
          }
        }
      }
      &:last-child {
        .el-breadcrumb__inner {
          a {
            color: #999;
          }
        }
      }
    }
  }
}
</style>
