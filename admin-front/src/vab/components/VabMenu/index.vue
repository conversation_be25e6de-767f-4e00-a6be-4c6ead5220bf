<template>
  <component
    :is="menuComponent"
    v-if="!item.hidden && handleAccess(item)"
    :item="item"
    :index="item.path"
    :route-children="routeChildren"
  >
    <template v-if="item.children && item.children.length">
      <vab-menu v-for="route in item.children" :key="route.fullPath" :item="route"></vab-menu>
    </template>
  </component>
</template>

<script>
import { checkRouterAccess } from "@/access/check";
export default {
  name: "VabMenu",
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      routeChildren: {},
      menuComponent: "",
    };
  },
  created() {
    const showChildren = this.handleChildren(this.item.children);
    if (showChildren.length === 0) {
      this.menuComponent = "MenuItem";
      this.routeChildren = this.item;
    } else if (showChildren.length === 1 && this.item.alwaysShow !== true) {
      this.menuComponent = "MenuItem";
      this.routeChildren = showChildren[0];
    } else {
      this.menuComponent = "Submenu";
    }
  },
  methods: {
    handleChildren(children = []) {
      if (children === null) return [];
      return children.filter((item) => item.hidden !== true);
    },
    handleAccess(item) {
      if (item.fullPath) {
        var checkRouterAccess1 = checkRouterAccess(item.fullPath);
        return checkRouterAccess1;
      }
      console.log("aaaaaa");
      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-tag {
    float: right;
    height: 16px;
    padding-right: 4px;
    padding-left: 4px;
    margin-top: calc((#{$base-menu-item-height} - 16px) / 2 + 1px);
    line-height: 16px;
    border: 0;
  }
}
</style>
