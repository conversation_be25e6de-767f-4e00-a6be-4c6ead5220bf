<template>
  <div class="nav-bar-container">
    <div class="left-panel">
      <span style="transform: translate(-10px, 9px)"><fold></fold></span>
      <tags-bar />
    </div>
    <div class="right-panel">
      <Socket></Socket>
      <!--          <error-log></error-log>-->
      <!--      <search></search>-->
      <notice></notice>
      <Setting></Setting>
      <full-screen></full-screen>
      <theme class="hidden-xs-only"></theme>
      <refresh></refresh>
      <avatar></avatar>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { handleFirstMenu, handleHeadMenu } from "@/utils/routes";
import { openFirstMenu } from "@/config/settings";
import { menus } from "../GalleryBar/menus";
import { storeMenus } from "../GalleryBar/storeMenus";
import { merchMenus } from "../GalleryBar/merchMenus";
import Socket from "../websocket";
import Setting from "@/vab/components/SettingTop/index.vue";
export default {
  name: "NavBar",
  components: {
    Socket,
    Setting,
  },
  props: {
    layout: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      firstMenu: "",
    };
  },
  computed: {
    ...mapGetters({
      routes: "routes/routes",
      // 区分后台类型，1-总后台，2-多门店，3-多商户
      systemType: "MUser/systemType",
    }),
    menuList() {
      if (this.systemType === 2) {
        return storeMenus();
      } else if (this.systemType === 3) {
        return merchMenus();
      } else {
        return menus();
      }
    },
    handleRoutes() {
      return this.menuList.filter((item) => item.hidden !== true && item.meta);
    },
  },
  watch: {
    $route: {
      handler() {
        const firstMenu = this.systemType === 2 ? handleHeadMenu() : handleFirstMenu();
        if (this.firstMenu !== firstMenu) {
          this.firstMenu = firstMenu;
          this.handleTabClick({ name: firstMenu }, true);
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleTabClick(tab, mounted) {
      const pageItem = this.menuList.find((item) => item.path === tab.name);
      if (!pageItem) {
        return;
      }
      const childrenArr = pageItem.children;
      this.$store.dispatch("routes/setPartialRoutes", childrenArr);
      if (mounted !== true && openFirstMenu) {
        this.$router.push(childrenArr[0].path);
      }
      //this.$store.dispatch("settings/openSideBar");
    },
  },
};
</script>

<style lang="scss" scoped>
.nav-bar-container {
  position: relative;
  height: $base-nav-bar-height;
  padding-right: $base-padding;
  padding-left: $base-padding;
  overflow: hidden;
  user-select: none;
  background: $base-color-white;
  box-shadow: $base-box-shadow;
  display: flex;

  .left-panel {
    width: calc(100% - 275px);
    display: flex;
    align-items: center;
    justify-items: center;
    height: $base-nav-bar-height;

    ::v-deep {
      .breadcrumb-container {
        margin-left: $base-padding;
      }

      .el-tabs {
        /*<!--margin-left: $base-padding;-->*/
        .el-tabs__header {
          margin: 0;
        }

        .el-tabs__item {
          > div {
            display: flex;
            align-items: center;

            i {
              margin-right: 3px;
            }
          }
        }
      }

      .el-tabs__nav-wrap::after {
        display: none;
      }
    }
  }

  .right-panel {
    width: 275px;
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: flex-end;
    height: $base-nav-bar-height;

    ::v-deep {
      [class*="ri-"] {
        margin-left: $base-padding;
        color: $base-color-gray;
        cursor: pointer;
      }

      button {
        [class*="ri-"] {
          margin-left: 0;
          color: $base-color-white;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.el-dropdown-menu__item {
  display: flex;
  align-content: center;
  align-items: center;

  svg {
    margin-right: 3px;
  }
}
</style>
