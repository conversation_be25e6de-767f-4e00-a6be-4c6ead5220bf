<template>
  <el-badge v-if="showNotice" :value="badge >= 100 ? '99+' : badge">
    <el-popover placement="bottom" trigger="hover" width="300">
      <vab-remix-icon slot="reference" icon-class="notification-line"></vab-remix-icon>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="未读消息" name="notice">
          <div class="notice-list">
            <el-scrollbar>
              <ul>
                <li v-for="(item, index) in list" :key="index">
                  <!--                  <el-avatar :size="45" :src="item.image"></el-avatar>-->
                  <p class="title">{{ item.title }}</p>
                  <p class="content">{{ item.content }}</p>
                  <el-button class="read-btn" type="text" size="mini" plain @click="hasRead(item.id, index)">
                    标记已读
                  </el-button>
                </li>
              </ul>
            </el-scrollbar>
          </div>
        </el-tab-pane>
        <!--        <el-tab-pane :label="translateTitle('邮件')" name="email">-->
        <!--          <div class="notice-list">-->
        <!--            <el-scrollbar>-->
        <!--              <ul>-->
        <!--                <li v-for="(item, index) in list" :key="index">-->
        <!--                  <el-avatar :size="45" :src="item.image"></el-avatar>-->
        <!--                  <span>{{ item.email }}</span>-->
        <!--                </li>-->
        <!--              </ul>-->
        <!--            </el-scrollbar>-->
        <!--          </div>-->
        <!--        </el-tab-pane>-->
      </el-tabs>
      <div class="notice-clear">
        <!--        <vab-remix-icon icon-class="close-circle-line"></vab-remix-icon>-->
        <!--        {{ translateTitle("清空消息") }}-->
        <el-pagination
          small
          layout="prev, pager, next"
          :total="badge"
          :current-page="page"
          :page-size="pageSize"
          @current-change="pageChange"
        ></el-pagination>
      </div>
    </el-popover>
  </el-badge>
</template>

<script>
import { mapGetters } from "vuex";
import { getAllMessage, receiveMessage } from "@/api/common";

export default {
  name: "Notice",
  data() {
    return {
      activeName: "notice",
      badge: null,
      list: [],
      page: 1,
      pageSize: 20,
    };
  },
  computed: {
    ...mapGetters({
      showNotice: "settings/showNotice",
    }),
  },
  created() {
    this.$nextTick(() => {
      if (this.showNotice) {
        this.getAllMessage();
      }
    });
  },

  methods: {
    handleClick(tab) {
      // this.fetchData();
    },
    handleClearNotice() {
      this.badge = null;
      this.list = [];
      this.$baseMessage("清空消息成功", "success");
    },
    // 获取通知
    async getAllMessage() {
      const { data, pageTotal } = await getAllMessage({
        page: this.page,
        pageSize: this.pageSize,
        receiveStatus: 4,
      });

      this.list = data;
      this.badge = pageTotal;
    },
    // 点击已读
    async hasRead(id, index) {
      const { err, data } = await receiveMessage(id);
      if (err) return;
      // this.list[index].receiveStatus = 5;
      // this.badge = this.badge - 1;
      this.getAllMessage();
    },
    pageChange(page) {
      this.page = page;
      this.getAllMessage();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-tabs__active-bar {
    min-width: 28px;
  }
}

.notice-list {
  height: 59vh;

  ul {
    padding: 0 15px 0 0;
    margin: 0;

    li {
      padding: 10px 0 10px 0;
      position: relative;
      border-bottom: 1px solid #f2f2f2;

      ::v-deep {
        .el-avatar {
          flex-shrink: 0;
          width: 50px;
          height: 50px;
          border-radius: 50%;
        }
      }

      .title {
        font-weight: bold;
      }
      .content {
        font-size: 12px;
      }
      .read-btn {
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  }
}

.notice-clear {
  /*display: flex;*/
  /*align-items: center;*/
  /*justify-content: center;*/
  /*padding: 10px 0 0 0;*/
  /*font-size: 14px;*/
  /*color: #515a6e;*/
  color: $base-color-blue;
  /*text-align: center;*/
  /*cursor: pointer;*/
  border-top: 1px solid #e8eaec;
  -webkit-transition: color 0.2s ease-in-out;
  transition: color 0.2s ease-in-out;

  i {
    margin-right: 3px;
  }
}
</style>
