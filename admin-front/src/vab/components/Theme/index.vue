<template>
  <span v-if="showTheme">
    <vab-remix-icon icon-class="brush-2-line" @click="handleOpenTheme"></vab-remix-icon>
    <!--    <div class="theme-setting">-->
    <!--      <div @click="handleOpenTheme">-->
    <!--        <vab-remix-icon icon-class="brush-2-line"></vab-remix-icon>-->
    <!--        <p>{{ 主题配置 }}</p>-->
    <!--      </div>-->
    <!--      <div @click="randomTheme">-->
    <!--        <vab-remix-icon icon-class="apps-line"></vab-remix-icon>-->
    <!--        <p>{{ 随机换肤 }}</p>-->
    <!--      </div>-->
    <!--      <div @click="getCode">-->
    <!--        <vab-remix-icon icon-class="file-copy-line"></vab-remix-icon>-->
    <!--        <p>{{ 拷贝源码 }}</p>-->
    <!--      </div>-->
    <!--    </div>-->

    <el-drawer title="主题配置" :visible.sync="drawerVisible" append-to-body direction="rtl" size="560px">
      <el-scrollbar class="theme-scrollbar">
        <div class="el-drawer__body">
          <el-form ref="form" :model="theme" label-width="90px">
            <el-form-item label="布局">
              <el-radio-group v-model="theme.layout">
                <el-radio-button label="gallery">画廊</el-radio-button>
                <el-radio-button label="comprehensive">综合</el-radio-button>
                <el-radio-button label="vertical">纵向</el-radio-button>
                <el-radio-button label="horizontal">横向</el-radio-button>
                <el-radio-button label="common">常规</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="主题">
              <el-radio-group v-model="theme.themeName" @click="saveTheme">
                <el-radio-button label="default">默认</el-radio-button>
                <el-radio-button label="ocean">海洋之心</el-radio-button>
                <el-radio-button label="green">绿荫草场</el-radio-button>
                <el-radio-button label="glory">荣耀典藏</el-radio-button>
                <el-radio-button label="white">碰触纯白</el-radio-button>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="头部">
              <el-radio-group v-model="theme.header" :disabled="theme.layout === 'common'">
                <el-radio-button label="fixed">固定</el-radio-button>
                <el-radio-button label="noFixed">不固定</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="多标签">
              <el-radio-group v-model="theme.showTagsBar">
                <el-radio-button :label="true">显示</el-radio-button>
                <el-radio-button :label="false">不显示</el-radio-button>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="国际化">
              <el-radio-group v-model="theme.showLanguage">
                <el-radio-button :label="true">显示</el-radio-button>
                <el-radio-button :label="false">不显示</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="进度条">
              <el-radio-group v-model="theme.showProgressBar">
                <el-radio-button :label="true">显示</el-radio-button>
                <el-radio-button :label="false">不显示</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="刷新">
              <el-radio-group v-model="theme.showRefresh">
                <el-radio-button :label="true">显示</el-radio-button>
                <el-radio-button :label="false">不显示</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="搜索">
              <el-radio-group v-model="theme.showSearch">
                <el-radio-button :label="true">显示</el-radio-button>
                <el-radio-button :label="false">不显示</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="通知">
              <el-radio-group v-model="theme.showNotice">
                <el-radio-button :label="true">显示</el-radio-button>
                <el-radio-button :label="false">不显示</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="全屏">
              <el-radio-group v-model="theme.showFullScreen">
                <el-radio-button :label="true">显示</el-radio-button>
                <el-radio-button :label="false">不显示</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <el-button @click="setDefaultTheme">恢复默认</el-button>
              <el-button type="primary" @click="saveTheme">保存</el-button>
            </el-form-item>
          </el-form>
          <el-alert :closable="false" title="主题配置描述" type="warning"></el-alert>
        </div>
      </el-scrollbar>
    </el-drawer>
  </span>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import {
  header as defaultHeader,
  layout as defaultLayout,
  showFullScreen as defaultShowFullScreen,
  showLanguage as defaultShowLanguage,
  showNotice as defaultShowNotice,
  showProgressBar as defaultShowProgressBar,
  showRefresh as defaultShowRefresh,
  showSearch as defaultShowSearch,
  showTagsBar as defaultShowTagsBar,
} from "@/config/settings";

export default {
  name: "Theme",
  data() {
    return {
      drawerVisible: false,
      theme: {
        themeName: "",
        layout: "",
        header: "",
        showLanguage: "",
        showProgressBar: "",
        showRefresh: "",
        showSearch: "",
        showTagsBar: "",
        showNotice: "",
        showFullScreen: "",
      },
    };
  },
  computed: {
    ...mapGetters({
      themeName: "settings/themeName",
      layout: "settings/layout",
      header: "settings/header",
      showLanguage: "settings/showLanguage",
      showProgressBar: "settings/showProgressBar",
      showRefresh: "settings/showRefresh",
      showSearch: "settings/showSearch",
      showTagsBar: "settings/showTagsBar",
      showNotice: "settings/showNotice",
      showFullScreen: "settings/showFullScreen",
      showTheme: "settings/showTheme",
    }),
  },
  created() {
    this.$baseEventBus.$on("theme", () => {
      this.handleOpenTheme();
    });
    this.$baseEventBus.$on("randomTheme", () => {
      this.randomTheme();
    });
    const theme = localStorage.getItem("vue-admin-beautiful-pro-theme");
    if (null !== theme) {
      this.theme = JSON.parse(theme);
      this.saveTheme();
    } else {
      this.handleSetData();
    }
  },
  methods: {
    ...mapActions({
      changeHeader: "settings/changeHeader",
      changeLayout: "settings/changeLayout",
      handleShowLanguage: "settings/handleShowLanguage",
      handleShowProgressBar: "settings/handleShowProgressBar",
      handleShowRefresh: "settings/handleShowRefresh",
      handleShowSearch: "settings/handleShowSearch",
      handleShowTagsBar: "settings/handleShowTagsBar",
      handleShowNotice: "settings/handleShowNotice",
      handleShowFullScreen: "settings/handleShowFullScreen",
    }),
    shuffle(array) {
      var m = array.length,
        t,
        i;
      while (m) {
        i = Math.floor(Math.random() * m--);
        t = array[m];
        array[m] = array[i];
        array[i] = t;
      }
      return array;
    },
    randomTheme() {
      const loading = this.$baseColorfullLoading(
        0,
        "随机换肤只用于演示会在页面刷新时自动失效，请放心切换，如需保存，在主题配置中保存..."
      );
      const themeNameArray = ["default", "ocean", "green", "glory", "white"];
      const layoutArray = ["horizontal", "vertical", "gallery", "comprehensive", "common"];
      this.theme.themeName = this.shuffle(themeNameArray)[0];
      this.theme.layout = this.shuffle(layoutArray)[0];
      if (document.body.getBoundingClientRect().width >= 992) {
        this.changeLayout(this.theme.layout);
      }
      document.getElementsByTagName("body")[0].className = `vue-admin-beautiful-pro-theme-${this.theme.themeName}`;
      loading.close();
    },
    handleSetData() {
      this.theme.themeName = this.themeName;
      this.theme.layout = this.layout;
      this.theme.header = this.header;
      this.theme.showLanguage = this.showLanguage;
      this.theme.showProgressBar = this.showProgressBar;
      this.theme.showRefresh = this.showRefresh;
      this.theme.showSearch = this.showSearch;
      this.theme.showTagsBar = this.showTagsBar;
      this.theme.showNotice = this.showNotice;
      this.theme.showFullScreen = this.showFullScreen;
      document.getElementsByTagName("body")[0].className = `vue-admin-beautiful-pro-theme-${this.themeName}`;
    },
    handleOpenTheme() {
      this.drawerVisible = true;
    },
    saveTheme() {
      let {
        themeName,
        layout,
        header,
        showLanguage,
        showProgressBar,
        showRefresh,
        showSearch,
        showTagsBar,
        showNotice,
        showFullScreen,
      } = this.theme;
      localStorage.setItem(
        "vue-admin-beautiful-pro-theme",
        `{
              "themeName":"${themeName}",
              "layout":"${layout}",
              "header":"${header}",
              "showLanguage":${showLanguage},
              "showProgressBar":${showProgressBar},
              "showRefresh":${showRefresh},
              "showSearch":${showSearch},
              "showTagsBar":${showTagsBar},
              "showNotice":${showNotice},
              "showFullScreen":${showFullScreen}
            }`
      );
      document.getElementsByTagName("body")[0].className = `vue-admin-beautiful-pro-theme-${themeName}`;

      if (document.body.getBoundingClientRect().width >= 992) {
        this.changeLayout(layout);
      }
      this.changeHeader(header);
      this.handleShowLanguage(showLanguage);
      this.handleShowProgressBar(showProgressBar);
      this.handleShowRefresh(showRefresh);
      this.handleShowSearch(showSearch);
      this.handleShowTagsBar(showTagsBar);
      this.handleShowNotice(showNotice);
      this.handleShowFullScreen(showFullScreen);
      this.drawerVisible = false;
    },
    setDefaultTheme() {
      let { themeName } = this.theme;
      document.getElementsByTagName("body")[0].classList.remove(`vue-admin-beautiful-pro-theme-${themeName}`);
      localStorage.removeItem("vue-admin-beautiful-pro-theme");
      this.$refs["form"].resetFields();
      Object.assign(this.$data, this.$options.data());
      this.changeLayout(defaultLayout);
      this.changeHeader(defaultHeader);
      this.handleShowLanguage(defaultShowLanguage);
      this.handleShowProgressBar(defaultShowProgressBar);
      this.handleShowRefresh(defaultShowRefresh);
      this.handleShowSearch(defaultShowSearch);
      this.handleShowTagsBar(defaultShowTagsBar);
      this.handleShowNotice(defaultShowNotice);
      this.handleShowFullScreen(defaultShowFullScreen);
      this.handleSetData();
      this.drawerVisible = false;
    },
    getCode() {
      const url = "https://github.com/vue-admin-beautiful/vue-admin-beautiful-pro/blob/master/src/views";
      let path = this.$route.path + "/index.vue";
      if (path === "/vab/icon/remixIcon/index.vue") {
        path = "/vab/icon/remixIcon.vue";
      }
      if (path === "/vab/icon/colorfulIcon/index.vue") {
        path = "/vab/icon/colorfulIcon.vue";
      }
      if (path === "/vab/icon/iconSelector/index.vue") {
        path = "/vab/icon/iconSelector.vue";
      }
      if (path === "/vab/table/comprehensiveTable/index.vue") {
        path = "/vab/table/comprehensiveTable.vue";
      }
      if (path === "/vab/table/inlineEditTable/index.vue") {
        path = "/vab/table/inlineEditTable.vue";
      }
      if (path === "/vab/table/customTable/index.vue") {
        path = "/vab/table/customTable.vue";
      }
      if (path === "/vab/drag/dialogDrag/index.vue") {
        path = "/vab/drag/dialogDrag.vue";
      }
      if (path === "/vab/drag/cardDrag/index.vue") {
        path = "/vab/drag/cardDrag.vue";
      }
      if (path === "/vab/form/comprehensiveForm/index.vue") {
        path = "/vab/form/comprehensiveForm.vue";
      }
      if (path === "/vab/form/stepForm/index.vue") {
        path = "/vab/form/stepForm.vue";
      }
      if (path === "/vab/editor/richTextEditor/index.vue") {
        path = "/vab/editor/richTextEditor.vue";
      }
      if (path === "/vab/editor/markdownEditor/index.vue") {
        path = "/vab/editor/markdownEditor.vue";
      }
      if (path === "/vab/menu1/menu1-1/menu1-1-1/index.vue") {
        path = "/vab/nested/menu1/menu1-1/menu1-1-1/index.vue";
      }
      if (path === "/vab/excel/exportExcel/index.vue") {
        path = "/vab/excel/exportExcel.vue";
      }
      if (path === "/vab/excel/exportSelectedExcel/index.vue") {
        path = "/vab/excel/exportSelectedExcel.vue";
      }
      if (path === "/vab/excel/exportMergeHeaderExcel/index.vue") {
        path = "/vab/excel/exportMergeHeaderExcel.vue";
      }
      window.open(url + path);
    },
  },
};
</script>

<style lang="scss" scoped>
@mixin right-bar {
  position: fixed;
  right: 0;
  z-index: $base-z-index;
  width: 60px;
  min-height: 60px;
  text-align: center;
  cursor: pointer;
  background: $base-color-blue;
  border-radius: $base-border-radius;

  > div {
    padding-top: 10px;
    border-bottom: 0 !important;

    &:hover {
      opacity: 0.9;
    }

    & + div {
      border-top: 1px solid $base-color-white;
    }

    p {
      padding: 0;
      margin: 0;
      font-size: $base-font-size-small;
      line-height: 30px;
      color: $base-color-white;
    }
  }
}

.theme-scrollbar {
  height: calc(100vh - 80px);
  overflow: hidden;
}

.theme-setting {
  @include right-bar;

  top: calc((100vh - 110px) / 2);

  ::v-deep {
    [class*="ri-"] {
      display: block !important;
      margin-right: auto !important;
      margin-left: auto !important;
      color: $base-color-white !important;
      fill: $base-color-white !important;
    }
  }
}

.el-drawer__body {
  padding: 0 20px 20px 20px;
}
</style>
<style lang="scss">
.el-drawer__wrapper {
  outline: none !important;

  * {
    outline: none !important;
  }
}

.vab-color-picker {
  .el-color-dropdown__link-btn {
    display: none;
  }
}
</style>
