<template>
  <vab-remix-icon v-if="showRefresh" icon-class="refresh-line" @click="refreshRoute"></vab-remix-icon>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  name: "Refresh",
  computed: {
    ...mapGetters({
      showRefresh: "settings/showRefresh",
    }),
  },
  mounted() {
    document.onkeydown = (e) => {
      let e1 = e || event || window.event || arguments.callee.caller.arguments[0];
      if (e1 && e1.code === "F8") {
        this.refreshRoute();
      }
    };
  },
  methods: {
    async refreshRoute() {
      this.$baseEventBus.$emit("reloadRouterView");
    },
  },
};
</script>
