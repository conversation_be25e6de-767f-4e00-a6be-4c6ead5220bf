<template>
  <span v-if="menuList.length && systemType === 1">
    <el-popover placement="bottom" trigger="hover">
      <div class="menu-box">
        <div v-for="(route, index) in menuList" :key="index" class="menu-col">
          <p>{{ route.meta.title }}</p>
          <ul v-if="route.children && route.children.length" class="menu-ul">
            <li v-for="(menu, indexT) in route.children" :key="indexT" class="menu-li" @click="menuClick(menu)">
              {{ menu.meta.title }}
            </li>
          </ul>
          <ul v-else class="menu-ul">
            <li class="menu-li" @click="menuClick(route)">
              {{ route.meta.title }}
            </li>
          </ul>
        </div>
      </div>
      <div slot="reference">
        <vab-remix-icon style="font-size: 18px" icon-class="settings-4-line"></vab-remix-icon>
      </div>
    </el-popover>
  </span>
</template>
<script>
import { menus } from "../GalleryBar/menus";
export default {
  name: "Setting",
  data() {
    return {
      menus: [],
    };
  },
  computed: {
    menuList() {
      const list = menus().find((item) => item.path === "/SystemSettings");
      return list ? list.children : [];
    },
  },
  methods: {
    menuClick(route) {
      this.$router.push(route.fullPath);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep {
  .el-dialog {
    &__header {
      display: none;
      border: 0 !important;
    }

    &__body {
      padding: 0;
      border: 0 !important;
    }

    .el-form-item__content {
      position: relative;

      i {
        position: absolute;
        top: 14px;
        left: $base-padding;
        z-index: $base-z-index;
      }

      .el-autocomplete {
        width: 100%;

        .el-input__inner {
          width: 100%;
          height: 60px;
          padding-left: $base-padding * 2.5;
          border: 0 !important;
        }
      }
    }
  }
}
/*  自定义menu样式*/
.menu-box {
  display: flex;
  padding: 10px 0;
  .menu-col {
    padding: 0 24px;
    font-size: 14px;
    border-right: 1px solid #eeeeee;
    &:last-child {
      border-right: 0;
    }
    p {
      font-weight: 300;
      color: rgba(0, 0, 0, 0.6);
    }
    .menu-ul {
      .menu-li {
        padding-top: 16px;
        color: #000000;
        cursor: pointer;
        &:hover {
          color: $base-color-blue;
        }
      }
    }
  }
}
</style>
