<template>
  <div class="tabs-bar-container">
    <fold v-if="layout === 'common'"></fold>
    <el-tabs
      v-model="tabActive"
      type="card"
      class="tabs-content"
      :class="{
        ['tabs-content-' + tabsBarStyle]: true,
      }"
      @tab-click="handleTabClick"
      @tab-remove="handleTabRemove"
    >
      <el-tab-pane v-for="item in visitedRoutes" :key="item.path" :closable="!isAffix(item)" :name="item.path">
        <span slot="label">
          <vab-remix-icon
            v-if="item.meta && item.meta.remixIcon"
            :is-custom-svg="item.meta.isCustomSvgIcon"
            :icon-class="item.meta.remixIcon"
          />
          <!--  如果没有图标那么取第二级的图标 -->
          <vab-remix-icon v-else :icon-class="item.parentRemixIcon" />
          <span v-if="item.meta.title === '商户采购单'">
            {{ systemType === 3 ? "商户入库单" : item.meta.title }}
          </span>
          <span v-else>{{ item.meta.title }}</span>
        </span>
        <!--        <span v-else slot="label">-->
        <!--          {{ translateTitle(item.meta.title) }}-->
        <!--        </span>-->
      </el-tab-pane>
    </el-tabs>

    <el-dropdown @command="handleCommand" @visible-chactiveange="handleVisibleChange">
      <span class="more">
        <!--        {{ translateTitle("更多") }}-->
        <!-- <vab-remix-icon
          icon-class="vab-tabs-more-icon"
          class="vab-dropdown"
          :class="{ 'vab-dropdown-active': active }"
        />-->

        <i style="font-size: 17px" class="el-icon-menu"></i>
      </span>
      <el-dropdown-menu slot="dropdown" class="tags-more">
        <el-dropdown-item command="closeOthersTags">
          <vab-remix-icon icon-class="close-line" />
          关闭其他
        </el-dropdown-item>
        <el-dropdown-item command="closeLeftTags">
          <vab-remix-icon icon-class="arrow-left-line" />
          关闭左侧
        </el-dropdown-item>
        <el-dropdown-item command="closeRightTags">
          <vab-remix-icon icon-class="arrow-right-line" />
          关闭右侧
        </el-dropdown-item>
        <el-dropdown-item command="closeAllTags">
          <vab-remix-icon icon-class="close-line" />
          关闭全部
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import { tabsBarStyle } from "@/config/settings";
export default {
  name: "TagsBar",
  props: {
    layout: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      affixTags: [],
      tabActive: "",
      created: false,
      active: false,
    };
  },
  computed: {
    ...mapGetters({
      systemType: "MUser/systemType",
      // visitedRoutes: "tagsBar/visitedRoutes",
      routes: "routes/routes",
    }),
    visitedRoutes() {
      if (this.systemType === 1) {
        return this.$store.getters["tagsBar/visitedRoutes"];
      } else {
        return this.$store.getters["tagsBar/visitedRoutes"].filter((item) => item.meta.title !== "经营概况");
      }
    },
    tabsBarStyle() {
      return tabsBarStyle;
    },
  },
  watch: {
    $route: {
      handler(route) {
        this.addTabs(route);
      },
    },
  },
  created() {
    this.initAffixTags(this.routes);
    this.addTabs(this.$route);
  },
  methods: {
    ...mapActions({
      addVisitedRoute: "tagsBar/addVisitedRoute",
      delVisitedRoute: "tagsBar/delVisitedRoute",
      delOthersVisitedRoutes: "tagsBar/delOthersVisitedRoutes",
      delLeftVisitedRoutes: "tagsBar/delLeftVisitedRoutes",
      delRightVisitedRoutes: "tagsBar/delRightVisitedRoutes",
      delAllVisitedRoutes: "tagsBar/delAllVisitedRoutes",
    }),
    handleVisibleChange(val) {
      this.active = val;
    },
    initAffixTags(routes) {
      routes.forEach((route) => {
        if (route.meta && route.meta.affix) this.addTabs(route);
        if (route.children) this.initAffixTags(route.children);
      });
    },
    /**
     * 添加标签页
     * @param tag route
     * @param init 是否是从router获取路由
     * @returns {Promise<void>}
     */
    async addTabs(tag, init = false) {
      let parentRemixIcon = "";
      if (tag.matched && tag.matched.length > 1) parentRemixIcon = tag.matched[0].meta.remixIcon || "";
      if (tag.name && tag.meta && tag.meta.tagHidden !== true) {
        const path = this.handleRouteRawPath(tag);
        await this.addVisitedRoute({
          path: path,
          query: tag.query,
          params: tag.params,
          name: tag.name,
          matched: init ? [tag.name] : (tag.matched || []).map((item) => item.components.default.name),
          parentRemixIcon,
          meta: { ...tag.meta },
        });
        this.tabActive = path;
      }
    },
    handleRouteRawPath(route) {
      return route.path;
      // return route.matched
      //   ? route.matched[route.matched.length - 1].path
      //   : route.path;
    },
    isActive(path) {
      return path === this.handleRouteRawPath(this.$route);
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix;
    },
    handleTabClick(tab) {
      if (!this.isActive(tab.name)) this.$router.push(this.visitedRoutes[tab.index]);
    },
    async handleTabRemove(fullPath) {
      await this.delVisitedRoute(fullPath);
      if (this.isActive(fullPath)) this.toLastTab();
    },
    handleCommand(command) {
      switch (command) {
        case "closeOthersTags":
          this.closeOthersTags();
          break;
        case "closeLeftTags":
          this.closeLeftTags();
          break;
        case "closeRightTags":
          this.closeRightTags();
          break;
        case "closeAllTags":
          this.closeAllTags();
          break;
      }
    },
    async closeOthersTags() {
      await this.delOthersVisitedRoutes(this.handleRouteRawPath(this.$route));
    },
    async closeLeftTags() {
      await this.delLeftVisitedRoutes(this.handleRouteRawPath(this.$route));
    },
    async closeRightTags() {
      await this.delRightVisitedRoutes(this.handleRouteRawPath(this.$route));
    },
    async closeAllTags() {
      await this.delAllVisitedRoutes();
      if (!this.affixTags.some((tag) => this.isActive(this.handleRouteRawPath(tag)))) this.toLastTab();
    },
    toLastTab() {
      const latestView = this.visitedRoutes.slice(-1)[0];
      if (latestView) this.$router.push(latestView);
      else this.$router.push("/");
    },
  },
};
</script>

<style lang="scss" scoped>
.tabs-bar-container {
  width: 100%;
  /*------------*/
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: space-between;
  height: $base-tags-bar-height;
  /*padding-right: $base-padding;
    padding-left: $base-padding;*/
  user-select: none;
  background: $base-color-white;
  /*border-top: 1px solid #f6f6f6;*/

  ::v-deep {
    .fold-unfold {
      margin-right: $base-padding;
    }
  }

  .tabs-content {
    width: calc(100% - 60px);

    &-card {
      height: $base-tag-item-height;

      ::v-deep {
        .el-tabs__nav-next,
        .el-tabs__nav-prev {
          height: $base-tag-item-height;
          line-height: $base-tag-item-height;
          transform: translateY(10px);
        }

        .el-tabs__header {
          border-bottom: 0;

          .el-tabs__nav {
            border: 0;
          }

          .el-tabs__item {
            box-sizing: border-box;
            height: $base-tag-item-height;
            margin-right: 5px;
            line-height: $base-tag-item-height;
            border: 1px solid $base-border-color;
            border-radius: $base-border-radius;
            transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;

            &.is-active {
              color: $base-color-white;
              background: $base-color-blue;
              border: 1px solid $base-color-blue;
            }
          }
        }
      }
    }
    &-smart {
      height: $base-tag-item-height;
      ::v-deep {
        .el-tabs__nav-next,
        .el-tabs__nav-prev {
          height: $base-tag-item-height;
          line-height: $base-tag-item-height;
        }

        .el-tabs__header {
          border-bottom: 0;

          .el-tabs__nav {
            border: 0;
          }

          .el-tabs__item {
            height: $base-tag-item-height;
            margin-right: 5px;
            line-height: $base-tag-item-height;
            border: 0;
            transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
            &.is-active {
              background: rgba($base-color-blue, 0.1);
              &:after {
                width: 100%;
              }
            }
            &:after {
              position: absolute;
              bottom: 0;
              left: 0;
              width: 0;
              height: 2px;
              content: "";
              background-color: $base-color-blue;
            }
            &:hover {
              background: rgba($base-color-blue, 0.1);
              &:after {
                width: 100%;
              }
            }
          }
        }
      }
    }
    &-smooth {
      height: $base-tag-item-height + 4;
      ::v-deep {
        .el-tabs__nav-next,
        .el-tabs__nav-prev {
          height: $base-tag-item-height + 4;
          line-height: $base-tag-item-height + 4;
          transform: translateY(10px);
        }

        .el-tabs__header {
          border-bottom: 0;

          .el-tabs__nav {
            border: 0;
          }

          .el-tabs__item {
            height: $base-tag-item-height + 4;
            margin-top: ($base-tags-bar-height - $base-tag-item-height - 4)/2;
            margin-right: -18px;
            line-height: $base-tag-item-height + 4;
            border: 0;
            transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
            &.is-active {
              color: $base-color-blue;
              background: rgba($base-color-blue, 0.1);
              mask: url("~@/assets/tabs_images/vab-tab.png");

              mask-size: 100% 100%;
              &:hover {
                color: $base-color-blue;
                background: rgba($base-color-blue, 0.1);
                mask: url("~@/assets/tabs_images/vab-tab.png");

                mask-size: 100% 100%;
              }
            }
            &:hover {
              z-index: $base-z-index;
              color: $base-color-black;
              background: #dee1e6;
              mask: url("~@/assets/tabs_images/vab-tab.png");

              mask-size: 100% 100%;
            }
          }
        }
      }
    }
  }

  .more {
    display: inline-block;
    width: 50px;
    height: 55px;
    line-height: 55px;
    cursor: pointer;
    text-align: center;
    &:hover {
      color: $base-color-blue;
    }
  }
}
</style>
