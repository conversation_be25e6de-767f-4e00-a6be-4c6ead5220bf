<template>
  <div style="display: inline-block">
    <el-dropdown v-if="parseInt(enterpriseScope) === 4 || systemType === 3" @command="handleCommand">
      <span class="avatar-dropdown">
        <img :src="avatar" alt="" class="user-avatar" />
        <div class="user-name">
          <span class="user-name-text">{{ userName }}</span>
          <vab-remix-icon icon-class="arrow-down-s-line"></vab-remix-icon>
        </div>
      </span>

      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="logout">
          <vab-remix-icon icon-class="logout-circle-r-line"></vab-remix-icon>
          退出登录
        </el-dropdown-item>
        <el-dropdown-item v-if="systemType === 1" command="editEnterprise">
          <vab-remix-icon icon-class="edit-circle-line"></vab-remix-icon>
          修改企业
        </el-dropdown-item>
        <el-dropdown-item command="editPwd">
          <vab-remix-icon icon-class="edit-line"></vab-remix-icon>
          账号设置
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <el-popover v-else placement="bottom" width="300" trigger="hover">
      <span slot="reference" class="avatar-dropdown">
        <img :src="avatar" alt="" class="user-avatar" />
        <div class="user-name">
          <span class="user-name-text">{{ userName }}</span>
          <vab-remix-icon icon-class="arrow-down-s-line"></vab-remix-icon>
        </div>
      </span>
      <div>
        <ul class="handel-row">
          <li class="handel-btn" @click="handleCommand('editPwd')">账号设置</li>
          <li v-if="systemType === 1" class="handel-btn" @click="handleCommand('editEnterprise')">
            {{ parseInt(enterpriseLength) > 1 ? "切换企业" : "修改企业" }}
          </li>
          <li class="handel-btn" @click="handleCommand('logout')">退出登录</li>
        </ul>
        <div v-if="systemType !== 5">
          <el-input
            v-model="keyword"
            size="small"
            clearable
            placeholder="搜索门店名称"
            @clear="pageChange(1)"
            @keyup.enter.native="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
          <ul class="store-ul">
            <!--          当前企业-->
            <li
              v-if="isSuper"
              class="child-store"
              :class="[systemType === 1 ? 'on-store' : '']"
              @click="changeStore(1)"
            >
              <img :src="enterprise.logo" class="store-img float_left" />
              <div class="float_left shop-info">
                <p class="store-name">{{ enterprise.enterpriseName }}</p>
                <p style="margin-top: 10px">
                  <el-tag type="primary" size="small">企业后台</el-tag>
                </p>
              </div>
              <span v-if="systemType === 1" class="ac-span">当前企业后台</span>
            </li>
            <!--          门店列表-->
            <li
              v-for="(item, index) in store_list"
              :key="index"
              class="child-store"
              :class="[[2, 3].includes(systemType) && storeData.id === item.id ? 'on-store' : '']"
              @click="changeStore(item.merchantId ? 3 : 2, item)"
            >
              <img :src="item.logo || enterprise.logo" alt="" class="store-img float_left" />
              <div class="float_left shop-info">
                <p class="store-name">{{ item.name }}</p>
                <div>
                  <el-tag v-if="item.enableStatus === 4" type="info" size="small"> 已打烊 </el-tag>
                  <el-tag type="primary" size="small">
                    {{ item.merchantId ? "入驻商户" : "门店" }}
                  </el-tag>
                  <el-tag v-if="item.isMaster === 5" type="primary" effect="dark" size="small"> 总店 </el-tag>
                </div>
              </div>
              <span v-if="storeData.id === item.id" class="ac-span">
                {{ systemType === 3 ? "当前入驻商户后台" : systemType === 2 ? "当前店铺后台" : "" }}
              </span>
            </li>
          </ul>
          <FooterPage
            v-if="total > 10"
            :page-size="pageSize"
            :total-page.sync="total"
            :current-page.sync="page"
            layout="prev, pager, next"
            :background="false"
            :small="true"
            @pageChange="pageChange"
            @sizeChange="sizeChange"
          ></FooterPage>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import { recordRoute } from "@/config/settings";
import { getShopByStaff, search } from "@/api/Shop";
import { isSuperAdmin } from "@/access/check";
export default {
  name: "Avatar",
  data() {
    return {
      keyword: "",
      store_list: [],
      page: 1,
      pageSize: 10,
      total: 0,
    };
  },
  computed: {
    ...mapGetters({
      avatar: "user/avatar",
      enterpriseScope: "MUser/enterpriseScope",
      enterprise: "MUser/enterprise",
      systemType: "MUser/systemType",
      storeData: "MUser/storeData",
      enterpriseLength: "MUser/enterpriseLength",
    }),
    isSuper() {
      return isSuperAdmin();
    },
  },
  created() {
    this.getShopByStaff();
  },
  methods: {
    ...mapActions({
      delAllVisitedRoutes: "tagsBar/delAllVisitedRoutes",
      setEnToken: "user/setEnToken",
      changeSystemType: "MUser/changeSystemType",
      changeStoreData: "MUser/changeStoreData",
    }),
    handleCommand(command) {
      switch (command) {
        case "logout":
          this.logout();
          break;
        case "personalCenter":
          this.personalCenter();
          break;
        case "editEnterprise":
          if (parseInt(this.enterpriseLength) === 1) {
            this.editEnterprise();
          } else {
            this.changeEnterprise();
          }
          break;
        case "editPwd":
          this.editPwd();
          break;
      }
    },
    changeStore(type, item) {
      this.delAllVisitedRoutes();
      this.changeSystemType(type);
      if (type === 2) {
        this.$router.push(`/SingleStore/goods/GoodsAdministration`);
        this.changeStoreData(item);
      } else if (type === 3) {
        this.$router.push(`/MerchIndex`);
        this.changeStoreData(item);
      } else {
        this.$router.push(`/`);
      }
      setTimeout(() => {
        window.location.reload();
      }, 500);
    },
    personalCenter() {
      this.$router.push("/personalCenter/personalCenter");
    },
    editEnterprise() {
      this.$router.push(`/Edit/EditStore/${this.enterprise.enterpriseId}`);
    },
    // 切换企业
    async changeEnterprise() {
      await this.delAllVisitedRoutes();
      await this.setEnToken("");
      await this.$router.push("/Enterprise");
    },
    // 账号设置
    editPwd() {
      this.$confirm("请选择您要进行的操作操作？", "确认信息", {
        distinguishCancelAndClose: true,
        confirmButtonText: "修改密码",
        cancelButtonText: "修改账号",
      })
        .then(() => {
          this.$router.push(`/Edit/EditPwd/${1}`);
        })
        .catch((action) => {
          if (action === "cancel") {
            this.$router.push(`/Edit/EditPwd/${2}`);
          }
        });
      // this.$router.push("/Edit/EditPwd");
    },
    async logout() {
      this.$confirm("确定要退出登录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await this.delAllVisitedRoutes();
        await this.$store.dispatch("user/logout");
        await this.changeStoreData({});
        if (recordRoute) {
          await this.$store.dispatch("MUser/changeSystemType", 1);
          const fullPath = this.$route.fullPath;
          await this.$router.push(`/login?redirect=${fullPath}`);
        } else if (this.systemType === 3) {
          await this.$router.push("/MerchantsLogin");
        } else {
          await this.$router.push("/login");
        }
      });
    },
    //  获取列表
    async getShopByStaff() {
      const data = await getShopByStaff({
        page: this.page,
        pageSize: this.pageSize,
      });
      this.store_list = data.data;
      this.total = data.pageTotal;
    },
    // 切页
    pageChange(val) {
      this.page = val;
      this.getShopByStaff();
    },
    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
  },
};
</script>
<style lang="scss" scoped>
.avatar-dropdown {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: center;
  justify-items: center;

  .user-avatar {
    width: 40px;
    height: 40px;
    margin-left: 15px;
    cursor: pointer;
    border-radius: 50%;
  }

  .user-name {
    position: relative;
    display: flex;
    align-content: center;
    align-items: center;
    height: 40px;
    margin-left: 6px;
    line-height: 40px;
    cursor: pointer;
    .user-name-text {
      display: inline-block;
      max-width: 70px;
      overflow: hidden;
      white-space: nowrap;
      vertical-align: middle;
    }
    [class*="ri-"] {
      margin-left: 0 !important;
    }
  }
}
.handel-row {
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
  display: flex;
}
.handel-btn {
  line-height: 50px;
  cursor: pointer;
  text-align: center;
  position: relative;
  flex: 3;
  &:after {
    content: "";
    display: inline-block;
    width: 1px;
    height: 14px;
    background-color: #ebeef5;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
.handel-btn:last-child:after {
  background-color: transparent;
}
.handel-btn:hover {
  background-color: rgb(236, 245, 255);
}
.store-ul {
  padding-top: 20px;
  height: calc(100vh - 310px);
  overflow: auto;
  li {
    position: relative;
    overflow: hidden;
    min-height: 80px;
    background: #f5f7f9;
    border: 1px solid #f2f2f2;
    border-radius: 4px;
    margin-bottom: 20px;
    cursor: pointer;
    padding: 14px;
    &:hover {
      border: 1px solid #2e7eff;
    }
    &.child-store {
      .store-img {
        width: 60px;
        height: 60px;
        border-radius: 4px;
      }
    }
    .shop-info {
      width: calc(100% - 100px);
    }
    .store-img {
      width: 60px;
      height: 60px;
      border-radius: 4px;
      margin-right: 20px;
      vertical-align: middle;
    }
    .store-name {
      margin-bottom: 10px;
    }
    .tab-span {
      width: 48px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      display: inline-block;
      margin-right: 8px;
      border-radius: 4px;
      color: #2e7eff;
      background: #daebff;
    }
    .first-tag {
      color: #fb6638;
      background: #fff3ef;
    }
  }
  .on-store {
    border: 1px solid #2e7eff;
    .ac-span {
      color: #ffffff;
      background-color: #2e7eff;
      position: absolute;
      top: 0;
      right: 0;
      padding: 0 10px;
      font-size: 12px;
    }
  }
}
</style>
