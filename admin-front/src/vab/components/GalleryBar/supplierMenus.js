import { checkActionAccess } from "@/access/check";

const menusList = [
  {
    path: "/",
    redirect: "/SupplierStorage/SupplierStorageOverview",
    meta: {
      title: "概况",
      remixIcon: "mac-line",
      affix: true,
    },
    children: [
      {
        path: "/SupplierStorage/SupplierStorageOverview",
        name: "SupplierStorageOverview",
        noKeepAlive: true,
        meta: {
          title: "概览",
          remixIcon: "mac-line",
          affix: true,
        },
        fullPath: "/SupplierStorage/SupplierStorageOverview",
      },
    ],
    fullPath: "/",
  },
  {
    path: "/SupplierStorage/Price",
    name: "SupplierStoragePrice",
    fullPath: "/SupplierStorage/SupplierStoragePrice",
    meta: {
      title: "商品",
      remixIcon: "price-tag-3-line",
    },
    children: [
      {
        path: "/SupplierStorage/SupplierStoragePrice",
        name: "SupplierStoragePrice",
        meta: {
          title: "定价",
          remixIcon: "price-tag-3-line",
        },
        fullPath: "/SupplierStorage/SupplierStoragePrice",
      },
    ],
  },
  {
    path: "/SupplierStorage/Inventory",
    name: "SupplierStorageInventory",
    fullPath: "/SupplierStorage/SupplierStorageInventory",
    meta: {
      title: "库存",
      remixIcon: "store-2-line",
    },
    children: [
      {
        path: "/SupplierStorage/SupplierStorageInventory",
        name: "SupplierStorageInventory",
        meta: {
          title: "库存状态",
          remixIcon: "store-2-line",
        },
        fullPath: "/SupplierStorage/SupplierStorageInventory",
      },
      {
        path: "/SupplierStorage/SupplierStorageFlow",
        name: "SupplierStorageFlow",
        meta: {
          title: "库存流水",
          remixIcon: "truck-line",
        },
        fullPath: "/SupplierStorage/SupplierStorageFlow",
      },
    ],
  },
  {
    path: "/SupplierStorage/Settlement",
    name: "SupplierStorageSettlement",
    fullPath: "/SupplierStorage/SupplierStorageSettlement",
    meta: {
      title: "结算",
      remixIcon: "file-list-3-line",
    },
    children: [
      {
        path: "/SupplierStorage/SupplierStorageSettlement",
        name: "SupplierStorageSettlement",
        meta: {
          title: "结算流水",
          remixIcon: "file-list-3-line",
        },
        fullPath: "/SupplierStorage/SupplierStorageSettlement",
      },
      {
        path: "/SupplierStorage/SettlementDetailList",
        name: "SettlementDetailList",
        meta: {
          title: "分账明细",
          remixIcon: "file-list-2-line",
        },
        fullPath: "/SupplierStorage/SettlementDetailList",
      },
    ],
  },
  {
    path: "/SupplierStorage/Account",
    name: "SupplierStorageAccount",
    fullPath: "/SupplierStorage/SupplierStorageAccount",
    meta: {
      title: "账户",
      remixIcon: "user-settings-line",
    },
    children: [
      {
        path: "/SupplierStorage/SupplierStorageAccount",
        name: "SupplierStorageAccount",
        meta: {
          title: "密码修改",
          remixIcon: "user-settings-line",
        },
        fullPath: "/SupplierStorage/SupplierStorageAccount",
      },
    ],
  },
];

export function supplierMenus() {
  const di = (items) => {
    return items.reduce((container, item) => {
      let { children } = item;
      if (children) {
        children = di(children);
      }
      if (children && !children.length) {
        return container;
      }
      if ((children && children.length) || checkActionAccess(item.meta.access)) {
        const target = children ? { ...item, children } : item;
        container.push(target);
      }

      // 重置菜单父级的fullPath，避免首个进入页面没有权限而导致整个菜单没办法使用
      container = container.map((item) => {
        return {
          ...item,
          handelShow: 5,
          fullPath: item.children ? item.children[0].fullPath : item.fullPath,
        };
      });
      return container.filter((item) => item.handelShow !== 4);
    }, []);
  };
  return di(menusList);
}
