<template>
  <el-scrollbar :class="{ 'is-collapse': collapse }" class="gallery-bar-container">
    <logo></logo>
    <el-tabs v-model="firstMenu" tab-position="left" @tab-click="handleTabClick">
      <el-tab-pane v-for="item in handleRoutes" :key="item.path" :name="item.path">
        <div v-if="!appMenus.includes(item.meta.title)" slot="label" :title="item.meta.title" class="gallery-grid">
          <el-popover v-if="collapse" placement="right" trigger="hover">
            <div class="menu-box">
              <div v-for="(route, index) in item.children" :key="index" class="menu-col">
                <p>{{ route.meta.title }}</p>
                <ul v-if="route.children && route.children.length" class="menu-ul">
                  <li v-for="(menu, indexT) in route.children" :key="indexT" class="menu-li" @click="menuClick(menu)">
                    {{ menu.meta.title }}
                  </li>
                </ul>
                <ul v-else class="menu-ul">
                  <li class="menu-li" @click="menuClick(route)">
                    {{ route.meta.title }}
                  </li>
                </ul>
              </div>
            </div>
            <div slot="reference">
              <vab-remix-icon
                v-if="item.meta.remixIcon"
                :icon-class="item.meta.remixIcon"
                :is-svg="item.meta.isCustomSvgIcon"
                class="vab-remix-icon"
              />
              {{ item.meta.title }}
            </div>
          </el-popover>
          <div v-else>
            <vab-remix-icon
              v-if="item.meta.remixIcon"
              :icon-class="item.meta.remixIcon"
              :is-svg="item.meta.isCustomSvgIcon"
              class="vab-remix-icon"
            />
            {{ item.meta.title }}
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-menu
      :background-color="variables['gallery-second-menu-background']"
      :default-active="activeMenu"
      :default-openeds="defaultOpens"
      :unique-opened="uniqueOpened"
      mode="vertical"
    >
      <vab-menu v-for="route in partialRoutes" :key="route.fullPath" :item="route"></vab-menu>
    </el-menu>
  </el-scrollbar>
</template>
<script>
import { defaultOpeneds, openFirstMenu, uniqueOpened } from "@/config/settings";
import variables from "@/config/variables.scss";
import { handleFirstMenu, handleHeadMenu } from "@/utils/routes";
import { mapGetters } from "vuex";
import { menus } from "./menus";
import { merchMenus } from "./merchMenus";
import { storeMenus } from "./storeMenus";
import { supplierMenus } from "./supplierMenus";

export default {
  name: "GalleryBar",
  data() {
    return {
      uniqueOpened,
      firstMenu: "",
      defaultOpens: defaultOpeneds,
      variables: variables,
      appMenus: [
        "单据模版",
        "分销",
        "营销",
        "收银台",
        "多商户",
        "多门店",
        "供应商管理端",
        "钱货日清对账",
        "积分商城",
        "销售提成",
        "设置",
        "商城",
        "满赠",
        "满额换购",
        "车载销售",
      ],
    };
  },
  computed: {
    ...mapGetters({
      collapse: "settings/collapse",
      routes: "routes/routes",
      partialRoutes: "routes/partialRoutes",
      // 区分后台类型，1-总后台，2-多门店，3-多商户，4-供应商
      systemType: "MUser/systemType",
    }),

    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    menuList() {
      if (this.systemType === 2) {
        return storeMenus();
      } else if (this.systemType === 3) {
        return merchMenus();
      } else if (this.systemType === 5) {
        return supplierMenus();
      } else {
        return menus();
      }
    },
    handleRoutes() {
      return this.menuList.filter((item) => item.hidden !== true && item.meta);
    },
  },
  watch: {
    $route: {
      handler(val) {
        const firstMenu = this.systemType === 2 ? handleHeadMenu() : handleFirstMenu();
        if (this.firstMenu !== firstMenu) {
          this.firstMenu = firstMenu;
          this.handleTabClick({ name: firstMenu }, true);
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleTabClick(tab, mounted) {
      const pageItem = this.menuList.find((item) => item.path === tab.name);
      if (!pageItem) {
        return;
      }
      const childrenArr = pageItem.children;
      this.$store.dispatch("routes/setPartialRoutes", childrenArr);
      if (mounted !== true && openFirstMenu) {
        this.$router.push(childrenArr[0].fullPath);
      }
      // this.$store.dispatch("settings/openSideBar");
    },
    menuClick(route) {
      this.$router.push(route.fullPath);
    },
  },
};
</script>
<style lang="scss" scoped>
@mixin active {
  &:hover {
    color: $base-color-blue;
    background-color: $base-gallery-second-menu-background-active !important;

    i,
    svg {
      color: $base-color-blue;
    }
  }

  &.is-active {
    color: $base-color-blue;
    background-color: $base-gallery-second-menu-background-active !important;
  }
}

.gallery-bar-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: $base-z-index;
  width: $base-left-menu-width;
  height: 100vh;
  overflow: hidden;
  background: $base-gallery-second-menu-background;
  box-shadow: $base-box-shadow;
  transition: width $base-transition-time;

  .gallery-grid {
    width: $base-left-menu-width-min;
    height: $base-left-menu-width-min;
    padding: 10px;
    padding-top: calc((#{$base-left-menu-width-min} - 55px) / 2);
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
    svg {
      display: block;
      height: 20px;
      margin: auto;
      margin-top: 10px;
      margin-bottom: -7px;
    }

    [class*="ri-"] {
      display: block;
      height: 20px;
      margin: auto;
    }
  }

  ::v-deep {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .el-tabs {
      position: fixed;
      width: $base-left-menu-width-min;

      .el-tabs__nav {
        height: calc(100vh - #{$base-logo-height});
        background: $base-gallery-first-menu-background;
        overflow-y: auto;
      }

      .el-tabs__item {
        height: auto;
        padding: 0;
        line-height: auto;
        color: $base-color-white;

        &.is-active {
          background: $base-color-blue;
        }
      }
    }

    .el-tabs__active-bar.is-left,
    .el-tabs--left .el-tabs__nav-wrap.is-left::after {
      display: none;
    }

    .el-tabs + .el-menu {
      left: $base-left-menu-width-min;
      display: fixed;
      width: calc(#{$base-left-menu-width} - #{$base-left-menu-width-min});
      border: 0;
    }

    .el-menu {
      border: 0;

      .vab-fas-icon {
        padding-right: 3px;
        font-size: $base-font-size-default;
      }

      .vab-remix-icon {
        padding-right: 3px;
        font-size: $base-font-size-default + 2;
      }

      .el-menu-item,
      .el-submenu__title {
        height: $base-menu-item-height;
        overflow: hidden;
        line-height: $base-menu-item-height;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;

        @include active;
      }
    }
  }

  &.is-collapse {
    ::v-deep {
      width: 0;
    }
  }
}

/*  自定义menu样式*/
.menu-box {
  display: flex;
  padding: 10px 0;
  .menu-col {
    padding: 0 24px;
    font-size: 14px;
    border-right: 1px solid #eeeeee;
    &:last-child {
      border-right: 0;
    }
    p {
      font-weight: 300;
      color: rgba(0, 0, 0, 0.6);
    }
    .menu-ul {
      .menu-li {
        padding-top: 16px;
        color: #000000;
        cursor: pointer;
        &:hover {
          color: $base-color-blue;
        }
      }
    }
  }
}
</style>
