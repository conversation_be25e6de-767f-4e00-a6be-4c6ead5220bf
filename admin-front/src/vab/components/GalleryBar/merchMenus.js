import { checkActionAccess } from "@/access/check";
import * as nodes from "@/access/node";
import store from "@/store";
const menusList = [
  {
    path: "/",
    redirect: "/MerchIndex",
    meta: {
      title: "概况",
      remixIcon: "mac-line",
      affix: true,
    },
    children: [
      {
        path: "/MerchIndex",
        name: "MerchIndex",
        noKeepAlive: true,
        meta: {
          title: "经营概况",
          remixIcon: "mac-line",
          affix: true,
        },
        fullPath: "/MerchIndex",
      },
    ],
    fullPath: "/",
  },
  {
    path: "/goods",
    redirect: "/goods/manageG/BaseDataList",
    alwaysShow: true,
    meta: {
      title: "商品",
      remixIcon: "shopping-bag-line",
    },
    children: [
      {
        path: "/goods/manageG",
        name: "ManageG",
        fullPath: "/goods/sale/PublishGoods",
        meta: {
          title: "商品管理",
          remixIcon: "apps-line",
        },
        children: [
          {
            path: "/goods/sale/MerchantsGoods",
            name: "MerchantsGoods",
            meta: {
              title: "商品列表",
            },
            fullPath: "/goods/sale/MerchantsGoods",
          },
          {
            path: "/goods/sale/PriceTable",
            name: "PriceTable",
            meta: {
              title: "调价表",
              // access: nodes.PriceTable,
            },
            fullPath: "/goods/sale/PriceTable",
          },
          // {
          //   path: "/goods/manageG/BaseDataList",
          //   name: "BaseDataList",
          //   meta: {
          //     title: "平台商品",
          //     access: nodes.BaseDataList,
          //   },
          //   fullPath: "/goods/manageG/BaseDataList",
          // },
          // {
          //   path: "/goods/manageG/GoodsClassify",
          //   name: "GoodsClassify",
          //   meta: {
          //     title: "商品分类",
          //     access: nodes.GoodsClassify,
          //   },
          //   fullPath: "/goods/manageG/GoodsClassify",
          // },
          // {
          //   path: "/goods/manageG/BrandManage",
          //   name: "BrandManage",
          //   meta: {
          //     title: "商品品牌",
          //     access: nodes.BrandManage,
          //   },
          //   fullPath: "/goods/manageG/BrandManage",
          // },
          // {
          //   path: "/goods/manageG/UnitMeasurement",
          //   name: "UnitMeasurement",
          //   meta: {
          //     title: "单位管理",
          //     access: nodes.UnitSet,
          //   },
          //   fullPath: "/goods/manageG/UnitMeasurement",
          // },
          // {
          //   path: "/goods/manageG/SpecManage",
          //   name: "SpecManage",
          //   meta: {
          //     title: "属性管理",
          //     access: nodes.specManage,
          //   },
          //   fullPath: "/goods/manageG/SpecManage",
          // },
        ],
      },
    ],
    fullPath: "/goods",
  },
  /*{
    path: "/order",
    name: "Order",
    redirect: "/order/manageO/orderQuery",
    alwaysShow: true,
    meta: {
      title: "订单",
      remixIcon: "file-list-2-line",
    },
    children: [
      {
        path: "/order/manageO/orderQuery",
        name: "ManageO",
        fullPath: "/order/manageO/orderQuery",
        meta: {
          title: "订单管理",
          remixIcon: "list-unordered",
        },
        children: [
          /* {
            path: "/order/manageO/newOrderList",
            name: "NewOrderList",
            meta: {
              title: "新订单列表",
              access: nodes.newOrderList,
            },
            fullPath: "/order/manageO/newOrderList",
          },

          {
            path: "/order/manageO/orderQuery",
            name: "OrderQuery",
            meta: {
              title: "订货单",
              access: nodes.orderQuery,
            },
            fullPath: "/order/manageO/orderQuery",
          },
          {
            path: "/order/manageO/selfOrder?type=2",
            name: "SelfOrder",
            meta: {
              title: "自提订单",
              access: nodes.selfOrder,
            },
            fullPath: "/order/manageO/selfOrder?type=2",
          },
          {
            path: "/order/manageO/ReturnWarehousingOrder",
            name: "ReturnWarehousingOrder",
            meta: {
              title: "退货单",
              access: nodes.ReturnWarehousingOrder,
            },
            fullPath: "/order/manageO/ReturnWarehousingOrder",
          },
          // {
          //   path: "/order/manageO/CancelOrder",
          //   name: "CancelOrder",
          //   meta: {
          //     title: "已取消",
          //     access: nodes.CancelOrder,
          //   },
          //   fullPath: "/order/manageO/CancelOrder",
          // },
          // {
          //   path: "/order/manageO/OrderAdd",
          //   name: "OrderAdd",
          //   meta: {
          //     noKeepAlive: true,
          //     title: "代客下单",
          //     access: nodes.OrderAdd,
          //   },
          //   fullPath: "/order/manageO/OrderAdd",
          // },
        ],
      },
      // {
      //   path: "/order/saleO",
      //   name: "SaleO",
      //   fullPath: "/order/saleO/AddSaleOrder",
      //   meta: {
      //     title: "销售单管理",
      //     remixIcon: "bill-line",
      //   },
      //   children: [
      //     {
      //       path: "/order/saleO/AddSaleOrder",
      //       name: "AddSaleOrder",
      //       meta: {
      //         title: "创建销售单",
      //         access: nodes.addSaleOrder,
      //       },
      //       fullPath: "/order/saleO/AddSaleOrder",
      //     },
      //     {
      //       path: "/order/saleO/SaleOrderList",
      //       name: "SaleOrderList",
      //       meta: {
      //         title: "销售单列表",
      //         access: nodes.saleOrderList,
      //       },
      //       fullPath: "/order/saleO/SaleOrderList",
      //     },
      //     {
      //       path: "/order/saleO/CashierOrder",
      //       name: "CashierOrder",
      //       meta: {
      //         title: "收银台订单",
      //         access: nodes.saleOrderList,
      //       },
      //       fullPath: "/order/saleO/CashierOrder",
      //     },
      //   ],
      // },
      {
        path: "/order/SaleTotalForm",
        name: "SaleTotalForm",
        fullPath: "/order/SaleTotalForm/GoodsForm",
        meta: {
          title: "销售报表",
          remixIcon: "file-text-line",
        },
        children: [
          {
            path: "/order/SaleTotalForm/GoodsForm",
            name: "GoodsForm",
            meta: {
              title: "商品汇总表",
              access: nodes.OrderStatistics,
            },
            fullPath: "/order/SaleTotalForm/GoodsForm",
          },
          // {
          //   path: "/order/SaleTotalForm/CustomerForm",
          //   name: "CustomerForm",
          //   meta: {
          //     title: "客户汇总表",
          //     access: nodes.OrderStatistics,
          //   },
          //   fullPath: "/order/SaleTotalForm/CustomerForm",
          // },
          // {
          //   path: "/order/SaleTotalForm/StaffForm",
          //   name: "StaffForm",
          //   meta: {
          //     title: "人员汇总表",
          //     access: nodes.OrderStatistics,
          //   },
          //   fullPath: "/order/SaleTotalForm/StaffForm",
          // },
        ],
      },
    ],
    fullPath: "/order",
  },*/
  /* {
    path: "/Customer",
    name: "Customer",
    redirect: "/Customer/CustomerAdmin/CustomerList",
    alwaysShow: true,
    meta: {
      title: "客户",
      remixIcon: "contacts-line",
    },
    children: [
      {
        path: "/Customer/CustomerAdmin",
        name: "CustomerAdmin",
        fullPath: "/Customer/CustomerAdmin/CustomerList",
        meta: {
          title: "客户管理",
          remixIcon: "user-3-line",
        },
        children: [
          {
            path: "/Customer/CustomerAdmin/CustomerList",
            name: "CustomerList",
            meta: {
              title: "客户列表",
              access: nodes.CustomerList,
            },
            fullPath: "/Customer/CustomerAdmin/CustomerList",
          },
          {
            path: "/Customer/CustomerAdmin/CustomerType",
            name: "CustomerType",
            meta: {
              title: "客户类型",
              access: nodes.CustomerType,
            },
            fullPath: "/Customer/CustomerAdmin/CustomerType",
          },
          {
            path: "/Customer/CustomerAdmin/CustomerQuery",
            name: "CustomerQuery",
            meta: {
              title: "客户查询",
              access: nodes.CustomerQuery,
            },
            fullPath: "/Customer/CustomerAdmin/CustomerQuery",
          },
        ],
      },
      {
        path: "/Customer/CustomerCheck",
        name: "CustomerCheck",
        fullPath: "/Customer/CustomerCheck/NotCheck",
        meta: {
          title: "客户审核",
          remixIcon: "user-star-line",
        },
        children: [
          {
            path: "/Customer/CustomerCheck/NotCheck",
            name: "NotCheck",
            meta: {
              title: "未审核",
              access: nodes.NotCheck,
            },
            fullPath: "/Customer/CustomerCheck/NotCheck",
          },
          {
            path: "/Customer/CustomerCheck/noPerfectData",
            name: "NoPerfectData",
            meta: {
              title: "待完善资料",
              access: nodes.getAuditAllCustomer,
            },
            fullPath: "/Customer/CustomerCheck/noPerfectData",
          },
        ],
      },
    ],
    fullPath: "/Customer",
  },*/
  /*{
    path: "/Purchase",
    redirect: "/Purchase/ManageP/Supplier",
    alwaysShow: true,
    meta: {
      title: "采购",
      remixIcon: "shopping-cart-line",
    },
    children: [
      {
        path: "/Purchase/ManageP",
        name: "ManageP",
        fullPath: "/Purchase/ManageP/Supplier",
        meta: {
          title: "采购管理",
          remixIcon: "shopping-cart-line",
        },
        children: [
          {
            path: "/Purchase/ManageP/PurchaseOrder",
            name: "PurchaseOrder",
            meta: {
              title: "采购单",
              access: nodes.PurchaseOrder,
            },
            fullPath: "/Purchase/ManageP/PurchaseOrder",
          },
          {
            path: "/Purchase/ManageP/PurchaseReturnOrder",
            name: "PurchaseReturnOrder",
            meta: {
              title: "采购退货单",
              access: nodes.PurchaseReturnOrder,
            },
            fullPath: "/Purchase/ManageP/PurchaseReturnOrder",
          },
          {
            path: "/Purchase/ManageP/PurchaseDetail",
            name: "PurchaseDetail",
            meta: {
              title: "采购明细",
              access: nodes.PurchaseDetail,
            },
            fullPath: "/Purchase/ManageP/PurchaseDetail",
          },
          {
            path: "/Purchase/ManageP/Supplier",
            name: "Supplier",
            meta: {
              title: "供应商管理",
              access: nodes.Supplier,
            },
            fullPath: "/Purchase/ManageP/Supplier",
          },
        ],
      },
      {
        path: "/Purchase/PurchaseTotalForm",
        name: "PurchaseTotalForm",
        fullPath: "/Purchase/PurchaseTotalForm/GoodsForm",
        meta: {
          title: "采购报表",
          access: nodes.PurchaseStatistics,
          remixIcon: "file-text-line",
        },
        children: [
          {
            path: "/Purchase/PurchaseTotalForm/GoodsForm",
            name: "PurchaseGoodsForm",
            meta: {
              title: "商品汇总表",
              access: nodes.PurchaseStatisticsGetAllPurchaseByFields,
            },
            fullPath: "/Purchase/PurchaseTotalForm/GoodsForm",
          },
          {
            path: "/Purchase/PurchaseTotalForm/SupplierForm",
            name: "PurchaseSupplierForm",
            meta: {
              title: "供应商汇总表",
              access: nodes.GetSupplierAllPurchaseByFields,
            },
            fullPath: "/Purchase/PurchaseTotalForm/SupplierForm",
          },
          {
            path: "/Purchase/PurchaseTotalForm/StaffForm",
            name: "PurchaseStaffForm",
            meta: {
              title: "人员汇总表",
              access: nodes.GetStaffAllPurchaseByFields,
            },
            fullPath: "/Purchase/PurchaseTotalForm/StaffForm",
          },
        ],
      },
    ],
    fullPath: "/Purchase",
  },*/
  {
    path: "/stock",
    redirect: "/stock/OutIn/outgoing",
    alwaysShow: true,
    meta: {
      title: "库存",
      remixIcon: "home-8-line",
    },
    children: [
      {
        path: "/Purchase/ManageP",
        name: "ManageP",
        fullPath: "/Purchase/ManageP/Supplier",
        meta: {
          title: "采购管理",
          remixIcon: "shopping-cart-line",
        },
        children: [
          {
            path: "/Purchase/ManageP/Merchant",
            name: "Merchant",
            meta: {
              title: "商户入库单",
            },
            fullPath: "/Purchase/ManageP/Merchant",
          },
          // {
          //   path: "/Purchase/ManageP/PurchaseOrder",
          //   name: "PurchaseOrder",
          //   meta: {
          //     title: "采购单",
          //     // access: nodes.PurchaseOrder,
          //   },
          //   fullPath: "/Purchase/ManageP/PurchaseOrder",
          // },
          /* {
            path: "/Purchase/ManageP/PurchaseReturnOrder",
            name: "PurchaseReturnOrder",
            meta: {
              title: "采购退货单",
              // access: nodes.PurchaseReturnOrder,
            },
            fullPath: "/Purchase/ManageP/PurchaseReturnOrder",
          },*/
          {
            path: "/Purchase/ManageP/PurchaseDetail",
            name: "PurchaseDetail",
            meta: {
              title: "采购明细",
              // access: nodes.PurchaseDetail,
            },
            fullPath: "/Purchase/ManageP/PurchaseDetail",
          },
          // {
          //   path: "/Purchase/ManageP/Supplier",
          //   name: "Supplier",
          //   meta: {
          //     title: "供应商管理",
          //     access: nodes.Supplier,
          //   },
          //   fullPath: "/Purchase/ManageP/Supplier",
          // },
        ],
      },
      {
        path: "/stock/WarehouseAdmin",
        name: "WarehouseAdmin",
        fullPath: "/stock/OutIn/outgoing",
        meta: {
          title: "库存管理",
          remixIcon: "home-gear-line",
        },
        children: [
          /* {
            path: "/stock/OutIn/outgoing",
            name: "Outgoing",
            meta: {
              title: "出库管理",
              access: nodes.InventoryOut,
            },
            fullPath: "/stock/OutIn/outgoing",
          },
          {
            path: "/stock/OutIn/storage",
            name: "Storage",
            meta: {
              title: "入库管理",
              access: nodes.InventoryIn,
            },
            fullPath: "/stock/OutIn/storage",
          },*/
          {
            path: "/stock/WarehouseAdmin/query",
            name: "Query",
            meta: {
              title: "库存查询",
            },
            fullPath: "/stock/WarehouseAdmin/query",
          },
          {
            path: "/stock/WarehouseAdmin/flowing",
            name: "Flowing",
            meta: {
              title: "库存流水",
              // access: nodes.flowing,
            },
            fullPath: "/stock/WarehouseAdmin/flowing",
          },
          {
            path: "/stock/summary/WarehouseStockSummary",
            name: "WarehouseStockSummary",
            meta: {
              title: "库存汇总",
              remixIcon: "file-text-line",
              // access: nodes.inventoryStatistics,
            },
            fullPath: "/stock/summary/WarehouseStockSummary",
          },
          {
            path: "/stock/WarehouseAdmin/Batch",
            name: "Batch",
            meta: {
              title: "批次流水",
              access: nodes.Batch,
            },
            fullPath: "/stock/WarehouseAdmin/Batch",
          },
          /* {
             path: "/stock/WarehouseAdmin/ShelfLife",
             name: "ShelfLife",
             meta: {
               title: "保质期查询",
               access: nodes.GetBatch,
             },
             fullPath: "/stock/WarehouseAdmin/ShelfLife",
           },*/
        ],
      },
      /* {
        path: "/stock/WarehouseManagement",
        name: "WarehouseManagement",
        fullPath: "/stock/WarehouseManagement/Management",
        meta: {
          title: "仓库管理",
          remixIcon: "home-6-line",
        },
        children: [
          {
            path: "/stock/WarehouseManagement/Management",
            name: "Management",
            meta: {
              title: "仓库管理",
              access: nodes.AllocationForm,
            },
            fullPath: "/stock/WarehouseManagement/Management",
          },
          {
            path: "/stock/WarehouseManagement/inventoryInfo",
            name: "InventoryInfo",
            meta: {
              title: "盘点单",
              access: nodes.inventoryInfo,
            },
            fullPath: "/stock/WarehouseManagement/inventoryInfo",
          },
          {
            path: "/stock/WarehouseManagement/AllocationForm",
            name: "AllocationForm",
            meta: {
              title: "调拨单",
              access: nodes.management,
            },
            fullPath: "/stock/WarehouseManagement/AllocationForm",
          },
        ],
      },*/
      // {
      //   path: "/stock/summary",
      //   name: "Summary",
      //   fullPath: "/stock/summary/WarehouseStockSummary",
      //   meta: {
      //     title: "汇总表",
      //     remixIcon: "file-text-line",
      //   },
      //   children: [
      //     {
      //       path: "/stock/summary/WarehouseStockSummary",
      //       name: "WarehouseStockSummary",
      //       meta: {
      //         title: "库存汇总",
      //         remixIcon: "file-text-line",
      //         access: nodes.inventoryStatistics,
      //       },
      //       fullPath: "/stock/summary/WarehouseStockSummary",
      //     },
      //   ],
      // },
    ],
    fullPath: "/stock",
  },
  /* {
    path: "/Reconciliation/SettlementStatement",
    fullPath: "/Reconciliation/SettlementStatement",
    meta: {
      title: "对账",
      remixIcon: "money-cny-circle-line",
    },
    children: [
      {
        path: "/Reconciliation/SettlementStatement",
        name: "SettlementStatement",
        meta: {
          title: "结算对账单",
          // access: nodes.inventoryStatistics,
        },
        fullPath: "/Reconciliation/SettlementStatement",
      },
    ],
  },*/
  {
    path: "/Settlement/SettlementLogs",
    fullPath: "/Settlement/SettlementLogs",
    meta: {
      title: "结算",
      remixIcon: "price-tag-2-line",
    },
    children: [
      {
        path: "/Settlement/SettlementLogs",
        name: "SettlementLogs",
        meta: {
          title: "结算记录",
          // access: nodes.inventoryStatistics,
        },
        fullPath: "/Settlement/SettlementLogs",
      },
    ],
  },
  {
    path: "/Finance/MultipleStore/InAndOutLogs",
    fullPath: "/Finance/MultipleStore/InAndOutLogs",
    meta: {
      title: "财务",
      remixIcon: "money-cny-box-line",
    },
    children: [
      {
        path: "/Finance/MultipleStore/InAndOutLogs",
        meta: {
          title: "财务报表",
          remixIcon: "money-cny-box-line",
        },
        fullPath: "/Finance/MultipleStore/InAndOutLogs",
        children: [
          {
            path: "/Finance/MultipleStore/InAndOutLogs",
            name: "InAndOutLogs",
            meta: {
              title: "收支记录",
              // access: nodes.inventoryStatistics,
            },
            fullPath: "/Finance/MultipleStore/InAndOutLogs",
          },
          {
            path: "/Finance/MultipleStore/WithdrawLogs",
            name: "WithdrawLogs",
            meta: {
              title: "提现记录",
              // access: nodes.inventoryStatistics,
            },
            fullPath: "/Finance/MultipleStore/WithdrawLogs",
          },
        ],
      },
    ],
  },
];
export function merchMenus() {
  // return menusList;
  const di = (items) => {
    return items.reduce((container, item) => {
      let { children } = item;
      if (children) {
        children = di(children);
      }
      if (children && !children.length) {
        return container;
      }
      if ((children && children.length) || checkActionAccess(item.meta.access)) {
        const target = children ? { ...item, children } : item;
        container.push(target);
      }

      // 重置菜单父级的fullPath，避免首个进入页面没有权限而导致整个菜单没办法使用
      container = container.map((item) => {
        let handelShow = 5;
        if (item.meta.title === "商品资料") {
          handelShow = parseInt(store.getters["MUser/enterpriseScope"]);
        } else if (item.meta.title === "保质期查询") {
          handelShow = parseInt(store.getters["MUser/shelfLifeSetUp"]);
        }

        return {
          ...item,
          handelShow: handelShow,
          fullPath: item.children ? item.children[0].fullPath : item.fullPath,
        };
      });
      return container.filter((item) => item.handelShow !== 4);
    }, []);
  };
  return di(menusList);
}
