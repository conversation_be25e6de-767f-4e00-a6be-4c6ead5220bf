import { checkActionAccess } from "@/access/check";
import * as nodes from "@/access/node";
import store from "@/store";
const menusList = [
  {
    path: "/SingleStore/goods",
    redirect: "/SingleStore/goods/GoodsAdministration",
    alwaysShow: true,
    meta: {
      title: "商品",
      remixIcon: "shopping-bag-line",
    },
    fullPath: "/SingleStore/goods",
    children: [
      {
        path: "/SingleStore/goods/GoodsAdministration",
        name: "GoodsAdministration",
        meta: {
          title: "商品管理",
          remixIcon: "apps-line",
        },
        fullPath: "/SingleStore/goods/GoodsAdministration",
        children: [
          {
            path: "/SingleStore/goods/GoodsAdministration",
            name: "GoodsAdministration",
            meta: {
              title: "商品管理",
            },
            fullPath: "/SingleStore/goods/GoodsAdministration",
          },
          {
            path: "/SingleStore/goods/GoodsData",
            name: "GoodsData",
            meta: {
              title: "商品资料",
            },
            fullPath: "/SingleStore/goods/GoodsData",
          },
        ],
      },
      {
        path: "/SingleStore/goods/priceTable",
        name: "priceTable",
        meta: {
          title: "价格管理",
          remixIcon: "bookmark-3-line",
        },
        fullPath: "/SingleStore/goods/priceTable",
        children: [
          {
            path: "/SingleStore/goods/priceTable",
            name: "priceTable",
            meta: {
              title: "价格管理",
            },
            fullPath: "/SingleStore/goods/priceTable",
          },
          {
            path: "/SingleStore/goods/priceAdjust",
            name: "priceAdjust",
            meta: {
              title: "商品调价单",
            },
            fullPath: "/SingleStore/goods/priceAdjust",
          },
          {
            path: "/SingleStore/goods/priceAdjustCustomer",
            name: "priceAdjustCustomer",
            meta: {
              title: "客户调价",
            },
            fullPath: "/SingleStore/goods/priceAdjustCustomer",
          },
          {
            path: "/SingleStore/goods/priceAdjustCustomerType",
            name: "priceAdjustCustomerType",
            meta: {
              title: "客户类型调价",
            },
            fullPath: "/SingleStore/goods/priceAdjustCustomerType",
          },
        ],
      },
    ],
  },
  {
    path: "/SingleStore/order",
    redirect: "/SingleStore/order/OrderList",
    alwaysShow: true,
    meta: {
      title: "订单",
      remixIcon: "file-list-2-line",
    },
    fullPath: "/SingleStore/order/OrderList",
    children: [
      {
        path: "/SingleStore/order/StoreOrderAdd",
        name: "OrderAdd",
        meta: {
          title: "代客下单",
          remixIcon: "edit-circle-line",
        },
        fullPath: "/SingleStore/order/StoreOrderAdd",
      },
      {
        path: "/SingleStore/order/OrderList",
        meta: {
          title: "订单管理",
          remixIcon: "list-unordered",
        },
        fullPath: "/SingleStore/order/OrderList",
        children: [
          {
            path: "/SingleStore/order/OrderList/all",
            name: "OrderListAll",
            meta: {
              title: "全部订单",
            },
            fullPath: "/SingleStore/order/OrderList/all",
          },
          {
            path: "/SingleStore/order/OrderList/waitAudit",
            name: "OrderListWaitAudit",
            meta: {
              title: "待审核",
            },
            fullPath: "/SingleStore/order/OrderList/waitAudit",
          },
          {
            path: "/SingleStore/order/OrderList/waitOutStock",
            name: "OrderListWaitOutStock",
            meta: {
              title: "待发货",
            },
            fullPath: "/SingleStore/order/OrderList/waitOutStock",
          },
          {
            path: "/SingleStore/order/OrderList/hasOutStock",
            name: "OrderListHasOutStock",
            meta: {
              title: "待收货",
            },
            fullPath: "/SingleStore/order/OrderList/hasOutStock",
          },
          {
            path: "/SingleStore/order/OrderList/finish",
            name: "OrderListFinish",
            meta: {
              title: "已完成",
            },
            fullPath: "/SingleStore/order/OrderList/finish",
          },
          {
            path: "/SingleStore/order/OrderList/close",
            name: "OrderListClose",
            meta: {
              title: "已关闭",
            },
            fullPath: "/SingleStore/order/OrderList/close",
          },
        ],
      },
      {
        path: "/SingleStore/order/ReturnOrder",
        name: "ReturnOrder",
        meta: {
          title: "退货单",
          remixIcon: "logout-circle-line",
        },
        fullPath: "/SingleStore/order/ReturnOrder",
      },
    ],
  },
  {
    path: "/SingleStore/staffSet",
    redirect: "/SingleStore/staffSet/StaffList",
    alwaysShow: true,
    meta: {
      title: "员工",
      remixIcon: "user-settings-line",
    },
    fullPath: "/SingleStore/staffSet/StaffList",
    children: [
      {
        path: "/SingleStore/staffSet/StaffList",
        name: "StaffList",
        meta: {
          title: "员工列表",
          remixIcon: "user-5-line",
        },
        fullPath: "/SingleStore/staffSet/StaffList",
      },
      {
        path: "/SingleStore/staffSet/RoleList",
        name: "RoleList",
        meta: {
          title: "角色管理",
          remixIcon: "shield-user-line",
        },
        fullPath: "/SingleStore/staffSet/RoleList",
      },
    ],
  },
  {
    path: "/SingleStore/Customer",
    redirect: "/SingleStore/Customer/StoreCustomer",
    alwaysShow: true,
    meta: {
      title: "客户",
      remixIcon: "contacts-line",
    },
    fullPath: "/SingleStore/Customer/StoreCustomer",
    children: [
      {
        path: "/SingleStore/Customer/StoreCustomer",
        name: "StoreCustomer",
        meta: {
          title: "客户列表",
          remixIcon: "user-3-line",
        },
        fullPath: "/SingleStore/Customer/StoreCustomer",
      },
    ],
  },
  {
    path: "/SingleStore/purchase",
    redirect: "/SingleStore/purchase/purchaseList",
    alwaysShow: true,
    meta: {
      title: "采购",
      remixIcon: "shopping-cart-line",
    },
    fullPath: "/SingleStore/purchase/purchaseList",
    children: [
      {
        path: "/SingleStore/purchase/purchaseList",
        name: "purchaseList",
        meta: {
          title: "采购管理",
          remixIcon: "shopping-cart-line",
        },
        fullPath: "/SingleStore/purchase/purchaseList",
        children: [
          {
            path: "/SingleStore/purchase/purchaseList",
            name: "purchaseList",
            meta: {
              title: "采购单",
            },
            fullPath: "/SingleStore/purchase/purchaseList",
          },
          {
            path: "/SingleStore/purchase/purchaseReturnList",
            name: "purchaseReturnList",
            meta: {
              title: "采购退货单",
            },
            fullPath: "/SingleStore/purchase/purchaseReturnList",
          },
          {
            path: "/SingleStore/purchase/purchaseDetailInfo",
            name: "purchaseDetailInfo",
            meta: {
              title: "采购明细",
            },
            fullPath: "/SingleStore/purchase/purchaseDetailInfo",
          },
          {
            path: "/SingleStore/purchase/SupplierList",
            name: "SupplierList",
            meta: {
              title: "供应商管理",
            },
            fullPath: "/SingleStore/purchase/SupplierList",
          },
        ],
      },
    ],
  },
  {
    path: "/SingleStore/Inventory",
    redirect: "/SingleStore/Inventory/InventoryOut",
    alwaysShow: true,
    meta: {
      title: "库存",
      remixIcon: "home-8-line",
    },
    fullPath: "/SingleStore/Inventory/InventoryOut",
    children: [
      {
        path: "/SingleStore/Inventory/InventoryOut",
        meta: {
          title: "库存管理",
          remixIcon: "home-gear-line",
        },
        fullPath: "/SingleStore/Inventory/InventoryOut",
        children: [
          {
            path: "/SingleStore/Inventory/InventoryOut",
            name: "InventoryOut",
            meta: {
              title: "出库管理",
            },
            fullPath: "/SingleStore/Inventory/InventoryOut",
          },
          {
            path: "/SingleStore/Inventory/InventoryIn",
            name: "InventoryIn",
            meta: {
              title: "入库管理",
            },
            fullPath: "/SingleStore/Inventory/InventoryIn",
          },
          {
            path: "/SingleStore/Inventory/InventoryQuery",
            name: "InventoryQuery",
            meta: {
              title: "库存查询",
            },
            fullPath: "/SingleStore/Inventory/InventoryQuery",
          },
          {
            path: "/SingleStore/Inventory/InventoryFlowing",
            name: "InventoryFlowing",
            meta: {
              title: "库存流水",
            },
            fullPath: "/SingleStore/Inventory/InventoryFlowing",
          },
          {
            path: "/SingleStore/Inventory/InventoryBatch",
            name: "InventoryBatch",
            meta: {
              title: "批次流水",
            },
            fullPath: "/SingleStore/Inventory/InventoryBatch",
          },
          {
            path: "/SingleStore/Inventory/InventoryShelfLife",
            name: "InventoryShelfLife",
            meta: {
              title: "保质期查询",
            },
            fullPath: "/SingleStore/Inventory/InventoryShelfLife",
          },
        ],
      },
      {
        path: "/SingleStore/Inventory/InventoryManagement",
        meta: {
          title: "仓库管理",
          remixIcon: "home-6-line",
        },
        fullPath: "/SingleStore/Inventory/InventoryManagement",
        children: [
          {
            path: "/SingleStore/Inventory/InventoryManagement",
            name: "InventoryManagement",
            meta: {
              title: "仓库管理",
            },
            fullPath: "/SingleStore/Inventory/InventoryManagement",
          },
          {
            path: "/SingleStore/Inventory/InventoryCheck",
            name: "InventoryCheck",
            meta: {
              title: "盘点单",
            },
            fullPath: "/SingleStore/Inventory/InventoryCheck",
          },
          {
            path: "/SingleStore/Inventory/InventoryAllocation",
            name: "InventoryAllocation",
            meta: {
              title: "调拨单",
            },
            fullPath: "/SingleStore/Inventory/InventoryAllocation",
          },
        ],
      },
    ],
  },
  {
    path: "/SingleStore/storeSet",
    redirect: "/SingleStore/storeSet/storeDetail",
    alwaysShow: true,
    meta: {
      title: "设置",
      remixIcon: "settings-4-line",
    },
    fullPath: "/SingleStore/storeSet/storeDetail",
    children: [
      {
        path: "/SingleStore/storeSet/storeDetail",
        name: "StoreDetail",
        meta: {
          title: "门店信息",
          remixIcon: "list-settings-line",
        },
        fullPath: "/SingleStore/storeSet/storeDetail",
      },
    ],
  },
];
export function storeMenus() {
  // return menusList;
  const di = (items) => {
    return items.reduce((container, item) => {
      let { children } = item;
      if (children) {
        children = di(children);
      }
      if (children && !children.length) {
        return container;
      }
      if ((children && children.length) || checkActionAccess(item.meta.access)) {
        const target = children ? { ...item, children } : item;
        container.push(target);
      }

      // 重置菜单父级的fullPath，避免首个进入页面没有权限而导致整个菜单没办法使用
      container = container.map((item) => {
        let handelShow = 5;
        if (item.meta.title === "商品资料") {
          handelShow = parseInt(store.getters["MUser/enterpriseScope"]);
        } else if (item.meta.title === "保质期查询") {
          handelShow = parseInt(store.getters["MUser/shelfLifeSetUp"]);
        }

        return {
          ...item,
          handelShow: handelShow,
          fullPath: item.children ? item.children[0].fullPath : item.fullPath,
        };
      });
      return container.filter((item) => item.handelShow !== 4);
    }, []);
  };
  return di(menusList);
}
