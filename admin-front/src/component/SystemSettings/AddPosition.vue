<template>
  <div>
    <!--    <el-dialog-->
    <!--      :title="isEdit?'修改角色':'新增角色'"-->
    <!--      :visible.sync="visible"-->
    <!--      width="35%"-->
    <!--      @close="close"-->
    <!--    >-->
    <el-form ref="form" :rules="rules" size="small" :model="form" label-width="120px">
      <el-form-item label="角色名称" prop="roleName">
        <el-input v-model="form.roleName" placeholder="请输入角色名称"></el-input>
      </el-form-item>
      <el-form-item label="分组到">
        <el-select v-model="form.pid" placeholder="请选择分组" clearable>
          <el-option v-for="item in position_data" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="text-align: right; padding-right: 20px">
        <el-button type="primary" @click="confirm">确 定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getAllRole, addRole, updateRole, getRoleInfo } from "@/api/Department";
export default {
  name: "AddPosition",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      form: {
        roleName: "",
        pid: "",
      },
      position_data: [],
      rules: {
        roleName: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
      },
    };
  },
  created() {
    if (this.id) {
      this.getRoleInfo();
    }
    this.getAllRole();
  },
  methods: {
    //  获取角色列表
    async getAllRole() {
      const data = await getAllRole({
        page: 1,
        pageSize: 999,
      });

      this.position_data = data.data;
    },
    //  创建角色
    async addRole() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const data = await addRole(this.form);

          this.$message({
            message: "创建成功",
            type: "success",
          });
          this.$emit("confirm");
        }
      });
    },
    //  修改角色
    async updateRole() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.id) {
            const data = await updateRole(this.id, this.form);

            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.$emit("confirm");
          }
        }
      });
    },
    //  获取角色详情
    async getRoleInfo() {
      if (!this.id) return;
      const data = await getRoleInfo(this.id);

      this.form = data.data;
    },
    close() {
      this.$emit("close");
    },
    confirm() {
      if (!this.form.roleName.trim()) {
        this.$message.warning("角色名称不能为空");
        return;
      }
      this.close();
      if (this.id) {
        this.updateRole();
      } else {
        this.addRole();
      }
    },
  },
};
</script>

<style scoped></style>
