<template>
  <div>
    <el-dialog
      :title="isEdit ? '修改部门' : '新建部门'"
      :visible.sync="visible"
      :modal="modal"
      width="35%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form ref="form" :rules="rules" size="small" :model="form" label-width="120px">
        <el-form-item label="部门名称" prop="departmentName">
          <el-input v-model="form.departmentName" placeholder="请输入部门名称"></el-input>
        </el-form-item>
        <el-form-item label="上级部门">
          <el-cascader
            v-model="department_position"
            :options="Department_data"
            :props="{
              label: 'departmentName',
              value: 'id',
              checkStrictly: true,
            }"
            clearable
            @change="departmentChange"
          >
            <div slot-scope="scope" @click="clickChange">
              {{ scope.data.departmentName }}
            </div>
          </el-cascader>
        </el-form-item>

        <el-form-item style="text-align: right; padding-right: 10px">
          <el-button type="primary" @click="confirm">确 定</el-button>
          <el-button @click="close">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { addDepartment, getAllDepartment, getDepartmentInfo, updateDepartment } from "@/api/Department";
export default {
  name: "AddDepartment",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    // 是否需要遮罩层
    modal: {
      type: Boolean,
      default: true,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      department_position: [],
      form: {
        pid: "0",
        order: "0", // 排序
        departmentName: "",
        desc: "",
        departmentPidPath: "",
      },
      Department_data: [],
      rules: {
        departmentName: [{ required: true, message: "请输入部门名称", trigger: "blur" }],
        desc: [{ required: true, message: "请选择上级部门", trigger: "change" }],
      },
    };
  },
  created() {
    this.getAllDepartment();
    if (this.isEdit) {
      this.getDepartmentInfo();
    }
  },
  methods: {
    departmentChange(val) {
      this.form.departmentPidPath = val.join(",");
    },
    // 获取部门列表 getAllDepartment
    async getAllDepartment() {
      const data = await getAllDepartment();

      this.Department_data = data.data;
    },
    //  详情  getDepartmentInfo
    async getDepartmentInfo() {
      if (!this.isEdit) return;
      const data = await getDepartmentInfo(this.id);

      this.department_position = data.data.extend.departmentPidPath.split(",").map((item) => {
        return parseInt(item);
      });

      const dataD = this.$_common.deepClone(data.data);
      delete dataD.extend;
      this.form = {
        ...dataD,
        departmentPidPath: data.data.extend.departmentPidPath,
      };
    },
    close() {
      this.$emit("close");
    },
    async confirm() {
      if (!this.form.departmentName.trim()) {
        this.$message.warning("部门名称不能为空");
        return;
      }
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          let target = {};
          const params = {
            ...this.form,
            pid: this.department_position.length ? this.department_position[this.department_position.length - 1] : "",
          };
          if (this.isEdit) {
            target = await updateDepartment(this.id, params);
          } else {
            target = await addDepartment(params);
          }
          const data = target;

          this.$message({
            message: "提交成功",
            type: "success",
          });
          // this.getAllDepartment()
          this.close();
          this.$emit("confirm");
        }
      });
    },
  },
};
</script>

<style scoped></style>
