<template>
  <div>
    <!--    <el-dialog-->
    <!--      :title="isEdit?'修改角色组':'新增角色组'"-->
    <!--      :visible.sync="visible"-->
    <!--      width="60%"-->
    <!--      @close="close"-->
    <!--    >-->
    <el-form ref="form" :rules="rules" size="small" :model="form" label-width="150px">
      <el-form-item label="角色组名称" prop="roleName">
        <el-input v-model="form.roleName" placeholder="请输入角色组名称"></el-input>
      </el-form-item>
      <el-form-item label="超级管理人员">
        <el-switch v-model="form.isAdministrator" :active-value="5" :inactive-value="4"></el-switch>
      </el-form-item>
      <el-form-item label=" 导航 / 功能菜单权限" style="background: #fff">
        <ul class="node-check" style="padding-bottom: 20px">
          <li v-for="item in fn_authority_list" :key="item.id" class="region-view">
            <div class="positon-view">
              <el-checkbox
                v-model="item.expand"
                :disabled="form.isAdministrator === 5"
                @change="firstChange(item.expand, item.children)"
              >
                {{ item.name }}
              </el-checkbox>
            </div>
            <div v-for="itemC in item.children" :key="itemC.id" style="padding: 0 25px">
              <el-checkbox
                v-model="itemC.expand"
                :disabled="form.isAdministrator === 5"
                @change="secondChange(itemC, itemC.children, item)"
              >
                {{ itemC.name }}
              </el-checkbox>
              <div v-for="itemD in itemC.children" :key="itemD.id" style="padding: 0 25px">
                <el-checkbox
                  v-model="itemD.expand"
                  :disabled="form.isAdministrator === 5"
                  @change="thirdChange(itemD, itemC, item, itemD.children)"
                >
                  {{ itemD.name }}
                </el-checkbox>
                <div style="padding: 0 25px">
                  <span v-for="itemDD in itemD.children" :key="itemDD.id" class="tree-node" style="margin-right: 10px">
                    <el-checkbox
                      v-model="itemDD.expand"
                      :disabled="form.isAdministrator === 5"
                      @change="fourChange(itemDD, itemD, itemC, item)"
                    >
                      {{ itemDD.name }}
                    </el-checkbox>
                  </span>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </el-form-item>
    </el-form>
    <div class="sun-bottom-view">
      <el-button type="primary" @click="confirm">确 定</el-button>
    </div>
    <!--    </el-dialog>-->
  </div>
</template>

<script>
import { getRoleInfo, updateRole, addRole } from "@/api/Department";
import { getAllModule } from "@/api/System";
export default {
  name: "AddPositionGroup",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      fn_authority_list: [],
      form: {
        roleName: "",
        isAdministrator: 4,
        pid: "",
        acl: [],
      },
      rules: {
        roleName: [{ required: true, message: "请输入角色组名称", trigger: "blur" }],
      },
    };
  },
  async created() {
    await this.getAllModule();
    if (this.id) {
      await this.getRoleInfo();
    }
  },
  methods: {
    // 获取菜单列表
    async getAllModule() {
      const { data } = await getAllModule({
        enableStatus: 5,
      });
      this.fn_authority_list = this.reduceNodeList(data);
    },
    reduceNodeList(data, expand) {
      let nodeList = data;
      for (let i in nodeList) {
        let item = nodeList[i];
        item.expand = !!expand;
        if (item.children) {
          this.reduceNodeList(item.children, expand);
        }
      }
      return nodeList;
    },
    /** 一级 */
    firstChange(isCheck, TwoData = []) {
      this.reduceNodeList(TwoData, isCheck);
    },
    /** 二级 */
    secondChange(twoData, threeData = [], oneData) {
      const isCheck = twoData.expand;
      this.reduceNodeList(threeData, isCheck);

      oneData.expand = !!oneData.children.find((itemO) => {
        return itemO.expand;
      });
    },
    /** 三级 */
    thirdChange(threeData, twoData = {}, oneData = {}, fourData) {
      const isCheck = threeData.expand;
      this.reduceNodeList(fourData, isCheck);

      twoData.expand = !!twoData.children.find((itemO) => {
        return itemO.expand;
      });
      oneData.expand = !!oneData.children.find((itemO) => {
        return itemO.expand;
      });
    },
    /** 四级 */
    fourChange(fourData, threeData = {}, twoData = {}, oneData = {}) {
      threeData.expand = !!threeData.children.find((itemO) => {
        return itemO.expand;
      });
      twoData.expand = !!twoData.children.find((itemO) => {
        return itemO.expand;
      });
      oneData.expand = !!oneData.children.find((itemO) => {
        return itemO.expand;
      });
      // this.nodeJsonData(this.form.acl, this.fn_authority_list)
    },
    nodeJsonData(nodeArr, data) {
      const nodeJson = nodeArr;
      for (let i in data) {
        let item = data[i];
        if (item.expand) {
          nodeJson.push({
            id: item.id,
            alias: item.alias,
            extend: item.extend,
            associate: item.associate,
            pid: item.pid || 0,
          });
        }
        if (item.children) {
          this.nodeJsonData(nodeJson, item.children);
        }
      }
      return nodeJson;
    },
    //  创建角色组
    async addRole() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const data = await addRole(this.form);

          this.$message({
            message: "创建成功",
            type: "success",
          });
        }
      });
    },
    //  修改角色
    async updateRole() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const data = await updateRole(this.id, this.form);

          this.$message({
            message: "修改成功",
            type: "success",
          });
        }
      });
    },
    //  获取角色详情
    async getRoleInfo() {
      if (!this.id) return;
      const { data } = await getRoleInfo(this.id);

      const acl = [];
      for (let i in data.acl) {
        acl.push(data.acl[i]);
      }
      this.form = {
        roleName: data.roleName,
        isAdministrator: data.isAdministrator || 4,
        pid: data.pid,
        acl: [],
      };
      const nodeList = this.$_common.deepClone(this.fn_authority_list);
      this.fn_authority_list = this.reduceNodeCheck(acl, nodeList);
    },
    reduceNodeCheck(checkTree, data) {
      const nodeList = data;
      nodeList.forEach((item) => {
        const indexCheck = checkTree.findIndex((itemC) => {
          return parseInt(itemC.id) === parseInt(item.id);
        });
        if (indexCheck >= 0) {
          item.expand = true;
          // item.showFieldJson = checkTree[indexCheck].showFieldJson
        }

        if (item.children) {
          this.reduceNodeCheck(checkTree, item.children);
        }
      });
      return nodeList;
    },
    close() {
      this.$emit("close");
    },
    async confirm() {
      if (!this.form.roleName.trim()) {
        this.$message.warning("名称不能为空");
        return;
      }
      if (this.form.isAdministrator === 5) {
        this.form.acl = [];
      } else {
        const acl = this.nodeJsonData(this.form.acl, this.fn_authority_list);
        this.form.acl = acl;
      }
      if (this.id) {
        await this.updateRole();
      } else {
        await this.addRole();
      }
      this.close();
      this.$emit("confirm");
    },
  },
};
</script>

<style scoped>
.positon-view {
  border-bottom: 1px solid #eee;
}
.region-view {
  background: #f7f8fa;
  padding: 0 20px;
}
.region-view:nth-child(even) {
  background: #fff;
}
.sun-bottom-view {
  text-align: right;
  padding-right: 20px;
  padding-bottom: 10px;
  position: fixed;
  bottom: 10px;
  right: 10px;
}
</style>
