<template>
  <div class="Container">
    <div v-if="isSearchDiv" class="btn-search-div">
      <div>
        <slot name="tip"></slot>
      </div>
      <vab-query-form>
        <vab-query-form-left-panel>
          <breadcrumb class="hidden-xs-only" />
        </vab-query-form-left-panel>
        <vab-query-form-right-panel>
          <div class="right-panel" style="text-align: right">
            <slot name="left"></slot>
            <span class="reset-btn" @click="resetSearch">重置筛选</span>
          </div>
        </vab-query-form-right-panel>
      </vab-query-form>
      <div class="b-button">
        <slot name="more"></slot>
      </div>
    </div>
    <div class="contmain">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "ContainerQuery",
  props: {
    isSearchDiv: {
      type: <PERSON>olean,
      default: true,
    },
  },
  methods: {
    resetSearch() {
      this.$baseEventBus.$emit("reloadRouterView");
    },
  },
};
</script>

<style>
.Container .el-divider--horizontal {
  margin: 0 0 10px;
}
.Container .el-card__body {
  padding: 10px;
}
.Container .el-form-item__label {
  font-size: 14px !important;
  color: #2d405e;
}
.b-button .el-form-item {
  margin-bottom: 10px !important;
}
</style>
<style scoped lang="scss">
.btn-search-div {
  padding: 16px 24px 6px;
  background-color: #ffffff;
  margin-bottom: 16px;
  border-radius: 3px;
}
.contmain {
  /*padding: 20px;*/
  border-radius: 3px;
  background-color: #ffffff;
}
.b-button {
  padding-top: 10px;
  text-align: left;
  /*width: calc(100% - 90px);*/
}
.right-panel {
  position: relative;
  text-align: right;
  padding-left: 80px;
  .reset-btn {
    display: inline-block;
    height: 32px;
    line-height: 32px;
    color: #4f5e7b;
    font-size: 14px;
    position: absolute;
    left: 0;
    top: 0;
    cursor: pointer;
    &:hover {
      color: $base-color-blue;
    }
  }
}
</style>
