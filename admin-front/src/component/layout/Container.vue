<template>
  <div class="Container">
    <div class="btn-search-div">
      <div>
        <slot name="tip"></slot>
      </div>
      <vab-query-form>
        <vab-query-form-left-panel>
          <breadcrumb class="hidden-xs-only" />
        </vab-query-form-left-panel>
        <vab-query-form-right-panel>
          <slot name="left"></slot>
        </vab-query-form-right-panel>
      </vab-query-form>
      <div class="b-button">
        <slot name="right"></slot>
      </div>
    </div>
    <div class="contmain">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "Container",
  props: {
    isSearchDiv: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style>
.Container .el-divider--horizontal {
  margin: 0 0 10px;
}
.Container .el-card__body {
  padding: 10px;
}
.Container .el-form-item__label {
  font-size: 14px !important;
  color: #2d405e;
}
.b-button .el-form-item {
  margin-bottom: 10px !important;
}
</style>
<style scoped>
.btn-search-div {
  padding: 16px 24px 6px;
  background-color: #ffffff;
  margin-bottom: 16px;
  border-radius: 3px;
  /*border-bottom: 1px solid #f2f2f2;*/
}
.contmain {
  /*padding: 20px;*/
  border-radius: 3px;
  background-color: #ffffff;
}
.b-button {
  padding-top: 10px;
  text-align: right;
}
</style>
