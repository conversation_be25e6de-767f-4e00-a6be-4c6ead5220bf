<template>
  <div class="ContainerTit">
    <div
      class="main"
      :style="{
        height: !isFooter ? 'calc(100vh - 100px)' : 'calc(100vh - 160px)',
      }"
    >
      <slot></slot>
    </div>
    <div v-if="isFooter" class="footer-div">
      <slot name="headr"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "ContainerTit",
  props: {
    isFooter: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style>
.ContainerTit .el-card__body {
  padding: 10px;
}
</style>
<style scoped>
.ContainerTit {
  position: relative;
  height: calc(100vh - 100px);
  /*overflow: auto;*/
}
.tit-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  line-height: 32px;
  padding: 10px 20px;
  border-bottom: 1px solid #f2f2f2;
}
.main {
  height: calc(100vh - 160px);
  overflow-y: auto;
  padding-bottom: 20px;
  /*padding: 20px 10px;*/
}
.footer-div {
  box-shadow: 0 -4px 10px #eee;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  text-align: center;
  background-color: #ffffff;
  padding: 16px 0;
  border-top: 1px solid #fafafa;
  z-index: 99;
  border-radius: 3px;
}
</style>
