<template>
  <el-dialog
    title="客户列表"
    :visible="isShow"
    width="60%"
    :modal="modal"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="close"
  >
    <el-form size="small" inline>
      <el-form-item>
        <el-input
          v-model="search_form.keyword"
          placeholder="客户名称/手机号"
          clearable
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <!--<el-form-item label="区域">
        <RegionSelect
          v-model="region"
          size="small"
          clearable
          @change="regionChange"
        />
      </el-form-item>-->
      <el-form-item v-if="systemType === 1">
        <SelectShop
          v-model="search_form.shopId"
          :clearable="true"
          placeholder="选择商铺"
          @clear="shopClear"
          @change="selShop"
        />
      </el-form-item>
      <el-form-item>
        <el-select v-model="search_form.salesManId" clearable placeholder="选择业务员" @change="pageChange(1)">
          <el-option v-for="(item, index) in options" :key="index" :label="item.staffName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <RegionSelect
          v-model="region"
          style="width: 200px"
          size="small"
          clearable
          check-strictly
          placeholder="所属区域"
          @change="regionChange"
        />
      </el-form-item>
      <!--<el-form-item label="部门">
        <DepartmentSel v-model="department" clearable @change="selBranch" />
      </el-form-item>-->
    </el-form>
    <el-table :data="customers" size="small" @row-dblclick="dbSelect" @selection-change="customerSel">
      <el-table-column prop="name" label="客户名称"></el-table-column>
      <el-table-column prop="mobile" label="联系电话">
        <template slot-scope="scope">
          {{ scope.row.mobile }}
        </template>
      </el-table-column>
      <el-table-column prop="customerType" label="客户类型"></el-table-column>
      <el-table-column prop="shopName" label="所属商铺" show-overflow-tooltip></el-table-column>
      <el-table-column width="100" label="选择">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      layout="prev, pager, next, jumper"
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage.vue";
import RegionSelect from "@/component/common/RegionSelectJSON.vue";
import SelectShop from "@/component/goods/SelectShop.vue";
import { getAllStaff } from "@/api/Department";
import { getAllCustomer } from "@/api/Customer";

export default {
  components: { SelectShop, RegionSelect, FooterPage },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    modal: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      customers: [],
      region: [],
      options: [],
      total: 100,
      page: 1,
      pageSize: 10,
      chooseCustomers: [],
      search_form: {
        keyword: "",
        provinceCode: "",
        cityCode: "",
        districtCode: "",
        departmentId: "",
        salesManId: "",
      },
    };
  },
  created() {
    this.getAllList();
    this.getAllStaff();
  },
  methods: {
    async getAllStaff(departmentId) {
      const data = await getAllStaff({
        page: 1,
        pageSize: 50,
        departmentId: departmentId,
      });

      this.options = data.data;
    },
    async getAllList() {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        enableStatus: 5,
        status: 2,
        keyword: this.search_form.keyword,
        shopId: this.search_form.shopId,
        salesManId: this.search_form.salesManId,
        provinceCode: this.search_form.provinceCode,
        cityCode: this.search_form.cityCode,
        districtCode: this.search_form.districtCode,
      };
      const { data } = await getAllCustomer(params);
      this.customers = data;
    },
    // 选择区域
    regionChange(val) {
      this.search_form.provinceCode = val[0];
      this.search_form.cityCode = val[1];
      this.search_form.districtCode = val[2];
      this.pageChange(1);
    },
    shopClear() {
      this.search_form.shopId = "";
      this.pageChange(1);
    },
    selShop(val, row) {
      this.pageChange(1);
    },
    pageChange(val) {
      this.page = val;
      this.getAllList();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    customerSel(row) {
      this.selChange(row[0].name);
      this.close();
    },
    close() {
      this.$emit("close");
    },
    confirm() {
      this.$emit("confirm", this.chooseCustomers);
    },
    dbSelect(row) {
      this.chooseCustomers = [row];
      this.$emit("confirm", this.chooseCustomers);
      this.close();
    },
  },
};
</script>

<style scoped></style>
