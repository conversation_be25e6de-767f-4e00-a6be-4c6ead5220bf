<template>
  <el-dialog
    :title="(isEdit ? '编辑' : '新增') + '客户类型'"
    :visible.sync="isShow"
    width="40%"
    :modal="modal"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <el-form ref="add_form" :model="add_form" :rules="rules" label-width="100px">
      <el-form-item label="客户类型：" prop="name">
        <el-input v-model="add_form.name" placeholder="请输入客户类型"></el-input>
      </el-form-item>
      <el-form-item label="是否默认：" prop="defaultStatus">
        <el-switch v-model="add_form.defaultStatus" :active-value="5" :inactive-value="4"></el-switch>
      </el-form-item>
      <el-form-item label="是否禁用：" prop="enableStatus">
        <el-radio-group v-model="add_form.enableStatus">
          <el-radio :label="4">是</el-radio>
          <el-radio :label="5">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="业务模式：" prop="modelType">
        <el-checkbox-group v-model="add_form.modelType">
          <el-checkbox label="1">B2C模式</el-checkbox>
          <el-checkbox label="2">B2B模式</el-checkbox>
          <el-checkbox label="3">多商户模式</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="addData">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addCustomerSource, editCustomerSource } from "@/api/System";
export default {
  name: "CutomerTypeAdd",
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    isShow: {
      type: Boolean,
      default: false,
    },
    typeId: {
      type: [Number, String],
      default: "",
    },
    // 是否需要遮罩层
    modal: {
      type: Boolean,
      default: true,
    },
    form: {
      type: Object,
      default: () => {
        return {
          name: "",
          defaultStatus: 4,
          enableStatus: 5,
          modelType: [],
        };
      },
    },
  },
  data() {
    return {
      rules: {
        name: [{ required: true, message: "请输入客户类型", trigger: "blur" }],
        modelType: [
          {
            type: "array",
            required: true,
            message: "请选择业务模式",
            trigger: "change",
          },
        ],
      },
      add_form: {
        name: "",
        defaultStatus: 4,
        enableStatus: 5,
        modelType: [],
      },
    };
  },
  watch: {
    form(Nval) {
      this.add_form = Nval;
    },
  },
  created() {
    this.add_form = this.form;
    console.log(this.form);
  },
  methods: {
    cancel() {
      this.$emit("cancel");
    },

    async addData() {
      if (!this.add_form.name.trim()) {
        this.$message.warning("客户类型不能为空");
        return;
      }
      this.$refs.add_form.validate(async (valid) => {
        if (valid) {
          let target = {};
          const params = {
            ...this.add_form,
            modelType: this.add_form.modelType.length ? this.add_form.modelType.join(",") : "",
          };
          if (!this.isEdit) {
            target = await addCustomerSource(params);
          } else {
            target = await editCustomerSource(this.typeId, params);
          }
          const data = target;

          this.cancel();
          this.$emit("confirm");
          // this.show_model = false
          // this.pageChange(1)
        }
      });
    },
  },
};
</script>

<style scoped></style>
