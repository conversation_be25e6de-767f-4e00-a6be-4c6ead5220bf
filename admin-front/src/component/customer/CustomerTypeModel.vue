<!--客户类型列表-->
<template>
  <el-dialog
    :modal="modal"
    title="客户类型列表"
    :visible="isShow"
    width="60%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <div class="search-wrp clearfix">
      <el-button v-if="isShowAdd" class="float_right btn" type="text" @click="pageChange(1)"> 【刷新】 </el-button>
    </div>

    <el-table size="small" :data="type_list" @selection-change="selectionChange">
      <el-table-column v-if="isCheck" type="selection" align="center" width="55"></el-table-column>
      <el-table-column label="ID" prop="id" min-width="60"></el-table-column>
      <el-table-column prop="name" label="客户类型" min-width="160"></el-table-column>
      <el-table-column prop="defaultStatus" label="是否默认" min-width="100">
        <template slot-scope="scope">
          <span :class="[scope.row.defaultStatus === 5 ? 'open-span' : '']">
            {{ scope.row.defaultStatus === 5 ? "是" : "否" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="enableStatus" label="状态" min-width="100">
        <template slot-scope="scope">
          <span :class="[scope.row.enableStatus === 5 ? 'open-span' : 'disabled-span']">
            {{ scope.row.enableStatus === 5 ? "启用" : "禁用" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="选择">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" plain type="primary" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="isCheck" slot="btn-div">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { getAllCustomerSource } from "@/api/System";
export default {
  name: "CustomerTypeModel",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isShowAdd: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    modal: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      choose_data: [],
      type_list: [],
      pageSize: 10,
      page: 1,
      selectedIndex: null,
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      selectedClient: {},
    };
  },
  created() {
    this.pageChange(1);
  },
  methods: {
    async getAllCustomerSource() {
      const data = await getAllCustomerSource({
        page: this.page,
        pageSize: this.pageSize,
      });

      this.type_list = data.data;
      this.total = data.pageTotal;
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    confirm() {
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    pageChange(page) {
      this.page = page;
      this.getAllCustomerSource();
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
.btn {
  margin-top: -5px;
}
</style>
