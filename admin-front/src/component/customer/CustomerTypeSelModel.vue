<template>
  <el-dialog
    title="客户类型选择"
    :visible.sync="isShow"
    width="600px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <div class="clearfix">
      <div class="float_left">
        <el-input v-model="search_key" placeholder="请输入客户类型名称" size="small" clearable>
          <el-button slot="append" icon="el-icon-search"></el-button>
        </el-input>
      </div>
      <el-button class="float_right" type="text">【刷新】 </el-button>
    </div>
    <el-table :data="customerTypes" size="mini">
      <el-table-column label="客户类型名称" prop="name"></el-table-column>
      <el-table-column label="选择" width="80px" align="center" header-align="center">
        <template slot-scope="scope">
          <el-button size="mini" icon="el-icon-check" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      total-page.sync="total"
      :current-page="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div slot="btn-div">
        <el-button size="small" type="primary" @click="confirm">确定 </el-button>
        <el-button size="small" @click="cancel">取消 </el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>

<script>
import { getAllCustomerSource } from "@/api/System";
import FooterPage from "@/component/common/FooterPage.vue";

export default {
  name: "CustomerTypeSelModel",
  components: { FooterPage },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      customerTypes: [],
      total: 100,
      page: 1,
      pageSize: 10,
      search_key: "",
      chooseCustomers: [],
    };
  },
  created() {
    this.getAllList();
  },
  methods: {
    async getAllList() {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.search_key,
      };
      const { data } = await getAllCustomerSource(params);
      this.customerTypes = data;
    },
    pageChange(val) {
      this.page = val;
      this.getAllList();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },
    cancel() {
      this.$emit("close");
    },
    confirm() {
      this.$emit("confirm", this.chooseCustomers);
    },
    dbSelect(row) {
      this.chooseCustomers = [row];
      this.$emit("confirm", this.chooseCustomers);
      this.cancel();
    },
  },
};
</script>

<style scoped></style>
