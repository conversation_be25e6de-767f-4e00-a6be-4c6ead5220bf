<template>
  <div class="consignment-config">
    <el-form ref="configForm" :model="configData" label-width="160px" size="small">
      <el-form-item label="启用代销：">
        <el-switch
          v-model="configData.enabled"
          active-text="启用"
          inactive-text="禁用"
          @change="enabledChange"
        ></el-switch>
      </el-form-item>

      <template v-if="configData.enabled">
        <el-form-item label="保证金要求：">
          <el-switch v-model="configData.depositRequired" active-text="需要" inactive-text="不需要"></el-switch>
        </el-form-item>

        <el-form-item v-if="configData.depositRequired" label="最低保证金额：">
          <el-input-number
            v-model="configData.minDepositAmount"
            :min="0"
            :precision="2"
            :step="100"
            style="width: 180px"
          >
            <template slot="append">元</template>
          </el-input-number>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "ConsignmentConfig",
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    supplierId: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      updateTimer: null,
      configData: {
        enabled: false,
        depositRequired: false,
        minDepositAmount: 1000,
      },
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.initConfig(val);
        }
      },
      immediate: true,
      deep: true,
    },
    // 只监听特定的属性变化，避免无限循环
    "configData.enabled": function(val) {
      this.$emit("enabledChange", val);
      this.emitUpdate();
    },
    "configData.depositRequired": function() {
      this.emitUpdate();
    },
    "configData.minDepositAmount": function() {
      this.emitUpdate();
    },
  },
  methods: {
    initConfig(config) {
      // 初始化配置，处理后端返回的数据格式
      if (!config) return;

      // 创建一个新对象，避免直接引用
      this.configData = {
        enabled: config.enabled || false,
        depositRequired: config.depositRequired || false,
        minDepositAmount: config.minDepositAmount || 1000,
      };
    },
    // 更新数据
    emitUpdate() {
      // 使用setTimeout避免频繁更新
      if (this.updateTimer) {
        clearTimeout(this.updateTimer);
      }
      this.updateTimer = setTimeout(() => {
        // 创建一个新对象发送，避免引用问题
        const configCopy = JSON.parse(JSON.stringify(this.configData));
        this.$emit("input", configCopy);
        this.$emit("change", configCopy);
      }, 300);
    },
    enabledChange(val) {
      this.$emit("enabledChange", val);
    },
    validate() {
      return this.$refs.configForm.validate();
    },
  },
};
</script>

<style lang="scss" scoped>
.consignment-config {
  padding: 10px 0;
}
.form-tip {
  color: #909399;
  font-size: 12px;
  margin-left: 10px;
}
.mt-2 {
  margin-top: 10px;
}
</style>
