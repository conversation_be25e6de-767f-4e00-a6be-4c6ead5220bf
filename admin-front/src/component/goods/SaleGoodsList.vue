<!--选择店铺商品-->
<template>
  <div>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="商品列表"
      :visible="isShow"
      width="60%"
      @close="cancel"
    >
      <div class="clearfix" style="padding-bottom: 10px">
        <el-input
          v-model="keyword"
          style="width: 240px"
          size="small"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" @click="pageChange(1)">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
        <div style="display: inline-block; margin: 0 10px">
          <GoodsCategory
            v-if="!userCenterId"
            v-model="categoryPath"
            :width="160"
            check-strictly
            clearable
            size="small"
            @change="cateChange"
          />
        </div>
        <el-select v-model="inSales" size="small" placeholder="请选择" @change="pageChange(1)">
          <el-option label="全部" :value="0"></el-option>
          <el-option label="销售中" :value="5"></el-option>
        </el-select>
      </div>
      <el-table
        ref="goodsTable"
        :data="goods_list"
        size="small"
        :row-key="
          (row) => {
            return row.id;
          }
        "
        @selection-change="selectionChange"
        @row-dblclick="selGoods"
      >
        <el-table-column v-if="isCheck" type="selection" :reserve-selection="true" width="55"></el-table-column>
        <el-table-column prop="title" label="商品名称"></el-table-column>
        <el-table-column prop="code" label="商品编码"></el-table-column>
        <el-table-column prop="categoryName" label="商品分类"></el-table-column>
        <el-table-column prop="inventorTotal" label="总库存">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.inventorTotal) }}
          </template>
        </el-table-column>
        <el-table-column label="选择商品">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-check" @click="selGoods(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="footer clearfix" style="padding-bottom: 10px">
        <div v-if="isCheck" class="float_left" style="padding-top: 20px">
          <el-button size="small" type="primary" @click="confirm"> 确定 </el-button>
          <el-button size="small" @click="cancel">取消</el-button>
        </div>
        <div class="float_right">
          <FooterPage
            :page-size="pre_page"
            :total-page.sync="total"
            :current-page.sync="page"
            @pageChange="pageChange"
            @sizeChange="sizeChange"
          ></FooterPage>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getGoodsByCategory, getAllGoods, searchGood } from "@/api/goods";
import GoodsCategory from "@/component/common/GoodsCategory.vue";
export default {
  name: "SaleGoodsList",
  components: {
    GoodsCategory,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    showSku: {
      type: Boolean,
      default: true,
    },
    shopId: {
      type: [Number, String, Array],
      default: 0,
    },
    userCenterId: {
      type: [Number, String],
      default: 0,
    },
    categoryId: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      keyword: "",
      category_id: "",
      categoryPath: [],
      choose_data: [],
      goods_list: [],
      loading: false,
      total: 0,
      pre_page: 10,
      page: 1,
      inSales: 5,
      pageLayout: "total, prev, pager, next",
    };
  },
  watch: {
    categoryId(val) {
      this.category_id = val;
    },
  },
  created() {
    this.getList();
  },
  mounted() {
    this.category_id = this.categoryId;
  },
  methods: {
    skuChange(index) {
      const target = this.$_common.deepClone(this.goods_list);
      const skuItem = target[index].skuData.find((item) => item.skuId === target[index].skuId);
      target[index] = {
        ...target[index],
        salePrice: skuItem.salePrice,
        unitName: skuItem.unitName,
        inventory: skuItem.inventory,
      };
      this.goods_list = target;
    },
    async getGoodsByCategory() {
      const { data, pageTotal } = await getGoodsByCategory({
        keyword: this.keyword,
        page: this.page,
        pageSize: this.pre_page,
        userCenterId: this.userCenterId,
        inSales: this.inSales,
        enableStatus: 5,
      });

      this.total = pageTotal;
      this.goods_list = data;
    },
    cateChange(val) {
      this.category_id = val[val.length - 1];
      this.pageChange(1);
    },
    //  商品列表
    async getAllGoods() {
      const { data, pageTotal } = await getAllGoods({
        page: this.page,
        pageSize: this.pre_page,
        shopId: this.shopId,
        inSales: this.inSales,
        enableStatus: 5,
      });

      this.total = pageTotal;
      this.goods_list = data;
      for (let i = 0; i < data.length; i++) {
        const isTrue = this.choose_data.find((itemF) => {
          return itemF.id === data[i].id;
        });
        if (isTrue) {
          this.toggleRowSelection([data[i]]);
        }
      }
    },
    //  搜索
    async searchGoods() {
      const { data, pageTotal } = await searchGood({
        keyword: this.keyword,
        categoryPath: this.category_id,
        page: this.page,
        pageSize: this.pre_page,
        shopId: this.shopId,
        inSales: this.inSales,
        enableStatus: 5,
      });

      this.total = pageTotal;
      this.goods_list = data;
      for (let i = 0; i < data.length; i++) {
        const isTrue = this.choose_data.find((itemF) => {
          return itemF.id === data[i].id;
        });
        if (isTrue) {
          this.toggleRowSelection([data[i]]);
        }
      }
    },
    getList() {
      if (this.userCenterId) {
        this.getGoodsByCategory();
      } else {
        const obj = {
          keyword: this.keyword,
          categoryId: this.category_id,
        };
        const isKey = this.$_common.isSerch(obj);
        if (isKey) {
          this.searchGoods();
        } else {
          this.getAllGoods();
        }
      }
    },
    toggleRowSelection(rows) {
      this.$nextTick(() => {
        rows.forEach((row) => {
          this.$refs.goodsTable.toggleRowSelection(row, true);
        });
      });
    },
    pageChange(page) {
      this.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.pre_page = size;
      this.pageChange(1);
    },
    selectionChange(val) {
      // this.choose_data = val;
      console.log(val);
      if (val.length) {
        // if (!this.choose_data.length) {
        //   this.choose_data = val;
        // } else {
        //   this.choose_data = this.$_common.unique(
        //     this.choose_data.concat(val),
        //     ["id", "skuId"]
        //   );
        // }
        this.choose_data = val;
      } else {
        for (let i = 0; i < this.goods_list.length; i++) {
          const index = this.choose_data.findIndex((itemF) => {
            return itemF.id === this.goods_list[i].id;
          });
          if (index > -1) {
            this.choose_data.splice(index, 1);
          }
        }
      }
      this.multipleSelectionFirst = val;
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 关闭弹窗
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    selGoods(row) {
      this.cancel();
      this.$emit("confirm", [row]);
    },
  },
};
</script>

<style scoped></style>
