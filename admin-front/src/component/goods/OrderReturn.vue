<template>
  <div>
    <el-dialog
      title="采购订单"
      :visible.sync="dialogVisible"
      width="60%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="close"
    >
      <div>
        <div style="padding-bottom: 10px">
          <el-input
            v-model="search_key"
            placeholder="请输入采购单号"
            size="small"
            style="width: 280px"
            clearable
            @clear="getAllPurchase"
          >
            <el-button slot="append" icon="el-icon-search" @click="getAllPurchase"></el-button>
          </el-input>
        </div>
        <vxe-table ref="orderReturn" :data="table_data" border="inner" auto-resize :expand-config="{ accordion: true }">
          <vxe-table-column v-if="isCheck" type="checkbox" width="60"></vxe-table-column>
          <vxe-table-column min-width="120" field="no" title="采购单号"></vxe-table-column>
          <vxe-table-column type="expand" title="商品清单" width="100" :fixed="expandFixed">
            <template #content="{ row }">
              <ul class="sku-ul">
                <li v-for="(item, index) in row.goodsData" :key="index" class="sku-li">
                  <div class="sku-info float_left">
                    <p>
                      <span class="label">商品名称:</span>
                      {{ item.goodsName }}
                    </p>
                    <p>
                      <span class="label">商品编码:</span>
                      {{ item.goodsCode }}
                    </p>
                    <p>
                      <span class="label">规格:</span>
                      {{ item.unitName }};{{ item.skuName }}
                    </p>
                    <p>
                      <span class="label">采购数量:</span>
                      <span>
                        {{ $_common.formatNub(item.buyerNum) }}
                      </span>
                    </p>
                    <p>
                      <span class="label">可退数量:</span>
                      <span>
                        {{ $_common.formatNub(item.returnOnNum) }}
                      </span>
                    </p>
                  </div>
                </li>
              </ul>
            </template>
          </vxe-table-column>
          <vxe-table-column min-width="130" field="warehouseName" title="仓库名称"></vxe-table-column>
          <vxe-table-column
            min-width="100"
            :field="parseInt(ifMerchant) === 5 ? 'merchantName' : 'supplierName'"
            :title="parseInt(ifMerchant) === 5 ? '商户' : '供应商'"
          ></vxe-table-column>
          <vxe-table-column min-width="120" field="purchaseAmount" title="采购价格">
            <template #default="{ row }">
              {{ $_common.formattedNumber(row.purchaseAmount) }}
            </template>
          </vxe-table-column>
          <vxe-table-column width="100" title="选择">
            <template #default="{ row }">
              <el-button style="margin-left: 0" size="mini" icon="el-icon-check" @click="dblclick(row)"></el-button>
            </template>
          </vxe-table-column>
        </vxe-table>
        <FooterPage
          :page-size="pre_page"
          :total-page.sync="total"
          :current-page.sync="page"
          @pageChange="pageChange"
          @sizeChage="sizeChange"
        ></FooterPage>
      </div>
      <div v-if="isCheck" style="text-align: center; padding-top: 10px">
        <el-button size="small" type="primary" @click="confirm">确认</el-button>
        <el-button size="small" @click="close">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllPurchase } from "@/api/Purchase";
export default {
  name: "OrderReturn",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: false,
    },
    ifMerchant: {
      type: [Number, String],
      default: 4,
    },
  },
  data() {
    return {
      search_key: "",
      pre_page: 10,
      total: 0,
      page: 1,
      table_data: [],
      choose_data: [],
      expandFixed: null,
    };
  },
  created() {
    this.getAllPurchase();
  },
  methods: {
    async getAllPurchase() {
      //  获取采购单
      const data = await getAllPurchase({
        returnStatus: "0,1",
        inStatus: "5,6",
        auditStatus: 2,
        deleteStatus: 5,
        ifMerchant: this.ifMerchant,
        purchaseType: parseInt(this.ifMerchant) === 5 ? 5 : 4,
        page: this.page,
        pageSize: this.pre_page,
        keyword: this.search_key,
      });

      this.table_data = data.data;
      this.total = data.pageTotal;
    },
    dblclick(row) {
      this.close();
      this.$emit("confirm", [row]);
    },
    pageChange(val) {
      this.page = val;
      this.getAllPurchase();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getAllPurchase();
    },
    // 关闭弹框
    close() {
      this.$emit("close");
    },
    confirm() {
      this.choose_data = this.$refs.orderReturn.getCheckboxRecords();
      this.$emit("confirm", this.choose_data);
      this.close();
    },

    handleSelectionChange(val) {
      this.choose_data = val;
    },
  },
};
</script>

<style scoped lang="scss">
.sku-ul {
  .sku-li {
    display: inline-block;
    margin-right: 10px;
    border: 1px solid #ebeef5;
    padding: 10px;
    width: 294px;
    vertical-align: middle;
    margin-bottom: 10px;
    .sku-img {
      width: 50px;
      margin-right: 8px;
    }
    .sku-info {
      line-height: 23px;
      color: #111111;
      .label {
        display: inline-block;
        width: 80px;
        color: #666666;
        text-align: right;
      }
    }
  }
}
</style>
