<!--选择商品-->
<template>
  <div>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="商品库"
      :visible="isShow"
      width="60%"
      @close="cancel"
    >
      <div class="clearfix" style="padding-bottom: 10px">
        <el-input
          v-model="keyword"
          style="width: 240px"
          size="small"
          clearable
          placeholder="请输入商品名称"
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" @click="pageChange(1)">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
        <span style="display: inline-block; margin-left: 10px">
          <GoodsCategory v-model="categoryId" :width="160" check-strictly clearable size="small" @change="goodsChane" />
        </span>
      </div>
      <el-table
        ref="goods_list"
        :data="goods_list"
        size="small"
        @selection-change="selectionChange"
        @row-dblclick="selGoods"
      >
        <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
        <el-table-column prop="materielCode" label="商品编码" min-width="140"></el-table-column>
        <el-table-column
          label="商品名称"
          fixed="left"
          prop="materielName"
          show-overflow-tooltip
          min-width="140"
        ></el-table-column>
        <el-table-column label="规格" min-width="140">
          <template slot-scope="scope"> {{ scope.row.unitName }}; {{ scope.row.skuName }} </template>
        </el-table-column>
        <el-table-column prop="inventoryNum" label="库存数量" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.inventoryNum) }}
          </template>
        </el-table-column>
        <el-table-column prop="costPrice" min-width="120" label="平均成本">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.costPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="total" label="总成本" min-width="120">
          <template slot-scope="scope">
            {{ $_common.formattedNumber(scope.row.total) }}
          </template>
        </el-table-column>
        <el-table-column label="选择商品" fixed="right" width="100">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-check" @click="selGoods(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="footer clearfix" style="padding-bottom: 10px">
        <div v-if="isCheck" class="float_left" style="padding-top: 20px">
          <el-button size="small" type="primary" @click="confirm"> 确定 </el-button>
          <el-button size="small" @click="cancel">取消</el-button>
        </div>
        <div class="float_right">
          <FooterPage
            :page-size="pre_page"
            :total-page.sync="total"
            :current-page.sync="page"
            @pageChange="pageChange"
            @sizeChange="sizeChange"
          ></FooterPage>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getInventoryByWarehouseId } from "@/api/Stock";
import GoodsCategory from "@/component/common/GoodsCategory.vue";
export default {
  name: "GoodsWarehouse",
  components: {
    GoodsCategory,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    warehouseId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      choose_data: [],
      goods_list: [],
      loading: false,
      total: 0,
      pre_page: 10,
      page: 1,
      pageLayout: "total, prev, pager, next",
      keyword: "",
      categoryId: [],
    };
  },
  created() {
    if (this.warehouseId) {
      this.getList();
    } else {
      this.$message("请选择仓库");
    }
  },
  methods: {
    goodsChane(val) {
      this.pageChange(1);
    },
    // 获取列表数据
    async getList() {
      const { data, pageTotal } = await getInventoryByWarehouseId({
        page: this.page,
        pageSize: this.pre_page,
        warehouseId: this.warehouseId,
        search: this.keyword,
        categoryId: this.categoryId[this.categoryId.length - 1],
      });

      this.goods_list = data;
      this.total = pageTotal;
    },
    pageChange(page) {
      this.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.pre_page = size;
      this.pageChange(1);
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 关闭弹窗
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    selGoods(row) {
      this.cancel();
      this.$emit("confirm", [row]);
    },
  },
};
</script>

<style scoped></style>
