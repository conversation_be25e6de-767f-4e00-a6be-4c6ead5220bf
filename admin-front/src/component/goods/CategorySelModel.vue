<template>
  <el-dialog
    title="选择分类"
    :visible.sync="dialogVisible"
    width="50%"
    :before-close="handleClose"
    :append-to-body="true"
  >
    <div class="category-container">
      <el-table
        ref="categoryTable"
        :data="categoryList"
        style="width: 100%"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="title" label="分类名称"></el-table-column>
        <el-table-column prop="categoryNo" label="图片" min-width="80">
          <template slot-scope="scope">
            <el-image fit="cover" :src="scope.row.images"></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="分类编码" min-width="140"></el-table-column>
        <el-table-column prop="goodsBasicTotal" label="商品数量" min-width="100"></el-table-column>
        <el-table-column label="选择">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-check" @click="dblclick(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getAllCategory } from "@/api/goods"; // 假设有这个API

export default {
  name: "CategorySelModel",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    // 已选择的分类ID列表
    defaultSelected: {
      type: Array,
      default: () => [],
    },
    onlyEnable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      categoryList: [],
      selectedNodes: [],
    };
  },
  created() {
    this.getCategoryList();
  },
  methods: {
    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedNodes = selection;
    },

    // 设置默认选中行
    setDefaultSelection() {
      this.$nextTick(() => {
        if (this.defaultSelected.length > 0) {
          this.categoryList.forEach((row) => {
            if (this.defaultSelected.includes(row.id)) {
              this.$refs.categoryTable.toggleRowSelection(row, true);
            }
          });
        }
      });
    },

    async getCategoryList() {
      try {
        const { data } = await getAllCategory({
          enableStatus: this.onlyEnable,
        });
        // 将树形数据转换为扁平结构
        // this.categoryList = this.flattenCategoryTree(data || []);
        this.categoryList = data || [];
        this.setDefaultSelection();
      } catch (error) {
        console.error("获取分类列表失败:", error);
        this.$message.error("获取分类列表失败");
      }
    },

    // 将树形数据转换为扁平结构
    flattenCategoryTree(tree, parentId = null, level = 0) {
      let result = [];
      tree.forEach((node) => {
        const newNode = { ...node, parentId, level };
        // 添加缩进效果
        newNode.name = "　".repeat(level) + newNode.name;
        result.push(newNode);
        if (node.children && node.children.length) {
          result = result.concat(this.flattenCategoryTree(node.children, node.id, level + 1));
        }
      });
      return result;
    },

    dblclick(row) {
      this.$emit("confirm", [row]);
      this.handleClose();
    },

    // 确认选择
    handleConfirm() {
      this.$emit("confirm", this.selectedNodes);
      this.handleClose();
    },

    // 关闭弹窗
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.category-container {
  height: 400px;
  overflow-y: auto;
}

.category-count {
  color: #999;
  margin-left: 4px;
}

// 自定义滚动条样式
.category-container {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #c1c1c1;
  }

  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #f1f1f1;
  }
}
</style>
