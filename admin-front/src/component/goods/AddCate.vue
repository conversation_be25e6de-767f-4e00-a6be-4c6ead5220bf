<!--新建分类-->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="isShow"
    width="40%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <div>
      <el-form ref="form" :model="form" :rules="base_rules" label-width="100px" :inline="false">
        <el-form-item label="分类编码:" prop="code">
          <el-input v-model="form.code" disabled placeholder="自动生成"></el-input>
        </el-form-item>
        <el-form-item label="分类名称:" prop="title">
          <el-input v-model="form.title" placeholder="请输入分类名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-tabs type="card">
            <el-tab-pane label="越南语">
              <el-input v-model="form.vi.title" placeholder="请输入分类名称"></el-input>
            </el-tab-pane>
          </el-tabs>
        </el-form-item>
        <el-form-item label="上级分类:" prop="pid">
          <el-cascader
            v-model="pid_arr"
            :options="classify_list"
            width="240"
            :props="{
              label: 'title',
              value: 'id',
              checkStrictly: true,
            }"
            clearable
            @change="categoryChange"
          />
          <p class="el-icon-warning" style="font-size: 12px; color: #e6a23c; display: block; margin-top: 10px">
            提示:若你新建一级分类，则无需选择上级分类
          </p>
        </el-form-item>

        <el-form-item label="分类图片:">
          <UploadQiniu
            :modal="false"
            :file-list="img_list"
            @uploadSuccess="uploadSuccess"
            @handleRemove="uploadRemove"
          />
        </el-form-item>

        <el-form-item v-if="!form.pid" label="一级分类广告图:">
          <UploadQiniu
            :modal="false"
            :file-list="img_ad_list"
            up-tip="建议图片尺寸：680px * 300px"
            @uploadSuccess="uploadAdSuccess"
            @handleRemove="uploadAdRemove"
          />
        </el-form-item>
        <el-form-item label="分类排序:">
          <el-input-number v-model="form.sort" :controls="false" placeholder="请输入分类排序"></el-input-number>
        </el-form-item>
        <el-form-item label="是否显示:">
          <template>
            <el-switch
              v-model="form.enableStatus"
              :active-value="5"
              :inactive-value="4"
              active-color="#36B365"
              inactive-color="#ff4949"
            ></el-switch>
          </template>
        </el-form-item>
        <el-form-item label="客户类型屏蔽">
          <div v-if="!customer_type_list.length" style="text-align: center">暂无客户类型</div>
          <el-checkbox-group v-model="form.notCustomerType">
            <el-checkbox v-for="(item, index) in customer_type_list" :key="index" :label="item.id">
              {{ item.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button :loading="sub_load" type="primary" @click="subCategory"> 确 定 </el-button>
    </span>
  </el-dialog>
</template>

<script>
import UploadQiniu from "@/component/common/UploadQiniu.vue";
import { addCategory, editCategory, getAllCategory, getCategoryInfoById } from "@/api/goods";
import { getAllCustomerSource } from "@/api/System";

export default {
  name: "AddCate",
  components: {
    UploadQiniu,
  },
  props: {
    dialogTitle: {
      type: String,
      default: "新建分类",
    },
    isShow: {
      type: Boolean,
      default: false,
    },
    cateId: {
      type: [Number, String],
      default: 0,
    },
    pidPath: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      sub_load: false,
      add_model: false,
      pid_arr: [],
      img_list: [],
      img_ad_list: [],
      classify_list: [],
      form: {
        code: "",
        images: "",
        sort: "",
        title: "",
        pid: "",
        enableStatus: 5,
        link: "",
        adImage: "",
        notCustomerType: [], // 经营屏蔽->客户类型选择数组
        vi: {
          title: null,
        },
      },
      cate_id: 0,
      base_rules: {
        // 基本信息验证
        title: [{ required: true, message: "请输入分类名称", trigger: "blur" }],
        images: [{ required: true, message: "请上传商品图片", trigger: "blur" }],
      },
      customer_type_list: [], // 经营屏蔽->客户类型列表
    };
  },
  async created() {
    await this.getAllCustomerSource();
    if (this.dialogTitle === "修改分类") {
      await this.getCategoryInfoById(this.cateId);
    }
    if (this.dialogTitle === "新建子分类") {
      this.form.pid = this.cateId;
      this.pid_arr = this.pidPath;
      this.form.link = this.pidPath.join(",");
    }
    await this.getList();
  },
  methods: {
    // 图片上传成功
    uploadSuccess(val, res, file, fileList) {
      this.form.images = val;
    },

    uploadRemove() {
      this.form.images = "";
    },
    // 图片上传成功
    uploadAdSuccess(val, res, file, fileList) {
      this.form.adImage = val;
    },
    uploadAdRemove() {
      this.form.adImage = "";
    },
    // 选择上级分类
    categoryChange(val) {
      this.form.pid = val[val.length - 1];
      this.form.link = val.join(",");
    },
    // 获取分类列表
    async getList() {
      const { data } = await getAllCategory();
      this.classify_list = data;
    },
    // 新增/编辑分类
    async subCategory() {
      if (!this.form.title.trim()) {
        this.$message.warning("分类名称不能为空");
        return;
      }
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.sub_load = true;
          if (this.dialogTitle === "修改分类") {
            await editCategory(this.cate_id, {
              ...this.form,
              notCustomerType: this.form.notCustomerType.join(","),
            });
          } else {
            await addCategory({
              ...this.form,
              notCustomerType: this.form.notCustomerType.join(","),
            });
          }
          this.sub_load = false;
          this.$message({
            type: "success",
            message: "提交成功",
          });
          this.sub_load = false;
          this.$emit("confirm", this.choose_data);
          this.cancel();

          // this.getList()
        }
      });
    },
    // 获取分类详情
    async getCategoryInfoById(id) {
      const { data } = await getCategoryInfoById(id);
      this.cate_id = id;
      this.pid_arr = data.link.split(",").map((item) => {
        return parseInt(item);
      });
      if (data.images) {
        this.img_list = [
          {
            name: "",
            url: data.images,
          },
        ];
      } else {
        this.img_list = [];
      }
      if (data.adImage) {
        this.img_ad_list = [
          {
            name: "",
            url: data.adImage,
          },
        ];
      } else {
        this.img_ad_list = [];
      }
      let arr = data.notCustomerType ? data.notCustomerType.split(",").map((item) => parseInt(item)) : [];
      this.form = {
        link: data.link,
        code: data.code,
        images: data.images,
        adImage: data.adImage,
        sort: data.sort,
        title: data.title,
        pid: data.pid,
        enableStatus: data.enableStatus,
        notCustomerType: arr,
        vi: !data.vi ? {} : data.vi,
      };
      // this.form.notCustomerType = data.notCustomerType
      //   ? data.notCustomerType.split(",").map((item) => parseInt(item))
      //   : [];
    },
    cancel() {
      this.$emit("cancel");
    },
    // 获取客户类型
    async getAllCustomerSource() {
      const data = await getAllCustomerSource({
        page: 1,
        pageSize: 50,
      });

      this.customer_type_list = data.data;
    },
  },
};
</script>

<style scoped></style>
