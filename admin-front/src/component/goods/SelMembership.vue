<!--所属商铺-->
<template>
  <div class="dis-inline">
    <el-select
      :value="value"
      :placeholder="placeholder"
      :clearable="clearable"
      filterable
      remote
      :size="size"
      :multiple="multiple"
      :disabled="disabled"
      :remote-method="remoteMethod"
      :loading="loading"
      :style="{ width: width + 'px' }"
      @change="selChange"
      @clear="clearChange"
    >
      <el-option v-for="(item, index) in shop_list" :key="index" :label="item.name" :value="item.id"></el-option>
    </el-select>
    <span class="el-icon-arrow-down sel-icon" style="margin-right: 15px"></span>
  </div>
</template>

<script>
import { getAllVipCard } from "@/api/Market";
export default {
  name: "SelMembership",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: [Number, String, Array],
      default: "",
    },
    width: {
      type: [Number, String],
      default: 200,
    },
    placeholder: {
      type: String,
      default: "请输入会员卡关键词",
    },
    size: {
      type: String,
      default: "",
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      shop_list: [],
      loading: false,
      page: 1,
      pre_page: 999,
      keyword: "",
    };
  },
  created() {
    this.getAllVipCard();
  },
  methods: {
    remoteMethod(query) {
      if (query !== "") {
        this.loading = true;
        this.keyword = query;
        this.getAllVipCard();
      }
    },
    clearChange() {
      this.$emit("clear");
    },
    selChange(val) {
      if (!val) return;
      let row = [];
      if (!this.multiple) {
        row = this.shop_list.filter((item) => item.id === val);
      } else {
        row = this.shop_list.filter((item) => val.indexOf(item.id) > -1);
      }
      this.$emit("change", val, row);
    },
    async getAllVipCard() {
      const params = {
        page: this.page,
        pageSize: this.pre_page,
        enableStatus: 5,
        keyword: this.keyword,
      };
      const data = await getAllVipCard(params);
      this.loading = false;
      this.shop_list = data.data;
      // this.$emit("getAllShop", data.data);
    },
    // async searchList(name) {
    //   const params = {
    //     page: this.page,
    //     pageSize: this.pre_page,
    //     name: name,
    //     shopPartnerId: "",
    //     shopType: "",
    //     enableStatus: 5,
    //   };
    //   const data = await search(params);
    //   this.loading = false;
    //   this.shop_list = data.data;
    // },
  },
};
</script>

<style scoped lang="scss">
.dis-inline {
  position: relative;
  .sel-icon {
    position: absolute;
    display: block;
    right: 5px;
    top: 50%;
    width: 25px;
    text-align: center;
    transform: translateY(-50%);
    color: #c0c4cc;
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
