<template>
  <div style="text-align: left">
    <el-dialog
      title="商铺列表"
      :visible.sync="dialogVisible"
      width="60%"
      :modal="modal"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="close"
    >
      <div>
        <div class="clearfix" style="padding-bottom: 10px">
          <div class="float_left">
            <el-input
              v-model="search_key"
              placeholder="请输入商铺名称"
              size="small"
              style="width: 280px"
              clearable
              @keyup.enter.native="pageChange(1)"
              @clear="pageChange(1)"
            >
              <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
            </el-input>
          </div>
          <el-button v-if="isShowAdd" class="float_right" type="text" @click="getList"> 【刷新】 </el-button>
        </div>

        <el-table size="mini" :data="table_data" @row-dblclick="dblclick" @selection-change="handleSelectionChange">
          <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
          <el-table-column prop="name" label="名称" align="center" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column label="营业时间" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.openTime.isAllDay === 1">全天</span>
              <span v-else> {{ scope.row.openTime.start }}--{{ scope.row.openTime.end }} </span>
            </template>
          </el-table-column>
          <el-table-column prop="area" label="地址" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.area">
                {{ scope.row.area.provinceName || "" }}-{{ scope.row.area.districtName || "" }}-{{
                  scope.row.area.cityName || ""
                }}-{{ scope.row.area.address || "" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="partnerName" label="负责人" align="center"></el-table-column>
          <el-table-column prop="mobile" label="负责人电话" align="center"></el-table-column>
          <el-table-column label="选择" align="center">
            <template slot-scope="scope">
              <el-button size="mini" icon="el-icon-check" @click="dblclick(scope.row)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="footer clearfix">
        <div v-if="isCheck" class="float_left" style="padding-top: 20px">
          <el-button size="small" type="primary" @click="confirm"> 确认 </el-button>
          <el-button size="small" @click="close">取消</el-button>
        </div>
        <div class="float_right">
          <FooterPage
            :page-size="pageSize"
            :total-page.sync="total"
            :current-page.sync="page"
            @pageChange="pageChange"
            @sizeChange="sizeChange"
          ></FooterPage>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllShop, search } from "@/api/Shop";
export default {
  name: "GoodsChooseShop",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    isShowAdd: {
      type: Boolean,
      default: false,
    },
    enable: {
      type: Boolean,
      default: false,
    },
    modal: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      search_key: "",
      pageSize: 10,
      total: 0,
      page: 1,
      table_data: [],
      choose_data: [],
      enableStatus: "",
    };
  },
  created() {
    if (this.enable) {
      this.enableStatus = 5;
    } else {
      this.enableStatus = "";
    }
    this.getList();
  },
  methods: {
    async getList() {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        enableStatus: this.enableStatus,
      };
      const data = await getAllShop(params);

      this.table_data = data.data;
      this.total = data.pageTotal;
      this.$emit("getAllShop", data.data);
    },
    async searchList() {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        name: this.search_key,
        shopPartnerId: "",
        shopType: "",
        enableStatus: this.enableStatus,
      };
      const data = await search(params);

      this.table_data = data.data;
      this.total = data.pageTotal;
    },
    getData() {
      const params = {
        name: this.search_key,
      };
      const isKey = this.$_common.isSerch(params);
      if (isKey) {
        this.searchList();
      } else {
        this.getList();
      }
    },
    dblclick(row) {
      this.close();
      this.$emit("confirm", [row]);
    },
    pageChange(val) {
      this.page = val;
      this.getData();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getData();
    },
    // 关闭弹框
    close() {
      this.$emit("close");
    },
    confirm() {
      this.$emit("confirm", this.choose_data);
      this.close();
    },
    handleSelectionChange(val) {
      this.choose_data = val;
    },
  },
};
</script>

<style scoped>
.footer {
  padding-bottom: 10px;
}
</style>
