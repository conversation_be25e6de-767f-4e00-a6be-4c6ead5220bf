<!--选择商品-->
<template>
  <div>
    <el-dialog
      title="商品库"
      :visible="isShow"
      width="60%"
      :modal="modal"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <div class="clearfix" style="padding-bottom: 10px">
        <el-input
          v-model="keyword"
          style="width: 240px"
          size="small"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" @click="pageChange(1)">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
        <span style="display: inline-block; margin-left: 10px">
          <GoodsCategory v-model="categoryId" :width="160" check-strictly clearable size="small" @change="goodsChane" />
        </span>
      </div>
      <el-table
        ref="goods_list"
        size="small"
        :data="goods_list"
        @selection-change="selectionChange"
        @row-dblclick="selGoods"
      >
        <el-table-column v-if="isCheck" type="selection" width="55" align="center"></el-table-column>
        <el-table-column min-width="160" prop="title" show-overflow-tooltip label="商品名称"></el-table-column>
        <el-table-column min-width="140" prop="code" label="商品编码"></el-table-column>
        <el-table-column prop="categoryTitle" label="商品分类" min-width="100"></el-table-column>
        <el-table-column v-if="shelfLifeSetUp === 5" prop="expireTime" label="保质期" min-width="100"></el-table-column>
        <el-table-column min-width="100" label="商品状态">
          <template slot-scope="scope">
            <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
            <span v-else class="danger-status">禁用</span>
          </template>
        </el-table-column>
        <el-table-column width="100" label="选择商品">
          <template slot-scope="scope">
            <el-button
              size="mini"
              :disabled="scope.row.enableStatus !== 5"
              icon="el-icon-check"
              @click="selGoods(scope.row)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
      <FooterPage
        :page-size="pre_page"
        :total-page.sync="total"
        :current-page.sync="page"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      ></FooterPage>
      <div v-if="isCheck" slot="footer" style="width: 100%; border-top: 1px solid #eee; padding-top: 10px">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllGoodsBasic, searchBasic, getGoodsBasicOfShopId } from "@/api/goods";
import GoodsCategory from "@/component/common/GoodsCategory.vue";
export default {
  name: "GoodsWarehouse",
  components: {
    GoodsCategory,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    selType: {
      type: String,
      default: "",
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    shopId: {
      type: [Number, String],
      default: 0,
    },
    enable: {
      type: Boolean,
      default: false,
    },
    // 是否需要遮罩层
    modal: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      choose_data: [],
      goods_list: [],
      loading: false,
      keyword: "",
      total: 0,
      pre_page: 10,
      page: 1,
      enableStatus: "",
      categoryId: [],
    };
  },
  created() {
    if (this.enable) {
      this.enableStatus = 5;
    } else {
      this.enableStatus = "";
    }
    if (this.shopId) {
      this.getGoodsBasicOfShopId();
    } else {
      this.getList();
    }
  },
  methods: {
    goodsChane(val) {
      this.pageChange(1);
    },
    skuChange(val, index) {
      let target = this.$_common.deepClone(this.goods_list);
      target[index].unitName = target[index].unitList.find((item) => item.id === val).unitName;
      this.goods_list = target;
    },
    // 获取列表数据
    async getList() {
      const { data, pageTotal } = await getAllGoodsBasic({
        page: this.page,
        pageSize: this.pre_page,
        enableStatus: this.enableStatus,
      });

      this.goods_list = data.map((item) => {
        let unitList = [];
        let unitName = "";
        let skuId = "";
        if (parseInt(item.specType) === 1) {
          unitList = item.masterUnit ? [item.masterUnit].concat(item.branchUnit || []) : [];
          unitName = item.masterUnit ? item.masterUnit.unitName : 0;
          skuId = item.masterUnit ? item.masterUnit.id : 0;
        } else if (parseInt(item.specType) === 2) {
          unitList = item.specMultiple.map((itemS) => {
            let unitName = itemS.specGroup
              .map((itemM) => {
                return itemM.specValueIdName;
              })
              .join("_");
            let specValueId = itemS.specGroup
              .map((itemM) => {
                return itemM.specValueId;
              })
              .join("_");
            return {
              unitName: unitName,
              specValueId: specValueId,
              id: itemS.id,
            };
          });
          skuId = item.specMultiple[0].id;
          unitName = unitList[0].unitName;
        }
        return {
          ...item,
          skuId: skuId,
          unitName: unitName,
          unitList: unitList,
        };
      });
      this.total = pageTotal;
    },
    // 商品搜索
    async goodsSearch() {
      const { data, pageTotal } = await searchBasic({
        page: this.page,
        pageSize: this.pre_page,
        keyword: this.keyword,
        enableStatus: this.enableStatus,
      });

      this.goods_list = data.map((item) => {
        let unitList = [];
        let unitName = "";
        let skuId = "";
        if (parseInt(item.specType) === 1) {
          unitList = item.masterUnit ? [item.masterUnit].concat(item.branchUnit || []) : [];
          unitName = item.masterUnit ? item.masterUnit.unitName : 0;
          skuId = item.masterUnit ? item.masterUnit.id : 0;
        } else if (parseInt(item.specType) === 2) {
          unitList = item.specMultiple.map((itemS) => {
            let unitName = itemS.specGroup
              .map((itemM) => {
                return itemM.specValueIdName;
              })
              .join("_");
            let specValueId = itemS.specGroup
              .map((itemM) => {
                return itemM.specValueId;
              })
              .join("_");
            return {
              unitName: unitName,
              specValueId: specValueId,
              id: itemS.id,
            };
          });
          skuId = item.specMultiple[0].id;
          unitName = unitList[0].unitName;
        }
        return {
          ...item,
          skuId: skuId,
          unitName: unitName,
          unitList: unitList,
        };
      });
      this.total = pageTotal;
    },
    // 商铺下允许销售的商品列表
    async getGoodsBasicOfShopId() {
      const { data, pageTotal } = await getGoodsBasicOfShopId({
        page: this.page,
        pageSize: this.pre_page,
        shopId: this.shopId,
        keyword: this.keyword,
        categoryId: this.categoryId[this.categoryId.length - 1],
        enableStatus: this.enableStatus,
      });

      this.goods_list = data.map((item) => {
        let unitList = [];
        let unitName = "";
        let skuId = "";
        if (parseInt(item.specType) === 1) {
          unitList = item.masterUnit ? [item.masterUnit].concat(item.branchUnit || []) : [];
          unitName = item.masterUnit ? item.masterUnit.unitName : 0;
          skuId = item.masterUnit ? item.masterUnit.id : 0;
        } else if (parseInt(item.specType) === 2) {
          unitList = item.specGroup.map((itemS) => {
            let unitName = itemS.params
              .map((itemM) => {
                return itemM.specValueIdName;
              })
              .join("_");
            let specValueId = itemS.params
              .map((itemM) => {
                return itemM.specValueId;
              })
              .join("_");
            return {
              unitName: unitName,
              specValueId: specValueId,
              id: itemS.id,
            };
          });
          // skuId = item.specMultiple[0].id
          // unitName = unitList[0].unitName
        }
        return {
          ...item,
          skuId: skuId,
          unitName: unitName,
          unitList: unitList,
        };
      });
      this.total = pageTotal;
    },
    pageChange(page) {
      this.page = page;
      if (this.shopId) {
        this.getGoodsBasicOfShopId();
      } else {
        if (this.keyword) {
          this.goodsSearch();
        } else {
          this.getList();
        }
      }
    },

    sizeChange(size) {
      this.pre_page = size;
      this.pageChange(1);
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 关闭弹窗
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    selGoods(row) {
      if (row.enableStatus !== 5) {
        return;
      }
      this.cancel();
      this.$emit("confirm", [row]);
    },
  },
};
</script>

<style scoped></style>
