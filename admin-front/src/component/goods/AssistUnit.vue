<template>
  <el-dialog
    :title="isEdit ? '修改辅助单位' : '新增辅助单位'"
    :visible.sync="isShow"
    width="40%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="close"
  >
    <el-form ref="ruleForm" :model="assistfform" :rules="assist_rules" label-width="120px">
      <el-form-item label="单位名称" prop="unitName">
        <el-select v-model="assistfform.unitId" placeholder="请选择辅助单位" @change="assitUnitChange">
          <el-option
            v-for="item in unit_options"
            :key="item.id"
            :label="item.unitName"
            :value="item.unitId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="换算率" prop="conversion">
        <el-input-number v-model="assistfform.conversion" placeholder="换算率" :controls="false"></el-input-number>
        <p style="font-size: 12px; color: #e6a23c">(1辅助单位=n基本单位, 例如: 1辅助单位=10基本单位)</p>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="subAssist">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getAllUnit } from "@/api/goods";
export default {
  name: "AssistUnit",
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    assistForm: {
      type: Object,
      default: () => {
        return {
          unitId: "",
          conversion: "",
        };
      },
    },
  },
  data() {
    return {
      assist_model: false,
      edit_assist_model: false,
      unit_index: 0,
      assist_index: 0,
      assistfform: {
        unitId: "",
        conversion: "",
      },
      unit_options: [
        {
          unitName: "件",
          unitId: 1,
        },
        {
          unitName: "袋",
          unitId: 2,
        },
        {
          unitName: "箱",
          unitId: 3,
        },
        {
          unitName: "包",
          unitId: 4,
        },
        {
          unitName: "KG",
          unitId: 5,
        },
        {
          unitName: "瓶",
          unitId: 6,
        },
        {
          unitName: "盒",
          unitId: 7,
        },
        {
          unitName: "卷",
          unitId: 8,
        },
        {
          unitName: "桶",
          unitId: 9,
        },
        {
          unitName: "斤",
          unitId: 10,
        },
      ],
      assist_rules: {
        // unitName: [{ required: true, message: '请选择辅助单位名称', trigger: 'blur' }],
        conversion: [
          {
            required: true,
            message: "请输入辅助单位与基本单位的换算关系",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    if (this.isEdit) {
      this.assistfform = this.assistForm;
    }
    this.getAllUnit();
  },
  methods: {
    // 请求基本单位数据
    async getAllUnit() {
      const { data } = await getAllUnit({
        page: 1,
        pageSize: 99,
      });

      this.unit_options = data.map((item) => {
        return {
          unitId: item.id,
          unitName: item.unitName,
        };
      });
    },
    // 选择辅助单位
    assitUnitChange(val) {
      const target = this.unit_options.find((item) => item.unitId === val);
      this.assistfform.unitName = target.unitName;
    },
    close() {
      this.assistfform = {
        unitName: "",
        unitId: "",
        conversion: "",
      };
      this.$emit("close");
    },
    // 弹窗提交
    subAssist() {
      // if (!this.edit_assist_model) {
      //   this.base_form.branchUnit.push(this.assistfform)
      // } else {
      //   this.base_form.branchUnit[this.assist_index] = this.assistfform
      // }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.assistfform.isMaster = 4;
          this.$emit("confirm", this.$_common.deepClone(this.assistfform));
          this.close();
        }
      });
    },
  },
};
</script>

<style scoped></style>
