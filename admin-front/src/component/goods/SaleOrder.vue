<template>
  <div>
    <el-dialog
      title="销售订单列表"
      :visible.sync="dialogVisible"
      width="60%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="close"
    >
      <div>
        <el-input
          v-model="order"
          style="width: 220px; margin-bottom: 10px"
          placeholder="请输入商品编号"
          clearable
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" @click="pageChange(1)">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
        <el-table size="mini" :data="table_data" @row-dblclick="dblclick" @selection-change="handleSelectionChange">
          <el-table-column v-if="isCheck" type="selection" align="center" width="55"></el-table-column>
          <el-table-column prop="id" label="ID" width="60"></el-table-column>
          <el-table-column prop="no" label="订单编号" min-width="200">
            <template slot-scope="scope">
              <span class="click-div" @click="openDetail(scope.row)">
                {{ scope.row.no }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="下单时间" min-width="160">
            <template slot-scope="scope">
              {{ $_common.formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="customerName" label="客户名称" min-width="120"></el-table-column>
          <el-table-column prop="realName" label="收货人" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.receiveData.realName }}
            </template>
          </el-table-column>
          <el-table-column prop="mobile" label="联系电话" min-width="110">
            <template slot-scope="scope">
              {{ scope.row.receiveData.mobile }}
            </template>
          </el-table-column>
          <el-table-column prop="payAmount" label="订单金额" min-width="80">
            <template slot-scope="scope">
              {{ $_common.formattedNumber(scope.row.payAmount) }}
            </template>
          </el-table-column>
          <el-table-column label="选择" width="100" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" icon="el-icon-check" @click="dblclick(scope.row)"></el-button>
            </template>
          </el-table-column>
        </el-table>
        <FooterPage
          :page-size="pre_page"
          :total-page.sync="total"
          :current-page.sync="page"
          @pageChange="pageChange"
          @sizeChage="sizeChange"
        ></FooterPage>
      </div>
      <div v-if="isCheck" style="text-align: center; padding-top: 10px">
        <el-button size="small" type="primary" @click="confirm">确认</el-button>
        <el-button size="small" @click="close">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllOrder } from "@/api/Order";
export default {
  name: "SaleOrder",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: false,
    },
    returnStatus: {
      type: [Number, String, Array],
      default: 0,
    },
  },
  data() {
    return {
      order: "",
      pre_page: 10,
      total: 0,
      page: 1,
      table_data: [],
      choose_data: [],
    };
  },
  created() {
    this.getAllOrder();
  },
  methods: {
    async getAllOrder() {
      //  获取订单
      const data = await getAllOrder({
        page: this.page,
        pageSize: this.pre_page,
        orderStatus: 5,
        returnStatus: this.returnStatus,
        outStatus: 5,
        order: this.order,
        search: { orderType: [1, 23, 24] },
      });

      this.table_data = data.data;
      this.total = data.pageTotal;
    },
    dblclick(row) {
      this.close();
      this.$emit("confirm", [row]);
    },
    pageChange(val) {
      this.page = val;
      this.getAllOrder();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getAllOrder();
    },
    // 关闭弹框
    close() {
      this.$emit("close");
    },
    confirm() {
      this.$emit("confirm", this.choose_data);
      this.close();
    },
    handleSelectionChange(val) {
      this.choose_data = val;
    },
    openDetail(row) {
      this.close();
      this.$router.push("/order/manageO/OrderDetails/" + row.userCenterId + "/" + row.id);
    },
  },
};
</script>

<style scoped></style>
