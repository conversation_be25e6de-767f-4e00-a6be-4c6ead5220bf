<!--选择商品-->
<template>
  <div>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="商品列表"
      :visible="isShow"
      width="60%"
      @close="cancel"
    >
      <el-table border :data="goods_list" size="small" @selection-change="selectionChange" @row-dblclick="selGoods">
        <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
        <el-table-column prop="goodsName" label="商品名称"></el-table-column>
        <el-table-column prop="goodsCode" label="商品编码"></el-table-column>
        <el-table-column prop="categoryName" label="分类"></el-table-column>
        <el-table-column prop="buyerUnitPrice" label="采购单价">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.buyerUnitPrice, 2) }}
          </template>
        </el-table-column>
        <el-table-column prop="buyerNum" label="采购数量">
          <template slot-scope="scope">
            {{ $_common.formatNub(scope.row.buyerNum, 2) }}
          </template>
        </el-table-column>
        <el-table-column prop="skuName" label="规格"></el-table-column>
        <el-table-column label="选择商品">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-check" @click="selGoods(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="isCheck" slot="footer" style="width: 100%; border-top: 1px solid #eee; padding-top: 10px">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPurchaseAndBatchInfoById } from "@/api/Purchase";
export default {
  name: "GoodsWarehouse",
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    id: {
      type: Number,
      default: 0,
    },
    shop: {
      type: [String, Number],
      default: 0,
    },
    goodsData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      choose_data: [],
      goods_list: [],
    };
  },
  created() {
    this.getPurchaseAndBatchInfoById();
  },
  methods: {
    async getPurchaseAndBatchInfoById() {
      const { data } = await getPurchaseAndBatchInfoById({
        id: this.id,
        warehouseId: this.shop,
      });
      this.goods_list = data;
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 关闭弹窗
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    selGoods(row) {
      this.cancel();
      this.$emit("confirm", [row]);
    },
  },
};
</script>

<style scoped></style>
