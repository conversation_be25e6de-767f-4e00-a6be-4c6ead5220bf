<!--选择商品-->
<template>
  <div>
    <el-dialog
      title="订单商品列表"
      :visible="isShow"
      width="60%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-table
        ref="goods_list"
        :data="goods_list"
        size="small"
        @selection-change="selectionChange"
        @row-dblclick="selGoods"
      >
        <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
        <el-table-column label="商品名称" fixed="left" min-width="220">
          <template slot-scope="scope">
            <div class="clearfix">
              <p class="goods-title">
                {{ scope.row.goodsName }}
              </p>
              <p class="goods-no">
                {{ scope.row.goodsCode }}
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="shopName" label="商铺名称" min-width="160"></el-table-column>

        <el-table-column prop="unitName" label="规格" min-width="80"></el-table-column>
        <el-table-column prop="price" label="价格" min-width="100"></el-table-column>
        <el-table-column prop="buyNum" label="数量" min-width="100"></el-table-column>
        <el-table-column label="选择商品">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-check" @click="selGoods(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="isCheck" slot="footer" style="width: 100%; border-top: 1px solid #eee; padding-top: 10px">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOrderInfoById } from "@/api/Order";
export default {
  name: "GoodsWarehouse",
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    orderId: {
      type: Number,
      default: 0,
    },
    userCenterId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      choose_data: [],
      goods_list: [],
      loading: false,
    };
  },
  created() {
    if (this.orderId) {
      this.getList();
    } else {
      this.$message("请选择订单");
    }
  },
  methods: {
    // 获取列表数据
    async getList() {
      const { data, pageTotal } = await getOrderInfoById(this.userCenterId, {
        orderId: this.orderId,
      });

      this.goods_list = data.goodsData;
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 关闭弹窗
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    selGoods(row) {
      this.cancel();
      this.$emit("confirm", [row]);
    },
  },
};
</script>

<style scoped></style>
