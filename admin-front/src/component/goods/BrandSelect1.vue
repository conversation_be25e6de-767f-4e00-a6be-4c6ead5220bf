<template>
  <vxe-pulldown ref="xDown4" transfer>
    <template #default>
      <vxe-input
        :value="value"
        suffix-icon="fa fa-search"
        placeholder="商品品牌"
        clearable
        @keyup="getList"
        @clear="clear"
        @focus="focusEvent"
        @suffix-click="suffixClick"
      ></vxe-input>
    </template>
    <template #dropdown>
      <div class="dropdown-view">
        <div class="search-view">
          <vxe-input
            v-model="search_key"
            type="search"
            clearable
            placeholder="请输入品牌名称"
            @search-click="pageChange(1)"
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          ></vxe-input>
        </div>
        <vxe-table height="380" :data="brand_list">
          <vxe-table-column v-if="isMultiple" type="checkbox" width="50"></vxe-table-column>
          <vxe-table-column min-width="150" field="code" title="编码"></vxe-table-column>
          <vxe-table-column min-width="300" field="title" title="名称"></vxe-table-column>
          <vxe-table-column width="100" title="操作">
            <template #default="{ row }">
              <el-button size="mini" icon="el-icon-check" @click="dblclick(row)"></el-button>
            </template>
          </vxe-table-column>
        </vxe-table>
        <FooterPage
          :page-size="pageSize"
          :total-page.sync="total"
          :current-page.sync="page"
          layout="total, prev, pager, next"
          @pageChange="pageChange"
          @sizeChange="sizeChange"
        >
          <div v-if="isMultiple" slot="btn-div" style="padding: 0 10px">
            <el-button size="mini" type="primary" @click="confirm"> 确认 </el-button>
          </div>
        </FooterPage>
      </div>
    </template>
  </vxe-pulldown>
</template>

<script>
import { getAllBrand } from "@/api/goods";
export default {
  name: "BrandSelect",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    // 是否多选
    isMultiple: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      brand_list: [],
      total: 0,
      page: 1,
      pageSize: 10,
      search_key: "",
      tableColumn: [
        { type: "checkbox", width: "60" },
        {
          field: "code",
          showOverflow: "ellipsis",
          width: "130",
          title: "编码",
        },
        { field: "title", showOverflow: "ellipsis", title: "名称" },
        { field: "sort", title: "排序" },
      ],
      loading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    focusEvent() {
      this.$refs.xDown4.showPanel();
    },
    async getList(val) {
      this.loading = true;
      const data = await getAllBrand({
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.search_key,
        enableStatus: 5,
      });
      this.loading = false;
      this.brand_list = data.data;
      this.total = data.pageTotal;
    },
    clear() {
      this.$refs.xDown4.hidePanel();
      this.$emit("confirm");
    },
    suffixClick() {
      this.$refs.xDown4.togglePanel();
    },
    dblclick(row) {
      this.$refs.xDown4.hidePanel();
      this.$emit("confirm", [row]);
    },
    confirm() {},
    pageChange(page) {
      this.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped lang="scss">
.dropdown-view {
  width: 600px;
  height: 500px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  .search-view {
    padding: 10px;
  }
}
</style>
