<!--多仓库价格编辑器-->
<template>
  <div class="warehouse-price-editor">
    <!-- 单一价格模式 -->
    <template v-if="isSinglePrice">
      <div class="price-content">
        <template v-if="editable">
          <el-input
            v-model.number="singlePrice"
            type="number"
            size="mini"
            :min="0"
            :precision="2"
            @blur="handleSinglePriceChange"
          >
            <template slot="prepend">¥</template>
          </el-input>
        </template>
        <template v-else>
          <span class="price-text" style="padding: 5px">¥{{ formatPrice(singlePrice) }}</span>
          <el-tooltip v-if="ladderPrice && ladderPrice.length" placement="top" popper-class="ladder-price-tooltip">
            <div slot="content">
              <div class="ladder-price-list">
                <div v-for="(item, index) in ladderPrice" :key="index" class="ladder-item">
                  <span>{{ item.from }}-{{ item.to === 9999999999 ? "∞" : item.to }}件</span>
                  <span>¥{{ formatPrice(item.price) }}/件</span>
                </div>
              </div>
            </div>
            <i class="el-icon-s-operation ladder-price-icon"></i>
          </el-tooltip>
        </template>
      </div>
    </template>

    <!-- 多仓库价格模式 -->
    <template v-else>
      <div v-for="(warehouse, index) in value" :key="warehouse.warehouseId" class="warehouse-item">
        <div class="warehouse-header">
          <span class="warehouse-name">{{ getWarehouseDisplayName(warehouse.warehouseId) }}</span>
        </div>
        <div class="price-content">
          <template v-if="editable">
            <el-input
              v-model.number="warehouseData[index].price"
              type="number"
              size="mini"
              :min="0"
              :precision="2"
              @blur="handlePriceChange(index)"
            >
              <template slot="prepend">¥</template>
            </el-input>
          </template>
          <template v-else>
            <span class="price-text">¥{{ formatPrice(warehouse.price) }}</span>
            <el-tooltip
              v-if="ladderPrice && ladderPrice[warehouse.warehouseId]"
              placement="top"
              popper-class="ladder-price-tooltip"
            >
              <div slot="content">
                <div class="ladder-price-list">
                  <div v-for="(item, idx) in ladderPrice[warehouse.warehouseId]" :key="idx" class="ladder-item">
                    <span>{{ item.from }}-{{ item.to === 999999999 ? "∞" : item.to }}件</span>
                    <span>¥{{ formatPrice(item.price) }}/件</span>
                  </div>
                </div>
              </div>
              <i class="el-icon-s-operation ladder-price-icon"></i>
            </el-tooltip>
          </template>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from "vuex";

export default {
  name: "WarehousePriceEditor",
  props: {
    // 仓库价格数据，可以是数组（多仓库）或数字/字符串（单一价格）
    value: {
      type: [Array, Number, String],
      required: true,
      default: () => [],
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: false,
    },
    // 阶梯价格数据
    ladderPrice: {
      type: [Array, Object],
      default: () => null,
    },
  },
  data() {
    return {
      // 内部维护的仓库数据
      warehouseData: [],
      // 单一价格值
      singlePrice: 0,
    };
  },
  computed: {
    ...mapState({
      warehouseList: (state) => state.warehouse.warehouseList,
      loading: (state) => state.warehouse.loading,
    }),
    ...mapGetters("warehouse", ["getWarehouseName"]),

    // 是否为单一价格模式
    isSinglePrice() {
      return typeof this.value === "number" || typeof this.value === "string";
    },
  },
  watch: {
    value: {
      handler(newVal) {
        if (this.isSinglePrice) {
          // 处理单一价格，支持数字和字符串
          this.singlePrice = Number(newVal || 0);
        } else {
          // 处理多仓库价格，确保是数组
          if (Array.isArray(newVal)) {
            this.warehouseData = newVal.map((item) => ({
              ...item,
              price: Number(item.price || 0),
            }));
          } else {
            // 如果不是数组，初始化为空数组
            this.warehouseData = [];
          }
          // 当值变化且仓库列表为空时，获取仓库数据
          if (this.warehouseList.length === 0) {
            this.fetchWarehouseList();
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    ...mapActions("warehouse", ["getWarehouseList"]),

    async fetchWarehouseList() {
      try {
        await this.getWarehouseList();
      } catch (error) {
        this.$message.error("获取仓库列表失败");
      }
    },

    // 格式化价格显示
    formatPrice(price) {
      return Number(price || 0).toFixed(2);
    },

    // 获取仓库显示名称
    getWarehouseDisplayName(warehouseId) {
      // 如果没有仓库ID，返回默认值
      if (!warehouseId) {
        return "未知仓库";
      }

      // 尝试从 getter 获取仓库名称
      const warehouseName = this.getWarehouseName(warehouseId);

      // 如果获取到的名称不是默认格式（仓库+ID），直接返回
      if (warehouseName && !warehouseName.startsWith("仓库")) {
        return warehouseName;
      }

      // 如果是默认格式或获取失败，尝试从仓库列表中直接查找
      const warehouse = this.warehouseList.find((w) => w.id === warehouseId);
      if (warehouse && warehouse.warehouseName) {
        return warehouse.warehouseName;
      }

      // 最后的兜底方案
      return warehouseName || "未知仓库";
    },

    // 处理单一价格变化
    handleSinglePriceChange() {
      // 确保价格为非负数
      if (this.singlePrice < 0) {
        this.singlePrice = 0;
      }
      // 实时触发更新
      this.$emit("input", Number(this.singlePrice || 0));
      this.$emit("on-price-change", { price: Number(this.singlePrice || 0) });
    },

    // 处理多仓库价格变化
    handlePriceChange(index) {
      const warehouse = this.warehouseData[index];
      // 确保价格为非负数
      if (warehouse.price < 0) {
        warehouse.price = 0;
      }

      // 立即更新数据
      const updatedData = this.warehouseData.map((item) => ({
        ...item,
        price: Number(item.price || 0),
      }));

      // 触发更新事件
      this.$emit("input", updatedData);
      this.$emit("on-price-change", {
        ...warehouse,
        price: Number(warehouse.price || 0),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.warehouse-price-editor {
  .warehouse-item {
    padding: 5px;

    &:last-child {
      border-bottom: none;
    }

    .warehouse-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2px;

      .warehouse-name {
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .price-content {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-input {
      width: 200px;
    }

    .price-text {
      color: #606266;
      font-size: 14px;
    }

    .ladder-price-icon {
      margin-left: 4px;
      color: #909399;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        color: #409eff;
      }
    }
  }
  ::v-deep .el-input-group__prepend {
    padding: 0 10px;
  }
}

// 阶梯价格提示样式
:deep(.ladder-price-tooltip) {
  .ladder-price-list {
    min-width: 150px;

    .ladder-item {
      display: flex;
      justify-content: space-between;
      padding: 4px 0;

      &:not(:last-child) {
        border-bottom: 1px solid #ebeef5;
      }

      span {
        &:first-child {
          margin-right: 12px;
          color: #909399;
        }

        &:last-child {
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
