<!--选择店铺商品-->
<template>
  <div>
    <el-dialog
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="优惠券列表"
      :visible="isShow"
      width="60%"
      @close="cancel"
    >
      <div class="clearfix" style="padding-bottom: 10px">
        <el-input
          v-model="keyword"
          style="width: 240px"
          size="mini"
          placeholder="请输入优惠券名称"
          clearable
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" @click="pageChange(1)">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
      </div>
      <el-table
        ref="coupon_list"
        border
        :data="coupon_list"
        size="small"
        @selection-change="selectionChange"
        @row-dblclick="selCoupon"
      >
        <el-table-column v-if="isCheck" type="selection" width="55" align="center"></el-table-column>
        <el-table-column prop="name" label="优惠券名称" align="center"></el-table-column>
        <el-table-column label="有效时间" prop="startTime" align="center">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.startTime, "yyyy-MM-dd") }} 至
            {{ $_common.formatDate(scope.row.endTime, "yyyy-MM-dd") }}
          </template>
        </el-table-column>
        <el-table-column prop="basicCode" label="发放日期" align="center" min-width="200">
          <template slot-scope="scope">
            {{ $_common.formatDate(scope.row.grantStartTime, "yyyy-MM-dd") }} 至
            {{ $_common.formatDate(scope.row.grantEndTime, "yyyy-MM-dd") }}
          </template>
        </el-table-column>
        <el-table-column label="发放范围" align="center" min-width="180" prop="useShopName">
          <template slot-scope="scope">
            {{ scope.row.useShopName.length ? scope.row.useShopName.join(",") : scope.row.useShopName[0] }}
          </template>
        </el-table-column>
        <el-table-column prop="reducePrice" align="center" label="面值(元)" min-width="120"></el-table-column>
        <el-table-column prop="minPrice" align="center" label="使用门槛" min-width="100">
          <template slot-scope="scope">
            {{ Number(scope.row.minPrice) === 0 ? "无门槛" : scope.row.minPrice }}
          </template>
        </el-table-column>
        <el-table-column prop="totalNum" align="center" label="总数量" min-width="100"></el-table-column>
        <el-table-column prop="usableNum" align="center" label="剩余数量" min-width="100"></el-table-column>
        <el-table-column label="选择商品" align="center">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-check" @click="selCoupon(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <FooterPage
        :page-size="pre_page"
        :total-page.sync="total"
        :current-page.sync="page"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      ></FooterPage>
      <div v-if="isCheck" slot="footer" style="width: 100%; border-top: 1px solid #eee; padding-top: 10px">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAll, searchMarket } from "@/api/Market";
export default {
  name: "CouponModel",
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    shopId: {
      type: [Number, String, Array],
      default: 0,
    },
    userCenterId: {
      type: Number,
      default: 0,
    },
    categoryId: {
      type: [Number, String],
      default: 0,
    },
    grantType: {
      type: Number,
      default: 0,
    },
    couponType: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      keyword: "",
      choose_data: [],
      coupon_list: [],
      loading: false,
      total: 0,
      pre_page: 10,
      page: 1,
      pageLayout: "total, prev, pager, next",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //  优惠券
    async getAll() {
      const { data, pageTotal } = await getAll({
        page: this.page,
        pageSize: this.pre_page,
        couponType: this.couponType,
        grantType: this.grantType,
        auditStatus: 2,
      });

      this.total = pageTotal;
      this.coupon_list = data;
    },
    //  搜索
    async searchMarket() {
      const { data, pageTotal } = await searchMarket({
        page: this.page,
        pageSize: this.pre_page,
        couponType: this.couponType,
        grantType: this.grantType,
        keyword: this.keyword,
        auditStatus: 2,
      });

      this.total = pageTotal;
      this.coupon_list = data;
    },
    getList() {
      const obj = {
        keyword: this.keyword,
      };
      const isKey = this.$_common.isSerch(obj);
      if (isKey) {
        this.searchMarket();
      } else {
        this.getAll();
      }
    },
    pageChange(page) {
      this.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.pre_page = size;
      this.pageChange(1);
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 关闭弹窗
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    selCoupon(row) {
      this.cancel();
      this.$emit("confirm", [row]);
    },
  },
};
</script>

<style scoped></style>
