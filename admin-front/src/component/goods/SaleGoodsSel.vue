<!--选择店铺商品-->
<template>
  <div style="text-align: left">
    <el-dialog
      title="商品列表"
      :visible="isShow"
      width="60%"
      top="50px"
      :append-to-body="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <div class="clearfix" style="padding-bottom: 10px">
        <el-input
          v-model="keyword"
          style="width: 240px"
          size="small"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" @click="pageChange(1)">
            <i class="el-icon-search"></i>
          </el-button>
        </el-input>
        <span style="display: inline-block; margin-left: 10px">
          <GoodsCategory v-model="categoryId" :width="160" check-strictly clearable size="small" @change="goodsChane" />
        </span>
        <span v-if="shopId === 0 || shopId == ''" style="display: inline-block; margin-left: 10px">
          <SelectShop v-model="selShopId" width="220" placeholder="所属店铺" @clear="clearShop" @change="selShop" />
        </span>
      </div>
      <el-table
        ref="goodsTable"
        :data="goods_list"
        height="600"
        size="small"
        :row-key="
          (row) => {
            return `${row.skuId}-${row.shopId}`;
          }
        "
        @selection-change="selectionChange"
        @row-dblclick="selGoods"
      >
        <el-table-column
          align="center"
          type="selection"
          width="55"
          :reserve-selection="true"
          :selectable="selectable"
        ></el-table-column>
        <el-table-column prop="code" label="商品编码" min-width="140"></el-table-column>
        <el-table-column prop="oldCode" label="商品编码（旧）" min-width="140"></el-table-column>
        <el-table-column prop="title" label="商品名称" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column label="规格" min-width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.unitName }};</span>
            <span v-for="(itemS, indexS) in scope.row.specGroup" :key="indexS"> {{ itemS.specValueName }}; </span>
          </template>
        </el-table-column>
        <el-table-column v-if="!baseGoods" label="可售库存" width="100">
          <template slot-scope="scope">
            <div :class="[scope.row.inventory <= 0 ? 'danger-status' : 'success-status']">
              {{ $_common.formatNub(scope.row.inventory) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="!baseGoods" prop="salePrice" label="销售价" min-width="100" max-width="180">
          <template slot-scope="scope">
            <WarehousePriceEditor
              v-model="scope.row.salePrice"
              :editable="false"
              :ladder-price="scope.row.ladderPrice"
            />
          </template>
        </el-table-column>
        <el-table-column prop="categoryName" label="商品分类" min-width="80"></el-table-column>
        <el-table-column prop="shopName" label="所属商铺" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column fixed="right" label="选择商品" width="100">
          <template slot-scope="scope">
            <el-button
              v-if="selectable(scope.row, scope.$index)"
              size="mini"
              icon="el-icon-check"
              @click="selGoods(scope.row)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="footer clearfix">
        <div class="float_left" style="padding-top: 20px">
          <el-button size="small" type="primary" @click="confirm"> 确 定 </el-button>
          <el-button size="small" @click="cancel">取消 </el-button>
        </div>
        <div class="float_right">
          <FooterPage
            :page-size="pre_page"
            :total-page.sync="total"
            :current-page.sync="page"
            :page-number="[15, 30, 50, 100]"
            @pageChange="pageChange"
            @sizeChange="sizeChange"
          ></FooterPage>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllGoodsBasicBySku, getAllGoodsBySku } from "@/api/goods";
import GoodsCategory from "@/component/common/GoodsCategory.vue";
import SelectShop from "@/component/goods/SelectShop.vue";
import WarehousePriceEditor from "@/component/goods/WarehousePriceEditor.vue";

export default {
  name: "SaleGoodsSel",
  components: {
    GoodsCategory,
    WarehousePriceEditor,
    SelectShop,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    shopId: {
      type: [Number, String, Array],
      default: 0,
    },
    userCenterId: {
      type: [Number, String],
      default: "",
    },
    // categoryId: {
    //   type: [Number, String],
    //   default: 0,
    // },
    baseGoods: {
      type: Boolean,
      default: false,
    },
    // 5 只显示基本单位 4:主辅助单位都显示
    isRevealSku: {
      type: [Number, String],
      default: 4,
    },
    merchantId: {
      type: [Number, String],
      default: "",
    },
    // 当前使用组件的页面是否是采购页面
    isPurchase: {
      type: Boolean,
      default: false,
    },
    // 当前使用组件的页面是否可以选择主单位
    isEqMaster: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      keyword: "",
      choose_data: [],
      goods_list: [],
      loading: false,
      total: 0,
      pre_page: 15,
      page: 1,
      categoryId: [],
      ifMerchant: "",
      selShopId: "",
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    goodsChane(val) {
      this.pageChange(1);
    },
    async getAllGoodsBasicBySku() {
      if (this.merchantId) {
        this.ifMerchant = 5;
      } else {
        this.ifMerchant = 4;
      }
      const { data, pageTotal } = await getAllGoodsBasicBySku({
        page: this.page,
        pageSize: this.pre_page,
        isRevealSku: this.isRevealSku, // 5 只显示基本单位 4:主辅助单位都显示
        keyword: this.keyword,
        categoryId: this.categoryId[this.categoryId.length - 1],
        ifMerchant: this.ifMerchant,
        merchantId: this.merchantId,
        shopId: this.selShopId,
      });
      this.total = pageTotal;
      this.goods_list = data;
      for (let i = 0; i < data.length; i++) {
        const isTrue = this.choose_data.find((itemF) => {
          return itemF.id === data[i].id && itemF.skuId === data[i].skuId;
        });
        if (isTrue) {
          this.toggleRowSelection([data[i]]);
        }
      }
    },
    async getAllGoodsBySku() {
      let params = {
        page: this.page,
        pageSize: this.pre_page,
        isRevealSku: this.isRevealSku, // 5 只显示基本单位 4:主辅助单位都显示
        keyword: this.keyword,
        categoryId: this.categoryId[this.categoryId.length - 1],
        userCenterId: this.userCenterId,
      };
      if (this.shopId) {
        params.shopId = Array.isArray(this.shopId) ? this.shopId : [this.shopId];
      }
      if (this.selShopId) {
        params.shopId = this.selShopId;
      }
      const { data, pageTotal } = await getAllGoodsBySku(params);

      this.total = pageTotal;
      this.goods_list = data;
      for (let i = 0; i < data.length; i++) {
        const isTrue = this.choose_data.find((itemF) => {
          return itemF.id === data[i].id && itemF.skuId === data[i].skuId;
        });
        if (isTrue) {
          this.toggleRowSelection([data[i]]);
        }
      }
    },
    toggleRowSelection(rows) {
      this.$nextTick(() => {
        rows.forEach((row) => {
          this.$refs.goodsTable.toggleRowSelection(row, true);
        });
      });
    },
    getList() {
      if (this.baseGoods) {
        this.getAllGoodsBasicBySku();
      } else {
        this.getAllGoodsBySku();
      }
    },
    pageChange(page) {
      this.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.pre_page = size;
      this.pageChange(1);
    },
    selectionChange(val) {
      if (val.length) {
        // if (!this.choose_data.length) {
        //   this.choose_data = val;
        //   console.log(this.choose_data);
        // } else {
        //   this.choose_data = this.$_common.unique(
        //     this.choose_data.concat(val),
        //     ["id", "skuId"]
        //   );
        // }
        this.choose_data = val;
      } else {
        for (let i = 0; i < this.goods_list.length; i++) {
          const index = this.choose_data.findIndex((itemF) => {
            return itemF.skuId === this.goods_list[i].skuId;
          });
          if (index > -1) {
            this.choose_data.splice(index, 1);
          }
        }
      }
      this.multipleSelectionFirst = val;
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 关闭弹窗
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    selGoods(row) {
      if (row.isEq === 5 && row.isMaster === 4 && this.isPurchase) {
        return;
      }
      this.cancel();
      this.$emit("confirm", [row]);
    },
    //四、设置表格中勾选框是否是禁用状态
    selectable(row, index) {
      // 抄码商品 不允许采购辅助单位
      if (row.isEq === 5 && row.isMaster === 4 && this.isPurchase) {
        return false; //禁用状态
      } else if (row.isEq === 5 && row.isMaster === 5 && !this.isEqMaster) {
        return false; //禁用状态
      } else {
        return true; //非禁用状态
      }
    },
    clearShop() {
      this.selShopId = "";
      this.pageChange(1);
    },
    selShop() {
      console.log(this.selShopId);
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.footer {
  padding-bottom: 10px;
}
</style>
