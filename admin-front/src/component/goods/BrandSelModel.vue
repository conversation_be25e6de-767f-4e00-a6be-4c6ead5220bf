<template>
  <el-dialog
    title="品牌列表"
    :visible.sync="dialogVisible"
    width="60%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="close"
  >
    <div style="padding-bottom: 10px" class="clearfix">
      <div class="float_left">
        <el-input
          v-model="search_key"
          placeholder="请输入品牌名称"
          size="small"
          style="width: 280px"
          clearable
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </div>
      <el-button v-if="isShowAdd" class="float_right" type="text" @click="getList"> 【刷新】 </el-button>
    </div>

    <el-table size="mini" :data="brand_list" @row-dblclick="dblclick" @selection-change="handleSelectionChange">
      <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
      <el-table-column prop="code" label="编码" min-width="100"></el-table-column>
      <el-table-column prop="title" label="名称" min-width="100"></el-table-column>
      <el-table-column prop="sort" label="排序" min-width="100"></el-table-column>
      <el-table-column prop="address" label="状态" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.enableStatus === 5" class="open-span"> 启用 </span>
          <span v-else class="disabled-span">禁用</span>
        </template>
      </el-table-column>
      <el-table-column label="选择">
        <template slot-scope="scope">
          <el-button size="mini" icon="el-icon-check" @click="dblclick(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="isCheck" slot="btn-div">
        <el-button size="small" type="primary" @click="confirm">确认</el-button>
        <el-button size="small" @click="close">取消</el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>

<script>
import { getAllBrand } from "@/api/goods";
import FooterPage from "@/component/common/FooterPage";
import { mapGetters } from "vuex";

export default {
  name: "BrandSelModel",
  components: {
    FooterPage,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    isShowAdd: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    enable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      total: 100,
      page: 1,
      pageSize: 10,
      search_key: "",
      brand_list: [],
      choose_brand: [],
      enableStatus: "",
    };
  },
  computed: {
    ...mapGetters({ storeData: "MUser/storeData" }),
  },
  created() {
    if (this.enable) {
      this.enableStatus = 5;
    } else {
      this.enableStatus = "";
    }
    this.getList();
  },
  methods: {
    async getList() {
      const data = await getAllBrand({
        page: this.page,
        pageSize: this.pageSize,
        keyword: this.search_key,
        enableStatus: this.enableStatus,
      });

      this.brand_list = data.data;
      this.total = data.pageTotal;
    },
    close() {
      this.$emit("close");
    },
    dblclick(row) {
      this.close();
      this.$emit("confirm", [row]);
    },
    confirm() {
      this.$emit("confirm", this.choose_brand);
      this.close();
    },
    handleSelectionChange(val) {
      this.choose_brand = val;
    },
    pageChange(page) {
      this.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
