<template>
  <el-dialog title="经营屏蔽" :visible.sync="dialogVisible" width="60%" @close="close">
    <div class="page-tip-div">
      <i class="el-icon-info"></i>
      符合以下条件的客户用将无法看到此分类下商品
    </div>
    <el-row :gutter="10">
      <el-col :span="8">
        <div class="detail-tab-item" style="margin-bottom: 0">
          <div class="detail-tab-title">地区屏蔽</div>
          <div class="detail-tab-main" style="height: calc(100vh - 380px); overflow: auto">
            <RegionTree :checked-key="region_arr" @checkChange="selRegion" />
          </div>
        </div>
      </el-col>
      <el-col :span="16">
        <div class="detail-tab-item">
          <div class="detail-tab-title">客户类型屏蔽</div>
          <div class="detail-tab-main">
            <div v-if="!customer_type_list.length" style="text-align: center">暂无客户类型</div>
            <el-checkbox-group v-model="customer_type_check">
              <el-checkbox v-for="(item, index) in customer_type_list" :key="index" :label="item.id">
                {{ item.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="detail-tab-item">
          <div class="detail-tab-title clearfix">
            <span class="float_left">客户屏蔽</span>
            <div class="float_right">
              <el-button type="primary" size="mini" @click="customer_show = true"> 选择客户 </el-button>
            </div>
          </div>
          <div class="detail-tab-main">
            <el-table border :data="customer_list">
              <el-table-column show-overflow-tooltip prop="code" label="编号"></el-table-column>
              <el-table-column show-overflow-tooltip prop="name" label="姓名"></el-table-column>
              <el-table-column prop="customerType" label="类型"></el-table-column>
              <el-table-column prop="shopName" label="所属商铺" show-overflow-tooltip></el-table-column>
              <el-table-column label="管理">
                <template slot-scope="scope">
                  <el-button type="text" @click="deleteCustomer(scope.$index)"> 删除 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>
    <!--  客户选择-->
    <ClientListModal
      v-if="customer_show"
      :is-show="customer_show"
      :is-check="true"
      :is-show-add="true"
      :modal="false"
      @cancel="customer_show = false"
      @confirm="customerSel"
    />
  </el-dialog>
</template>
<script>
import RegionTree from "@/component/common/RegionTree";
import ClientListModal from "@/component/common/ClientListModal";

import { getAllCustomerSource } from "@/api/System";
export default {
  components: {
    RegionTree,
    ClientListModal,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      region_arr: [], // 经营屏蔽->当前商品已选择过的区域
      base_form: {
        notArea: [], //经营屏蔽地区
      },
      customer_type_list: [], // 经营屏蔽->客户类型列表
      customer_type_check: [], // 经营屏蔽->客户类型选择数组
      customer_show: false,
    };
  },
  created() {
    this.getAllCustomerSource();
  },
  methods: {
    // 地区选择
    selRegion(row) {
      this.base_form.notArea = row.map((item) => {
        return item.codePath;
      });
    },
    // 获取客户类型
    async getAllCustomerSource() {
      const data = await getAllCustomerSource({
        page: 1,
        pageSize: 50,
      });

      this.customer_type_list = data.data;
    },
    // 选择客户
    customerSel(val) {
      this.customer_list = val;
    },
    // 删除客户
    deleteCustomer(index) {
      this.customer_list.splice(index, 1);
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>
<style></style>
