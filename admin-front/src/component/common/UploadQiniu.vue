<template>
  <div class="upload-info">
    <ul class="upload-pic-ul">
      <li
        v-for="(item, index) in file_list"
        :key="index"
        v-dragging="{ item: item, list: file_list, group: 'item' }"
        class="upload-pic-li"
        :style="{ width: width + 'px', height: width + 'px' }"
      >
        <video v-if="item.content && item.content.indexOf('mp4') >= 0" :src="item.content" class="upload-img"></video>
        <img v-else class="upload-img" :src="item.content" alt="" />
        <div class="shadow-img"></div>
        <i class="el-icon-delete" @click="uploadRemove(index, item)"></i>
      </li>
    </ul>

    <div
      class="upload-pic"
      :style="{
        width: width + 'px',
        height: width + 'px',
        lineHeight: width + 2 + 'px',
      }"
      @click="beforeUpload"
    >
      <i class="el-icon-plus"></i>
    </div>
    <PicLibrary
      v-if="pic_model_show"
      :img-height="imgHeight"
      :modal="modal"
      :limit="limit"
      :is-show="pic_model_show"
      @cancel="pic_model_show = false"
      @confirm="uploadSuccess"
    />
  </div>
</template>

<script>
import PicLibrary from "./PicLibrary.vue";

export default {
  components: {
    PicLibrary,
  },
  props: {
    imgHeight: {
      type: Number,
      default: 750,
    },
    limit: {
      type: Number,
      default: 1,
    },
    width: {
      type: [Number, String],
      default: 146,
    },
    isBtn: {
      type: String,
      default: "picture-card",
    },
    upTip: {
      type: String,
      default: "",
    },
    modal: {
      type: Boolean,
      default: true,
    },
    fileList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 是否请求删除接口
    trueDel: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      loading: false,
      pic_model_show: false,
      file_list: [],
    };
  },
  watch: {
    fileList(val) {
      if (val.length > this.limit) {
        this.$message.warning(`抱歉，您至多能选择${this.limit}张图片`);
        return;
      }
      this.file_list = val.map((item) => {
        return {
          ...item,
          content: item.url || item.content,
        };
      });
    },
  },
  created() {
    this.file_list = this.fileList.map((item) => {
      return {
        ...item,
        content: item.url || item.content,
      };
    });
  },
  mounted() {
    // 页面内组件 拖拽事件
    this.$dragging.$on("dragged", (res) => {
      this.$emit("imgSortChange", this.file_list);
    });
  },
  methods: {
    beforeUpload() {
      this.pic_model_show = true;
      this.$emit("beforeUpload");
    },
    // 上传成功
    uploadSuccess(fileList) {
      this.file_list = fileList;
      const uploadPicUrlY = fileList[0].content;
      const file = fileList[0];
      this.$emit("uploadSuccess", uploadPicUrlY, {}, file, fileList);
    },
    uploadRemove(index, file) {
      this.file_list.splice(index, 1);
      this.$emit("handleRemove", file, this.file_list);
    },
  },
};
</script>
<style scoped>
.logoTip {
  font-size: 12px;
}
.upload-pic {
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  box-sizing: border-box;
  width: 148px;
  height: 148px;
  cursor: pointer;
  line-height: 146px;
  vertical-align: top;
  text-align: center;
  display: inline-block;
  margin-bottom: 10px;
}
.upload-pic i {
  font-size: 28px;
  color: #8c939d;
}
.upload-pic-ul {
  display: inline-block;
}
.upload-pic-li {
  display: inline-block;
  border-radius: 6px;
  border: 1px solid #c0ccda;
  position: relative;
  width: 148px;
  height: 148px;
  margin-right: 10px;
  cursor: move;
}
.upload-pic-li .el-icon-delete {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #fff;
  border-radius: 6px;
  font-size: 20px;
  transform: translate(-10px, -10px);
  display: none;
  cursor: pointer;
}
.shadow-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: none;
}
.upload-pic-li:hover .shadow-img,
.upload-pic-li:hover .el-icon-delete {
  display: block;
}
.upload-img {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
</style>
