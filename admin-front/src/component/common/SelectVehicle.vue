<template>
  <div class="vehicle-select">
    <el-select
      v-model="currentValue"
      :disabled="disabled"
      :clearable="clearable"
      filterable
      remote
      reserve-keyword
      placeholder="请选择车辆"
      :remote-method="remoteMethod"
      :loading="loading"
      @change="handleChange"
    >
      <el-option v-for="item in optionsList" :key="item.id" :label="item.licensePlate" :value="item.licensePlate">
        <span>{{ item.licensePlate }}</span>
        <span class="vehicle-detail">{{ item.brand }} {{ item.model }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { getAllVehicle } from "@/api/CarSale";

export default {
  name: "SelectVehicle",
  props: {
    value: {
      type: String,
      default: "",
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentValue: this.value,
      optionsList: [],
      loading: false,
      timeout: null,
    };
  },
  watch: {
    value(val) {
      this.currentValue = val;
    },
  },
  created() {
    // 初始加载数据
    this.remoteMethod("");
  },
  methods: {
    async remoteMethod(query) {
      if (this.timeout) {
        clearTimeout(this.timeout);
      }

      this.timeout = setTimeout(async () => {
        this.loading = true;
        try {
          const params = {
            keyword: query,
            page: 1,
            pageSize: 20,
          };

          const res = await getAllVehicle(params);
          if (res.errorcode === 0) {
            this.optionsList = res.data || [];
          } else {
            this.$message.error(res.message || "获取车辆列表失败");
          }
        } catch (error) {
          console.error("获取车辆列表出错", error);
        } finally {
          this.loading = false;
        }
      }, 300);
    },
    handleChange(value) {
      const selectedVehicle = this.optionsList.find((item) => item.licensePlate === value);
      this.$emit("input", value);
      this.$emit(
        "change",
        value,
        this.optionsList.filter((item) => item.licensePlate === value)
      );
      return selectedVehicle;
    },
  },
};
</script>

<style scoped>
.vehicle-select {
  display: inline-block;
  width: 100%;
}
.vehicle-detail {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style>
