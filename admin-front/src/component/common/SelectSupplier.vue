<!--所属商铺-->
<template>
  <div class="dis-inline">
    <el-select
      :value="value"
      :placeholder="placeholder"
      :clearable="clearable"
      filterable
      remote
      :size="size"
      :multiple="multiple"
      :disabled="disabled"
      :remote-method="remoteMethod"
      :loading="loading"
      :style="{ width: width + 'px' }"
      @change="selChange"
      @clear="clearChange"
    >
      <el-option v-for="(item, index) in supplier_list" :key="index" :label="item.title" :value="item.id">
        <span style="float: left">{{ item.title }}</span>
        <span style="float: right; margin-left: 30px">{{ item.inventoryNum }}</span>
      </el-option>
    </el-select>
    <span class="sel-btn" style="margin-right: 15px" @click="openVisible">
      <i class="el-icon-search"></i>
    </span>

    <el-dialog
      title="供应商列表"
      :visible="supplier_show"
      width="60%"
      :modal="modal"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="visibleCancel"
    >
      <div class="search-wrp clearfix">
        <el-input
          v-model="keyword"
          size="small"
          style="width: 260px"
          clearable
          placeholder="供应商名称"
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" type="primary" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </div>

      <el-table :data="supplier_list" size="small" @row-dblclick="dbSelect" @selection-change="customerSel">
        <el-table-column prop="title" label="供应商名称"></el-table-column>
        <el-table-column prop="realName" label="联系人" align="center"></el-table-column>
        <el-table-column prop="mobile" label="联系电话">
          <template slot-scope="scope">
            {{ scope.row.mobile }}
          </template>
        </el-table-column>
        <el-table-column v-if="showInventory" label="库存" align="center">
          <template slot-scope="scope">
            {{ scope.row.inventoryNum }}
          </template>
        </el-table-column>
        <el-table-column label="选择" align="center">
          <template slot-scope="scope">
            <el-button icon="el-icon-check" size="mini" @click="dbSelect(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <FooterPage
        layout="prev, pager, next, jumper"
        :page-size="pre_page"
        :total-page.sync="total"
        :current-page.sync="page"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      ></FooterPage>
    </el-dialog>
  </div>
</template>

<script>
import { getAllSupplier } from "@/api/Purchase";
export default {
  name: "SelectSupplier",
  components: {},
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: [Number, String, Array],
      default: "",
    },
    placeholder: {
      type: String,
      default: "供应商名称",
    },
    size: {
      type: String,
      default: "",
    },
    width: {
      type: [String, Number],
      default: "150",
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    enable: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    modal: {
      type: Boolean,
      default: true,
    },
    params: {
      type: Object,
      default: () => ({ skuId: null, warehouseId: null }),
    },
    showInventory: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      supplier_list: [],
      supplier_show: false,
      loading: false,
      total: 0,
      page: 1,
      pre_page: 10,
      keyword: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    remoteMethod(query) {
      this.loading = true;
      this.pre_page = 999;
      this.keyword = query;
      this.getList();
    },
    clearChange() {
      this.$emit("clear");
    },
    selChange(val) {
      if (!val) return;
      let row = [];
      if (!this.multiple) {
        row = this.supplier_list.filter((item) => item.id === val);
      } else {
        row = this.supplier_list.filter((item) => val.indexOf(item.id) > -1);
      }
      // console.log(row)
      this.$emit("change", val, row);
    },
    async getList() {
      let params = {
        page: this.page,
        pageSize: this.pre_page,
        enableStatus: 5,
        keyword: this.keyword,
      };
      if (this.showInventory) {
        params = {
          ...params,
          ...this.params,
        };
      }
      const data = await getAllSupplier(params);
      this.loading = false;

      this.supplier_list = data.data;
      this.total = data.pageTotal;
      this.$emit("getAllSupplier", data.data);
      if (this.isDefault) {
        this.$emit("default", data.data[0].id, data.data[0]);
      }
    },
    dbSelect(row) {
      this.selChange(row.id);
      this.visibleCancel();
    },
    customerSel(row) {
      this.selChange(row[0].id);
      this.visibleCancel();
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
    visibleCancel() {
      this.supplier_show = false;
    },
    openVisible() {
      this.keyword = "";
      this.supplier_show = true;
      this.sizeChange(10);
    },
  },
};
</script>

<style scoped lang="scss">
.dis-inline {
  position: relative;
  .sel-btn {
    display: block;
    width: 25px;
    line-height: 32px;
    text-align: center;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #c0c4cc;
    font-size: 13px;
  }
}
.search-wrp {
  padding-bottom: 10px;
}
</style>
