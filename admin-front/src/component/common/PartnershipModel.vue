<template>
  <div>
    <el-dialog
      title="合伙人列表"
      :visible.sync="dialogVisible"
      width="60%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="close"
    >
      <div>
        <div style="padding-bottom: 10px" class="clearfix">
          <el-input
            v-model="search_key"
            placeholder="请输入合伙人名称"
            size="small"
            clearable
            style="width: 280px"
            @clear="searchList"
          >
            <el-button slot="append" icon="el-icon-search" @click="searchList"></el-button>
          </el-input>
          <el-button v-if="isShowAdd" class="float_right" size="mini" type="text" @click="searchList">
            【刷新】
          </el-button>
        </div>
        <el-table
          size="mini"
          border
          :data="table_data"
          @row-dblclick="dblclick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
          <el-table-column prop="name" label="合伙企业/人名称" align="center" min-width="160"></el-table-column>
          <el-table-column prop="mobile" label="负责人账号" align="center" min-width="160"></el-table-column>
          <el-table-column prop="mobile" label="负责人手机号" align="center" min-width="120"></el-table-column>
          <el-table-column prop="area" label="合伙人地址" align="center" show-overflow-tooltip min-width="180">
            <template slot-scope="scope">
              {{ scope.row.area.provinceName }}-{{ scope.row.area.cityName }}-{{ scope.row.area.districtName }}-{{
                scope.row.area.address
              }}
            </template>
          </el-table-column>
          <el-table-column label="选择" align="center">
            <template slot-scope="scope">
              <el-button size="mini" icon="el-icon-check" @click="dblclick(scope.row)"></el-button>
            </template>
          </el-table-column>
        </el-table>
        <FooterPage
          :page-size="pre_page"
          :total-page.sync="total"
          :current-page.sync="page"
          @pageChange="pageChange"
          @sizeChage="sizeChange"
        ></FooterPage>
      </div>
      <div v-if="isCheck" style="text-align: center; padding-top: 10px">
        <el-button size="small" type="primary" @click="confirm">确认</el-button>
        <el-button size="small" @click="close">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { partherSearch } from "@/api/Shop";
export default {
  name: "PartnershipModel",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    isShowAdd: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      search_key: "",
      pre_page: 10,
      total: 0,
      page: 1,
      table_data: [],
      choose_data: [],
    };
  },
  created() {
    this.searchList();
  },
  methods: {
    async searchList() {
      const params = {
        page: this.page,
        pageSize: this.pre_page,
        name: this.search_key,
      };
      const data = await partherSearch(params);

      this.table_data = data.data;
      this.total = data.pageTotal;
    },
    dblclick(row) {
      this.close();
      this.$emit("confirm", [row]);
    },
    pageChange(val) {
      this.page = val;
      this.searchList();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.searchList();
    },
    // 关闭弹框
    close() {
      this.$emit("close");
    },
    confirm() {
      this.$emit("confirm", this.choose_data);
      this.close();
    },
    handleSelectionChange(val) {
      this.choose_data = val;
    },
  },
};
</script>

<style scoped></style>
