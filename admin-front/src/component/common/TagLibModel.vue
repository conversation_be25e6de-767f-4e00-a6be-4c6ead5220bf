<template>
  <el-dialog :title="title" :modal="true" :visible="isShow" width="70%" @close="cancel">
    <div v-for="(item, index) in tagLibs" :key="index">
      <div class="detail-tab-item">
        <div class="detail-tab-title clearfix">
          <span class="float_left">{{ item.name }}</span>
        </div>
        <span class="detail-tab-main">
          <el-checkbox
            v-for="(tag, index1) in item.children"
            :key="new Date().getTime() + index + index1"
            :label="tag.name"
            :checked="tag.checked"
            @change="(val) => checkChange(val, index, index1)"
          ></el-checkbox>
        </span>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getAllCustomerTagLib } from "@/api/Customer";

export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    taglibIds: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      title: "选择标签",
      tagLibs: [],
    };
  },
  watch: {
    taglibIds: function (val) {
      this.tagLibs.forEach((item) => {
        item.children.forEach((tag) => {
          tag.checked = val.includes(tag.id);
        });
      });
    },
  },
  async created() {
    await this.getAllCustomerTagLib();
  },
  methods: {
    cancel() {
      this.$emit("cancel");
    },
    async getAllCustomerTagLib() {
      const { data } = await getAllCustomerTagLib();
      this.tagLibs = data;
    },
    checkChange(val, index, index1) {
      this.tagLibs[index].children[index1].checked = val;
    },
    confirm() {
      const checkeds = [];
      this.tagLibs.forEach((item) => {
        item.children.forEach((tag) => {
          if (tag.checked) {
            checkeds.push(tag);
          }
        });
      });
      this.$emit("confirm", checkeds);
      this.reset();
      this.cancel();
    },
    reset() {
      this.tagLibs.forEach((item) => {
        item.children.forEach((tag) => {
          tag.checked = false;
        });
      });
      this.$forceUpdate();
    },
  },
};
</script>

<style scoped>
.detail-tab-item {
  //padding-bottom: unset;
}
</style>
