<template>
  <!--      :http-request="uploadFileRequest"-->
  <!--  accept="image/jpg,image/png,image/jpeg,image/gif"-->
  <el-upload
    ref="upload"
    :action="action"
    :auto-upload="true"
    accept=".pem"
    :headers="headers"
    :file-list="fileList"
    :on-preview="handlePreview"
    :on-exceed="handleExceed"
    :on-remove="handleRemove"
    :on-success="upSuccess"
    :on-error="upError"
  >
    <el-button size="small">选择上传</el-button>
  </el-upload>
</template>

<script>
import { getEnToken } from "@/utils/accessToken";
import { baseURL } from "@/config/settings";
import { uploadFile } from "@/api/common";
export default {
  props: {
    // { name: 'food2.jpeg', url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100' }
    fileList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      action: baseURL + "/Common/Upload/uploadFile",
    };
  },
  computed: {
    headers() {
      return {
        token: getEnToken(),
        Authorization: this.$store.getters["user/accessToken"],
      };
    },
  },
  methods: {
    //文件超出个数限制时的钩子
    handleExceed(files, fileList) {
      // this.$message.warning(`当前限制上传 ${this.limit} 张图片`);
    },
    // 文件列表移除文件时的钩子
    handleRemove(file, fileList) {
      this.$emit("handleRemove", file, fileList);
    },
    // 点击文件列表中已上传的文件时的钩子
    handlePreview(file) {
      console.log(file);
    },
    upSuccess(res, file, fileList) {
      if (!res.state) {
        this.$message.error(res.data);
        return;
      }

      this.$emit("uploadSuccess", res, file, fileList);
    },
    upError(err, file, fileList) {
      this.$message.error(err);
      this.$emit("upError");
    },
  },
};
</script>

<style scoped></style>
