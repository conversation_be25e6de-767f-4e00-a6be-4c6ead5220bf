<!--选择库区库位列表弹窗-->
<template>
  <el-dialog
    title="选择库区库位"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible="isShow"
    width="70%"
    @close="cancel"
  >
    <el-form size="small" inline>
      <el-form-item>
        <el-select
          v-model="areaId"
          filterable
          style="width: 100%"
          placeholder="请选择所属库区"
          @change="handleAreaChange"
        >
          <el-option v-for="item in area_list" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table
      ref="warehouseTable"
      v-loading="loading"
      :data="tableData"
      size="small"
      @row-dblclick="dbSelect"
      @selection-change="selectionChange"
    >
      <el-table-column prop="id" label="ID" min-width="140px" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="code" label="库位编码" min-width="140px"></el-table-column>
      <el-table-column prop="name" label="库位名称" min-width="140px"></el-table-column>
      <el-table-column prop="areaName" label="库区名称" min-width="140px"></el-table-column>
      <el-table-column label="选择">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
  </el-dialog>
</template>

<script>
import { getAllReservoir, getAllStorageLocation } from "@/api/Stock";
import FooterPage from "@/component/common/FooterPage";
export default {
  name: "ClientListModal",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    warehouseId: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      tableData: [],
      choose_data: [],
      pre_page: 10,
      page: 1,
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      areaId: "",
      area_list: [],
    };
  },
  created() {
    this.getAllStorageLocation();
    this.getAllReservoir();
  },
  methods: {
    async getAllStorageLocation() {
      const { data, pageTotal } = await getAllStorageLocation({
        page: this.page,
        pageSize: this.pre_page,
        warehouseId: this.warehouseId,
        areaId: this.areaId,
        enableStatus: 5,
      });

      this.tableData = data;
      this.total = pageTotal;

      for (let i = 0; i < data.length; i++) {
        const isTrue = this.choose_data.find((itemF) => {
          return itemF.id === data[i].id;
        });
        if (isTrue) {
          this.$nextTick(() => {
            this.$refs.warehouseTable.toggleRowSelection(data[i], true);
          });
        }
      }
    },
    // 搜索前页数变1
    searchClick() {
      this.page = 1;
      this.getAllStorageLocation();
    },
    selectionChange(val) {
      if (!this.choose_data.length) {
        this.choose_data = val;
      } else {
        this.choose_data = this.$_common.unique(this.choose_data.concat(val), ["id"]);
      }
    },
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getAllStorageLocation();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
    //  请求库区列表
    async getAllReservoir() {
      const data = await getAllReservoir({
        page: 1,
        pageSize: 999,
        warehouseId: this.warehouseId,
      });
      this.area_list = data.data;
    },
    handleAreaChange(val) {
      this.areaId = val;
      console.log(this.areaId);
      this.getAllStorageLocation();
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
