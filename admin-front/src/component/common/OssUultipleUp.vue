<template>
  <!--  阿里云多图上传-->
  <el-upload
    ref="upload"
    class="upload-pic"
    action="#"
    :auto-upload="true"
    :limit="50"
    accept="image/jpg,image/png,image/jpeg,image/gif"
    :file-list="fileList"
    list-type="text"
    :on-preview="picCardPreview"
    :before-upload="beforePicUpload"
    :on-exceed="handleExceed"
    :on-remove="removePic"
    :http-request="uploadQiniu"
    :multiple="true"
  >
    <slot name="upbtn">
      <i class="el-icon-plus"></i>
    </slot>
  </el-upload>
</template>

<script>
import OSS from "ali-oss";
import { aliUpSetting } from "@/config/settings.js";
export default {
  name: "QiniuMultipleUp",
  data() {
    return {
      dialogImageUrl: "",
      dialogVisible: false,
      fileList: [],
      token: "",
      limit: 50,
    };
  },
  methods: {
    picCardPreview(file) {
      // 上传图预览
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    beforePicUpload(file) {
      // 图片校验
      const limitPic =
        file.type === "image/png" ||
        file.type === "image/jpg" ||
        file.type === "image/jpeg" ||
        file.type === "image/gif";
      if (!limitPic) {
        this.$notify.warning({
          title: "警告",
          message: "请上传格式为image/png,image/jpg,image/jpeg,image/gif的图片",
        });
      }
      const limitSize = file.size / 1024 / 1024 / 5 < 5;
      if (!limitSize) {
        this.$notify.warning({
          title: "警告",
          message: "图片大小必须小于5M",
        });
      }
      return limitPic && limitSize;
    },
    removePic(file, fileList) {
      // 移除图片
      this.fileList = fileList;
    },
    handleExceed(files, fileList) {
      // 文件超出个数限制
      this.$message.warning(`当前限制上传 ${this.limit} 张图片`);
    },
    uploadQiniu(request) {
      // 上传阿里云
      const Loading = this.$baseColorfullLoading(2, "图片正在上传...");
      this.handleUpload(request)
        .then((result) => {
          console.log(result);
          Loading.close();
          if (!result.name) {
            this.$message.error({
              message: "图片上传失败,请重新上传",
              duration: 2000,
            });
          } else {
            this.$refs.upload.clearFiles();
            this.$emit("uploadSuccess", result.name, result.url);
          }
        })
        .catch((err) => {
          Loading.close();
          this.$message.error({
            message: `图片上传失败${err}`,
            duration: 2000,
          });
        });
    },
    handleUpload(request) {
      //oss 基本配置
      const client = new OSS(aliUpSetting);
      const promise = new Promise((resolve, reject) => {
        const file = request.file;
        let name = request.file.name;
        client
          .put(name, file)
          .then((res) => {
            const data = {
              ...res.res,
              name: res.name,
              url: res.url,
            };
            if (data.status === 200) {
              resolve(data);
            } else {
              reject(data);
            }
          })
          .catch((err) => {
            this.$message.error({
              message: `上传失败[${err.status}]`,
              duration: 2000,
            });
          });
      });
      return promise;
    },
  },
};
</script>
