<!--员工列表弹窗-->
<template>
  <el-dialog
    :modal="modal"
    title="员工列表"
    :visible="isShow"
    width="50%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <div class="search-wrp">
      <el-input
        v-model="keywords"
        size="small"
        style="width: 240px"
        clearable
        placeholder="请输入名称/手机号"
        @clear="searchClick"
      >
        <el-button slot="append" type="primary" icon="el-icon-search" @click="searchClick"></el-button>
      </el-input>
      <DepartmentSel
        v-model="departmentPidPath"
        style="width: 240px; display: inline-block; margin-left: 10px"
        clearable
        @change="departmentChange"
      />
    </div>
    <el-table
      ref="staffRef"
      v-loading="loading"
      :data="tabelData"
      @row-dblclick="dbSelect"
      @selection-change="handleSelectionChange"
    >
      <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
      <el-table-column prop="staffName" label="姓名"></el-table-column>
      <el-table-column prop="departmentName" label="部门"></el-table-column>
      <el-table-column prop="mobile" label="手机号"></el-table-column>
      <el-table-column label="选择" width="100">
        <template slot-scope="scope">
          <el-button size="mini" icon="el-icon-check" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="isCheck" slot="btn-div">
        <el-button size="small" type="primary" @click="confirm">确认</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import DepartmentSel from "@/component/common/DepartmentSel";
import { getAllStaff } from "@/api/Department";
import { updateReceivedStatus } from "@/api/Finance";
export default {
  name: "ClientListModal",
  components: {
    FooterPage,
    DepartmentSel,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    isserch: {
      type: Boolean,
      default: true,
    },
    modal: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      search_data: [],
      choose_data: [],
      tabelData: [],
      pre_page: 10,
      page: 1,
      // modalShow: this.clientModalShow,
      selectedIndex: null,
      keywords: "",
      departmentPidPath: [],
      departmentId: "",
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      selectedClient: {},
      row_id: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      const { data, pageTotal } = await getAllStaff({
        page: this.page,
        pageSize: this.pre_page,
        deleteStatus: 5,
        keyword: this.keywords,
        departmentId: this.departmentId,
      });
      this.loading = false;

      for (let i = 0; i < data.length; i++) {
        const isTrue = this.choose_data.find((itemF) => {
          return itemF.id === data[i].id;
        });
        if (isTrue) {
          this.$nextTick(() => {
            data[i] = isTrue;
            this.$refs.staffRef.toggleRowSelection(data[i], true);
          });
        }
      }

      this.tabelData = data;
      this.total = pageTotal;
    },
    // 搜索前页数变1
    searchClick() {
      this.page = 1;
      this.getList();
    },
    // 双击选择
    dbSelect(row) {
      if (this.isserch) {
        this.$emit("confirm", [{ ...row }]);
        this.cancel();
      }
      this.row_id = row;
    },
    // 选择部门
    departmentChange(val) {
      this.departmentId = val[val.length - 1];
      this.departmentPidPath = val.join(",");
      this.pageChange(1);
    },
    confirm() {
      this.$emit("confirm", this.choose_data, [{ ...this.row_id }]);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
    handleSelectionChange(val) {
      if (!this.choose_data.length) {
        this.choose_data = val;
      } else {
        this.choose_data = this.$_common.unique(this.choose_data.concat(val), ["id"]);
      }
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
