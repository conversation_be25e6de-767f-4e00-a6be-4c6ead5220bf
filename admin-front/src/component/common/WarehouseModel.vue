<!--选择仓库列表弹窗-->
<template>
  <el-dialog
    title="选择仓库"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible="isShow"
    width="70%"
    @close="cancel"
  >
    <el-table
      ref="warehouseTable"
      v-loading="loading"
      :data="tableData"
      size="small"
      @row-dblclick="dbSelect"
      @selection-change="selectionChange"
    >
      <el-table-column v-if="isCheck" type="selection" width="55" align="center"></el-table-column>
      <el-table-column prop="warehouseCode" show-overflow-tooltip min-width="140" label="仓库编码"></el-table-column>
      <el-table-column prop="warehouseName" min-width="130" show-overflow-tooltip label="仓库名称"></el-table-column>
      <el-table-column prop="contactName" label="联系人" min-width="100"></el-table-column>
      <el-table-column prop="contactMobile" min-width="100" label="联系方式"></el-table-column>
      <el-table-column prop="address" min-width="160" label="仓库地址" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.contactAddress }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.enableStatus === 5" class="success-status"> 启用 </span>
          <span v-else class="danger-status">禁用</span>
        </template>
      </el-table-column>
      <el-table-column label="选择">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="isCheck" slot="btn-div">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { getAllWarehouse } from "@/api/Stock";
export default {
  name: "ClientListModal",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    notId: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      tableData: [],
      choose_data: [],
      searchForm: {},
      pre_page: 10,
      page: 1,
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      const { data, pageTotal } = await getAllWarehouse({
        page: this.page,
        pageSize: this.pre_page,
        notId: this.notId,
        enableStatus: 5,
      });

      this.tableData = data;
      this.total = pageTotal;

      for (let i = 0; i < data.length; i++) {
        const isTrue = this.choose_data.find((itemF) => {
          return itemF.id === data[i].id;
        });
        if (isTrue) {
          this.$nextTick(() => {
            this.$refs.warehouseTable.toggleRowSelection(data[i], true);
          });
        }
      }
    },
    // 搜索前页数变1
    searchClick() {
      this.page = 1;
      this.getList();
    },
    selectionChange(val) {
      if (!this.choose_data.length) {
        this.choose_data = val;
      } else {
        this.choose_data = this.$_common.unique(this.choose_data.concat(val), ["id"]);
      }
    },
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
