<template>
  <el-cascader
    :size="size"
    :disabled="disabled"
    :clearable="clearable"
    :filterable="filterable"
    :placeholder="placeholder"
    :value="value"
    :options="areaData"
    :props="props"
    style="width: 150px"
    @expand-change="expandChange"
    @change="change"
    @clear="clear"
  >
    <div slot-scope="scope" @click="clickChange">
      {{ scope.data.label }}
    </div>
  </el-cascader>
</template>

<script>
import Region from "@/assets/area.json";
import { getAllProvince, getAllCityByProvinceCode, getAllAreaByCityCode } from "@/api/common";
export default {
  name: "RegionSelectJSON",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: Array,
      default: () => {
        return [];
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "请选择所属区域",
    },
    props: {
      type: Object,
      default: () => {
        return {
          label: "label",
          value: "value",
        };
      },
    },
    size: {
      type: String,
      default: "small",
    },
    checkStrictly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      areaData: Region,
      province_list: [],
      city_list: [],
      district_list: [],
      province_code: "",
      city_code: "",
    };
  },
  watch: {
    value(val) {
      console.log("watch", val);
      this.getRegion();
    },
  },
  async created() {
    this.props.checkStrictly = this.checkStrictly;
    // await this.getAllProvince()
    if (this.value.length) {
      console.log("created", this.value);
      await this.getRegion();
    }
  },
  methods: {
    // 自定义选择即改变
    clickChange(event) {
      event.target.parentElement.parentElement.firstChild.click();
    },
    // 获取省市区名称
    async getRegion() {
      if (this.value[0]) {
        let city = {};
        let district = {};
        const province = this.areaData.find((item) => item.value === parseInt(this.value[0]));
        if (province.children) {
          city = province.children.find((item) => item.value === parseInt(this.value[1]));
        }
        if (city && city.children) {
          district = city.children.find((item) => item.value === parseInt(this.value[2]));
        }

        this.$emit("getRegion", province, city, district);
      }
    },
    // 获取省列表
    async getAllProvince() {
      const { data } = await getAllProvince();

      this.province_list = data;
      this.province_code = data[0].code;

      const areaData = this.province_list.map((item) => {
        return {
          ...item,
          children: [],
        };
      });
      this.areaData = areaData;
    },
    // 获取市列表
    async getAllCityByProvinceCode(code) {
      const { data } = await getAllCityByProvinceCode(code);

      const index = this.areaData.findIndex((item) => item.code === code);
      this.areaData[index].children = data.map((item) => {
        return {
          ...item,
          children: [],
        };
      });
    },
    // 获取区列表
    async getAllAreaByCityCode(code, pcode) {
      const { data } = await getAllAreaByCityCode(code);

      const index = this.areaData.findIndex((item) => item.code === pcode);
      const indexT = this.areaData[index].children.findIndex((item) => item.code === code);
      this.areaData[index].children[indexT].children = data;
    },
    expandChange(val) {
      if (val[1]) {
        // this.getAllAreaByCityCode(val[1], val[0])
      } else if (val[0]) {
        // this.getAllCityByProvinceCode(val[0])
        // await this.getAllAreaByCityCode(this.value[1], this.value[0])
      }
    },
    change(val) {
      const params = [];
      if (val[0]) {
        params[0] = this.areaData.find((item) => item.value === val[0]);
      }
      if (val[1]) {
        const provinceData = this.areaData.find((item) => item.value === val[0]);
        params[1] = provinceData.children.find((item) => item.value === val[1]);
      }
      if (val[2]) {
        const provinceData = this.areaData.find((item) => item.value === val[0]);
        const cityData = provinceData.children.find((item) => item.value === val[1]);
        params[2] = cityData.children.find((item) => item.value === val[2]);
      }
      this.$emit("change", val, params);
    },
    clear() {
      this.$emit("clear");
    },
  },
};
</script>

<style scoped></style>
