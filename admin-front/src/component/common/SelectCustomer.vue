<!--客户-->
<template>
  <div class="dis-inline">
    <el-select
      :value="value"
      :placeholder="placeholder"
      :clearable="clearable"
      filterable
      remote
      :size="size"
      :multiple="multiple"
      :disabled="disabled"
      :remote-method="remoteMethod"
      :loading="loading"
      :style="{ width: `${Number(width) + 30}px` }"
      @change="selChange"
      @clear="clearChange"
    >
      <el-option v-for="(item, index) in customer_list" :key="index" :label="item.name" :value="item.name"></el-option>
    </el-select>
    <span class="sel-btn" @click="openVisible">
      <i class="el-icon-search"></i>
    </span>

    <el-dialog
      title="客户列表"
      :visible="customer_show"
      width="60%"
      :modal="modal"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="visibleCancel"
    >
      <el-form size="small" inline>
        <el-form-item>
          <el-input
            v-model="search_form.keyword"
            placeholder="客户名称/手机号"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <!--<el-form-item label="区域">
          <RegionSelect
            v-model="region"
            size="small"
            clearable
            @change="regionChange"
          />
        </el-form-item>-->
        <el-form-item v-if="systemType === 1">
          <SelectShop
            v-model="search_form.shopId"
            :clearable="true"
            placeholder="选择商铺"
            @clear="shopClear"
            @change="selShop"
          />
        </el-form-item>
        <el-form-item>
          <el-select v-model="search_form.salesManId" clearable placeholder="选择业务员" @change="pageChange(1)">
            <el-option
              v-for="(item, index) in options"
              :key="index"
              :label="item.staffName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <RegionSelect
            v-model="region"
            style="width: 200px"
            size="small"
            clearable
            check-strictly
            placeholder="所属区域"
            @change="regionChange"
          />
        </el-form-item>
        <!--<el-form-item label="部门">
          <DepartmentSel v-model="department" clearable @change="selBranch" />
        </el-form-item>-->
      </el-form>
      <el-table :data="customer_list" size="small" @row-dblclick="dbSelect" @selection-change="customerSel">
        <el-table-column prop="name" label="客户名称"></el-table-column>
        <el-table-column prop="mobile" label="联系电话">
          <template slot-scope="scope">
            {{ scope.row.mobile }}
          </template>
        </el-table-column>
        <el-table-column prop="customerType" label="客户类型"></el-table-column>
        <el-table-column prop="shopName" label="所属商铺" show-overflow-tooltip></el-table-column>
        <el-table-column width="100" label="选择">
          <template slot-scope="scope">
            <el-button icon="el-icon-check" size="mini" @click="dbSelect(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <FooterPage
        layout="prev, pager, next, jumper"
        :page-size="pre_page"
        :total-page.sync="total"
        :current-page.sync="page"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      ></FooterPage>
    </el-dialog>
  </div>
</template>

<script>
import { getAllCustomer, searchCustomer, CustomerInitialsSearch } from "@/api/Customer";
import RegionSelect from "@/component/common/RegionSelectJSON";
import DepartmentSel from "@/component/common/DepartmentSel";
import SelectShop from "@/component/goods/SelectShop.vue";
import { mapGetters } from "vuex";
import { getAllStaff } from "@/api/Department";
export default {
  name: "SelectCustomer",
  components: {
    RegionSelect,
    // DepartmentSel,
    SelectShop,
  },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: [Number, String, Array],
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择客户",
    },
    size: {
      type: String,
      default: "",
    },
    width: {
      type: [Number, String],
      default: "150",
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    enable: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    modal: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      batchSetting: {
        region: "",
      },
      options: [],
      saleman: "", //业务员
      UserType: [
        {
          value: "1",
          label: "客户类型",
        },
      ],
      customerLabel: [
        {
          value: "选项1",
          label: "客户标签",
        },
      ],
      customer_list: [],
      customer_show: false,
      loading: false,
      total: 0,
      page: 1,
      pre_page: 10,
      region: [],
      department: [],
      shopId: "",
      search_form: {
        keyword: "",
        provinceCode: "",
        cityCode: "",
        districtCode: "",
        departmentId: "",
        salesManId: "",
      },
    };
  },
  computed: {
    ...mapGetters({
      systemType: "MUser/systemType",
      storeData: "MUser/storeData",
    }),
  },
  created() {
    this.getList();
    this.getAllStaff();
  },
  mounted() {
    // if (this.systemType === 2) {
    //   this.shopId = this.storeData.id;
    // }
  },
  methods: {
    async getAllStaff(departmentId) {
      const data = await getAllStaff({
        page: 1,
        pageSize: 50,
        departmentId: departmentId,
      });

      this.options = data.data;
    },
    remoteMethod(query) {
      if (query !== "") {
        this.CustomerInitialsSearch(query);
      } else {
        this.getList();
      }
    },
    async CustomerInitialsSearch(condition) {
      const { data } = await CustomerInitialsSearch({
        condition: condition || "",
      });
      this.customer_list = data;
    },
    // 选择区域
    regionChange(val) {
      this.search_form.provinceCode = val[0];
      this.search_form.cityCode = val[1];
      this.search_form.districtCode = val[2];
      this.pageChange(1);
    },
    // 选择商铺
    selShop(val, row) {
      this.pageChange(1);
    },
    shopClear() {
      this.search_form.shopId = "";
      this.pageChange(1);
    },
    // 选择部门
    selBranch(val) {
      this.search_form.departmentId = val[val.length - 1];
      this.pageChange(1);
    },
    clearChange() {
      this.$emit("clear");
    },
    selChange(val) {
      if (!val) return;
      let row = [];
      if (!this.multiple) {
        row = this.customer_list.filter((item) => item.name === val);
      } else {
        row = this.customer_list.filter((item) => val === item.name);
      }
      // console.log(row)
      this.$emit("change", val, row);
    },
    async getList() {
      const params = {
        page: this.page,
        pageSize: this.pre_page,
        enableStatus: 5,
        status: 2,
        shopId: this.search_form.shopId,
        keyword: this.search_form.keyword,
        salesManId: this.search_form.salesManId,
        provinceCode: this.search_form.provinceCode,
        cityCode: this.search_form.cityCode,
        districtCode: this.search_form.districtCode,
      };
      const data = await getAllCustomer(params);
      this.loading = false;
      this.customer_list = data.data;
      this.total = data.pageTotal;
      this.$emit("getAllCustomer", data.data);
      if (this.isDefault) {
        this.$emit("default", data.data[0].id, data.data[0]);
      }
    },
    dbSelect(row) {
      this.selChange(row.name);
      this.visibleCancel();
    },
    customerSel(row) {
      this.selChange(row[0].name);
      this.visibleCancel();
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
    visibleCancel() {
      this.customer_show = false;
    },
    openVisible() {
      if (this.disabled) return;
      this.customer_show = true;
      this.sizeChange(10);
    },
  },
};
</script>

<style>
.dis-inline .el-input--suffix .el-input__inner {
  padding-right: 46px !important;
}
</style>
<style scoped lang="scss">
.dis-inline {
  position: relative;
  .sel-btn {
    display: block;
    width: 25px;
    line-height: 32px;
    text-align: center;
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #c0c4cc;
    font-size: 13px;
    vertical-align: middle;
  }
}
.search-wrp {
  padding-bottom: 10px;
}
</style>
