<!--客户列表弹窗-->
<template>
  <el-dialog
    :modal="modal"
    title="客户列表"
    :visible="isShow"
    width="60%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <div class="search-wrp clearfix">
      <el-input
        v-model="searchForm.keyWord"
        size="small"
        style="width: 300px"
        clearable
        placeholder="客户名称/手机号"
        @keyup.enter.native="pageChange(1)"
        @clear="pageChange(1)"
      >
        <el-button slot="append" type="primary" icon="el-icon-search" @click="pageChange(1)"></el-button>
      </el-input>
      <el-button v-if="isShowAdd" class="float_right btn" type="text" @click="pageChange(1)"> 【刷新】 </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="clientList"
      size="small"
      @row-dblclick="dbSelect"
      @selection-change="selectionChange"
    >
      <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
      <el-table-column prop="name" label="客户名称"></el-table-column>
      <el-table-column prop="mobile" label="联系电话">
        <template slot-scope="scope">
          {{ scope.row.mobile }}
        </template>
      </el-table-column>
      <el-table-column prop="customerType" label="客户类型"></el-table-column>
      <el-table-column prop="shopName" label="所属商铺" show-overflow-tooltip></el-table-column>
      <el-table-column label="选择">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" plain type="primary" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="isCheck" slot="btn-div" class="foot-btn-div">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { getAllCustomer, searchCustomer } from "@/api/Customer";
export default {
  name: "ClientListModal",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isShowAdd: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    modal: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      choose_data: [],
      clientList: [],
      pre_page: 10,
      page: 1,
      // modalShow: this.clientModalShow,
      selectedIndex: null,
      searchForm: {
        keyWord: "",
      },
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      selectedClient: {},
    };
  },
  created() {
    this.pageChange(1);
  },
  methods: {
    async getList() {
      this.loading = true;
      const { data, pageTotal } = await getAllCustomer({
        page: this.page,
        pageSize: this.pre_page,
        enableStatus: 5,
        status: 2,
        keyword: this.searchForm.keyWord,
      });
      this.loading = false;

      this.clientList = data;
      this.total = pageTotal;
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    confirm() {
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
.btn {
  margin-top: -5px;
}
</style>
