<!--部门选择-->
<template>
  <div>
    <el-cascader
      :disabled="disabled"
      :clearable="clearable"
      :filterable="filterable"
      :placeholder="placeholder"
      :value="value"
      :options="Department_data"
      :props="props"
      :style="{ width: width + 'px' }"
      :size="size"
      @expand-change="expandChange"
      @change="change"
    >
      <div slot-scope="scope" @click="clickChange">
        {{ scope.data.departmentName }}
      </div>
    </el-cascader>
    <el-button v-if="isShowAdd" type="text" size="mini" @click="getAllDepartment"> 【刷新】 </el-button>
  </div>
</template>

<script>
import { getAllDepartment } from "@/api/Department";
export default {
  name: "DepartmentSel",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: Array,
      default: () => {
        return [];
      },
    },
    isShowAdd: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "请选择部门",
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [Number, String],
      default: 200,
    },
    size: {
      type: String,
      default: "small",
    },
  },
  data() {
    return {
      Department_data: [],
      props: {
        label: "departmentName",
        value: "id",
        checkStrictly: true,
      },
    };
  },
  created() {
    this.getAllDepartment();
  },
  methods: {
    // 获取部门列表 getAllDepartment
    async getAllDepartment() {
      const data = await getAllDepartment();

      this.Department_data = data.data;
    },
    change(val) {
      this.$emit("change", val);
    },
    expandChange(val) {
      this.$emit("expandChange", val);
    },
  },
};
</script>

<style scoped></style>
