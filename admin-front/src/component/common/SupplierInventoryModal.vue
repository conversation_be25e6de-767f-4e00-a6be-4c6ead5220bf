<!--供应商列表弹窗-->
<template>
  <el-dialog
    :modal="modal"
    :title="title"
    :visible="isShow"
    width="60%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <el-table
      v-loading="loading"
      :data="tableData"
      size="small"
      border
      @row-dblclick="dbSelect"
      @selection-change="selectionChange"
    >
      <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
      <el-table-column prop="supplierName" label="供应商名称" align="center"></el-table-column>
      <el-table-column prop="num" label="库存" align="center">
        <template slot-scope="scope">
          <span>{{ $_common.formatNub(scope.row.num) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="unitName" label="单位" align="center"></el-table-column>
      <el-table-column label="选择" align="center">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" plain type="primary" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <div v-if="isCheck" slot="footer" style="width: 100%; border-top: 1px solid #eee; padding-top: 10px">
      <el-button size="small" type="primary" @click="confirm">确定</el-button>
      <el-button size="small" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getAllInventorySupplier } from "@/api/Stock";
import FooterPage from "@/component/common/FooterPage";
export default {
  name: "SupplierInventoryModal",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    modal: {
      type: Boolean,
      default: false,
    },
    warehouseId: {
      type: Number,
      required: true,
    },
    skuId: {
      type: Number,
      default: 0,
    },
    title: {
      type: String,
      default: "供应商",
    },
  },
  data() {
    return {
      tableData: [],
      choose_data: [],
      searchForm: {},
      pre_page: 10,
      page: 1,
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      const { data, pageTotal } = await getAllInventorySupplier({
        page: this.page,
        pageSize: this.pre_page,
        warehouseId: this.warehouseId,
        skuId: this.skuId,
      });
      this.loading = false;

      this.tableData = data;
      this.total = pageTotal;
    },
    // 搜索前页数变1
    searchClick() {
      this.page = 1;
      this.getList();
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped></style>
