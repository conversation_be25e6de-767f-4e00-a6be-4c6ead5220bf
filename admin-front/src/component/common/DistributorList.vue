<!--账户列表弹窗-->
<template>
  <el-dialog
    title="分销商"
    :visible="isShow"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="60%"
    @close="cancel"
  >
    <el-form :inline="true" style="margin-bottom: 0" size="small">
      <el-form-item>
        <el-input
          v-model="search"
          placeholder="手机号/姓名"
          class="input-with-select"
          style="width: 200px"
          clearable
          @clear="pageChange(1)"
          @keyup.enter.native="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
    </el-form>
    <vxe-table ref="xTable1" border="inner" :data="tableData" @radio-change="radioChangeEvent">
      <vxe-table-column min-width="100" field="name" title="姓名"></vxe-table-column>
      <vxe-table-column min-width="100" field="mobile" title="手机号"></vxe-table-column>
      <vxe-table-column field="gradeName" min-width="100" title="等级"></vxe-table-column>
      <vxe-table-column field="totalMoney" title="累计佣金" min-width="100">
        <template #default="{ row }">
          {{ $_common.formattedNumber(row.totalMoney) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="waitMoney" title="提现佣金" min-width="100">
        <template #default="{ row }">
          {{ $_common.formattedNumber(row.waitMoney) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="beginMoney" title="成为分销商时间" min-width="150">
        <template #default="{ row }">
          {{ $_common.formatDate(row.createTime) }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="subTotal" title="下线总数" min-width="100">
        <template #default="{ row }">
          {{ row.statistics ? row.statistics.sub || 0 : 0 }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="subBusinessman" title="下级分销商总数" min-width="100">
        <template #default="{ row }">
          {{ row.statistics ? row.statistics.subBusinessman || 0 : 0 }}
        </template>
      </vxe-table-column>
      <vxe-table-column title="选择" width="90">
        <template #default="{ row }">
          <el-button icon="el-icon-check" size="mini" @click="dbSelect(row)"></el-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div slot="btn-div">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>

<script>
import { getAllBusinessman } from "@/api/Commission";
import FooterPage from "@/component/common/FooterPage";
export default {
  name: "AccountType",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      page: 1,
      pre_page: 10,
      total: 0,
      choose_data: [],
      tableData: [],
      search: "",
    };
  },
  created() {
    this.getAllBusinessman();
  },
  methods: {
    radioChangeEvent({ row }) {
      this.choose_data = row;
    },
    //  获取列表
    async getAllBusinessman() {
      let params = {
        page: this.page,
        pageSize: this.pre_page,
        search: this.search,
      };
      const { data, pageTotal } = await getAllBusinessman(params);
      this.tableData = data;
      this.total = pageTotal;
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    confirm() {
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getAllBusinessman();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
