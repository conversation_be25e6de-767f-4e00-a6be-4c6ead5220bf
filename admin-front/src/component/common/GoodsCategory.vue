<!--商品分类选择-->
<template>
  <div>
    <el-cascader
      v-if="isType === 'select'"
      :disabled="disabled"
      :clearable="clearable"
      :filterable="filterable"
      :placeholder="placeholder"
      :value="value"
      :options="categoryData"
      :props="props"
      :style="{ width: width + 'px' }"
      :size="size"
      @expand-change="expandChange"
      @visible-change="visibleChange"
      @change="change"
    >
      <div slot-scope="scope" @click="clickChange">
        {{ scope.data.title }}
      </div>
    </el-cascader>
    <el-button v-if="isShowAdd" size="mini" type="text" @click="getList"> 【刷新】 </el-button>
    <el-cascader-panel
      v-if="isType === 'panel'"
      :value="value"
      :options="categoryData"
      :props="props"
      :style="{ width: width + 'px' }"
      :size="size"
      @expand-change="expandChange"
      @visible-change="visibleChange"
      @change="change"
    >
      <div slot-scope="scope" @click="clickChange">
        {{ scope.data.title }}
      </div>
    </el-cascader-panel>
    <el-tree
      v-if="isType === 'tree'"
      ref="tree"
      :data="categoryData"
      show-checkbox
      node-key="id"
      :props="defaultProps"
      @check="checkChange"
    ></el-tree>
  </div>
</template>

<script>
import { getAllCategoryPost } from "@/api/goods";
export default {
  name: "GoodsCategory",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    categoryPath: {
      type: Array,
      default: () => {
        return [];
      },
    },
    num: {
      type: Number,
      default: 1,
    },
    value: {
      type: Array,
      default: () => {
        return [];
      },
    },
    checkedKeys: {
      type: Array,
      default: () => {
        return [];
      },
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    isShowAdd: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isType: {
      type: String,
      default: "select",
    },
    placeholder: {
      type: String,
      default: "请选择商品分类",
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    checkStrictly: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [Number, String],
      default: 200,
    },
    size: {
      type: String,
      default: "small",
    },
  },
  data() {
    return {
      add_show: false,
      categoryData: [],
      props: {
        label: "title",
        value: "id",
      },
      defaultProps: {
        children: "children",
        label: "title",
      },
    };
  },
  async created() {
    this.props.checkStrictly = this.checkStrictly;
    this.props.multiple = this.multiple;
    await this.getList();
    if (this.isType === "tree") {
      this.$refs.tree.setCheckedKeys(this.checkedKeys);
    }
  },
  methods: {
    del() {
      if (this.categoryPath.length > 1) {
        this.categoryPath.splice(this.num, 1);
      } else {
        this.$message.warning("至少保留一条商品");
      }
      // this.$emit("confirm", this.categoryPath);
    },
    // 自定义选择即改变
    clickChange(event) {
      event.target.parentElement.parentElement.firstChild.click();
    },
    // 商品分类列表
    async getList() {
      const { data } = await getAllCategoryPost({
        enableStatus: 5,
      });

      if (JSON.stringify(data) === "{}") {
        this.categoryData = [];
      } else {
        this.categoryData = data;
      }
    },
    checkChange(data, checkedKeysObj) {
      this.$emit("change", data, checkedKeysObj);
    },
    change(val) {
      let idpath = [];
      if (!this.multiple) {
        idpath = val;
      } else {
        idpath = val[0];
      }
      let cateArr = [];
      let cateOne = {};
      let catetwo = {};
      let catethree = {};
      let catefour = {};
      if (idpath[0]) {
        cateOne = this.categoryData.find((item) => item.id === idpath[0]);
        cateArr.push(cateOne);
      }
      if (idpath[1]) {
        catetwo = cateOne.children.find((item) => item.id === idpath[1]);
        cateArr.push(catetwo);
      }
      if (idpath[2]) {
        catethree = catetwo.children.find((item) => item.id === idpath[2]);
        cateArr.push(catethree);
      }
      if (idpath[3]) {
        catefour = catethree.children.find((item) => item.id === idpath[3]);
        cateArr.push(catefour);
      }
      this.$emit("change", val, cateArr);
    },
    expandChange(val) {
      this.$emit("expandChange", val);
    },
    visibleChange(val) {
      this.$emit("visibleChange", val);
    },
  },
};
</script>

<style scoped></style>
