<template>
  <div>
    <vxe-modal :value="show" title="批次录入" width="60%" @close="cancel">
      <template #default>
        <div class="goods-name">
          <span class="label">仓库名称</span>
          <span class="value">{{ goodsData.warehouseName }}</span>
        </div>
        <div v-if="goodsData.supplierName" class="goods-name">
          <span class="label">供应商</span>
          <span class="value">{{ goodsData.supplierName }}</span>
        </div>
        <div class="goods-name">
          <span class="label">商品名称</span>
          <span class="value">
            {{ goodsData.materielName }}
          </span>
        </div>
        <div class="goods-name">
          <span class="label">可售库存</span>
          <span class="value">
            {{ $_common.formatNub(goodsData.inventoryNum) }}
          </span>
        </div>
        <vxe-table
          stripe
          show-overflow
          max-height="500"
          :edit-config="{ trigger: 'click', mode: 'cell' }"
          :data="goods_list"
          @edit-actived="editChange"
        >
          <vxe-table-column field="outAreaName" title="调出库区" min-width="200"></vxe-table-column>
          <vxe-table-column field="outStorageLocationName" title="调出库位名称" min-width="200">
            <template #default="{ row, rowIndex }">
              <el-input v-model="row.outStorageLocationName" placeholder="调出库位">
                <i slot="suffix" class="el-input__icon el-icon-search" @click="foldShow(row, rowIndex)"></i>
              </el-input>
            </template>
          </vxe-table-column>
          <vxe-table-column field="num" title="库位数量" min-width="200"></vxe-table-column>
          <vxe-table-column
            field="bringNum"
            title="调出数量"
            min-width="200"
            :edit-render="{
              name: '$input',
              placeholder: '请输入调出数量',
              props: {
                type: 'float',
                digits: 2,
                min: 0,
                max: num,
              },
            }"
          ></vxe-table-column>
          <vxe-table-column width="120" title="操作">
            <template #default="{ row, rowIndex }">
              <el-button type="text" size="mini" :disabled="goods_list.length === 1" @click="delData(row, rowIndex)">
                删除
              </el-button>
              <el-button type="text" size="mini" @click="addListGoods"> 新增 </el-button>
            </template>
          </vxe-table-column>
        </vxe-table>
        <div class="float_right" style="margin-top: 20px">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </div>
      </template>
    </vxe-modal>
    <!--    选择调出库位-->
    <CallInLocation
      v-if="fold_show"
      :is-check="false"
      :is-show="fold_show"
      :warehouse-id="warehouseId"
      :sku-id="goodsData.skuId"
      :supplier-id="supplierId"
      @confirm="selFold"
      @cancel="fold_show = false"
    />
  </div>
</template>

<script>
import CallInLocation from "@/component/common/CallInLocation";
const batchItem = {
  outAreaId: "",
  outAreaName: "",
  outAreaCode: "",
  outStorageLocationCode: "",
  outStorageLocationId: "",
  outStorageLocationName: "",
  num: "",
  bringNum: "",
};
export default {
  name: "CalloutStorageLocation",
  components: {
    CallInLocation,
  },
  model: {
    prop: "show",
    event: "change",
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    goodsData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    warehouseId: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      goods_list: [],
      fold_show: false,
      num: "",
      index: "",
      supplierId: "",
      // 行数据
      dataItem: {},
    };
  },
  // created() {
  //   batchItem.skuId = this.goodsData.skuId;
  // },
  mounted() {
    this.dataItem = {
      ...this.goodsData,
      ...batchItem,
    };
    this.goods_list.push(this.$_common.deepClone(this.dataItem));
  },
  methods: {
    change() {},
    cancel() {
      this.$emit("cancel");
    },
    confirm() {
      const arr = this.$_common
        .deepClone(this.goods_list)
        .filter((item) => !!item.outStorageLocationId)
        .map((item) => {
          item.num = item.bringNum;
          delete item.bringNum;
          delete item.id;
          return {
            ...item,
            // productionData: parseInt(new Date(item.productionData).getTime()),
          };
        });
      console.log(arr);
      this.$emit("confirm", arr, this.supplierId);
      this.cancel();
    },
    delData(row, index) {
      this.goods_list.splice(index, 1);
    },
    addListGoods() {
      this.goods_list.push(this.$_common.deepClone(this.dataItem));
    },
    // 改变数量
    // editChange({ row, rowIndex, column }) {
    //   if (column.title === "入库数量") {
    //     this.$set(this.goods_list, rowIndex, {
    //       ...row,
    //       inCostTotal: this.$NP.times(Number(row.inCost), Number(row.inNum)),
    //     });
    //   }
    //   if (column.title !== "生产日期" || !row.productionData) {
    //     return;
    //   }
    //   const goodsItem = this.$_common.deepClone(this.goodsData);
    //   this.$set(this.goods_list, rowIndex, {
    //     ...goodsItem,
    //     inCostTotal: 0,
    //     inNum: "",
    //     otherNum: "",
    //     productionData: row.productionData,
    //   });
    // },
    editChange({ row, rowIndex, column }) {
      this.num = row.num;
    },
    foldShow(row, index) {
      this.index = index;
      this.supplierId = row.supplierId;
      this.fold_show = true;
    },
    selFold(e) {
      if (
        this.goods_list.findIndex((item) => {
          return item.outStorageLocationId === e[0].storageLocationId;
        }) !== -1 &&
        this.goods_list.findIndex((item) => {
          return item.outAreaId === e[0].areaId;
        }) !== -1
      ) {
        this.$message.warning("不能选择同一库区");
        return;
      }
      console.log("e", e);
      this.goods_list[this.index].outStorageLocationName = e[0].storageLocationName;
      this.goods_list[this.index].num = e[0].num;
      this.goods_list[this.index].outAreaId = e[0].areaId;
      this.goods_list[this.index].outAreaName = e[0].areaName;
      this.goods_list[this.index].outAreaCode = e[0].areaCode;
      this.goods_list[this.index].outStorageLocationId = e[0].storageLocationId;
      this.goods_list[this.index].outStorageLocationCode = e[0].storageLocationCode;
    },
  },
};
</script>

<style scoped lang="scss">
.goods-name {
  height: 32px;
  line-height: 32px;
  padding: 0 15px;
  display: inline-block;
  font-size: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #666666;
  margin: 10px 0;
  margin-right: 10px;
  .value {
    color: #2d405e;
    margin-left: 10px;
  }
}
</style>
