<!--秒杀列表弹窗-->
<template>
  <el-dialog
    title="活动列表"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible="isShow"
    width="60%"
    @close="cancel"
  >
    <div class="search-wrp">
      <el-input
        v-model="searchForm.keyWord"
        size="mini"
        style="width: 40%"
        clearable
        placeholder="请输入活动名称"
        @keyup.enter.native="pageChange(1)"
        @clear="pageChange(1)"
      >
        <el-button slot="append" type="primary" icon="el-icon-search" @click="pageChange(1)"></el-button>
      </el-input>
    </div>

    <el-table
      v-loading="loading"
      :data="seckill_list"
      size="small"
      border
      @row-dblclick="dbSelect"
      @selection-change="selectionChange"
    >
      <el-table-column v-if="isCheck" type="selection" width="55" align="center"></el-table-column>
      <el-table-column prop="title" label="活动标题" align="center"></el-table-column>
      <el-table-column label="活动时间" align="center">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.startTime, "yyyy-MM-dd") }}至{{
            $_common.formatDate(scope.row.endTime, "yyyy-MM-dd")
          }}
        </template>
      </el-table-column>
      <el-table-column label="客户类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.customerRange === 5 ? "全部客户" : "部分客户" }}
        </template>
      </el-table-column>
      <el-table-column label="选择" align="center">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" plain type="primary" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <div v-if="isCheck" slot="footer" style="width: 100%; border-top: 1px solid #eee; padding-top: 10px">
      <el-button size="small" type="primary" @click="confirm">确定</el-button>
      <el-button size="small" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { searchActivity } from "@/api/Market";
export default {
  name: "SeckillList",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      choose_data: [],
      seckill_list: [],
      pre_page: 10,
      page: 1,
      // modalShow: this.clientModalShow,
      selectedIndex: null,
      searchForm: {
        keyWord: "",
      },
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      selectedClient: {},
    };
  },
  created() {
    this.pageChange(1);
  },
  methods: {
    //  客户搜索
    async searchCustomer() {
      const { data, pageTotal } = await searchActivity({
        page: this.page,
        pageSize: this.pre_page,
        enableStatus: 5,
        title: this.searchForm.keyWord,
        activityType: 20,
        auditStatus: 2,
      });
      this.loading = false;

      this.seckill_list = data;
      this.total = pageTotal;
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    confirm() {
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.searchCustomer();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
