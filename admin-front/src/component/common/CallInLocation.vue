<!--选择库区库位列表弹窗-->
<template>
  <el-dialog
    title="库区库位"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible="isShow"
    width="50%"
    @close="cancel"
  >
    <!--    <el-form size="small" inline>-->
    <!--      <el-form-item>-->
    <!--        <el-select-->
    <!--          v-model="areaId"-->
    <!--          filterable-->
    <!--          style="width: 100%"-->
    <!--          placeholder="请选择所属库区"-->
    <!--        >-->
    <!--          <el-option-->
    <!--            v-for="item in area_list"-->
    <!--            :key="item.id"-->
    <!--            :label="item.name"-->
    <!--            :value="item.id"-->
    <!--          ></el-option>-->
    <!--        </el-select>-->
    <!--      </el-form-item>-->
    <!--    </el-form>-->
    <el-table
      ref="warehouseTable"
      v-loading="loading"
      :data="tableData"
      size="small"
      @row-dblclick="dbSelect"
      @selection-change="selectionChange"
    >
      <el-table-column prop="storageLocationId" label="ID" min-width="140px"></el-table-column>
      <el-table-column prop="areaName" label="所属库区" min-width="140px"></el-table-column>
      <el-table-column prop="storageLocationName" label="所属库位" min-width="140px"></el-table-column>
      <el-table-column prop="num" label="商品数量" min-width="140px"></el-table-column>
      <el-table-column label="选择">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--    <FooterPage-->
    <!--      :page-size="pre_page"-->
    <!--      :total-page.sync="total"-->
    <!--      :current-page.sync="page"-->
    <!--      @pageChange="pageChange"-->
    <!--      @sizeChange="sizeChange"-->
    <!--    ></FooterPage>-->
  </el-dialog>
</template>

<script>
import { getAreaDateBySkuId, getSupplierAreaDateBySkuId } from "@/api/Stock";
export default {
  name: "ClientListModal",
  components: {
    // FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    warehouseId: {
      type: [Number, String],
      default: 0,
    },
    skuId: {
      type: [Number, String],
      default: 0,
    },
    materielId: {
      type: [Number, String],
      default: 0,
    },
    supplierId: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      tableData: [],
      choose_data: [],
      pre_page: 10,
      page: 1,
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      areaId: "",
      area_list: [],
    };
  },
  created() {
    this.getAreaDateBySkuId();
    // this.getAllReservoir();
  },
  methods: {
    async getAreaDateBySkuId() {
      const params = {
        warehouseId: this.warehouseId,
        skuId: this.skuId,
        basicGoodsId: this.materielId,
      };
      if (this.supplierId) {
        params.supplierId = this.supplierId;
        const { data } = await getSupplierAreaDateBySkuId(params);
        if (data.length) {
          this.tableData = data[0].areaDate;
        }
      } else {
        const { data } = await getAreaDateBySkuId(params);
        if (data.length) {
          this.tableData = data[0].areaDate;
        }
      }
    },
    // 搜索前页数变1
    searchClick() {
      this.page = 1;
      this.getAreaDateBySkuId();
    },
    selectionChange(val) {
      if (!this.choose_data.length) {
        this.choose_data = val;
      } else {
        this.choose_data = this.$_common.unique(this.choose_data.concat(val), ["id"]);
      }
    },
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getAreaDateBySkuId();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
    //  请求库区列表
    // async getAllReservoir() {
    //   const data = await getAllReservoir({
    //     page: 1,
    //     pageSize: 999,
    //     warehouseId: this.warehouseId,
    //   });
    //   this.area_list = data.data;
    // },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
