<!--选择仓库列表弹窗-->
<template>
  <el-dialog title="选择商户" :visible="isShow" width="70%" @close="cancelMerchant">
    <div style="padding-bottom: 10px">
      <el-input
        v-model="keyword"
        placeholder="请输入商户名称"
        size="small"
        style="width: 280px"
        clearable
        @clear="pageChange(1)"
      >
        <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
      </el-input>
    </div>
    <el-table
      ref="mearchTable"
      v-loading="loading"
      :data="tableData"
      size="small"
      @row-dblclick="confirmSelect"
      @selection-change="selectionChange"
    >
      <!--      <el-table-column-->
      <!--        v-if="isCheck"-->
      <!--        type="selection"-->
      <!--        width="55"-->
      <!--        align="center"-->
      <!--      ></el-table-column>-->
      <el-table-column prop="name" show-overflow-tooltip min-width="140" label="商户名称"></el-table-column>
      <el-table-column prop="contactName" label="联系人" min-width="100"></el-table-column>
      <el-table-column prop="contactMobile" min-width="100" label="联系方式"></el-table-column>
      <el-table-column label="选择">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" @click="confirmSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="isCheck" slot="btn-div">
        <el-button size="small" type="primary" @click="confirmMerchant"> 确定 </el-button>
        <el-button size="small" @click="cancelMerchant">取消</el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { getAllWarehouse } from "@/api/Stock";
import { getAllMerchant } from "@/api/Merchants";
export default {
  name: "ClientListModal",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    notId: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      tableData: [],
      Merchant_data: [],
      searchForm: {},
      pre_page: 10,
      page: 1,
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      keyword: "",
    };
  },
  created() {
    this.getAllMerchant();
  },
  methods: {
    async getAllMerchant() {
      const res = await getAllMerchant({
        deleteStatus: 5,
        auditStatus: 2,
        enabledStatus: 5,
        page: this.page,
        pageSize: this.pre_page,
        search: this.keyword,
      });
      this.tableData = res.data;
      this.total = res.pageTotal;
      for (let i = 0; i < res.data.length; i++) {
        const isTrue = this.Merchant_data.find((itemF) => {
          return itemF.id === res.data[i].id;
        });
        if (isTrue) {
          this.$nextTick(() => {
            this.$refs.mearchTable.toggleRowSelection(res.data[i], true);
          });
        }
      }
    },
    // 搜索前页数变1
    searchClick() {
      this.page = 1;
      this.getAllMerchant();
    },
    selectionChange(val) {
      if (!this.Merchant_data.length) {
        this.Merchant_data = val;
      } else {
        this.Merchant_data = this.$_common.unique(this.Merchant_data.concat(val), ["id"]);
      }
    },
    confirmMerchant() {
      this.cancelMerchant();
      this.$emit("confirmMerchant", this.Merchant_data);
    },
    // 双击选择
    confirmSelect(row) {
      this.Merchant_data = row;
      this.$emit("confirmMerchant", row);
      // console.log(row);
      this.cancelMerchant();
    },
    // 关闭弹窗
    cancelMerchant() {
      this.$emit("cancelMerchant");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getAllMerchant();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
