<!--
 @pageChange  监控当前页码数据返回导出方法
 @sizeChange  监控当前每页显示数据返回导出方法
 :totalPage    总条数
 :currentPage 当前页码

 调用方式
<footer-page :totalPage.sync="total" :currentPage.sync="cur_page" @pageChange="pageChange" @sizeChange="sizeChange"></footer-page>
-->

<template>
  <div class="footer-page clearfix">
    <div class="float_left" style="padding-top: 20px">
      <slot name="btn-div"></slot>
    </div>
    <div class="float_right" style="padding-right: 20px">
      <el-pagination
        :background="background"
        :small="small"
        :current-page="currentPage"
        :page-sizes="pageNumber"
        :page-size="pageSize"
        :layout="layout"
        :total="totalPage"
        @size-change="sizeChange"
        @current-change="pageChange"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    pageSize: {
      type: Number,
      default: 10,
    },
    totalPage: {
      type: Number,
      default: 0,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    pageNumber: {
      type: Array,
      default: () => {
        return [10, 20, 50, 100, 500];
      },
    },
    layout: {
      type: String,
      default: () => {
        return "total, sizes, prev, pager, next, jumper";
      },
    },
    background: {
      type: Boolean,
      default: false,
    },
    small: {
      type: Boolean,
      default: false,
    },
  },
  // data () {
  //   return {
  //     pre_page: 10
  //   }
  // },
  methods: {
    pageChange(cval) {
      this.$emit("pageChange", cval);
    },
    sizeChange(cval) {
      this.$emit("sizeChange", cval);
    },
  },
};
</script>
<style lang="scss">
.footer-page {
  padding-bottom: 20px;
  .batch-checkbox {
    width: 55px;
    text-align: center;
    display: inline-block;
  }
  .el-pager li {
    min-width: 24px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    padding: 0;
    border-radius: 4px;
    &.active {
      background: #2153d4;
      color: #ffffff;
    }
  }
}
</style>
