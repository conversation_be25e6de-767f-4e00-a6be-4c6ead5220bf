<!--角色选择-->
<template>
  <div>
    <el-cascader
      :disabled="disabled"
      :clearable="clearable"
      :filterable="filterable"
      placeholder="请选择角色"
      :value="value"
      :options="position_data"
      :props="props"
      :style="{ width: width + 'px' }"
      :size="size"
      @expand-change="expandChange"
      @change="change"
    ></el-cascader>
    <el-button v-if="isShowAdd" size="mini" type="text" @click="getAllRole"> 【刷新】 </el-button>
  </div>
</template>

<script>
import { getAllRole } from "@/api/Department";
export default {
  name: "RegionSelect",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: Array,
      default: () => {
        return [];
      },
    },
    isShowAdd: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    checkStrictly: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [Number, String],
      default: 200,
    },
    size: {
      type: String,
      default: "small",
    },
  },
  data() {
    return {
      position_data: [],
      props: {
        label: "roleName",
        value: "id",
      },
    };
  },
  async created() {
    this.props.checkStrictly = this.checkStrictly;
    await this.getAllRole();
  },
  methods: {
    //  获取角色列表
    async getAllRole() {
      const data = await getAllRole();

      this.position_data = data.data;
    },
    change(val) {
      this.$emit("change", val);
    },
    expandChange(val) {
      this.$emit("expandChange", val);
    },
  },
};
</script>

<style scoped></style>
