<template>
  <div>
    <el-dialog
      title="客户列表"
      :visible="customerShow"
      width="60%"
      :modal="modal"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="visibleCancel"
    >
      <el-form size="small" inline>
        <el-form-item>
          <el-input
            v-model="keyword"
            placeholder="客户名称/手机号"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="keyword"
            placeholder="线路名称"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <SelectShop v-model="shopId" :clearable="true" placeholder="选择商铺" @clear="shopClear" @change="selShop" />
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="customer_list" size="small" @selection-change="customerSel">
        <el-table-column ref="warehouseTable" type="selection" width="55" align="center"></el-table-column>
        <el-table-column prop="name" label="客户名称"></el-table-column>
        <el-table-column prop="mobile" label="客户电话">
          <template slot-scope="scope">
            {{ scope.row.mobile }}
          </template>
        </el-table-column>
        <el-table-column label="客户线路"></el-table-column>
        <el-table-column prop="customerType" label="客户类型"></el-table-column>
        <el-table-column prop="shopName" label="所属商铺" show-overflow-tooltip></el-table-column>
        <el-table-column width="100" label="选择">
          <template slot-scope="scope">
            <el-button icon="el-icon-check" size="mini" @click="dbSelect(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <FooterPage
        layout="prev, pager, next, jumper"
        :page-size="pre_page"
        :total-page.sync="total"
        :current-page.sync="page"
        @pageChange="pageChange"
        @sizeChange="sizeChange"
      >
        <div slot="btn-div">
          <el-button size="small" type="primary" @click="confirm"> 确定 </el-button>
          <el-button size="small" @click="visibleCancel">取消</el-button>
        </div>
      </FooterPage>
    </el-dialog>
  </div>
</template>
<script>
import SelectShop from "@/component/goods/SelectShop.vue";
import { getAllCustomer } from "@/api/Customer";
export default {
  components: {
    SelectShop,
  },
  props: {
    customerShow: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      keyword: "",
      shopId: "",
      page: 1,
      pre_page: 10,
      total: 0,
      customer_list: [],
      loading: false,
      choose_data: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
    shopClear() {
      this.shopId = "";
      this.pageChange(1);
    },
    // 选择商铺
    selShop(val, row) {
      this.pageChange(1);
    },
    async getList() {
      const params = {
        page: this.page,
        pageSize: this.pre_page,
        enableStatus: 5,
        status: 2,
        shopId: this.shopId,
        keyword: this.keyword,
      };
      const data = await getAllCustomer(params);
      this.loading = false;
      this.customer_list = data.data;
      this.total = data.pageTotal;
    },
    customerSel(val) {
      this.choose_data = val;
    },
    dbSelect(val) {
      this.choose_data = val;
      this.visibleCancel();
    },
    visibleCancel() {
      this.$emit("close");
    },
    confirm() {
      this.$emit("close");
    },
  },
};
</script>
<style></style>
