<!--供应商列表弹窗-->
<template>
  <el-dialog
    :modal="modal"
    title="供应商"
    :visible="isShow"
    width="60%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <div class="clearfix">
      <div class="search-wrp float_left">
        <el-input
          v-model="searchForm.name"
          size="mini"
          style="width: 100%"
          clearable
          placeholder="请输入供应商名称/联系人"
          @clear="searchClick"
        >
          <el-button slot="append" type="primary" icon="el-icon-search" @click="searchClick"></el-button>
        </el-input>
      </div>
      <el-button
        v-if="isShowAdd"
        class="loading-btn float_right"
        type="text"
        size="mini"
        :loding="loading"
        @click="getList"
      >
        【刷新】
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="tabelData"
      size="small"
      border
      @row-dblclick="dbSelect"
      @selection-change="selectionChange"
    >
      <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
      <el-table-column prop="title" label="供应商名称" align="center"></el-table-column>
      <el-table-column prop="realName" label="联系人" align="center"></el-table-column>
      <el-table-column prop="mobile" label="联系电话" align="center"></el-table-column>
      <el-table-column label="选择" align="center">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" plain type="primary" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <div v-if="isCheck" slot="footer" style="width: 100%; border-top: 1px solid #eee; padding-top: 10px">
      <el-button size="small" type="primary" @click="confirm">确定</el-button>
      <el-button size="small" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { getAllSupplier } from "@/api/Purchase";
export default {
  name: "SupplierModal",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    isShowAdd: {
      type: Boolean,
      default: false,
    },
    modal: {
      type: Boolean,
      default: false,
    },
    enable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tabelData: [],
      choose_data: [],
      searchForm: {},
      pre_page: 10,
      page: 1,
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
      enableStatus: "",
    };
  },
  created() {
    if (this.enable) {
      this.enableStatus = 5;
    } else {
      this.enableStatus = "";
    }
    this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      const { data, pageTotal } = await getAllSupplier({
        page: this.page,
        pageSize: this.pre_page,
        enableStatus: this.enableStatus,
        keyword: this.searchForm.name,
      });
      this.loading = false;

      this.tabelData = data;
      this.total = pageTotal;
    },
    // 搜索前页数变1
    searchClick() {
      this.page = 1;
      this.getList();
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
.loading-btn {
  margin-top: 15px;
}
</style>
