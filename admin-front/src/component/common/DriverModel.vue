<template>
  <el-dialog
    title="选择司机"
    :modal="false"
    :visible="isShow"
    width="70%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <el-table
      v-loading="loading"
      :data="type_data"
      size="small"
      @row-dblclick="dbSelect"
      @selection-change="selectionChange"
    >
      <el-table-column v-if="isCheck" ref="warehouseTable" type="selection" width="55" align="center"></el-table-column>
      <el-table-column label="ID" width="80px" prop="id"></el-table-column>
      <el-table-column prop="driverName" label="司机姓名"></el-table-column>
      <el-table-column prop="phone" label="司机电话"></el-table-column>
      <el-table-column prop="plateNumber" label="车牌号"></el-table-column>
      <!--      <el-table-column prop="state" label="状态">-->
      <!--        <template slot-scope="scope">-->
      <!--          <div>-->
      <!--            <span v-if="scope.row.state === 5" class="success-status">-->
      <!--              启用-->
      <!--            </span>-->
      <!--            <span v-if="scope.row.state === 4" class="danger-status">禁用</span>-->
      <!--          </div>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="选择">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    >
      <div v-if="isCheck" slot="btn-div">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </FooterPage>
  </el-dialog>
</template>
<script>
import { getAllDriver } from "@/api/Customer";
export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      type_data: [],
      pre_page: 10,
      page: 1,
      total: 0,
      choose_data: [],
      loading: false,
    };
  },
  created() {
    this.getAllDriver();
  },
  methods: {
    // 获取司机列表
    async getAllDriver() {
      const { data, pageTotal } = await getAllDriver({
        page: this.page,
        pageSize: this.pre_page,
        state: 5,
      });
      this.type_data = data;
      this.total = pageTotal;
      for (let i = 0; i < data.length; i++) {
        const isTrue = data.forEach((itemF) => {
          return itemF.id === data[i].id;
        });
        if (isTrue) {
          this.$nextTick(() => {
            this.$refs.warehouseTable.toggleRowSelection(this.type_data[i], true);
          });
        }
      }
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
    // 搜索前页数变1
    searchClick() {
      this.page = 1;
      this.getAllDriver();
    },
    selectionChange(val) {
      if (!this.choose_data.length) {
        this.choose_data = val;
      } else {
        this.choose_data = this.$_common.unique(this.choose_data.concat(val), ["id"]);
      }
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
  },
};
</script>
<style></style>
