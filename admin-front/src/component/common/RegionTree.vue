<template>
  <el-tree
    ref="regionTree"
    :data="areaData"
    :props="props"
    node-key="codePath"
    :default-expanded-keys="['0-0-0']"
    show-checkbox
    @check="checkChange"
  ></el-tree>
</template>

<script>
import Region from "@/assets/area.json";
import { getAllProvince, getAllCityByProvinceCode, getAllAreaByCityCode } from "@/api/common";
export default {
  name: "RegionTree",
  props: {
    checkedKey: {
      type: Array,
      default: () => {
        return [];
      },
    },
    alreadyRegion: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      props: {
        label: "label",
        children: "children",
        isLeaf: "leaf",
      },
      areaData: [],
      province_list: [],
      city_list: [],
      district_list: [],
      province_code: "",
      city_code: "",
    };
  },
  watch: {
    checkedKey(Nval) {
      this.$refs.regionTree.setCheckedKeys(Nval);
      this.areaData = this.setDisabled(this.$_common.deepClone(this.areaData), this.alreadyRegion, Nval);
    },
    alreadyRegion(Nval) {
      this.areaData = this.setDisabled(this.$_common.deepClone(this.areaData), Nval);
    },
  },

  created() {
    this.handelData();
  },
  mounted() {
    this.$refs.regionTree.setCheckedKeys(this.checkedKey);
    this.areaData = this.setDisabled(this.$_common.deepClone(this.areaData), this.alreadyRegion, this.checkedKey);
  },
  methods: {
    // 处理数据
    handelData() {
      const areaData = Region.map((item) => {
        return {
          ...item,
          codePath: item.value + "-0-0",
          children: item.children
            ? item.children.map((itemC) => {
                return {
                  ...itemC,
                  codePath: item.value + "-" + itemC.value + "-0",
                  children: itemC.children
                    ? itemC.children.map((itemCC) => {
                        return {
                          ...itemCC,
                          codePath: item.value + "-" + itemC.value + "-" + itemCC.value,
                        };
                      })
                    : [],
                };
              })
            : [],
        };
      });
      this.areaData = [
        {
          id: 0,
          label: "全国",
          children: areaData,
          expand: "true",
          codePath: "0-0-0",
        },
      ];
    },
    // 设置不可选择数据
    setDisabled(list = [], nval = [], wval = []) {
      for (let i in list) {
        let item = list[i];
        item.disabled =
          !!nval.find((itemN) => itemN === item.codePath) && !wval.find((itemN) => itemN === item.codePath);
        if (item.children) {
          this.setDisabled(item.children, nval, wval);
        }
      }
      return list;
    },
    // 获取省列表
    async getAllProvince() {
      const { data } = await getAllProvince();

      this.areaData = data.map((item) => {
        return {
          ...item,
        };
      });
    },
    // 获取市列表
    async getAllCityByProvinceCode(code) {
      const { data } = await getAllCityByProvinceCode(code);

      this.city_list = data.map((item) => {
        return {
          ...item,
        };
      });
    },
    // 获取区列表
    async getAllAreaByCityCode(code) {
      const { data } = await getAllAreaByCityCode(code);

      this.district_list = data.map((item) => {
        return {
          ...item,
        };
      });
    },
    async checkChange(val, valarr) {
      // console.log(valarr);
      this.$emit("checkChange", valarr.checkedNodes);
    },
  },
};
</script>

<style scoped></style>
