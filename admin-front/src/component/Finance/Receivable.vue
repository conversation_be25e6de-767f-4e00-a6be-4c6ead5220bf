<!--应收单弹窗-->
<template>
  <el-dialog
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    title="应收单"
    :visible="isShow"
    width="60%"
    @close="cancel"
  >
    <el-table
      v-loading="loading"
      :data="tableData"
      size="small"
      @row-dblclick="dbSelect"
      @selection-change="selectionChange"
    >
      <el-table-column v-if="isCheck" type="selection" width="55"></el-table-column>
      <el-table-column prop="no" label="单据编号" align="center" min-width="180"></el-table-column>
      <el-table-column prop="customerName" label="客户名称" align="center" min-width="200"></el-table-column>
      <el-table-column prop="financeType" label="应收类型" align="center" min-width="200"></el-table-column>
      <el-table-column prop="receiveMoney" label="实际应收金额" align="center" min-width="150">
        <template slot-scope="scope">
          {{ $_common.formattedNumber(scope.row.receiveMoney) }}
        </template>
      </el-table-column>
      <el-table-column prop="shopName" label="商铺" align="center" min-width="150"></el-table-column>
      <el-table-column prop="receiptTypeId" label="单据类型" align="center" min-width="200">
        <template slot-scope="scope">
          {{ scope.row.receiptTypeId === 1 ? "销售订单" : "销售退货单" }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center" min-width="150">
        <template slot-scope="scope">
          <span v-if="scope.row.auditStatus === 1" class="info-status"> 待审核 </span>
          <span v-else class="success-status">已审核</span>
        </template>
      </el-table-column>
      <el-table-column label="选择" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button icon="el-icon-check" size="mini" plain type="primary" @click="dbSelect(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pre_page"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>
    <div v-if="isCheck" slot="footer" style="width: 100%; border-top: 1px solid #eee; padding-top: 10px">
      <el-button size="small" type="primary" @click="confirm">确定</el-button>
      <el-button size="small" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import FooterPage from "@/component/common/FooterPage";
import { getAllReceive } from "@/api/Finance";
export default {
  name: "Receivable",
  components: {
    FooterPage,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: true,
    },
    id: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      tableData: [],
      choose_data: [],
      searchForm: {},
      pre_page: 10,
      page: 1,
      total: 0,
      loading: false,
      pageLayout: "total, prev, pager, next",
    };
  },
  created() {
    if (this.id) {
      this.getList();
    } else {
      this.$message.warning("请选择收款单位");
    }
  },
  methods: {
    async getList() {
      this.loading = true;
      const { data, pageTotal } = await getAllReceive({
        page: this.page,
        pageSize: this.pre_page,
        customerId: this.id,
        auditStatus: 2,
      });
      this.loading = false;

      this.tableData = data;
      this.total = pageTotal;
    },
    // 搜索前页数变1
    searchClick() {
      this.page = 1;
      this.getList();
    },
    selectionChange(val) {
      this.choose_data = val;
    },
    confirm() {
      this.cancel();
      this.$emit("confirm", this.choose_data);
    },
    // 双击选择
    dbSelect(row) {
      this.choose_data = [row];
      this.$emit("confirm", this.choose_data);
      this.cancel();
    },
    // 关闭弹窗
    cancel() {
      this.$emit("cancel");
    },
    // 改变页数
    pageChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.pre_page = val;
      this.pageChange(1);
    },
  },
};
</script>

<style scoped>
.search-wrp {
  padding: 15px 0;
}
</style>
