import * as $Access from "@/access/node";
import { onKeepAlive, title } from "@/config/settings";
import * as $_common from "@/utils";
import { mapGetters } from "vuex";

export default {
  computed: {
    ...mapGetters({
      systemType: "MUser/systemType",
      userName: "MUser/userName",
    }),
    nowExpireTime() {
      return this.$store.getters["MUser/nowExpireTime"];
    },
    shelfLifeSetUp() {
      return parseInt(this.$store.getters["MUser/shelfLifeSetUp"]);
    },
    // userName() {
    //   return this.$store.getters["MUser/userName"];
    // },
    $_common() {
      return $_common;
    },
    $Access() {
      return $Access;
    },
  },
  data() {
    return {
      enterprise_title: title,
      onKeepAlive: onKeepAlive,
      sub_loading: false,
    };
  },
  methods: {
    // 判断企业是否过期
    enterExpireTime() {
      if (new Date().getTime() >= this.nowExpireTime * 1000) {
        this.$router.push("/ExpireTip");
      }
    },
    // 级联选择器自定义选择即改变
    clickChange(event) {
      event.target.parentElement.parentElement.firstChild.click();
    },
  },
};
