import { storage, tokenTableName } from "@/config/settings";
import cookie from "js-cookie";

/**

 * @description 获取accessToken
 * @returns {string|ActiveX.IXMLDOMNode|Promise<any>|any|IDBRequest<any>|MediaKeyStatus|FormDataEntryValue|Function|Promise<Credential | null>}
 */
export function getAccessToken() {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.getItem(tokenTableName);
    } else if ("sessionStorage" === storage) {
      return sessionStorage.getItem(tokenTableName);
    } else if ("cookie" === storage) {
      return cookie.get(tokenTableName);
    } else {
      return localStorage.getItem(tokenTableName);
    }
  } else {
    return localStorage.getItem(tokenTableName);
  }
}

/**

 * @description 存储accessToken
 * @param accessToken
 * @returns {void|*}
 */
export function setAccessToken(accessToken) {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.setItem(tokenTableName, accessToken);
    } else if ("sessionStorage" === storage) {
      return sessionStorage.setItem(tokenTableName, accessToken);
    } else if ("cookie" === storage) {
      return cookie.set(tokenTableName, accessToken);
    } else {
      return localStorage.setItem(tokenTableName, accessToken);
    }
  } else {
    return localStorage.setItem(tokenTableName, accessToken);
  }
}
/**
 * 获取企业token
 * @returns {String}
 */
export function getEnToken() {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.getItem("enToken");
    } else if ("sessionStorage" === storage) {
      return sessionStorage.getItem("enToken");
    } else if ("cookie" === storage) {
      return cookie.get("enToken");
    } else {
      return localStorage.getItem("enToken");
    }
  } else {
    return localStorage.getItem("enToken");
  }
}
/**
 * 设置企业token
 * @returns {String}
 */
export function setEnToken(Token) {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.setItem("enToken", Token);
    } else if ("sessionStorage" === storage) {
      return sessionStorage.setItem("enToken", Token);
    } else if ("cookie" === storage) {
      return cookie.set("enToken", Token);
    } else {
      return localStorage.setItem("enToken", Token);
    }
  } else {
    return localStorage.setItem("enToken", Token);
  }
}
/**

 * @description 移除accessToken
 * @returns {void|Promise<void>}
 */
export function removeAccessToken() {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.removeItem(tokenTableName);
    } else if ("sessionStorage" === storage) {
      return sessionStorage.clear();
    } else if ("cookie" === storage) {
      return cookie.remove(tokenTableName);
    } else {
      return localStorage.removeItem(tokenTableName);
    }
  } else {
    return localStorage.removeItem(tokenTableName);
  }
}
/**
 * 获取供应商token
 * @returns {String}
 */
export function getSupplierToken() {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.getItem("supplierToken");
    } else if ("sessionStorage" === storage) {
      return sessionStorage.getItem("supplierToken");
    } else if ("cookie" === storage) {
      return cookie.get("supplierToken");
    } else {
      return localStorage.getItem("supplierToken");
    }
  } else {
    return localStorage.getItem("supplierToken");
  }
}
/**
 * 设置供应商token
 * @returns {String}
 */
export function setSupplierToken(Token) {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.setItem("supplierToken", Token);
    } else if ("sessionStorage" === storage) {
      return sessionStorage.setItem("supplierToken", Token);
    } else if ("cookie" === storage) {
      return cookie.set("supplierToken", Token);
    } else {
      return localStorage.setItem("supplierToken", Token);
    }
  } else {
    return localStorage.setItem("supplierToken", Token);
  }
}
