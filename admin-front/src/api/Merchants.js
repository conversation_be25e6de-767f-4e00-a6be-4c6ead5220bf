import request from "@/utils/request";
// 添加商户
export function addMerchant(data) {
  return request({
    url: "Merchant/Merchant/addMerchant",
    method: "post",
    data,
  });
}
// 商户列表
export function getAllMerchant(data) {
  return request({
    url: "Merchant/Merchant/getAllMerchant",
    method: "post",
    data,
  });
}
// 修改商户
export function updateMerchant(id, data) {
  return request({
    url: `Merchant/Merchant/updateMerchant/${id}`,
    method: "put",
    data,
  });
}

// 保存设置
export function saveSettingMerchant(data) {
  return request({
    url: "Merchant/Merchant/saveSetting",
    method: "post",
    data,
  });
}
// 获取设置
export function getSettingMerchant(data) {
  return request({
    url: "Merchant/Merchant/getSetting",
    method: "post",
    data,
  });
}
// 商户详情
export function getInfoMerchant(id, data) {
  return request({
    url: `Merchant/Merchant/getInfoMerchant/${id}`,
    method: "get",
    data,
  });
}
// 删除商户
export function delMerchant(id, data) {
  return request({
    url: `Merchant/Merchant/delMerchant/${id}`,
    method: "get",
    data,
  });
}
// 入驻添加
// export function getSettingMerchant(data) {
//   return request({
//     url: "Merchant/Merchant/getSetting",
//     method: "post",
//     data,
//   });
// }
// 入驻审核
export function auditApply(id, data) {
  return request({
    url: `Merchant/Merchant/auditApply/${id}`,
    method: "put",
    data,
  });
}
// 商户申请提现
export function MerchantWithdrawAdd(data) {
  return request({
    url: `Merchant/MerchantWithdraw/add`,
    method: "post",
    data,
  });
}
// 商户结算
export function getMerchantWithdraw(data) {
  return request({
    url: `Merchant/MerchantWithdraw/get`,
    method: "post",
    data,
  });
}
// 商户结算
export function MerchantWithdrawGetAll(data) {
  return request({
    url: `Merchant/MerchantWithdraw/getAll`,
    method: "post",
    data,
  });
}
// 更新提现审核状态
export function MerchantUpdateAuditStatus(id, data) {
  return request({
    url: `Merchant/MerchantWithdraw/updateAuditStatus/${id}`,
    method: "put",
    data,
  });
}
// 获取商户基本配置
export function MerchantGetSettingField(id, data) {
  return request({
    url: `Merchant/Merchant/getSettingField`,
    method: "post",
    data,
  });
}
// 商户资金变动记录
export function Merchantlog(data) {
  return request({
    url: `Merchant/Merchant/log`,
    method: "post",
    data,
  });
}

// 启用禁用
export function enabledMerchant(id, data) {
  return request({
    url: `Merchant/Merchant/enabledMerchant/${id}`,
    method: "get",
    data,
  });
}
// 所有结算单
export function getAllMerchantSettlement(data) {
  return request({
    url: `Merchant/MerchantSettlement/getAllMerchantSettlement`,
    method: "POST",
    data,
  });
}
// 结算单状态审核
export function updateMerchantSettlement(data) {
  return request({
    url: `Merchant/MerchantSettlement/updateMerchantSettlement`,
    method: "POST",
    data,
  });
}
// 商户提现列表
export function getAllMerchantWithdraw(data) {
  return request({
    url: `Merchant/MerchantWithdraw/getAll`,
    method: "post",
    data,
  });
}
//商户申请提现
export function addMerchantWithdraw(data) {
  return request({
    url: `Merchant/MerchantWithdraw/add`,
    method: "post",
    data,
  });
}
// 商户列表不分页
export function getAllMerchantList(data) {
  return request({
    url: `Merchant/Merchant/getAllMerchantList`,
    method: "post",
    data,
  });
}
// 收支
export function getAllMerchantDetail(data) {
  return request({
    url: `Merchant/MerchantDetail/getAllMerchantDetail`,
    method: "post",
    data,
  });
}
// 导出收支
export function exportgetAllMerchantDetail(data) {
  return request({
    url: `/Purchase/Purchase/getAllMerchantDetail`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 结算导出
export function exportgetAllMerchantSettlement(data) {
  return request({
    url: `/Purchase/Purchase/getAllMerchantSettlement`,
    method: "post",
    responseType: "blob",
    data,
  });
}
