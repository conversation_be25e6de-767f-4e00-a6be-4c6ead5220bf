import request from "@/utils/request";

/**
 * 获取当前登录供应商的个人信息
 */
export function getSupplierProfile() {
  return request({
    url: `/Purchase/Supplier/getSupplierByUserCenterId`,
    method: "get",
  });
}

/**
 * 更新供应商个人信息
 * @param {Object} data 更新的数据
 */
export function updateSupplierProfile(data) {
  return request({
    url: `/Purchase/Supplier/updateSupplierProfile`,
    method: "post",
    data,
  });
}
