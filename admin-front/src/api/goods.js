import request from "@/utils/request";

/**
 * 商品添加
 * */
/* --- 基础资料管理 --- */

// 基础商品资料(以sku为列)
export function getAllGoodsBasicBySku(data) {
  return request({
    url: "/GoodsManage/GoodsBasic/getAllGoodsBasicBySku",
    method: "post",
    data,
  });
}

// 导入商品基础数据
export function goodsImport(data) {
  return request({
    url: "/GoodsManage/GoodsBasic/goodsImport",
    method: "post",
    data,
  });
}

// 单店铺商品导入
export function goodsQuickImport(data) {
  return request({
    url: "/Goods/QuickGoods/goodsQuickImport",
    method: "post",
    data,
  });
}

// 商品基本资料搜索
export function searchBasic(data) {
  return request({
    url: "/GoodsManage/GoodsBasic/search",
    method: "post",
    data,
  });
}

// 商品基本资料搜索导出
export function exportSearchBasic(data) {
  return request({
    url: "/GoodsManage/GoodsBasic/search",
    method: "post",
    responseType: "blob",
    data,
  });
}

// 添加自定义属性
export function defineSpec(data) {
  return request({
    url: "/GoodsManage/SpecManage/defineSpec",
    method: "post",
    data,
  });
}

// 添加商品(单店铺版新增)
export function addBasicAndPublishGoods(data) {
  return request({
    url: "/Goods/QuickGoods/addBasicAndPublishGoods",
    method: "post",
    data,
  });
}

// 编辑商品&&基础资料(单店铺版)
export function editQuickGoods(data) {
  return request({
    url: "/Goods/QuickGoods/editQuickGoods",
    method: "post",
    data,
  });
}

// 基础资料&&商品详情(单店铺版)
export function getQuickGoodsInfo(id, data) {
  return request({
    url: `/Goods/QuickGoods/getQuickGoodsInfo/${id}`,
    method: "post",
    data,
  });
}

// 添加商品基础数据
export function addGoodsBasic(data) {
  return request({
    url: `/GoodsManage/GoodsBasic/addGoodsBasic`,
    method: "post",
    data,
  });
}

// 获取全部基础商品
export function getAllGoodsBasic(data) {
  return request({
    url: `/GoodsManage/GoodsBasic/getAllGoodsBasic`,
    method: "post",
    data,
  });
}

// 导出全部基础商品
export function exportGetAllGoodsBasic(data) {
  return request({
    url: `/GoodsManage/GoodsBasic/getAllGoodsBasic`,
    responseType: "blob",
    method: "post",
    data,
  });
}

// 获取指定基础商品
export function getGoodsBasicInfoById(id, data) {
  return request({
    url: `/GoodsManage/GoodsBasic/getGoodsBasicInfoById/${id}`,
    method: "post",
    data,
  });
}

// 获取商品列表 含搜索 Goods/ApiGoods/getGoodsByCategory
export function getGoodsByCategory(data) {
  return request({
    url: `/Goods/ApiGoods/getGoodsByCategory`,
    method: "post",
    data,
  });
}

// 基础商品启用/禁用
export function updateEnableStatus(data) {
  return request({
    url: `/GoodsManage/GoodsBasic/updateEnableStatus`,
    method: "put",
    data,
  });
}

// 删除基础商品
export function delGoodsBasic(id, data) {
  return request({
    url: `/GoodsManage/GoodsBasic/delGoodsBasic/${id}`,
    method: "delete",
    data,
  });
}

// 修改基础商品
export function editGoodsBasic(id, data) {
  return request({
    url: `/GoodsManage/GoodsBasic/editGoodsBasic/${id}`,
    method: "put",
    data,
  });
}

// 商铺下允许销售的商品列表
export function getGoodsBasicOfShopId(data) {
  return request({
    url: `/GoodsManage/GoodsBasic/getGoodsBasicOfShopId`,
    method: "post",
    data,
  });
}

// 移动基础商品的分类
export function updateCategory(data) {
  return request({
    url: `/GoodsManage/GoodsBasic/updateCategory`,
    method: "post",
    data,
  });
}

// 商品批量上下架 Goods/Goods/updateEnableStatus
export function BatchUnloading(data) {
  return request({
    url: `/Goods/Goods/updateEnableStatus`,
    method: "post",
    data,
  });
}

// 商品置顶
export function setTop(id, data) {
  return request({
    url: `/Goods/Goods/setTop/${id}`,
    method: "get",
    data,
  });
}

// 批量设置销量
export function setSalesNumBatch(data) {
  return request({
    url: `/Goods/Goods/setSalesNumBatch`,
    method: "post",
    data,
  });
}

// 批量设置销量
export function setSalesNum(data) {
  return request({
    url: `/Goods/Goods/setSalesNum`,
    method: "post",
    data,
  });
}

// 计量单位
// 列表 GoodsManage/Units/getAll
export function getAllUnit(data) {
  return request({
    url: `/GoodsManage/Units/getAll`,
    method: "post",
    data,
  });
}

// 添加 GoodsManage/Units/add
export function addUnit(data) {
  return request({
    url: `/GoodsManage/Units/add`,
    method: "post",
    data,
  });
}

// 编辑 GoodsManage/Units/edit/7
export function editUnit(id, data) {
  return request({
    url: `/GoodsManage/Units/edit/${id}`,
    method: "put",
    data,
  });
}

// 启用/禁用计量单位 GoodsManage/Units/updateEnablesStatus
export function updateEnablesStatus(data) {
  return request({
    url: `/GoodsManage/Units/updateEnablesStatus`,
    method: "put",
    data,
  });
}

// 删除 GoodsManage/Units/del/7
export function delUnit(id, data) {
  return request({
    url: `/GoodsManage/Units/del/${id}`,
    method: "delete",
    data,
  });
}

/* --- 规格管理 --- */

// 添加属性名/值
export function SpecManageAdd(data) {
  return request({
    url: `/GoodsManage/SpecManage/add`,
    method: "post",
    data,
  });
}

// 编辑
export function SpecManageedit(id, data) {
  return request({
    url: `/GoodsManage/SpecManage/edit/${id}`,
    method: "post",
    data,
  });
}

// 删除
export function SpecManagedel(id, data) {
  return request({
    url: `/GoodsManage/SpecManage/del/${id}`,
    method: "delete",
    data,
  });
}

// 详情
export function SpecManageinfo(id, data) {
  return request({
    url: `/GoodsManage/SpecManage/info/${id}`,
    method: "get",
    data,
  });
}

// 列表
export function SpecManagegetAll(data) {
  return request({
    url: `/GoodsManage/SpecManage/getAll`,
    method: "post",
    data,
  });
}

/* --- 商品管理 --- */

// 商品列表（以sku为列）
export function getAllGoodsBySku(data) {
  return request({
    url: `/Goods/Goods/getAllGoodsBySku`,
    method: "post",
    data,
  });
}

// 商品搜索
export function searchGood(data) {
  return request({
    url: `/Goods/Goods/search`,
    method: "post",
    data,
  });
}

// 商品搜索 导出
export function exportSearchGood(data) {
  return request({
    url: `/Goods/Goods/search`,
    responseType: "blob",
    method: "post",
    data,
  });
}

// 添加商品
export function addGoods(data) {
  return request({
    url: `/Goods/Goods/addGoods`,
    method: "post",
    data,
  });
}

// 商品详情
export function getGoodsInfo(id, data) {
  return request({
    url: `/Goods/Goods/getGoodsInfo/${id}`,
    method: "post",
    data,
  });
}

// 删除商品
export function delGoods(id, data) {
  return request({
    url: `/Goods/Goods/delGoods/${id}`,
    method: "get",
    data,
  });
}

// 商品修改
export function editGoods(id, data) {
  return request({
    url: `/Goods/Goods/editGoods/${id}`,
    method: "put",
    data,
  });
}

// 商品列表
export function getAllGoods(data) {
  return request({
    url: `/Goods/Goods/getAllGoods`,
    method: "post",
    data,
  });
}

// 商品列表（优化）
export function getAllGoodsList(data) {
  return request({
    url: `/Goods/Goods/getAllGoodsList`,
    method: "post",
    data,
  });
}

// 商品列表导出（优化）
export function exportGetAllGoodsList(data) {
  return request({
    url: `/Goods/Goods/getAllGoodsList`,
    method: "post",
    responseType: "blob",

    data,
  });
}

// 商品列表导出
export function exportGetAllGoods(data) {
  return request({
    url: `/Goods/Goods/getAllGoods`,
    method: "post",
    responseType: "blob",
    data,
  });
}

// 审核商品
export function auditGoods(id, data) {
  return request({
    url: `/Goods/Goods/audit/${id}`,
    method: "put",
    data,
  });
}

// 关键字搜索
export function getGoodsByCondition(data) {
  return request({
    url: `/Goods/Goods/getGoodsByCondition`,
    method: "post",
    data,
  });
}

// 商品上下架
export function GoodsUpdateEnableStatus(data) {
  return request({
    url: `/Goods/Goods/updateEnableStatus`,
    method: "post",
    data,
  });
}

// 批量设置运费
export function batchGoodsExpress(data) {
  return request({
    url: `/Goods/Goods/batchGoodsExpress`,
    method: "post",
    data,
  });
}

/* --- 品牌管理 --- */

// 添加品牌
export function addBrand(data) {
  return request({
    url: `/GoodsManage/GoodsBrand/addBrand`,
    method: "post",
    data,
  });
}

// 修改品牌
export function editBrand(id, data) {
  return request({
    url: `/GoodsManage/GoodsBrand/editBrand/${id}`,
    method: "put",
    data,
  });
}

// 获取品牌详情
export function getBrandInfoById(id, data) {
  return request({
    url: `/GoodsManage/GoodsBrand/getBrandInfoById/${id}`,
    method: "put",
    data,
  });
}

// 品牌的显示和隐藏
export function updateBrandStatus(id, data) {
  return request({
    url: `/GoodsManage/GoodsBrand/updateBrandStatus/${id}`,
    method: "put",
    data,
  });
}

// 删除品牌
export function delBrand(id, data) {
  return request({
    url: `/GoodsManage/GoodsBrand/delBrand/${id}`,
    method: "delete",
    data,
  });
}

// 品牌列表
export function getAllBrand(data) {
  return request({
    url: `/GoodsManage/GoodsBrand/getAllBrand`,
    method: "post",
    data,
  });
}

/* --- 分类管理 --- */

// 设置品牌
export function setBrand(data) {
  return request({
    url: `/GoodsManage/GoodsBasic/setBrand`,
    method: "put",
    data,
  });
}

// 批量设置单位
export function setSku(data) {
  return request({
    url: `/GoodsManage/GoodsBasic/setSku`,
    method: "put",
    data,
  });
}

// 设置不可销售的店铺 GoodsManage/GoodsBasic/setNoSalesShop
export function setNoSalesShop(data) {
  return request({
    url: `/GoodsManage/GoodsBasic/setNoSalesShop`,
    method: "put",
    data,
  });
}

// 添加基础商品分类
export function addCategory(data) {
  return request({
    url: `/GoodsCategory/GoodsCategory/addCategory`,
    method: "post",
    data,
  });
}

// 分类的显示和隐藏
export function updateCategoryStatus(id, data) {
  return request({
    url: `/GoodsCategory/GoodsCategory/updateCategoryStatus/${id}`,
    method: "put",
    data,
  });
}

// 获取分类详情
export function getCategoryInfoById(id, data) {
  return request({
    url: `/GoodsCategory/GoodsCategory/getCategoryInfoById/${id}`,
    method: "get",
    data,
  });
}

// 获取所有商品分类
export function getAllCategory(data) {
  return request({
    url: `/GoodsCategory/GoodsCategory/getAllCategory`,
    method: "get",
    data,
  });
}

// 删除指定分类
export function delCategory(id, data) {
  return request({
    url: `/GoodsCategory/GoodsCategory/delCategory/${id}`,
    method: "delete",
    data,
  });
}

// 修改分类
export function editCategory(id, data) {
  return request({
    url: `/GoodsCategory/GoodsCategory/editCategory/${id}`,
    method: "put",
    data,
  });
}

// 添加商品页面，获取分类
export function getAllCategoryPost(data) {
  return request({
    url: `/GoodsCategory/GoodsCategory/getAllCategory`,
    method: "post",
    data,
  });
}

// 根据条码获取商品
export function getGoodsByBarCode(data) {
  return request({
    url: `/Goods/Goods/getGoodsByBarCode`,
    method: "post",
    data,
  });
}

// 获取materielId与goodsId
export function getRelMap(data) {
  return request({
    url: `/Goods/Goods/getRelMap`,
    method: "post",
    data,
  });
}

// 商品价格波动图
export function getPriceTrend(id, data) {
  return request({
    url: `Goods/Goods/getPriceTrend/${id}`,
    method: "POST",
    data,
  });
}

//批量启/禁用负库存
export function batchSetDistribution(data) {
  return request({
    url: `Goods/Goods/batchSetDistribution`,
    method: "POST",
    data,
  });
}

// 添加商品服务
export function addGoodsSupport(data) {
  return request({
    url: "Goods/GoodsSupport/addGoodsSupport",
    method: "post",
    data,
  });
}

// 修改商品服务
export function editGoodsSupport(data) {
  return request({
    url: "/Goods/GoodsSupport/editGoodsSupport",
    method: "post",
    data,
  });
}

// 删除商品服务
export function delGoodsSupport(id, data) {
  return request({
    url: `Goods/GoodsSupport/delGoodsSupport/${id}`,
    method: "post",
    data,
  });
}

//获取所有商品服务
export function getAllGoodsSupport(data) {
  return request({
    url: "Goods/GoodsSupport/getAllGoodsSupport",
    method: "post",
    data,
  });
}

//查看所有商品服务
export function getGoodsSupportInfo(id, data) {
  return request({
    url: `/Goods/GoodsSupport/getGoodsSupportInfo/${id}`,
    method: "post",
    data,
  });
}

// 商品分组
export function getAllGoodsGroups(data) {
  return request({
    url: `Goods/GoodsGroups/getAllGoodsGroups`,
    method: "post",
    data,
  });
}

// 新增商品分组
export function addGoodsGroups(data) {
  return request({
    url: `Goods/GoodsGroups/addGoodsGroups`,
    method: "post",
    data,
  });
}

// 商品分组启用/禁用
export function enableGoodsGroups(id, data) {
  return request({
    url: `Goods/GoodsGroups/enableGoodsGroups/${id}`,
    method: "post",
    data,
  });
}

// 商品分组修改
export function updateGoodsGroups(id, data) {
  return request({
    url: `Goods/GoodsGroups/updateGoodsGroups/${id}`,
    method: "post",
    data,
  });
}

// 获取指定商品分组
export function getReservoirInfo(id, data) {
  return request({
    url: `Goods/GoodsGroups/getReservoirInfo/${id}`,
    method: "post",
    data,
  });
}

// 删除商品分组
export function deleteGoodsGroups(id, data) {
  return request({
    url: `Goods/GoodsGroups/deleteGoodsGroups/${id}`,
    method: "post",
    data,
  });
}

//修改排序字段
export function setSort(data) {
  return request({
    url: `Goods/Goods/setSort`,
    method: "post",
    data,
  });
}
