import request from "@/utils/request";
/**
 * 素材库
 * */
// 素材分类添加
export function addMaterialCategory(data) {
  return request({
    url: `/Material/Material/addMaterialCategory`,
    method: "post",
    data,
  });
}
// 素材内容添加
export function addMaterialContent(data) {
  return request({
    url: `/Material/Material/addMaterialContent`,
    method: "post",
    data,
  });
}
// 素材分类编辑
export function updateMaterialCategory(data) {
  return request({
    url: `/Material/Material/updateMaterialCategory`,
    method: "post",
    data,
  });
}
// 素材内容删除
export function delMaterialContent(data) {
  return request({
    url: `/Material/Material/delMaterialContent`,
    method: "post",
    data,
  });
}
// 素材分类删除
export function delMaterialCategory(id, data) {
  return request({
    url: `/Material/Material/delMaterialCategory/${id}`,
    method: "delete",
    data,
  });
}
// 素材分类列表
export function getAllMaterialCategory(data) {
  return request({
    url: `/Material/Material/getAllMaterialCategory`,
    method: "post",
    data,
  });
}
// 素材内容列表
export function getAllMaterialContent(data) {
  return request({
    url: `/Material/Material/getAllMaterialContent`,
    method: "post",
    data,
  });
}
// 素材内容编辑
export function updateMaterialContent(data) {
  return request({
    url: `/Material/Material/updateMaterialContent`,
    method: "post",
    data,
  });
}
