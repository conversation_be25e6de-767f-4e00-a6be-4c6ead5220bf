import request from "@/utils/request";
/* --- 系统管路 商铺列表 --- */
// 商铺合作
//  商铺合作添加  ShopPartner/ShopPartner/addShopPartner  post
export function addShopPartner(data) {
  return request({
    url: `/ShopPartner/ShopPartner/addShopPartner`,
    method: "post",
    data,
  });
}
// 获取暂存数据 Shop/Shop/getTempData
export function getTempShopData(data) {
  return request({
    url: `/Shop/Shop/getTempData`,
    method: "get",
    data,
  });
}
//  商铺合作删除  ShopPartner/ShopPartner/deleteShopPartner/1  del
export function deleteShopPartner(id, data) {
  return request({
    url: `/ShopPartner/ShopPartner/deleteShopPartner/${id}`,
    method: "delete",
    data,
  });
}
//  商铺合作修改  ShopPartner/ShopPartner/updateShopPartner/1 put
export function updateShopPartner(id, data) {
  return request({
    url: `/ShopPartner/ShopPartner/updateShopPartner/${id}`,
    method: "put",
    data,
  });
}
//  商铺合作列表  ShopPartner/ShopPartner/getAllShopPartner  post
export function getAllShopPartner(data) {
  return request({
    url: `/ShopPartner/ShopPartner/getAllShopPartner`,
    method: "post",
    data,
  });
}
//  商铺合作详情  ShopPartner/ShopPartner/getShopPartnerInfo/1  get
export function getShopPartnerInfo(id, data) {
  return request({
    url: `/ShopPartner/ShopPartner/getShopPartnerInfo/${id}`,
    method: "get",
    data,
  });
}
// 商铺合作的启用禁用  ShopPartner/ShopPartner/enableShopPartner put
export function enableShopPartner(data) {
  return request({
    url: `/ShopPartner/ShopPartner/enableShopPartner`,
    method: "put",
    data,
  });
}
// 合作搜索  ShopPartner/ShopPartner/search post
export function partherSearch(data) {
  return request({
    url: `/ShopPartner/ShopPartner/search`,
    method: "put",
    data,
  });
}
// 获取企业下的管理员(添加合伙人页面下拉列表)
export function getManagerList(data) {
  return request({
    url: `/ShopPartner/ShopPartner/getManagerList`,
    method: "get",
    data,
  });
}

//  商铺管理
//  添加商铺 Shop/Shop/addShop post
export function addShop(data) {
  return request({
    url: `/Shop/Shop/addShop`,
    method: "post",
    data,
  });
}
//  商铺详情 Shop/Shop/getShopInfo/1 get
export function getShopInfo(id, data) {
  return request({
    url: `/Shop/Shop/getShopInfo/${id}`,
    method: "get",
    data,
  });
}
//  编辑商铺  Shop/Shop/editShop/1  put
export function editShop(id, data) {
  return request({
    url: `/Shop/Shop/editShop/${id}`,
    method: "put",
    data,
  });
}
//  删除商铺  Shop/Shop/delShop/1 get
export function delShop(id, data) {
  return request({
    url: `/Shop/Shop/delShop/${id}`,
    method: "get",
    data,
  });
}
//  商铺列表 Shop/Shop/getAllShop post
export function getAllShop(data) {
  return request({
    url: `/Shop/Shop/getAllShop`,
    method: "post",
    data,
  });
}
// 更换商铺负责人  Shop/Shop/changeManagerForShop post
export function changeManagerForShop(data) {
  return request({
    url: `/Shop/Shop/changeManagerForShop`,
    method: "post",
    data,
  });
}
// 商铺搜索  Shop/Shop/search post
export function search(data) {
  return request({
    url: `/Shop/Shop/search`,
    method: "post",
    data,
  });
}
// 商铺管理启用禁用  Shop/Shop/updateShopStatus post
export function updateShopStatus(data) {
  return request({
    url: `/Shop/Shop/updateShopStatus`,
    method: "post",
    data,
  });
}
// 当前企业下已有的销售区域
export function getSalesAreaInEnterprise(data) {
  return request({
    url: `/Shop/Shop/getSalesAreaInEnterprise`,
    method: "get",
    data,
  });
}
// 获取员工所在商铺列表
export function getShopByStaff(data) {
  return request({
    url: `/Shop/Shop/getShopByStaff`,
    method: "post",
    data,
  });
}
// 获取商铺下的员工
export function getStaffByShopId(data) {
  return request({
    url: `Department/Staff/getStaffByShopId`,
    method: "post",
    data,
  });
}
// 设置店长
export function setStaffType(data) {
  return request({
    url: `Shop/Shop/setStaffType`,
    method: "post",
    data,
  });
}
// 职工详情
export function getStaffInfo(data) {
  return request({
    url: `Department/Staff/getStaffInfo`,
    method: "put",
    data,
  });
}
// 设置总店
export function setTopShop(id, data) {
  return request({
    url: `/Shop/Shop/setTopShop/${id}`,
    method: "get",
    data,
  });
}
// 删除店长
export function delManager(data) {
  return request({
    url: `/Shop/Shop/delManager`,
    method: "post",
    data,
  });
}
