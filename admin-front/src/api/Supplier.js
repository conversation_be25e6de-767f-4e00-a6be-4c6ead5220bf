import request from "@/utils/request";

// 报价单列表
export function getAllSupplierOfferPrice(data) {
  return request({
    url: `/Price/SupplierOfferPrice/getAll`,
    method: "post",
    data,
  });
}
// 报价单详情
export function getSupplierOfferPrice(id, data) {
  return request({
    url: `/Price/SupplierOfferPrice/get/${id}`,
    method: "get",
    data,
  });
}
// 报价单审核
export function auditSupplierOfferPrice(id, data) {
  return request({
    url: `/Price/SupplierOfferPrice/audit/${id}`,
    method: "put",
    data,
  });
}
// 结算列表
export function getAllSupplierWithdrawal(data) {
  return request({
    url: `/Purchase/SupplierWithdrawal/getAll`,
    method: "post",
    data,
  });
}
// 审核
export function updateAuditStatus(id, data) {
  return request({
    url: `/Purchase/SupplierWithdrawal/updateAuditStatus/${id}`,
    method: "put",
    data,
  });
}
