import request from "@/utils/request";

/**
 * 获取供应商提现申请列表
 * @param {Object} data 查询参数
 * @returns {Promise} 请求结果
 */
export function getSupplierWithdrawList(data) {
  return request({
    url: `/Purchase/SupplierWithdrawal/getAll`,
    method: "post",
    data,
  });
}

/**
 * 提交提现申请
 * @param {Object} data 提现申请数据
 * @returns {Promise} 请求结果
 */
export function addSupplierWithdraw(data) {
  return request({
    url: `/Purchase/SupplierWithdrawal/add`,
    method: "post",
    data,
  });
}

/**
 * 获取供应商账户余额信息
 * @param {Number} supplierId 供应商ID
 * @returns {Promise} 请求结果
 */
export function getSupplierBalance(supplierId) {
  return request({
    url: `/Purchase/SupplierWithdrawal/getWithdrawal/${supplierId}`,
    method: "get",
  });
}

/**
 * 取消提现申请
 * @param {Number} id 提现申请ID
 * @param {Object} data 取消原因等数据
 * @returns {Promise} 请求结果
 */
export function cancelWithdraw(id, data) {
  return request({
    url: `/Purchase/SupplierWithdrawal/cancel/${id}`,
    method: "put",
    data,
  });
}
