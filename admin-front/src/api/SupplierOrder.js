import request from "@/utils/request";

/**
 * 获取供应商订单列表
 * @param {Object} data 查询参数
 */
export function getSupplierOrders(data) {
  return request({
    url: `/Order/SupplierOrder/getAll`,
    method: "post",
    data,
  });
}

/**
 * 获取供应商订单详情
 * @param {Number} id 订单ID
 */
export function getSupplierOrderDetail(id) {
  return request({
    url: `/Order/SupplierOrder/get/${id}`,
    method: "get",
  });
}

/**
 * 获取产品发货状态
 * @param {Object} data 查询参数
 */
export function getProductShippingStatus(data) {
  return request({
    url: `/Order/SupplierOrder/getShippingStatus`,
    method: "post",
    data,
  });
}

/**
 * 获取分账明细
 * @param {Number} orderId 订单ID
 */
export function getOrderSettlementDetail(orderId) {
  return request({
    url: `/Finance/SettlementDetail/getOrderSettlementDetail`,
    method: "post",
    data: {
      orderId,
    },
  });
}

/**
 * 导出供应商订单数据
 * @param {Object} data 查询参数
 */
export function exportSupplierOrders(data) {
  return request({
    url: `/Order/SupplierOrder/export`,
    method: "post",
    responseType: "blob",
    data,
  });
}
