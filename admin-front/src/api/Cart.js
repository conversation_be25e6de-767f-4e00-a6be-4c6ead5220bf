import request from "@/utils/request";

/* --------------加入购物车-------------------- */
// 加入购物车
export function addCart(id, data) {
  return request({
    url: `/Cart/Cart/addCart/${id}`,
    method: "post",
    data,
  });
}
// 获取购物车
export function getCartByUserCenterId(id, data) {
  return request({
    url: `/Cart/Cart/getCartByUserCenterId/${id}`,
    method: "get",
    data,
  });
}
// 移出购物车
export function delCart(id, data) {
  return request({
    url: `/Cart/Cart/delCart/${id}`,
    method: "post",
    data,
  });
}
// 商品选中/取消选中
export function updateSelection(id, data) {
  return request({
    url: `/Cart/Cart/updateSelection/${id}`,
    method: "put",
    data,
  });
}
// 更新购物车某个商品数量
export function updateBuyNum(id, data) {
  return request({
    url: `/Cart/Cart/updateBuyNum/${id}`,
    method: "put",
    data,
  });
}
// 清空购物车
export function clearCart(id, data) {
  return request({
    url: `/Cart/Cart/clearCart/${id}`,
    method: "delete",
    data,
  });
}
