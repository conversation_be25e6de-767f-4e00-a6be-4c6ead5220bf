import request from "@/utils/request";
// 设置
export function setSettings(data) {
  return request({
    url: `/Cashier/Settings/set`,
    method: "post",
    data,
  });
}
export function getSettings(data) {
  return request({
    url: `/Cashier/Settings/get`,
    method: "get",
    data,
  });
}
// 交班记录
export function getRecordList(data) {
  return request({
    url: `/Cashier/PushMoney/getRecordList`,
    method: "post",
    data,
  });
}
// 提成明细
export function getAllPushMoney(data) {
  return request({
    url: `/Cashier/PushMoney/getAll`,
    method: "post",
    data,
  });
}
// 提成统计
export function overView(data) {
  return request({
    url: `/Cashier/Cashier/overView`,
    method: "post",
    data,
  });
}
// 交班详情
export function getRecordInfo(id, data) {
  return request({
    url: `/Cashier/PushMoney/getRecordInfo/${id}`,
    method: "get",
    data,
  });
}
// 商品列表
export function GoodsByCategory(data) {
  return request({
    url: `/Cashier/CashierGoods/getGoodsByCategory`,
    method: "post",
    data,
  });
}
// 商品详情
export function getGoodsDetail(id, data) {
  return request({
    url: `/Cashier/CashierGoods/getGoodsDetail/${id}`,
    method: "post",
    data,
  });
}
// 收银台登录记录
export function loggerConnect(data) {
  return request({
    url: `/Cashier/PushMoney/loggerConnect`,
    method: "post",
    data,
  });
}
// 收银申请交接
export function connectDetails(data) {
  return request({
    url: `/Cashier/PushMoney/connectDetails`,
    method: "post",
    data,
  });
}
// 收银确认交接
export function confirmConnect(id, data) {
  return request({
    url: `/Cashier/PushMoney/confirmConnect/${id}`,
    method: "post",
    data,
  });
}
// 会员
export function searchCustomerDetails(data) {
  return request({
    url: `/Cashier/Cashier/searchCustomerDetails`,
    method: "post",
    data,
  });
}
// 加入购物车
export function addCartCashier(data) {
  return request({
    url: `/Cashier/Cashier/addCart`,
    method: "post",
    data,
  });
}
// 购物车详情
export function getCartByUser(data) {
  return request({
    url: `/Cashier/Cashier/getCartByUserCenterId`,
    method: "post",
    data,
  });
}
// 修改购物车商品数量
export function updateBuyNumCashier(id, data) {
  return request({
    url: `/Cashier/Cashier/updateBuyNum/${id}`,
    method: "put",
    data,
  });
}
// 删除购物车
export function delCartCashier(data) {
  return request({
    url: `/Cashier/Cashier/delCart`,
    method: "post",
    data,
  });
}
// 清空购物车
export function clearCartCashier(data) {
  return request({
    url: `/Cashier/Cashier/clearCart`,
    method: "put",
    data,
  });
}
// 改价
export function changePrice(data) {
  return request({
    url: `/Cashier/Cashier/changePrice`,
    method: "post",
    data,
  });
}
// 挂单
export function saveEntryData(data) {
  return request({
    url: `/Cashier/Cashier/saveEntryData`,
    method: "post",
    data,
  });
}
// 挂单列表
export function getAllEntryData(data) {
  return request({
    url: `/Cashier/Cashier/getAllEntryData`,
    method: "post",
    data,
  });
}
// 取单
export function getEntryData(id, data) {
  return request({
    url: `/Cashier/Cashier/getEntryData/${id}`,
    method: "get",
    data,
  });
}
// 删除挂单
export function delEntryData(id, data) {
  return request({
    url: `/Cashier/Cashier/delEntryData/${id}`,
    method: "delete",
    data,
  });
}
// 优惠活动
export function activityAll(data) {
  return request({
    url: `/Cashier/Cashier/activityAll`,
    method: "post",
    data,
  });
}
