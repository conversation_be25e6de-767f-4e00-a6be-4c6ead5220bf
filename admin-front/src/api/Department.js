import request from "@/utils/request";
// 获取员工详情
export function getStaff(id, data) {
  return request({
    url: `/Department/Staff/getStaffInfo/${id}`,
    method: "post",
    data,
  });
}
export function updateStaffStaff(id, data) {
  return request({
    url: `/Department/Staff/updateStaff/${id}`,
    method: "post",
    data,
  });
}
//  账号
//  职工管理
//  职工添加  /Department/Staff/addStaff post
export function addStaff(data) {
  return request({
    url: `/Department/Staff/addStaff`,
    method: "post",
    data,
  });
}
//  职工删除  Department/Staff/deleteStaff/1  post
export function deleteStaff(data) {
  return request({
    url: `/Department/Staff/deleteStaff`,
    method: "post",
    data,
  });
}
//  职工修改  Department/Staff/updateStaff/1  put
export function updateStaff(id, data) {
  return request({
    url: `/Department/Staff/updateStaff/${id}`,
    method: "put",
    data,
  });
}
//  职工列表  Department/Staff/getAllStaff  post
export function getAllStaff(data) {
  return request({
    url: `/Department/Staff/getAllStaff`,
    method: "post",
    data,
  });
}
//  职工详情  Department/Staff/getStaffInfo/1 get
export function getStaffInfo(id, data) {
  return request({
    url: `/Department/Staff/getStaffInfo/${id}`,
    method: "get",
    data,
  });
}
//  员工业绩提成修改
export function updateStaffTargetAndReward(id, data) {
  return request({
    url: `/Department/Staff/updateStaffTargetAndReward/${id}`,
    method: "post",
    data,
  });
}
//  提成流水
export function getAllStaffRewardDesc(data) {
  return request({
    url: `/Department/Staff/getAllStaffRewardDesc`,
    method: "post",
    data,
  });
}
//  部门管理
//  部门添加  Department/Department/addDepartment  post
export function addDepartment(data) {
  return request({
    url: `/Department/Department/addDepartment`,
    method: "post",
    data,
  });
}
//  部门删除  Department/Department/deleteDepartment/1 del
export function deleteDepartment(id, data) {
  return request({
    url: `/Department/Department/deleteDepartment/${id}`,
    method: "delete",
    data,
  });
}
//  部门修改  Department/Department/updateDepartment/1  put
export function updateDepartment(id, data) {
  return request({
    url: `/Department/Department/updateDepartment/${id}`,
    method: "put",
    data,
  });
}
//  部门列表 Department/Department/getAllDepartment  post
export function getAllDepartment(data) {
  return request({
    url: `/Department/Department/getAllDepartment`,
    method: "post",
    data,
  });
}
//  部门详情  Department/Department/getDepartmentInfo/1 get
export function getDepartmentInfo(id, data) {
  return request({
    url: `/Department/Department/getDepartmentInfo/${id}`,
    method: "get",
    data,
  });
}
//  角色管理
//  角色列表  Department/Role/getAllRole  post
export function getAllRole(data) {
  return request({
    url: `/Department/Role/getAllRole`,
    method: "post",
    data,
  });
}
//  角色详请 Department/Role/getRoleInfo/1  get
export function getRoleInfo(id, data) {
  return request({
    url: `/Department/Role/getRoleInfo/${id}`,
    method: "get",
    data,
  });
}
//  角色修改  Department/Role/updateRole/1 put
export function updateRole(id, data) {
  return request({
    url: `/Department/Role/updateRole/${id}`,
    method: "put",
    data,
  });
}
//  角色删除  Department/Role/deleteRole/1   del
export function deleteRole(id, data) {
  return request({
    url: `/Department/Role/deleteRole/${id}`,
    method: "delete",
    data,
  });
}
//  角色添加  Department/Role/addRole
export function addRole(data) {
  return request({
    url: `/Department/Role/addRole`,
    method: "post",
    data,
  });
}
// 收银台
// 收银员/导购员列表
export function getAllStaffStaff(data) {
  return request({
    url: `/Department/Staff/getAllStaff`,
    method: "post",
    data,
  });
}
