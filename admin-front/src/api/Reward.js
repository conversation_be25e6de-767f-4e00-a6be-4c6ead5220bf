import request from "@/utils/request";
/* --------------提成规则----------------- */

// 提成规则修改
export function updateRewardRule(id, data) {
  return request({
    url: `/Reward/Reward/updateRewardRule/${id}`,
    method: "put",
    data,
  });
}
// 提成规则禁用
export function enableRewardRule(id, data) {
  return request({
    url: `/Reward/Reward/enableRewardRule/${id}`,
    method: "get",
    data,
  });
}
// 提成规则删除
export function deleteRewardRule(id, data) {
  return request({
    url: `/Reward/Reward/deleteRewardRule/${id}`,
    method: "delete",
    data,
  });
}
// 提成规则添加
export function addRewardRule(data) {
  return request({
    url: `/Reward/Reward/addRewardRule`,
    method: "post",
    data,
  });
}
// 提成规则详情
export function getRewardRuleInfo(id, data) {
  return request({
    url: `/Reward/Reward/getRewardRuleInfo/${id}`,
    method: "get",
    data,
  });
}
// 提成规则列表
export function getAllRewardRule(data) {
  return request({
    url: `/Reward/Reward/getAllRewardRule`,
    method: "post",
    data,
  });
}
