import request from "@/utils/request";
/**
 * 订单管理
 * */
// 批量获取订单详情
export function getOrderInfoByIds(data) {
  return request({
    url: `/Order/Order/getOrderInfoByIds`,
    method: "post",
    data,
  });
}
// 新增销售单
export function addSalesOrder(id, data) {
  return request({
    url: `/Order/Order/addSalesOrder/${id}`,
    method: "post",
    data,
  });
}
// 确认收款(货到付款待出库订单)
export function updateOrderPayData(id, data) {
  return request({
    url: `/Order/Order/updateOrderPayData/${id}`,
    method: "put",
    data,
  });
}
// 增加打印次数
export function printIncr(id, data) {
  return request({
    url: `/Order/Order/printIncr/${id}`,
    method: "put",
    data,
  });
}
// 订单搜索  Order/Order/search/1
export function searchOrder(data) {
  return request({
    url: `/Order/Order/search`,
    method: "post",
    data,
  });
}
//订单搜索 Order/Order/keywordSearch
export function keywordSearch(data) {
  return request({
    url: `/Order/Order/keywordSearch`,
    method: "post",
    data,
  });
}
// 订单导出
export function exportKeywordSearch(data) {
  return request({
    url: `/Order/Order/keywordSearch`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 订单搜索导出
export function exportSearchOrder(data) {
  return request({
    url: `/Order/Order/search`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 获取所有订单
export function getAllOrder(data) {
  return request({
    url: `/Order/Order/getAllOrder`,
    method: "post",
    data,
  });
}
// 获取所有订单 导出
export function exportGetAllOrder(data) {
  return request({
    url: `/Order/Order/getAllOrder`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 审核订单
export function updateAuditStatus(id, data) {
  return request({
    url: `/Order/Order/updateAuditStatus/${id}`,
    method: "put",
    data,
  });
}
// 驳回订单
export function revokeAudit(id, data) {
  return request({
    url: `/Order/Order/revokeAudit/${id}`,
    method: "put",
    data,
  });
}
// 批量审核订单
export function batchUpdateAuditStatus(data) {
  return request({
    url: `/Order/Order/batchUpdateAuditStatus`,
    method: "post",
    data,
  });
}
// 删除订单
export function delOrder(id, data) {
  return request({
    url: `/Order/Order/delOrder/${id}`,
    method: "delete",
    data,
  });
}
// 订单分配业务员
export function setSalesMan(data) {
  return request({
    url: `/Order/Order/setSalesMan`,
    method: "post",
    data,
  });
}

// 获取订单详情
export function getOrderInfoById(id, data) {
  return request({
    url: `/Order/Order/getOrderInfoById/${id}`,
    method: "post",
    data,
  });
}
//销售订单导出
export function exprotsgetOrderProfit(data) {
  return request({
    url: `Order/Order/getOrderProfit`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 销售订单汇总
export function getAllOrderData(data) {
  return request({
    url: `/Order/Order/getAllOrderData`,
    method: "post",
    data,
  });
}
// 销售订单汇总打印
export function exportGetAllOrderData(data) {
  return request({
    url: `/Order/Order/getAllOrderData`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 添加订单
export function addOrder(id, data) {
  return request({
    url: `/Order/Order/addOrder/${id}`,
    method: "post",
    data,
  });
}
// 新添加订单
export function newAddOrder(data) {
  return request({
    url: `/Order/Order/add`,
    method: "post",
    data,
  });
}
// 订单编辑保存 before
export function saveEdit(data) {
  return request({
    url: `/Order/Order/saveEdit`,
    method: "post",
    data,
  });
}
// 订单编辑
export function orderEdit(id, data) {
  return request({
    url: `/Order/Order/orderEdit/${id}`,
    method: "post",
    data,
  });
}
// 新订单编辑
export function newOrderEdit(id, data) {
  return request({
    url: `/Order/Order/edit/${id}`,
    method: "post",
    data,
  });
}
// 订单再次出库
export function reStockOut(id, data) {
  return request({
    url: `/Order/Order/reStockOut/${id}`,
    method: "post",
    data,
  });
}
// 修改订单的发货信息
export function editOrderExpress(id, data) {
  return request({
    url: `/Order/Order/editOrderExpress/${id}`,
    method: "put",
    data,
  });
}
// 取消订单
export function updateOrderStatus(id, data) {
  return request({
    url: `/Order/Order/updateOrderStatus/${id}`,
    method: "put",
    data,
  });
}
// 销售退货添加
export function addOrderOut(data) {
  return request({
    url: `/Order/OrderReturn/addOrderReturn`,
    method: "post",
    data,
  });
}
// 销售退货单列表
export function getAllOrderOut(data) {
  return request({
    url: `/Order/OrderReturn/getAllOrderReturn`,
    method: "post",
    data,
  });
}
// 销售退货单列表
export function exportGetAllOrderOut(data) {
  return request({
    url: `/Order/OrderReturn/getAllOrderReturn`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 销售退货审核
export function auditOrderOut(id, data) {
  return request({
    url: `/Order/OrderReturn/auditOrderReturn/${id}`,
    method: "put",
    data,
  });
}
// 删除销售退货单
export function deleteOrderOut(id, data) {
  return request({
    url: `/Order/OrderReturn/deleteOrderReturn/${id}`,
    method: "get",
    data,
  });
}
// 销售退货详情
export function getOrderOut(id, data) {
  return request({
    url: `/Order/OrderReturn/getOrderReturn/${id}`,
    method: "get",
    data,
  });
}
// 销售退货修改
export function updateOrderOut(id, data) {
  return request({
    url: `/Order/OrderReturn/updateOrderReturn/${id}`,
    method: "put",
    data,
  });
}
// 销售订单毛利明细
export function getOrderProfit(data) {
  return request({
    url: `/Order/Order/getOrderProfit`,
    method: "put",
    data,
  });
}
// 退货单的搜索
export function searchAllOrderOut(data) {
  return request({
    url: `/Order/OrderReturn/searchAllOrderReturn`,
    method: "post",
    data,
  });
}
// 退货单的搜索
export function exportSearchAllOrderOut(data) {
  return request({
    url: `/Order/OrderReturn/searchAllOrderReturn`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 收银台订单
export function getAllOrderOrder(data) {
  return request({
    url: `/Order/Order/getAllOrder`,
    method: "post",
    data,
  });
}
// 核销订单
export function OrderVerification(id, data) {
  return request({
    url: `/Order/Order/verification/${id}`,
    method: "put",
    data,
  });
}
// 获取核销订单
export function getOrderByVerifyCode(data) {
  return request({
    url: `/Order/Order/getOrderByVerifyCode`,
    method: "post",
    data,
  });
}
// 收银台创建订单
export function cashierOrder(data) {
  return request({
    url: `/Order/Order/cashierOrder`,
    method: "post",
    data,
  });
}
// 销售日对账
export function statistics(data) {
  return request({
    url: `/Order/Order/statistics`,
    method: "post",
    data,
  });
}
// 缺货单
export function getDistributionAll(data) {
  return request({
    url: `/Order/Order/getDistributionAll`,
    method: "POST",
    data,
  });
}
// 抄码商品退款
export function retAmount(data) {
  return request({
    url: `Order/Order/retAmount`,
    method: "POST",
    data,
  });
}
//物流信息
export function addLogistics(data) {
  return request({
    url: `Order/Order/addLogistics`,
    method: "POST",
    data,
  });
}
//销售退货驳回
export function rejectOrderReturn(data) {
  return request({
    url: `Order/OrderReturn/rejectOrderReturn`,
    method: "put",
    data,
  });
}
//拣货中心待拣货单据列表
export function getAllOrderPicking(data) {
  return request({
    url: `Order/OrderPicking/getAllOrderPicking`,
    method: "post",
    data,
  });
}
//指定拣货单据
export function getOrderPickingInfo(id, data) {
  return request({
    url: `Order/OrderPicking/getOrderPickingInfo/${id}`,
    method: "post",
    data,
  });
}
// 待拣货明细
export function getAllPickingGoodsDetail(data) {
  return request({
    url: `Order/OrderPicking/getAllPickingGoodsDetail`,
    method: "post",
    data,
  });
}
