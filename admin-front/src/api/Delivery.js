import request from "@/utils/request";
// 配送线路列表
export function getAllDeliveryRoute(data) {
  return request({
    url: "Delivery/Delivery/getAllDeliveryRoute",
    method: "post",
    data,
  });
}
// 配送线路添加
export function addDeliveryRoute(data) {
  return request({
    url: "Delivery/Delivery/addDeliveryRoute",
    method: "post",
    data,
  });
}
// 配送线路详情
export function getDeliveryRouteInfo(id, data) {
  return request({
    url: `Delivery/Delivery/getDeliveryRouteInfo/${id}`,
    method: "get",
    data,
  });
}
// 配送线路修改
export function updateDeliveryRoute(id, data) {
  return request({
    url: `Delivery/Delivery/updateDeliveryRoute/${id}`,
    method: "put",
    data,
  });
}
//配送线路启用/禁用
export function enableDeliveryRoute(id, data) {
  return request({
    url: `Delivery/Delivery/enableDeliveryRoute/${id}`,
    method: "put",
    data,
  });
}
//配送线路删除
export function deleteDeliveryRoute(id, data) {
  return request({
    url: `Delivery/Delivery/deleteDeliveryRoute/${id}`,
    method: "delete",
    data,
  });
}
//配送线路客户
export function getDeliveryRouteCustomer(data) {
  return request({
    url: `Delivery/Delivery/getDeliveryRouteCustomer`,
    method: "post",
    data,
  });
}
// 配送线路客户排序
export function updateDeliveryRouteCustomer(id, data) {
  return request({
    url: `Delivery/Delivery/updateDeliveryRouteCustomer/${id}`,
    method: "put",
    data,
  });
}
//获取所有的待司机配送单
export function getAllDistribution(data) {
  return request({
    url: `Delivery/DistributionCentre/getAllDistribution`,
    method: "post",
    data,
  });
}
