import request from "@/utils/request";

/**
 * 车销管理 - 车辆信息管理
 */

// 添加车辆
export function addVehicle(data) {
  return request({
    url: `/CarSale/Vehicle/addVehicle`,
    method: "post",
    data,
  });
}

// 更新车辆信息
export function updateVehicle(vehicleId, data) {
  return request({
    url: `/CarSale/Vehicle/updateVehicle/${vehicleId}`,
    method: "put",
    data,
  });
}

// 删除车辆
export function deleteVehicle(vehicleId) {
  return request({
    url: `/CarSale/Vehicle/deleteVehicle/${vehicleId}`,
    method: "delete",
  });
}

// 获取车辆详情
export function getVehicleDetail(vehicleId) {
  return request({
    url: `/CarSale/Vehicle/getVehicleDetail/${vehicleId}`,
    method: "get",
  });
}

// 获取车辆列表
export function getAllVehicle(data) {
  return request({
    url: `/CarSale/Vehicle/getAllVehicle`,
    method: "post",
    data,
  });
}

// 更新车辆状态
export function updateVehicleStatus(vehicleId, data) {
  return request({
    url: `/CarSale/Vehicle/updateVehicleStatus/${vehicleId}`,
    method: "put",
    data,
  });
}

/**
 * 车销管理 - 利润分成层级管理
 */

// 添加利润分成层级
export function addProfitTier(data) {
  return request({
    url: `/CarSale/ProfitTier/addProfitTier`,
    method: "post",
    data,
  });
}

// 更新利润分成层级
export function updateProfitTier(tierId, data) {
  return request({
    url: `/CarSale/ProfitTier/updateProfitTier`,
    method: "post",
    data: { tierId, ...data },
  });
}

// 获取利润分成层级详情
export function getProfitTierInfo(tierId) {
  return request({
    url: `/CarSale/ProfitTier/getProfitTierDetail`,
    method: "post",
    data: { tierId },
  });
}

// 获取利润分成层级列表
export function getAllProfitTier(data) {
  return request({
    url: `/CarSale/ProfitTier/getProfitTierList`,
    method: "post",
    data,
  });
}

// 删除利润分成层级
export function deleteProfitTier(tierId) {
  return request({
    url: `/CarSale/ProfitTier/deleteProfitTier`,
    method: "post",
    data: { tierId },
  });
}

// 根据利润获取适用的利润分成层级
export function getProfitTierByProfit(profit, ownershipMode) {
  return request({
    url: `/CarSale/ProfitTier/getProfitTierByProfit`,
    method: "post",
    data: { profit, ownershipMode },
  });
}

/**
 * 车销管理 - 收入账户管理
 */

// 创建收入记录
export function createRevenue(data) {
  return request({
    url: `/CarSale/Revenue/createRevenue`,
    method: "post",
    data,
  });
}

// 更新收入记录
export function updateRevenue(revenueId, data) {
  return request({
    url: `/CarSale/Revenue/updateRevenue/${revenueId}`,
    method: "put",
    data,
  });
}

// 获取收入记录详情
export function getRevenueInfo(revenueId) {
  return request({
    url: `/CarSale/Revenue/getRevenueInfo/${revenueId}`,
    method: "get",
  });
}

// 获取收入记录列表
export function getAllRevenue(data) {
  return request({
    url: `/CarSale/Revenue/getAllRevenue`,
    method: "post",
    data,
  });
}

// 更新结算状态
export function updateSettlementStatus(revenueId, status, settlementDate) {
  return request({
    url: `/CarSale/Revenue/updateSettlementStatus/${revenueId}`,
    method: "put",
    data: { settlementStatus: status, settlementDate },
  });
}

/**
 * 审核收入记录
 * @param {number} revenueId 收入记录ID
 * @param {Object} data 审核数据
 * @param {number} data.auditStatus 审核状态：2-审核通过, 3-审核拒绝
 * @param {string} data.auditRemark 审核备注
 * @returns {Promise}
 */
export function auditRevenue(revenueId, data) {
  return request({
    url: `/CarSale/Revenue/auditRevenue/${revenueId}`,
    method: "put",
    data,
  });
}

/**
 * 批量审核收入记录
 * @param {Object} data 审核数据
 * @param {Array<number>} data.revenueIds 收入记录ID数组
 * @param {number} data.auditStatus 审核状态：2-审核通过, 3-审核拒绝
 * @param {string} data.auditRemark 审核备注
 * @returns {Promise}
 */
export function batchAuditRevenue(data) {
  return request({
    url: `/CarSale/Revenue/batchAuditRevenue`,
    method: "post",
    data,
  });
}

/**
 * 获取销售统计数据
 * @param {Object} params 查询参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @returns {Promise}
 */
export function getSalesStatistics(params) {
  return request({
    url: "/carsale/revenue/statistics",
    method: "get",
    params,
  });
}

/**
 * 获取销售员业绩统计数据
 * @param {Object} params 查询参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @returns {Promise}
 */
export function getSalesmanStatistics(params) {
  return request({
    url: "/carsale/revenue/salesman-statistics",
    method: "get",
    params,
  });
}

/**
 * 获取销售员销售明细
 * @param {Object} params 查询参数
 * @param {number} params.salesManId 销售员ID
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @returns {Promise}
 */
export function getSalesmanDetail(params) {
  return request({
    url: "/carsale/revenue/salesman-detail",
    method: "get",
    params,
  });
}

/**
 * 检查车牌号是否已存在
 * @param {string} licensePlate 车牌号
 * @param {number} vehicleId 车辆ID（编辑模式下排除自身）
 * @returns {Promise}
 */
export function checkLicensePlateExists(licensePlate, vehicleId = 0) {
  return request({
    url: `/CarSale/Vehicle/checkLicensePlateExists`,
    method: "post",
    data: { licensePlate, vehicleId },
  });
}

/**
 * 车销管理 - 业务员结算管理
 */

/**
 * 获取结算配置列表
 * @param {Object} params 查询参数
 * @param {Number} params.shopId 店铺ID（可选）
 * @param {Number} params.status 状态（可选）
 * @param {Number} params.page 页码（可选，默认1）
 * @param {Number} params.pageSize 每页数量（可选，默认20）
 * @returns {Promise} 返回Promise对象
 */
export function getSettlementConfigList(params) {
  return request({
    url: "/CarSale/StaffSettlement/getSettlementConfigList",
    method: "get",
    params,
  });
}

/**
 * 保存结算配置
 * @param {Object} data 结算配置数据
 * @param {Number} data.id 配置ID（新增时为null）
 * @param {Number} data.shopId 店铺ID
 * @param {String} data.financeType 财务类型
 * @param {Number} data.settlementType 结算类型（1:按时间周期，2:按业务完成情况）
 * @param {Number} data.cyclePeriod 周期类型（1:日结，2:周结，3:月结，4:季结，5:年结）
 * @param {Number} data.cycleDay 结算日（月结/季结/年结时生效）
 * @param {Number} data.businessCompleteThreshold 业务完成阈值
 * @param {Number} data.isAutoSettle 是否自动结算（1:是，0:否）
 * @returns {Promise} 返回Promise对象
 */
export function saveSettlementConfig(data) {
  return request({
    url: "/CarSale/StaffSettlement/saveSettlementConfig",
    method: "post",
    data,
  });
}

/**
 * 执行手动结算
 * @param {Object} data 结算参数
 * @param {Number} data.shopId 店铺ID
 * @param {Number} data.settlementType 结算类型（1:按时间周期，2:按业务完成情况）
 * @param {Number} data.periodStart 结算开始时间戳（settlementType=1时必填）
 * @param {Number} data.periodEnd 结算结束时间戳（settlementType=1时必填）
 * @param {String} data.periodDesc 结算周期描述（如"2024年7月结算"）
 * @param {Array} data.salesManIds 业务员ID列表（settlementType=2时必填）
 * @returns {Promise} 返回Promise对象
 */
export function executeSettlement(data) {
  // 根据结算类型调用不同的结算方法
  if (data.settlementType === 1) {
    return request({
      url: "/CarSale/StaffSettlement/settleByTimePeriod",
      method: "post",
      data: {
        shopId: data.shopId,
        periodStart: data.periodStart,
        periodEnd: data.periodEnd,
        periodDesc: data.periodDesc,
        salesManIds: data.salesManIds || [],
      },
    });
  } else if (data.settlementType === 2) {
    return request({
      url: "/CarSale/StaffSettlement/settleByBusinessComplete",
      method: "post",
      data: {
        shopId: data.shopId,
        salesManIds: data.salesManIds,
      },
    });
  }
}

/**
 * 获取员工应付单列表
 * @param {Object} params 查询参数
 * @param {Number} params.shopId 店铺ID（可选）
 * @param {Number} params.salesManId 业务员ID（可选）
 * @param {String} params.batchNo 批次号（可选）
 * @param {Number} params.status 状态（可选）
 * @param {Number} params.periodStart 结算开始时间戳（可选）
 * @param {Number} params.periodEnd 结算结束时间戳（可选）
 * @param {Number} params.page 页码（可选，默认1）
 * @param {Number} params.pageSize 每页数量（可选，默认20）
 * @returns {Promise} 返回Promise对象
 */
export function getStaffPayReceiptList(params) {
  return request({
    url: "/CarSale/StaffSettlement/getStaffPayReceiptList",
    method: "get",
    params,
  });
}

/**
 * 获取员工应付单详情
 * @param {Number} id 应付单ID
 * @returns {Promise} 返回Promise对象
 */
export function getStaffPayReceiptInfo(id) {
  return request({
    url: "/CarSale/StaffSettlement/getStaffPayReceiptInfo",
    method: "get",
    params: { id },
  });
}

/**
 * 审核员工应付单
 * @param {Object} data 审核参数
 * @param {Number} data.id 应付单ID
 * @param {Number} data.status 审核状态（1:通过，3:拒绝）
 * @param {String} data.auditRemark 审核备注
 * @param {Number} data.auditUserId 审核人ID
 * @param {String} data.auditUserName 审核人姓名
 * @returns {Promise} 返回Promise对象
 */
export function auditStaffPayReceipt(data) {
  return request({
    url: "/CarSale/StaffSettlement/auditStaffPayReceipt",
    method: "post",
    data,
  });
}

/**
 * 更新结算配置状态
 * @param {Number} id 配置ID
 * @param {Number} status 状态（1:启用，0:停用）
 * @returns {Promise} 返回Promise对象
 */
export function updateSettlementConfigStatus(id, status) {
  return request({
    url: "/CarSale/StaffSettlement/updateSettlementConfigStatus",
    method: "post",
    data: { id, status },
  });
}
