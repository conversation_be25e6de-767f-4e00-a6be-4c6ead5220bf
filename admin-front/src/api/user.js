import { tokenName } from "@/config/settings";
import request from "@/utils/request";

export async function login(data) {
  return request({
    url: "/UserCenter/UserCenterLogin/login",
    method: "post",
    data,
  });
}

export async function socialLogin(data) {
  return request({
    url: "/socialLogin",
    method: "post",
    data,
  });
}
export function getUserInfo(accessToken) {
  //此处为了兼容mock.js使用data传递accessToken，如果使用mock可以走headers
  return request({
    url: "/userInfo",
    method: "post",
    data: {
      [tokenName]: accessToken,
    },
  });
}

export function logout() {
  return request({
    url: "/logout",
    method: "post",
  });
}

export function register() {
  return request({
    url: "/register",
    method: "post",
  });
}

export function updateUserCenterData(data) {
  return request({
    url: "/Enterprise/UserEnterprise/updateUserCenterData",
    method: "post",
    data,
  });
}

// 首页概况
export function Enterprise(data) {
  return request({
    url: `/Enterprise/Enterprise/overview`,
    method: "post",
    data,
  });
}
// 获取企业列表
export function getAllEnterprise(data) {
  return request({
    url: "/Enterprise/UserEnterprise/getAllEnterprise",
    method: "get",
    data,
  });
}
// 所有企业类型
export function getAllEnterpriseCategory() {
  return request({
    url: "/Common/EnterpriseCategory/getAllEnterpriseCategory",
    method: "get",
  });
}
// 企业详情
export function getEnterpriseInfo() {
  return request({
    url: "/Enterprise/Enterprise/getEnterpriseInfo",
    method: "get",
  });
}
// 企业修改
export function updateEnterprise(data) {
  return request({
    url: "/Enterprise/Enterprise/updateEnterprise",
    method: "put",
    data,
  });
}
// 企业删除
export function deleteEnterprise(data) {
  return request({
    url: "/Enterprise/Enterprise/deleteEnterprise",
    method: "delete",
    data,
  });
}

// 企业添加
export function addEnterprise(data) {
  return request({
    url: "/Enterprise/UserEnterprise/addEnterprise",
    method: "post",
    data,
  });
}
// 获取企业列表（依据手机号）
export function getAllEnterprisePhone(data) {
  return request({
    url: "/UserCenter/UserCenterRegister/getAllEnterprise",
    method: "post",
    data,
  });
}
// 生成AUTHORIZATION
export function createToken(data) {
  return request({
    url: "/UserCenter/UserCenterLogin/createToken",
    method: "post",
    data,
  });
}
// 获取当前登录员工信息
export function getStaffByToken(roleType, data) {
  return request({
    url: `/Enterprise/Enterprise/getStaffByToken/${roleType}`,
    method: "get",
    data,
  });
}

// 获取当前登录供应商信息
export function getSupplierByToken(roleType, data) {
  return request({
    url: `/Enterprise/Enterprise/getSupplierByToken/${roleType}`,
    method: "get",
    data,
  });
}

//  当前登录用户的权限
export function getAclList(id, data) {
  return request({
    url: `Enterprise/UserEnterprise/getAclList/${id}`,
    method: "get",
    data,
  });
}

// 用户注册
export function Register(data) {
  return request({
    url: `UserCenter/UserCenterRegister/addUserCenter`,
    method: "post",
    data,
  });
}
// 判断手机号是否注册
export function mobileIsRegister(phone) {
  return request({
    url: `UserCenter/UserCenterRegister/mobileIsRegister/${phone}`,
    method: "get",
  });
}
// 忘记密码
export function forgetPassword(data) {
  return request({
    url: `UserCenter/UserCenterRegister/forgetPassword`,
    method: "post",
    data,
  });
}
// 小程序绑定手机号
export function appletsRegister(data) {
  return request({
    url: `UserCenter/UserCenterRegister/appletsRegister`,
    method: "post",
    data,
  });
}
//修改手机号(账号)
export function updateUserMobile(data) {
  return request({
    url: `UserCenter/UserCenterRegister/updateUserMobile`,
    method: "post",
    data,
  });
}
