import request from "@/utils/request";
/* ------财务类型----------- */
//  新增财务类型  Finance/FinanceType/addFinanceType  post
export function addFinanceType(data) {
  return request({
    url: `/Finance/FinanceType/addFinanceType`,
    method: "post",
    data,
  });
}
//  编辑财务类型  Finance/FinanceType/editFinanceType/1
export function editFinanceType(id, data) {
  return request({
    url: `/Finance/FinanceType/editFinanceType/${id}`,
    method: "put",
    data,
  });
}
// 删除 Finance/FinanceType/delFinanceType/1
export function delFinanceType(id, data) {
  return request({
    url: `/Finance/FinanceType/delFinanceType/${id}`,
    method: "delete",
    data,
  });
}
//  财务类型列表 Finance/FinanceType/getAllFinanceType
export function getAllFinanceType(data) {
  return request({
    url: `/Finance/FinanceType/getAllFinanceType`,
    method: "post",
    data,
  });
}
// 列表Finance/FinanceType/getAllFinanceTypeNoPage
export function getAllFinanceTypeNoPage(id, data) {
  return request({
    url: `/Finance/FinanceType/getAllFinanceTypeNoPage/${id}`,
    method: "post",
    data,
  });
}
//  启用和禁用  Finance/FinanceType/updateFinanceTypeStatus
export function updateFinanceTypeStatus(data) {
  return request({
    url: `/Finance/FinanceType/updateFinanceTypeStatus`,
    method: "post",
    data,
  });
}
// 默认  Finance/FinanceType/updateFinanceTypeDefaultStatus
export function updateFinanceTypeDefaultStatus(data) {
  return request({
    url: `/Finance/FinanceType/updateFinanceTypeDefaultStatus`,
    method: "post",
    data,
  });
}
/* -----------财务-------------- */
//  应收单列表  Finance/Receive/getAllReceive  post
export function getAllReceive(data) {
  return request({
    url: `/Finance/Receive/getAllReceive`,
    method: "post",
    data,
  });
}
//  应收单列表导出
export function exportGetAllReceive(data) {
  return request({
    url: `/Finance/Receive/getAllReceive`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 应收单搜索  Finance/Receive/search
export function searchAllReceive(data) {
  return request({
    url: `/Finance/Receive/search`,
    method: "post",
    data,
  });
}
// 应收单搜索 导出
export function exportSearchAllReceive(data) {
  return request({
    url: `/Finance/Receive/search`,
    responseType: "blob",
    method: "post",
    data,
  });
}
//  应收详情
export function getReceiveInfo(data) {
  return request({
    url: `/Finance/Receive/getReceiveInfo`,
    method: "post",
    data,
  });
}
// 更新应收单审核状态  Finance/Receive/updateReceiveStatus
export function updateReceiveStatus(data) {
  return request({
    url: `/Finance/Receive/updateReceiveStatus`,
    method: "post",
    data,
  });
}
// 根据订单no查询应收单
export function getReceivedByOrder(data) {
  return request({
    url: `/Finance/Received/getReceivedByOrder`,
    method: "post",
    data,
  });
}
//  应付单列表
export function getAllPay(data) {
  return request({
    url: `/Finance/Pay/getAllPay`,
    method: "post",
    data,
  });
}
//  应付单列表导出
export function exportGetAllPay(data) {
  return request({
    url: `/Finance/Pay/getAllPay`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 应付单搜索  Finance/Pay/search
export function searchPay(data) {
  return request({
    url: `/Finance/Pay/search`,
    method: "post",
    data,
  });
}
// 应付单搜索
export function exportSearchPay(data) {
  return request({
    url: `/Finance/Pay/search`,
    responseType: "blob",
    method: "post",
    data,
  });
}

// 更新应付单列表状态  Finance/Pay/updatePayStatus
export function updatePayStatus(data) {
  return request({
    url: `/Finance/Pay/updatePayStatus`,
    method: "post",
    data,
  });
}
// 应付详情
export function getPayInfo(data) {
  return request({
    url: `/Finance/Pay/getPayInfo`,
    method: "post",
    data,
  });
}
/* --------账户管理------------ */
// 新增账户 Finance/Account/addAccount
export function addAccount(data) {
  return request({
    url: `/Finance/Account/addAccount`,
    method: "post",
    data,
  });
}
//  账户列表 Finance/Account/getAllAccount
export function getAllAccount(data) {
  return request({
    url: `/Finance/Account/getAllAccount`,
    method: "post",
    data,
  });
}
//  编辑账户 Finance/Account/editAccount/1
export function editAccount(id, data) {
  return request({
    url: `/Finance/Account/editAccount/${id}`,
    method: "put",
    data,
  });
}
//  删除账户 Finance/Account/delAccount/1
export function delAccount(id, data) {
  return request({
    url: `/Finance/Account/delAccount/${id}`,
    method: "delete",
    data,
  });
}
//  设置默认账户 Finance/Account/updateAccountDefaultStatus
export function updateAccountDefaultStatus(data) {
  return request({
    url: `/Finance/Account/updateAccountDefaultStatus`,
    method: "post",
    data,
  });
}
// 账户的启用禁用  Finance/Account/updateAccountStatus
export function updateAccountStatus(data) {
  return request({
    url: `/Finance/Account/updateAccountStatus`,
    method: "post",
    data,
  });
}
//  账户的详情 Finance/Account/getAccountInfo/1
export function getAccountInfo(id, data) {
  return request({
    url: `/Finance/Account/getAccountInfo/${id}`,
    method: "get",
    data,
  });
}
// 账户明细  Finance/AccountDetail/getAllAccountDetail post
export function getAllAccountDetail(data) {
  return request({
    url: `/Finance/AccountDetail/getAllAccountDetail`,
    method: "post",
    data,
  });
}
/* -----收款单---------- */
//  新增收款单 Finance/Received/addReceived
export function addReceived(data) {
  return request({
    url: `/Finance/Received/addReceived`,
    method: "post",
    data,
  });
}
//  删除收款单(未审核通过情况下可执行)
export function deleteReceived(id, data) {
  return request({
    url: `/Finance/Received/deleteReceived/${id}`,
    method: "delete",
    data,
  });
}
// 导出
export function exportsgetAllReceived(data) {
  return request({
    url: `/Finance/Received/getAllReceived`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 收款单列表 Finance/Received/getAllReceived
export function getAllReceived(data) {
  return request({
    url: `Finance/Received/getAllReceived`,
    method: "post",
    data,
  });
}
//  收款单详情 Finance/Received/getReceivedInfo
export function getReceivedInfo(data) {
  return request({
    url: `/Finance/Received/getReceivedInfo`,
    method: "post",
    data,
  });
}
//  审核收款单  updateReceivedStatus
export function updateReceivedStatus(data) {
  return request({
    url: `/Finance/Received/updateReceivedStatus`,
    method: "post",
    data,
  });
}
// 审核收款申请单
export function updateReceiptRequisitionStatus(data) {
  return request({
    url: `Finance/ReceiptRequisition/updateReceiptRequisitionStatus`,
    method: "post",
    data,
  });
}
//  获取收款单暂存数据
export function getTempReceivedData(data) {
  return request({
    url: `/Finance/Received/getTempReceivedData`,
    method: "get",
    data,
  });
}
//  收款单搜索
export function ReceivedSearch(data) {
  return request({
    url: `/Finance/Received/search`,
    method: "post",
    data,
  });
}
// 编辑收款单 Finance/Received/editReceived/1
export function editReceived(id, data) {
  return request({
    url: `/Finance/Received/editReceived/${id}`,
    method: "post",
    data,
  });
}
/* -------------付款单---------- */
//  新增付 款单 Finance/Paid/addPaid
export function addPaid(data) {
  return request({
    url: `/Finance/Paid/addPaid`,
    method: "post",
    data,
  });
}

// 付款单搜索  Finance/Paid/search
export function searchPaid(data) {
  return request({
    url: `/Finance/Paid/search`,
    method: "post",
    data,
  });
}
// 付款单列表 Finance/Paid/getAllPaid
export function getAllPaid(data) {
  return request({
    url: `/Finance/Paid/getAllPaid`,
    method: "post",
    data,
  });
}
export function exportsgetAllPaid(data) {
  return request({
    url: `/Finance/Paid/getAllPaid`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 供应商付款单列表
export function getSupplierAllPaid(data) {
  return request({
    url: `/Finance/Paid/getSupplierAllPaid`,
    method: "post",
    data,
  });
}
export function exportsGetSupplierAllPaid(data) {
  return request({
    url: `/Finance/Paid/getSupplierAllPaid`,
    method: "post",
    responseType: "blob",
    data,
  });
}
//  付款单详情Finance/Paid/getPaidInfo
export function getPaidInfo(data) {
  return request({
    url: `/Finance/Paid/getPaidInfo`,
    method: "post",
    data,
  });
}
//  供应商付款单详情Finance/Paid/getSupplierPaidInfo
export function getSupplierPaidInfo(data) {
  return request({
    url: `/Finance/Paid/getSupplierPaidInfo`,
    method: "post",
    data,
  });
}
//  审核付款单 Finance/Paid/updatePaidStatus
export function updatePaidStatus(data) {
  return request({
    url: `/Finance/Paid/updatePaidStatus`,
    method: "post",
    data,
  });
}
//  获取付款单暂存数据 Finance/Paid/getTempPaidData
export function getTempPaidData(data) {
  return request({
    url: `/Finance/Paid/getTempPaidData`,
    method: "get",
    data,
  });
}
//  编辑付款单Finance/Paid/editPaid/1
export function editPaid(id, data) {
  return request({
    url: `/Finance/Paid/editPaid/${id}`,
    method: "post",
    data,
  });
}
// 客户余额表
export function getAllCustomerBalance(data) {
  return request({
    url: `/Finance/CustomerBalance/getAllCustomerBalance`,
    method: "post",
    data,
  });
}
export function exportgetAllCustomerBalance(data) {
  return request({
    url: `Finance/CustomerBalance/getAllCustomerBalance`,
    method: "post",
    responseType: "blob",
    data,
  });
}
//  客户余额明细表
export function getAllCustomerBalanceDetail(data) {
  return request({
    url: `/Finance/CustomerBalanceDetail/getAllCustomerBalanceDetail`,
    method: "post",
    data,
  });
}
//客户余额导出
export function exportgetAllCustomerBalanceDetail(data) {
  return request({
    url: `/Finance/CustomerBalanceDetail/getAllCustomerBalanceDetail`,
    method: "post",
    responseType: "blob",
    data,
  });
}
//  供应商余额列表
export function getAllSupplierBalance(data) {
  return request({
    url: `/Finance/SupplierBalance/getAllSupplierBalance`,
    method: "post",
    data,
  });
}
//  供应商余额列表导出
export function exportGetAllSupplierBalance(data) {
  return request({
    url: `/Finance/SupplierBalance/getAllSupplierBalance`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 供应商导出
export function exportgetAllSupplierBalanceDetail(data) {
  return request({
    url: `/Finance/SupplierBalanceDetail/getAllSupplierBalanceDetail`,
    method: "post",
    responseType: "blob",
    data,
  });
}
//  供应商余额明细
export function getAllSupplierBalanceDetail(data) {
  return request({
    url: `/Finance/SupplierBalanceDetail/getAllSupplierBalanceDetail`,
    method: "post",
    data,
  });
}
//  添加资金转账单
export function addAccountTransfer(data) {
  return request({
    url: `/Finance/AccountTransfer/addAccountTransfer`,
    method: "post",
    data,
  });
}
//  转账单详情
export function getAccountTransferInfo(id, data) {
  return request({
    url: `/Finance/AccountTransfer/getAccountTransferInfo/${id}`,
    method: "get",
    data,
  });
}
//  编辑资金转账单
export function editAccountTransfer(id, data) {
  return request({
    url: `/Finance/AccountTransfer/editAccountTransfer/${id}`,
    method: "put",
    data,
  });
}
//  列表
export function getAllAccountTransfer(data) {
  return request({
    url: `/Finance/AccountTransfer/getAllAccountTransfer`,
    method: "post",
    data,
  });
}
//  审核
export function updateAccountTransferStatus(id, data) {
  return request({
    url: `/Finance/AccountTransfer/updateAccountTransferStatus/${id}`,
    method: "put",
    data,
  });
}
//  获取暂存的数据 Finance/AccountTransfer/getTempAccountTransferData
export function getTempAccountTransferData(data) {
  return request({
    url: `/Finance/AccountTransfer/getTempAccountTransferData`,
    method: "post",
    data,
  });
}
// 财务日对账
export function getTodayStatistics(data) {
  return request({
    url: `Finance/Account/getTodayStatistics`,
    method: "post",
    data,
  });
}
// 收款申请单列表
export function getAllReceiptRequisition(data) {
  return request({
    url: `Finance/ReceiptRequisition/getAllReceiptRequisition`,
    method: "post",
    data,
  });
}
// 新增收款申请单
export function addReceiptRequisition(data) {
  return request({
    url: `Finance/ReceiptRequisition/addReceiptRequisition`,
    method: "post",
    data,
  });
}
// 编辑收款申请单
export function editReceiptRequisition(id, data) {
  return request({
    url: `Finance/ReceiptRequisition/editReceiptRequisition/${id}`,
    method: "post",
    data,
  });
}
// 收款申请单详情
export function getReceiptRequisitionInfo(id, data) {
  return request({
    url: `Finance/ReceiptRequisition/getReceiptRequisitionInfo/${id}`,
    method: "get",
    data,
  });
}
// 查看所有费用类型
export function getAllAccountType(data) {
  return request({
    url: `Finance/AccountType/getAllAccountType`,
    method: "post",
    data,
  });
}
// 新增费用类型
export function addAccountType(data) {
  return request({
    url: `Finance/AccountType/addAccountType`,
    method: "post",
    data,
  });
}
// 删除费用类型
export function delAccountType(id, data) {
  return request({
    url: `Finance/AccountType/delAccountType/${id}`,
    method: "delete",
    data,
  });
}
// 删除费用类型
export function getAccountTypeInfo(id, data) {
  return request({
    url: `Finance/AccountType/getAccountTypeInfo/${id}`,
    method: "post",
    data,
  });
}
// 编辑费用类型
export function editAccountType(data) {
  return request({
    url: `Finance/AccountType/editAccountType`,
    method: "post",
    data,
  });
}
// 所有费用单
export function getAllExpenseSingle(data) {
  return request({
    url: `Finance/ExpenseSingle/getAllExpenseSingle`,
    method: "post",
    data,
  });
}
// 新增费用单
export function addExpenseSingle(data) {
  return request({
    url: `Finance/ExpenseSingle/addExpenseSingle`,
    method: "post",
    data,
  });
}
// 获取指定费用单
export function getExpenseSingleInfo(id, data) {
  return request({
    url: `Finance/ExpenseSingle/getExpenseSingleInfo/${id}`,
    method: "get",
    data,
  });
}
// 编辑费用单
export function editExpenseSingle(id, data) {
  return request({
    url: `Finance/ExpenseSingle/editExpenseSingle/${id}`,
    method: "post",
    data,
  });
}
// 删除费用单
export function delExpenseSingle(id, data) {
  return request({
    url: `Finance/ExpenseSingle/delExpenseSingle/${id}`,
    method: "delete",
    data,
  });
}
// 费用单审核
export function updateExpenseStatus(data) {
  return request({
    url: `Finance/ExpenseSingle/updateExpenseStatus`,
    method: "post",
    data,
  });
}
// 所有退款单
export function getAllRefund(data) {
  return request({
    url: `Finance/Refund/getAllRefund`,
    method: "post",
    data,
  });
}
// 新增退款单
export function addRefund(data) {
  return request({
    url: `Finance/Refund/addRefund`,
    method: "post",
    data,
  });
}
// 获取指定退款单
export function getRefundInfo(data) {
  return request({
    url: `Finance/Refund/getRefundInfo`,
    method: "post",
    data,
  });
}
// 编辑退款单
export function editRefund(id, data) {
  return request({
    url: `Finance/Refund/editRefund/${id}`,
    method: "post",
    data,
  });
}
// 审核退款单
export function updateRefundStatus(data) {
  return request({
    url: `Finance/Refund/updateRefundStatus`,
    method: "post",
    data,
  });
}
//收款核销记录
export function getAllReceivedOffset(data) {
  return request({
    url: `Finance/Received/getAllReceivedOffset`,
    method: "post",
    data,
  });
}
//付款核销记录
export function getAllPaidOffset(data) {
  return request({
    url: `Finance/Paid/getAllPaidOffset`,
    method: "post",
    data,
  });
}
