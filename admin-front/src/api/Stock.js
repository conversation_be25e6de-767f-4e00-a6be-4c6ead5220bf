import request from "@/utils/request";
/**
 * 库存管理
 * */
/* -----------仓库管理----------------- */
// 仓库详情
export function getWarehouseInfo(id, data) {
  return request({
    url: `/Stock/Warehouse/getWarehouseInfo/${id}`,
    method: "get",
    data,
  });
}
// 仓库列表
export function getAllWarehouse(data) {
  return request({
    url: `/Stock/Warehouse/getAllWarehouse`,
    method: "post",
    data,
  });
}
// 仓库启用
export function enableWarehouse(data) {
  return request({
    url: `/Stock/Warehouse/enableWarehouse`,
    method: "post",
    data,
  });
}
// 单仓库价格启用
export function enablePricing(data) {
  return request({
    url: `/Stock/Warehouse/enablePricing`,
    method: "post",
    data,
  });
}
// 仓库修改
export function updateWarehouse(id, data) {
  return request({
    url: `/Stock/Warehouse/updateWarehouse/${id}`,
    method: "put",
    data,
  });
}
// 仓库删除
export function deleteWarehouse(id, data) {
  return request({
    url: `/Stock/Warehouse/deleteWarehouse/${id}`,
    method: "delete",
    data,
  });
}
// 仓库添加
export function addWarehouse(data) {
  return request({
    url: `/Stock/Warehouse/addWarehouse`,
    method: "post",
    data,
  });
}
// 仓库期初添加PageDesignEdit
export function addWarehouseBeginning(data) {
  return request({
    url: `/Stock/Warehouse/addWarehouseBeginning`,
    method: "post",
    data,
  });
}
/* -----------出库----------------- */
// 除了销售出库——————出库设置物流信息
export function stockaddLogistics(data) {
  return request({
    url: `/Stock/InventoryOut/addLogistics`,
    method: "post",
    data,
  });
}
// 出库列表
export function getAllSaleOut(data) {
  return request({
    url: `/Stock/InventoryOut/getAllInventoryOut`,
    method: "post",
    data,
  });
}
// 出库列表
export function exportGetAllSaleOut(data) {
  return request({
    url: `/Stock/InventoryOut/getAllInventoryOut`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 出库详情
export function getSaleOutInfo(id, data) {
  return request({
    url: `/Stock/InventoryOut/getInventoryOutInfo/${id}`,
    method: "get",
    data,
  });
}
// 出库详情
export function getSaleOutInfoT(data) {
  return request({
    url: `/Stock/InventoryOut/getInventoryOutInfo`,
    method: "post",
    data,
  });
}
// 出库搜索  Stock/InventoryOut/searchAllSaleOut
export function searchAllSaleOut(data) {
  return request({
    url: `/Stock/InventoryOut/searchAllInventoryOut`,
    method: "post",
    data,
  });
}
// 出库状态修改
export function updateSaleOutStatus(data) {
  return request({
    url: `/Stock/InventoryOut/updateInventoryOutStatus`,
    method: "post",
    data,
  });
}
// 出库测试接口
export function saveOutInventory(id, data) {
  return request({
    url: `/Stock/InventoryOut/saveOutInventory/${id}`,
    method: "put",
    data,
  });
}
// 出库统计 Stock/InventoryOut/statisticsAllInventoryOut
export function statisticsAllSaleOut(data) {
  return request({
    url: `/Stock/InventoryOut/statisticsAllInventoryOut`,
    method: "post",
    data,
  });
}
/* -----------入库----------------- */
// 入库驳回
export function rejectInventory(data) {
  return request({
    url: `/Stock/InventoryIn/rejectInventory`,
    method: "post",
    data,
  });
}
// 采购入库删除商品
export function deleteDetailByInventoryInId(data) {
  return request({
    url: `/Stock/InventoryIn/deleteDetailByInventoryInId`,
    method: "post",
    data,
  });
}
// 入库搜索
export function searchAllPurchaseIn(data) {
  return request({
    url: `/Stock/InventoryIn/searchAllInventoryIn`,
    method: "post",
    data,
  });
}
// 入库详情
export function getPurchaseInInfo(id, data) {
  return request({
    url: `/Stock/InventoryIn/getInventoryInInfo/${id}`,
    method: "put",
    data,
  });
}
// 入库详情2
export function getPurchaseInInfoT(data) {
  return request({
    url: `/Stock/InventoryIn/getInventoryInInfo`,
    method: "put",
    data,
  });
}
// 入库列表
export function getAllPurchaseIn(data) {
  return request({
    url: `/Stock/InventoryIn/getAllInventoryIn`,
    method: "post",
    data,
  });
}
// 入库列表
export function exportsGetAllPurchaseIn(data) {
  return request({
    url: `/Stock/InventoryIn/getAllInventoryIn`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 入库状态修改
export function updatePurchaseInStatus(data) {
  return request({
    url: `/Stock/InventoryIn/updateInventoryInStatus`,
    method: "post",
    data,
  });
}
// 入库统计  stock/InventoryIn/statisticsAllPurchaseIn
export function statisticsAllPurchaseIn(data) {
  return request({
    url: `/Stock/InventoryIn/statisticsAllInventoryIn`,
    method: "post",
    data,
  });
}
/* ------------库存列表------------ */
// 库存列表 Stock/Inventory/getInventoryAll  post
export function getInventoryAll(data) {
  return request({
    url: `/Stock/Inventory/getInventoryAll`,
    method: "post",
    data,
  });
}
// 库存列表
export function exportGetInventoryAll(data) {
  return request({
    url: `/Stock/Inventory/getInventoryAll`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 库存流水列表
export function getInventoryDetailsAll(data) {
  return request({
    url: `/Stock/Inventory/getInventoryDetailsAll`,
    method: "post",
    data,
  });
}
// 库存流水列表
export function exportGetInventoryDetailsAll(data) {
  return request({
    url: `/Stock/Inventory/getInventoryDetailsAll`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 库存流水搜索  stock/Inventory/searchAllInventoryDetails
export function searchAllInventoryDetails(data) {
  return request({
    url: `/Stock/Inventory/searchAllInventoryDetails`,
    method: "post",
    data,
  });
}
// 库存流水搜索
export function exportSearchAllInventoryDetails(data) {
  return request({
    url: `/Stock/Inventory/searchAllInventoryDetails`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 保质期查询(批次列表)
export function getAllBatch(data) {
  return request({
    url: `/Stock/Inventory/getAllBatch`,
    method: "post",
    data,
  });
}
// 搜索
export function searchAllInventoryBatch(data) {
  return request({
    url: `/Stock/Inventory/searchAllInventoryBatch`,
    method: "post",
    data,
  });
}
/* --------------盘点-------------------- */
// 盘点库存详情
export function getStocktakingInfo(id, data) {
  return request({
    url: `/Stock/Stocktaking/getStocktakingInfo/${id}`,
    method: "get",
    data,
  });
}
// 盘点库存修改
export function updateStocktaking(id, data) {
  return request({
    url: `/Stock/Stocktaking/updateStocktaking/${id}`,
    method: "put",
    data,
  });
}
// 盘点库存审核
export function auditStocktaking(id, data) {
  return request({
    url: `/Stock/Stocktaking/auditStocktaking/${id}`,
    method: "put",
    data,
  });
}
// 盘点库存列表
export function getAllStocktaking(data) {
  return request({
    url: `/Stock/Stocktaking/getAllStocktaking`,
    method: "post",
    data,
  });
}
// 盘点库存列表导出
export function exportGetAllStocktaking(data) {
  return request({
    url: `/Stock/Stocktaking/getAllStocktaking`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 盘点单搜索 stock/Stocktaking/searchAllStocktaking
export function searchAllStocktaking(data) {
  return request({
    url: `/Stock/Stocktaking/searchAllStocktaking`,
    method: "post",
    data,
  });
}
// 盘点单搜索 导出
export function exportSearchAllStocktaking(data) {
  return request({
    url: `/Stock/Stocktaking/searchAllStocktaking`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 盘点库存添加
export function addStocktaking(data) {
  return request({
    url: `/Stock/Stocktaking/addStocktaking`,
    method: "post",
    data,
  });
}
/* -----------库存汇总表----------------- */

// 库存汇总
export function inventoryStatistics(data) {
  return request({
    url: `/Stock/Inventory/inventoryStatistics`,
    method: "post",
    data,
  });
}
// 库存汇总
export function exportsInventoryStatistics(data) {
  return request({
    url: `/Stock/Inventory/inventoryStatistics`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 库存汇总搜索
export function searchInventoryStatistics(data) {
  return request({
    url: `/Stock/Inventory/searchInventoryStatistics`,
    method: "post",
    data,
  });
}
/* -----------其他----------------- */
//  采购单列表Stock
export function StockGetAllPurchase(data) {
  return request({
    url: `/Stock/InventoryIn/getAllPurchaseIn`,
    method: "post",
    data,
  });
}
// 库存分布
export function getWarehouseInventory(id, data) {
  return request({
    url: `/Stock/Inventory/getWarehouseInventory/${id}`,
    method: "get",
    data,
  });
}
/* ----------调拨仓库添加------------- */
//  仓库调拨添加  Stock/Allocate/addAllocate post
export function addAllocate(data) {
  return request({
    url: `/Stock/Allocate/addAllocate`,
    method: "post",
    data,
  });
}
//  仓库库存列表 stock/Inventory/getInventoryByWarehouseId post
export function getInventoryByWarehouseId(data) {
  return request({
    url: `/stock/Inventory/getInventoryByWarehouseId`,
    method: "post",
    data,
  });
}
//  仓库调拨列表 Stock/Allocate/getAllAllocate post
export function getAllAllocate(data) {
  return request({
    url: `/Stock/Allocate/getAllAllocate`,
    method: "post",
    data,
  });
}
//  仓库调拨删除 Stock/Allocate/deleteAllocate/1
export function deleteAllocate(id, data) {
  return request({
    url: `/Stock/Allocate/deleteAllocate/${id}`,
    method: "delete",
    data,
  });
}
//  获取详情  Stock/Allocate/getAllocateInfo/1 get
export function getAllocateInfo(id, data) {
  return request({
    url: `/Stock/Allocate/getAllocateInfo/${id}`,
    method: "get",
    data,
  });
}
//  编辑调拨单 Stock/Allocate/updateAllocate  post
export function updateAllocate(id, data) {
  return request({
    url: `/Stock/Allocate/updateAllocate/${id}`,
    method: "post",
    data,
  });
}
//  审核调拨单 Stock/Allocate/auditAllocate/2  put
export function auditAllocate(id, data) {
  return request({
    url: `/Stock/Allocate/auditAllocate/${id}`,
    method: "put",
    data,
  });
}
// 调拨单搜索  stock/Allocate/searchAllocate
export function searchAllocate(data) {
  return request({
    url: `/Stock/Allocate/searchAllocate`,
    method: "post",
    data,
  });
}
// 查询物料批次数据
export function getBatchByIds(data) {
  return request({
    url: `/Stock/Inventory/getBatchByIds`,
    method: "post",
    data,
  });
}
// 换算sku数量
export function getSkuNum(data) {
  return request({
    url: `/Stock/Inventory/getSkuNum`,
    method: "post",
    data,
  });
}
// 换算基本单位sku数量
export function getMasterSkuNum(data) {
  return request({
    url: `/Stock/Inventory/getMasterSkuNum`,
    method: "post",
    data,
  });
}
/* --------------库区库位-------------- */
// 库区添加
export function addReservoir(data) {
  return request({
    url: `/Stock/ReservoirArea/addReservoir`,
    method: "post",
    data,
  });
}
// 库区详情
export function getReservoirInfo(id, data) {
  return request({
    url: `/Stock/ReservoirArea/getReservoirInfo/${id}`,
    method: "get",
    data,
  });
}
// 库区列表
export function getAllReservoir(data) {
  return request({
    url: `/Stock/ReservoirArea/getAllReservoir`,
    method: "post",
    data,
  });
}
// 库区列表(不分页)
export function getListReservoir(data) {
  return request({
    url: `/Stock/ReservoirArea/getListReservoir`,
    method: "post",
    data,
  });
}
// 库区启用/禁用
export function enableReservoir(id, data) {
  return request({
    url: `/Stock/ReservoirArea/enableReservoir/${id}`,
    method: "put",
    data,
  });
}
// 库区修改
export function updateReservoir(id, data) {
  return request({
    url: `/Stock/ReservoirArea/updateReservoir/${id}`,
    method: "put",
    data,
  });
}
// 库区删除
export function deleteReservoir(id, data) {
  return request({
    url: `/Stock/ReservoirArea/deleteReservoir/${id}`,
    method: "get",
    data,
  });
}
// 库位列表
export function getAllStorageLocation(data) {
  return request({
    url: `/Stock/StorageLocation/getAllStorageLocation`,
    method: "post",
    data,
  });
}
// 库位添加
export function addStorageLocation(data) {
  return request({
    url: `/Stock/StorageLocation/addStorageLocation`,
    method: "post",
    data,
  });
}
// 库位详情
export function getStorageLocationInfo(id, data) {
  return request({
    url: `/Stock/StorageLocation/getStorageLocationInfo/${id}`,
    method: "post",
    data,
  });
}
// 库位启用/禁用
export function enableStorageLocation(id, data) {
  return request({
    url: `/Stock/StorageLocation/enableStorageLocation/${id}`,
    method: "post",
    data,
  });
}
// 库位修改
export function updateStorageLocation(id, data) {
  return request({
    url: `/Stock/StorageLocation/updateStorageLocation/${id}`,
    method: "post",
    data,
  });
}
// 根据skuId获取库区数量
export function getAreaDateBySkuId(data) {
  return request({
    url: `/Stock/Inventory/getAreaDateBySkuId`,
    method: "post",
    data,
  });
}
// 获取供应商库区库存
export function getSupplierAreaDateBySkuId(data) {
  return request({
    url: `/Stock/Inventory/getSupplierAreaDateBySkuId`,
    method: "post",
    data,
  });
}
// 批量下载库位二维码
export function batchDownloadQRCode(data) {
  return request({
    url: `/Stock/StorageLocation/batchDownloadQRCode`,
    method: "post",
    data,
    responseType: "blob", // 设置响应类型为blob以处理文件下载
  });
}
/* --------------库区库位end-------------- */
// 新增报损单
export function addReportLoss(data) {
  return request({
    url: `/Stock/ReportLoss/addReportLoss`,
    method: "post",
    data,
  });
}
// 报损单列表
export function getAllReportLoss(data) {
  return request({
    url: `/Stock/ReportLoss/getAllReportLoss`,
    method: "post",
    data,
  });
}
// 报损单详情
export function getReportLossInfo(id, data) {
  return request({
    url: `/Stock/ReportLoss/getReportLossInfo/${id}`,
    method: "get",
    data,
  });
}
// 报损单审核
export function auditReportLoss(id, data) {
  return request({
    url: `/Stock/ReportLoss/auditReportLoss/${id}`,
    method: "put",
    data,
  });
}
// 报损单删除
export function deleteReportLoss(id, data) {
  return request({
    url: `/Stock/ReportLoss/deleteReportLoss/${id}`,
    method: "delete",
    data,
  });
}
//成本均摊
export function countPurchaseCost(data) {
  return request({
    url: `Stock/InventoryIn/countPurchaseCost`,
    method: "post",
    data,
  });
}
// 仓库库存
export function exportgetInventoryByWarehouseId(data) {
  return request({
    url: `stock/Inventory/getInventoryByWarehouseId`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 调拨导出
export function exportgetAllAllocate(data) {
  return request({
    url: `Stock/Allocate/getAllAllocate`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 指定拣货单
export function getPickingInfo(id, data) {
  return request({
    url: `Stock/Picking/getPickingInfo/${id}`,
    method: "post",
    data,
  });
}
//新增拣货单
export function addPicking(data) {
  return request({
    url: `Stock/Picking/addPicking`,
    method: "post",
    data,
  });
}
//待处理拣货
export function getAllPicking(data) {
  return request({
    url: `Stock/Picking/getAllPicking`,
    method: "post",
    data,
  });
}
//拣货明细
export function getAllPickingGoodsDetail(data) {
  return request({
    url: `Stock/Picking/getAllPickingGoodsDetail`,
    method: "post",
    data,
  });
}

export function exportInitData() {
  return request({
    url: `Stock/Warehouse/exportInitData`,
    responseType: "blob",
    method: "get",
  });
}

// 获取供应商库存
export function getAllInventorySupplier(data) {
  return request({
    url: `Stock/InventorySupplier/getAllInventory`,
    method: "post",
    data,
  });
}

// 获取供应商库存
export function exportGetAllInventorySupplier(data) {
  return request({
    url: `Stock/InventorySupplier/getAllInventory`,
    responseType: "blob",
    method: "post",
    data,
  });
}

// 获取供应商库存明细
export function getAllInventorySupplierDetails(data) {
  return request({
    url: `/Stock/InventorySupplier/getAllDetails`,
    method: "post",
    data,
  });
}

// 导出供应商库存明细
export function exportGetAllInventorySupplierDetails(data) {
  return request({
    url: `/Stock/InventorySupplier/getAllDetails`,
    responseType: "blob",
    method: "post",
    data,
  });
}

// 供应商申请付款
export function applySupplierPayment(data) {
  return request({
    url: `/Stock/InventorySupplier/generatePayable`,
    method: "post",
    data,
  });
}
