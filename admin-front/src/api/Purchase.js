import request from "@/utils/request";

//  采购汇总
//  采购商品汇总  purchase/purchase/getAllPurchaseByFields
export function getAllPurchaseByFields(data) {
  return request({
    url: `/purchase/purchase/getAllPurchaseByFields`,
    method: "post",
    data,
  });
}
//采购汇总导出
export function exportgetAllPurchaseByFields(data) {
  return request({
    url: `/purchase/purchase/getAllPurchaseByFields`,
    method: "post",
    responseType: "blob",
    data,
  });
}
/* -----------供应商----------------- */

//  添加供应商  Purchase/Supplier/addSupplier  post
export function addSupplier(data) {
  return request({
    url: `/Purchase/Supplier/addSupplier`,
    method: "post",
    data,
  });
}
//  供应商详情  Purchase/Supplier/getSupplierInfoById/1  get
export function getSupplierInfoById(id, data) {
  return request({
    url: `/Purchase/Supplier/getSupplierInfoById/${id}`,
    method: "get",
    data,
  });
}

// 编辑供应商  Purchase/Supplier/editSupplier/1  put
export function editSupplier(id, data) {
  return request({
    url: `/Purchase/Supplier/editSupplier/${id}`,
    method: "put",
    data,
  });
}
//  供应商显示/隐藏  Purchase/Supplier/updateEnableStatus/1 put
export function supplierEnableStatus(id, data) {
  return request({
    url: `/Purchase/Supplier/updateEnableStatus/${id}`,
    method: "put",
    data,
  });
}
// 删除供应商  Purchase/Supplier/delSupplier/1  del
export function delSupplier(id, data) {
  return request({
    url: `/Purchase/Supplier/delSupplier/${id}`,
    method: "delete",
    data,
  });
}
//  供应商列表   Purchase/Supplier/getAllSupplier  post
export function getAllSupplier(data) {
  return request({
    url: `/Purchase/Supplier/getAllSupplier`,
    method: "post",
    data,
  });
}

/* -----采购订单-------- */
//  采购订单一键完结
export function retMoney(id, data) {
  return request({
    url: `/Purchase/Purchase/retMoney/${id}`,
    method: "post",
    data,
  });
}
//  采购订单再次入库
export function reStockIn(id, data) {
  return request({
    url: `/Purchase/Purchase/reStockIn/${id}`,
    method: "post",
    data,
  });
}
//  增加采购订单  Purchase/Purchase/addPurchase  post
export function addPurchase(data) {
  return request({
    url: `/Purchase/Purchase/addPurchase`,
    method: "post",
    data,
  });
}
// 采购单详情  Purchase/Purchase/getPurchaseInfoById/1 get
export function getPurchaseInfoById(id, data) {
  return request({
    url: `/Purchase/Purchase/getPurchaseInfoById/${id}`,
    method: "get",
    data,
  });
}
// 更新审核状态  Purchase/Purchase/updateAuditStatus/1 put
export function updatePurchaseStatus(id, data) {
  return request({
    url: `/Purchase/Purchase/updateAuditStatus/${id}`,
    method: "put",
    data,
  });
}
//    删除采购单  Purchase/Purchase/delPurchase/1  del
export function delPurchase(id, data) {
  return request({
    url: `/Purchase/Purchase/delPurchase/${id}`,
    method: "delete",
    data,
  });
}
//  采购单列表  Purchase/Purchase/getAllPurchase  post
export function getAllPurchase(data) {
  return request({
    url: `/Purchase/Purchase/getAllPurchase`,
    method: "post",
    data,
  });
}
//  采购单列表 导出
export function exportGetAllPurchase(data) {
  return request({
    url: `/Purchase/Purchase/getAllPurchase`,
    responseType: "blob",
    method: "post",
    data,
  });
}
//  编辑采购单列表  Purchase/Purchase/editPurchase/1  put
export function editPurchase(id, data) {
  return request({
    url: `/Purchase/Purchase/editPurchase/${id}`,
    method: "put",
    data,
  });
}
// 采购明细
export function getAllPurchaseDetails(data) {
  return request({
    url: `/Purchase/Purchase/getAllPurchaseDetails`,
    method: "post",
    data,
  });
}
// 采购明细
export function exportGetAllPurchaseDetails(data) {
  return request({
    url: `/Purchase/Purchase/getAllPurchaseDetails`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 采购明细搜索
export function searchAllPurchaseDetails(data) {
  return request({
    url: `/Purchase/Purchase/searchAllPurchaseDetails`,
    method: "post",
    data,
  });
}
// 采购明细搜索
export function exportSearchAllPurchaseDetails(data) {
  return request({
    url: `/Purchase/Purchase/searchAllPurchaseDetails`,
    method: "post",
    responseType: "blob",
    data,
  });
}

/* --------------采购退货单-------------------- */
//  增加采购退货单  Purchase/PurchaseOut/addPurchaseOut post
export function addPurchaseOut(data) {
  return request({
    url: `/Purchase/PurchaseOut/addPurchaseOut`,
    method: "post",
    data,
  });
}
//  采购退货单详情  Purchase/PurchaseOut/getPurchaseOutInfoById/1 get
export function getPurchaseOutInfoById(id, data) {
  return request({
    url: `/Purchase/PurchaseOut/getPurchaseOutInfoById/${id}`,
    method: "get",
    data,
  });
}
//  更新审核状态  Purchase/PurchaseOut/updateAuditStatus/1  put
export function updatePurchaseOut(id, data) {
  return request({
    url: `/Purchase/PurchaseOut/updateAuditStatus/${id}`,
    method: "put",
    data,
  });
}
//  删除采购退货单  Purchase/PurchaseOut/delPurchaseOut/1  del
export function delPurchaseOut(id, data) {
  return request({
    url: `/Purchase/PurchaseOut/delPurchaseOut/${id}`,
    method: "delete",
    data,
  });
}
//  采购退货单列表  Purchase/PurchaseOut/getAllPurchaseOut  post
export function getAllPurchaseOut(data) {
  return request({
    url: `/Purchase/PurchaseOut/getAllPurchaseOut`,
    method: "post",
    data,
  });
}
//  采购退货单列表
export function exportGetAllPurchaseOut(data) {
  return request({
    url: `/Purchase/PurchaseOut/getAllPurchaseOut`,
    responseType: "blob",
    method: "post",
    data,
  });
}
// 编辑采购退货单  Purchase/PurchaseOut/editPurchase/1   put
export function editPurchaseOut(id, data) {
  return request({
    url: `/Purchase/PurchaseOut/editPurchase/${id}`,
    method: "put",
    data,
  });
}
// 采购退货页面统计数据  Purchase/Purchase/getAllData  get
export function getAllData(data) {
  return request({
    url: `/Purchase/Purchase/getAllData`,
    method: "get",
    data,
  });
}
// 采购退货需要的采购订单列表
export function getAllPurchaseAndDetails(data) {
  return request({
    url: `/Purchase/Purchase/getAllPurchaseAndDetails`,
    method: "post",
    data,
  });
}
// 采购订单详情和批次数据(采购退货单用)  Purchase/Purchase/getPurchaseAndBatchInfoById
export function getPurchaseAndBatchInfoById(data) {
  return request({
    url: `/Purchase/Purchase/getPurchaseAndBatchInfoById`,
    method: "post",
    data,
  });
}
// 添加供应商为用户
export function addSupplierUserCenter(id, data) {
  return request({
    url: `Purchase/Supplier/addSupplierUserCenter/${id}`,
    method: "get",
    data,
  });
}
// 导入供应商
export function supplierImport(data) {
  return request({
    url: `Purchase/Supplier/supplierImport`,
    method: "post",
    data,
  });
}
