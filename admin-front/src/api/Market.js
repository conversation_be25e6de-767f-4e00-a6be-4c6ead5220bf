import request from "@/utils/request";
/* -----------营销-------------------- */
// 优惠券搜素  Market/Coupon/search  post
export function searchMarket(data) {
  return request({
    url: `/Market/Coupon/search`,
    method: "post",
    data,
  });
}
//  添加优惠券 Market/Coupon/add post
export function addMarket(data) {
  return request({
    url: `/Market/Coupon/add`,
    method: "post",
    data,
  });
}
//  优惠券详情  Market/Coupon/getInfo/1 get
export function getInfo(id, data) {
  return request({
    url: `/Market/Coupon/getInfo/${id}`,
    method: "post",
    data,
  });
}
//  编辑 Market/Coupon/edit/1 put
export function editMarket(id, data) {
  return request({
    url: `/Market/Coupon/edit/${id}`,
    method: "post",
    data,
  });
}
//  审核 Market/Coupon/updateAuditStatus
export function updateAuditStatusMarket(data) {
  return request({
    url: `/Market/Coupon/updateAuditStatus`,
    method: "put",
    data,
  });
}
//  优惠券启用/禁用
export function CouponUpdateEnableStatus(data) {
  return request({
    url: `/Market/Coupon/updateEnableStatus`,
    method: "put",
    data,
  });
}
//  删除Market/Coupon/del/1
export function delMarket(id, data) {
  return request({
    url: `/Market/Coupon/del/${id}`,
    method: "delete",
    data,
  });
}
//  列表 Market/Coupon/getAll
export function getAll(data) {
  return request({
    url: `/Market/Coupon/getAll`,
    method: "post",
    data,
  });
}
// 发放记录  Market/UserCoupon/getAll
export function getUserCoupon(data) {
  return request({
    url: `/Market/UserCoupon/getAll`,
    method: "post",
    data,
  });
}
// 营销活动上下架 Market/Activity/updateEnableStatus
export function activityStatus(data) {
  return request({
    url: `/Market/Activity/updateEnableStatus`,
    method: "put",
    data,
  });
}

/* ------------商品促销----------------- */
// 促销活动搜索  Market/Activity/search
export function searchActivity(data) {
  return request({
    url: `/Market/Activity/getAll`,
    method: "post",
    data,
  });
}
//  新增 Market/Activity/add
export function addActivity(data) {
  return request({
    url: `/Market/Activity/add`,
    method: "post",
    data,
  });
}
//  详情 Market/Activity/getInfo/1
export function getInfoActivity(id, data) {
  return request({
    url: `/Market/Activity/getInfo/${id}`,
    method: "get",
    data,
  });
}
//  编辑 Market/Activity/edit/1
export function editActivity(id, data) {
  return request({
    url: `/Market/Activity/edit/${id}`,
    method: "put",
    data,
  });
}
//  审核 Market/Activity/updateAuditStatus
export function updateActivityStatus(data) {
  return request({
    url: `/Market/Activity/updateAuditStatus`,
    method: "put",
    data,
  });
}
//  删除 Market/Activity/del/1
export function delActivity(id, data) {
  return request({
    url: `/Market/Activity/del/${id}`,
    method: "delete",
    data,
  });
}
//  列表
export function getAllActivity(data) {
  return request({
    url: `/Market/Activity/getAll`,
    method: "post",
    data,
  });
}
//  会员卡
// 获取会员卡暂存数据 Market/VipCard/getTempData
export function getTempVipData(data) {
  return request({
    url: `/Market/VipCard/getTempData`,
    method: "get",
    data,
  });
}
//  新增会员卡 Market/VipCard/addVipCard
export function addVipCard(data) {
  return request({
    url: `/Market/VipCard/addVipCard`,
    method: "post",
    data,
  });
}
//  会员卡详情 Market/VipCard/getVipCardInfo/1
export function getVipCardInfo(id, data) {
  return request({
    url: `/Market/VipCard/getVipCardInfo/${id}`,
    method: "get",
    data,
  });
}
//  编辑会员卡 Market/VipCard/editVipCard/1
export function editVipCard(id, data) {
  return request({
    url: `/Market/VipCard/editVipCard/${id}`,
    method: "post",
    data,
  });
}
//  删除会员卡 Market/VipCard/delVipCard/1
export function delVipCard(id, data) {
  return request({
    url: `/Market/VipCard/delVipCard/${id}`,
    method: "delete",
    data,
  });
}
//  启用/禁用 Market/VipCard/updateVipCardStatus
export function updateVipCardStatus(data) {
  return request({
    url: `/Market/VipCard/updateVipCardStatus`,
    method: "post",
    data,
  });
}
//  会员卡列表 Market/VipCard/getAllVipCard
export function getAllVipCard(data) {
  return request({
    url: `/Market/VipCard/getAllVipCard`,
    method: "post",
    data,
  });
}
//  领取记录 Market/VipCard/receiveRecord
export function receiveRecord(data) {
  return request({
    url: `/Market/VipCard/receiveRecord`,
    method: "post",
    data,
  });
}
//指定客户会员卡一键失效 Market/VipCard/invalidVipCard
export function invalidVipCard(data) {
  return request({
    url: `/Market/VipCard/invalidVipCard`,
    method: "post",
    data,
  });
}
//  组合套餐列表 Market/ComBinPackage/getAll
export function getAllComBinPackage(data) {
  return request({
    url: `/Market/ComBinPackage/getAll`,
    method: "post",
    data,
  });
}
//  组合套餐列表 启用/禁用 Market/ComBinPackage/enable/1
export function enableComBinPackage(id, data) {
  return request({
    url: `/Market/ComBinPackage/enable/${id}`,
    method: "put",
    data,
  });
}
//  组合套餐添加  Market/ComBinPackage/add
export function addComBinPackage(data) {
  return request({
    url: `Market/ComBinPackage/add`,
    method: "post",
    data,
  });
}
//  组合套餐编辑  Market/ComBinPackage/add
export function editComBinPackage(id, data) {
  return request({
    url: `Market/ComBinPackage/edit/${id}`,
    method: "put",
    data,
  });
}
// 组合套餐详情
export function getComBinPackage(id, data) {
  return request({
    url: `Market/ComBinPackage/get/${id}`,
    method: "post",
    data,
  });
}

// 满额换购活动列表
export function getAllFullBuy(data) {
  return request({
    url: "/Market/FullBuy/getAllFullBuy",
    method: "post",
    data,
  });
}

// 满额换购活动详情
export function getFullBuyInfo(id) {
  return request({
    url: `/Market/FullBuy/getInfo/${id}`,
    method: "get",
  });
}

// 满额换购活动新增
export function addFullBuy(data) {
  return request({
    url: "/Market/FullBuy/addFullBuy",
    method: "post",
    data,
  });
}

// 满额换购活动编辑
export function editFullBuy(id, data) {
  return request({
    url: `/Market/FullBuy/editFullBuy/${id}`,
    method: "post",
    data,
  });
}

// 满额换购活动状态更新
export function updateStatusFullBuy(id, data) {
  return request({
    url: `/Market/FullBuy/updateStatus/${id}`,
    method: "post",
    data,
  });
}

// 满额换购活动删除
export function delFullBuy(id, data) {
  return request({
    url: `/Market/FullBuy/delFullBuy/${id}`,
    method: "post",
    data,
  });
}

// 满额换购活动审核
export function auditFullBuy(id, data) {
  return request({
    url: `/Market/FullBuy/auditFullBuy/${id}`,
    method: "post",
    data,
  });
}

// 满赠活动列表
export function getAllFullGive(data) {
  return request({
    url: "/Market/FullGive/getAllFullGive",
    method: "post",
    data,
  });
}

// 满赠活动详情
export function getFullGiveInfo(id) {
  return request({
    url: `/Market/FullGive/get/${id}`,
    method: "get",
  });
}

// 满赠活动新增
export function addFullGive(data) {
  return request({
    url: "/Market/FullGive/add",
    method: "post",
    data,
  });
}

// 满赠活动编辑
export function editFullGive(id, data) {
  return request({
    url: `/Market/FullGive/edit/${id}`,
    method: "post",
    data,
  });
}

export function updateStatusFullGive(id, data) {
  return request({
    url: `/Market/FullGive/updateStatus/${id}`,
    method: "put",
    data,
  });
}

export function delFullGive(id, data) {
  return request({
    url: `/Market/FullGive/del/${id}`,
    method: "delete",
    data,
  });
}
