import request from "@/utils/request";
/* -----------商城设置----------------- */
// 消息模板管理
// 模板消息保存
export function saveTemplateMessage(data) {
  return request({
    url: `/System/TemplateMessage/saveTemplateMessage`,
    method: "post",
    data,
  });
}
// 消息模板获取
export function getTemplateMessage(data) {
  return request({
    url: `/System/TemplateMessage/getTemplateMessage`,
    method: "get",
    data,
  });
}
// 一键配置模板消息
export function autoSystemTemplateMessage(data) {
  return request({
    url: `/System/TemplateMessage/autoSystemTemplateMessage`,
    method: "get",
    data,
  });
}
//  系统设置详情
export function getSystemSettingsInfo(type, data) {
  return request({
    url: `/SystemSettings/System/getSystemSettingsInfo/${type}`,
    method: "get",
    data,
  });
}
//  系统设置详情
export function updateSystemSettings(id, data) {
  if (id) {
    return request({
      url: `/SystemSettings/System/updateSystemSettings/${id}`,
      method: "put",
      data,
    });
  } else {
    return request({
      url: `/SystemSettings/System/updateSystemSettings`,
      method: "put",
      data,
    });
  }
}
//  系统设置详情
export function getSystemSettingsInfo2(data) {
  return request({
    url: `/SystemSettings/System/getSystemSettingsInfo/4`,
    method: "get",
    data,
  });
}
//  字节跳动基本设置
export function saveByteDanceSetting(id, data) {
  return request({
    url: `/SystemSettings/System/saveByteDanceSetting/${id}`,
    method: "post",
    data,
  });
}
//  微信小程序授权
export function preAuthCode(data) {
  return request({
    url: `/SystemSettings/System/preAuthCode`,
    method: "post",
    data,
  });
}
// 分类设置编辑
export function setClassSetting(data) {
  return request({
    url: `/System/ClassSetting/setClassSetting`,
    method: "post",
    data,
  });
}
// 获取分类设置
export function getClassSettingInfo(data) {
  return request({
    url: `/System/ClassSetting/getClassSettingInfo`,
    method: "get",
    data,
  });
}
/* ---------客户类型--------------- */
// 新增
export function addCustomerSource(data) {
  return request({
    url: `/System/CustomerSource/addCustomerSource`,
    method: "post",
    data,
  });
}

// 编辑
export function editCustomerSource(id, data) {
  return request({
    url: `/System/CustomerSource/editCustomerSource/${id}`,
    method: "post",
    data,
  });
}
// 详情
export function getCustomerSourceInfo(id, data) {
  return request({
    url: `/System/CustomerSource/getCustomerSourceInfo/${id}`,
    method: "get",
    data,
  });
}
// 删除
export function delCustomerSource(id) {
  return request({
    url: `/System/CustomerSource/delCustomerSource/${id}`,
    method: "delete",
  });
}
// 启用禁用
export function updateCustomerSourceStatus(data) {
  return request({
    url: `/System/CustomerSource/updateCustomerSourceStatus`,
    method: "post",
    data,
  });
}
// 获取客户类型列表
export function getAllCustomerSource(data) {
  return request({
    url: `/System/CustomerSource/getAllCustomerSource`,
    method: "post",
    data,
  });
}
// 客户类型无分页
export function getCustomerSourceList(data) {
  return request({
    url: `/System/CustomerSource/getCustomerSourceList`,
    method: "post",
    data,
  });
}
// 默认
export function updateCustomerSourceDefaultStatus(data) {
  return request({
    url: `/System/CustomerSource/updateDefaultStatus`,
    method: "post",
    data,
  });
}
/* -----------模块管理----------------- */
// 添加模块
export function addModule(data) {
  return request({
    url: `/System/Module/addModule`,
    method: "post",
    data,
  });
}
// 模块详情
export function ModuleGetModuleInfo(id, data) {
  return request({
    url: `/System/Module/getModuleInfo/${id}`,
    method: "get",
    data,
  });
}
// 修改模块
export function editModule(id, data) {
  return request({
    url: `/System/Module/editModule/${id}`,
    method: "post",
    data,
  });
}
// 删除模块
export function delModule(id, data) {
  return request({
    url: `/System/Module/delModule/${id}`,
    method: "delete",
    data,
  });
}
// 模块启用禁用
export function updateModuleStatus(data) {
  return request({
    url: `/System/Module/updateModuleStatus`,
    method: "post",
    data,
  });
}
// 模块列表
export function getAllModule(data) {
  return request({
    url: `/System/Module/getAllModule`,
    method: "post",
    data,
  });
}
/* -----------模版设置----------------- */
//  添加系统模板
export function SystemTemplateAdd(data) {
  return request({
    url: `/System/SystemTemplate/add`,
    method: "post",
    data,
  });
}
//  获取系统模板
export function SystemTemplategetAll(data) {
  return request({
    url: `/System/SystemTemplate/getAll`,
    method: "post",
    data,
  });
}
// 提交审核
export function submitAudit(data) {
  return request({
    url: `/System/EnterpriseBindTemplate/submitAudit`,
    method: "post",
    data,
  });
}
// 查询审核状态
export function getAuditStatus(data) {
  return request({
    url: `/System/EnterpriseBindTemplate/getAuditStatus`,
    method: "post",
    data,
  });
}
// 发布通过审核的小程序 】
export function release(data) {
  return request({
    url: `/System/EnterpriseBindTemplate/release`,
    method: "post",
    data,
  });
}
// 提交代码
export function submitCode(id, data) {
  return request({
    url: `/System/EnterpriseBindTemplate/submitCode/${id}`,
    method: "post",
    data,
  });
}
// 获取提交代码后小程序状态
export function getWxStatus(data) {
  return request({
    url: `/System/EnterpriseBindTemplate/getWxStatus`,
    method: "get",
    data,
  });
}
// 企业使用模版启用/停用
export function bindTemplate(id, data) {
  return request({
    url: `/System/EnterpriseBindTemplate/bindTemplate/${id}`,
    method: "put",
    data,
  });
}
// 添加企业模版模块数据
export function EnterpriseTemplateModuleAdd(data) {
  return request({
    url: `/System/EnterpriseTemplateModule/add`,
    method: "post",
    data,
  });
}
// 获取当前企业下改模块的数据
export function getModuleInfo(id, data) {
  return request({
    url: `/System/EnterpriseTemplateModule/getModuleInfo/${id}`,
    method: "post",
    data,
  });
}
// 编辑企业模版模块数据
export function EnterpriseTemplateModuleEdit(data) {
  return request({
    url: `/System/EnterpriseTemplateModule/edit`,
    method: "put",
    data,
  });
}
// 获取模版模块
export function TemplateModuleGetAll(id, data) {
  return request({
    url: `/System/TemplateModule/getAll/${id}`,
    method: "get",
    data,
  });
}
// 获取企业模版
export function getAllTemplate(data) {
  return request({
    url: `/System/EnterpriseBindTemplate/getAllTemplate`,
    method: "get",
    data,
  });
}
/* ------------页面设计----------------- */
// 保存页面
export function PageSave(data) {
  return request({
    url: `/System/Page/save`,
    method: "post",
    data,
  });
}
// 获取所有页面
export function PageGetAll(data) {
  return request({
    url: `/System/Page/getAll`,
    method: "post",
    data,
  });
}
// 获取页面详情
export function getPageInfo(id, data) {
  return request({
    url: `/System/Page/getPageInfo/${id}`,
    method: "get",
    data,
  });
}
// 删除页面
export function PageDel(id, data) {
  return request({
    url: `/System/Page/del/${id}`,
    method: "delete",
    data,
  });
}
// 启用/停用页面
export function PageUpdateEnableStatus(data) {
  return request({
    url: `/System/Page/updateEnableStatus`,
    method: "put",
    data,
  });
}
// 获取专题活动
export function getSpecial(data) {
  return request({
    url: `/System/Page/getSpecial`,
    method: "post",
    data,
  });
}

/* --------------支付方式-------------------- */
// 企业配置支付方式  System/EnterpriseBindPayment/set  post
export function setPay(data) {
  return request({
    url: `/System/EnterpriseBindPayment/set`,
    method: "post",
    data,
  });
}
// 获取配置详情  System/PaymentSetting/getPaymentInfoById/1  get
export function getPayment(id, data) {
  return request({
    url: `/System/PaymentSetting/getPaymentInfoById/${id}`,
    method: "get",
    data,
  });
}
// 增加支付方式
export function addPayment(data) {
  return request({
    url: `/System/PaymentSetting/addPayment`,
    method: "post",
    data,
  });
}
// 支付方式详情
export function getPaymentInfoById(id, data) {
  return request({
    url: `/System/PaymentSetting/getPaymentInfoById/${id}`,
    method: "get",
    data,
  });
}
// 支付方式列表
export function getAllPayment(data) {
  return request({
    url: `/System/PaymentSetting/getAllPayment`,
    method: "post",
    data,
  });
}
// 支付方式 启用/禁用
export function PaymentupdateEnableStatus(id, data) {
  return request({
    url: `/System/PaymentSetting/updateEnableStatus/${id}`,
    method: "put",
    data,
  });
}
// 默认设置
export function updateDefaultStatus(id, data) {
  return request({
    url: `/System/PaymentSetting/updateDefaultStatus/${id}`,
    method: "put",
    data,
  });
}
// 编辑支付方式
export function savePaySetting(id, data) {
  return request({
    url: `/System/PaymentSetting/savePaySetting/${id}`,
    method: "put",
    data,
  });
}
// 删除支付方式
export function delPayment(id, data) {
  return request({
    url: `/System/PaymentSetting/delPayment/${id}`,
    method: "delete",
    data,
  });
}
/* --------------配送方式-------------------- */
// 配送方式添加
export function addDelivery(data) {
  return request({
    url: `/System/DeliverySetting/addDelivery`,
    method: "post",
    data,
  });
}
// 获取配送方式详情
export function getDeliveryInfoById(id, data) {
  return request({
    url: `/System/DeliverySetting/getDeliveryInfoById/${id}`,
    method: "get",
    data,
  });
}
// 配送方式列表
export function getAllDelivery(data) {
  return request({
    url: `/System/DeliverySetting/allDelivery`,
    method: "post",
    data,
  });
}
export function AllDelivery(data) {
  return request({
    url: `/System/DeliverySetting/getAllDelivery`,
    method: "post",
    data,
  });
}
// 配送方式启用/禁用
export function DeliveryupdateEnableStatus(id, data) {
  return request({
    url: `/System/DeliverySetting/updateEnableStatus/${id}`,
    method: "put",
    data,
  });
}
// 设置默认/取消默认
export function DeliveryupdateDefaultStatus(id, data) {
  return request({
    url: `/System/DeliverySetting/updateDefaultStatus/${id}`,
    method: "put",
    data,
  });
}
// 编辑配送信息
export function editDelivery(id, data) {
  return request({
    url: `/System/DeliverySetting/editDelivery/${id}`,
    method: "put",
    data,
  });
}
// 删除配送
export function delDelivery(id, data) {
  return request({
    url: `/System/DeliverySetting/delDelivery/${id}`,
    method: "delete",
    data,
  });
}
// 获取运费模版
export function getAllExpressRule(data) {
  return request({
    url: `/System/DeliverySetting/getAllExpressRule`,
    method: "post",
    data,
  });
}
// 设置默认运费模版
export function setDefaultRule(id, data) {
  return request({
    url: `/System/DeliverySetting/setDefaultRule/${id}`,
    method: "post",
    data,
  });
}
// 配置配送方式
export function setDataDelivery(id, data) {
  return request({
    url: `/System/DeliverySetting/setData/${id}`,
    method: "post",
    data,
  });
}

// 配送方式详情
export function DeliveryInfo(id, data) {
  return request({
    url: `/System/DeliverySetting/getInfo/${id}`,
    method: "get",
    data,
  });
}
// 删除运费模版
export function delDeliveryRule(id, data) {
  return request({
    url: `/System/DeliverySetting/delDeliveryRule/${id}`,
    method: "put",
    data,
  });
}
// 获取运费模版详情
export function getRuleInfo(id, data) {
  return request({
    url: `/System/DeliverySetting/getRuleInfo/${id}`,
    method: "get",
    data,
  });
}
// 获取快递公司列表
export function getAllExpress(data) {
  return request({
    url: `/System/DeliverySetting/getAllExpress`,
    method: "post",
    data,
  });
}
/* -----------自提点------------ */
// 设置自提点
export function setSelfData(data) {
  return request({
    url: `/System/DeliverySetting/setSelfData`,
    method: "post",
    data,
  });
}
// 删除自提点
export function delDeliverySelfRule(id, data) {
  return request({
    url: `/System/DeliverySetting/delDeliverySelfRule/${id}`,
    method: "delete",
    data,
  });
}
// 获取自提点详情
export function getSelfRuleInfo(id, data) {
  return request({
    url: `/System/DeliverySetting/getSelfRuleInfo/${id}`,
    method: "get",
    data,
  });
}
// 获取自提点
export function getAllSelfExpressRule(data) {
  return request({
    url: `/System/DeliverySetting/getAllSelfExpressRule`,
    method: "get",
    data,
  });
}
/* -------基本设置------ */
//  基本设置  System/BasicSetup/setting  POST
export function setting(data) {
  return request({
    url: `/System/BasicSetup/setting`,
    method: "post",
    data,
  });
}
//  获取基本设置  System/BasicSetup/getBasicSetup  get
export function getBasicSetup(data) {
  return request({
    url: `/System/BasicSetup/getBasicSetup`,
    method: "get",
    data,
  });
}
/* -------公告设置------ */
//  公告列表 System/Announcement/getAllAnnouncement
export function getAllAnnouncemen(data) {
  return request({
    url: `/System/Announcement/getAllAnnouncement`,
    method: "post",
    data,
  });
}
//  公告列表 System/Announcement/getAllAnnouncement
export function getAllAnnouncement(data) {
  return request({
    url: `/System/Announcement/getAllAnnouncement`,
    method: "post",
    data,
  });
}
//  公告添加 System/Announcement/addAnnouncement
export function addAnnouncement(data) {
  return request({
    url: `/System/Announcement/addAnnouncement`,
    method: "post",
    data,
  });
}
//  公告删除 System/Announcement/delAnnouncement/1
export function delAnnouncement(id, data) {
  return request({
    url: `/System/Announcement/delAnnouncement/${id}`,
    method: "delete",
    data,
  });
}
//  公告编辑 System/Announcement/editAnnouncement/1
export function editAnnouncement(id, data) {
  return request({
    url: `/System/Announcement/editAnnouncement/${id}`,
    method: "put",
    data,
  });
}
//  公告启用/禁用 System/Announcement/onAnnouncement/1
export function onAnnouncement(id, data) {
  return request({
    url: `/System/Announcement/onAnnouncement/${id}`,
    method: "get",
    data,
  });
}
// 公告是否弹出
export function upAnnouncement(id, data) {
  return request({
    url: `/System/Announcement/upAnnouncement/${id}`,
    method: "get",
    data,
  });
}
//  公告详情 System/Announcement/getAnnouncementInfo/1
export function getAnnouncementInfo(id, data) {
  return request({
    url: `/System/Announcement/getAnnouncementInfo/${id}`,
    method: "get",
    data,
  });
}
/* -------模板设置------ */
// 单据模板
export function getInfoReceiptTemplate(id, data) {
  return request({
    url: `/System/ReceiptTemplate/getInfo/${id}`,
    method: "get",
    data,
  });
}
// 打印模板列表
export function getAllReceiptTemplate(data) {
  return request({
    url: `/System/ReceiptTemplate/getAll`,
    method: "get",
    data,
  });
}
// 模板保存
export function saveReceiptTemplate(data) {
  return request({
    url: `/System/ReceiptTemplate/save`,
    method: "post",
    data,
  });
}
// 获取单据类型模板id
export function getTemplateInfo(id, data) {
  return request({
    url: `/System/ReceiptTemplate/getTemplateInfo/${id}`,
    method: "post",
    data,
  });
}
// 流程设置
// 流程获取
export function getAllProcessSetting(data) {
  return request({
    url: `System/ProcessSetting/getAll`,
    method: "get",
    data,
  });
}
// 提交
export function setAllProcessSetting(data) {
  return request({
    url: `System/ProcessSetting/set`,
    method: "put",
    data,
  });
}

// 获取语音配置
export function NoticeSetting(data) {
  return request({
    url: `System/NoticeSetting/get`,
    method: "get",
    data,
  });
}

// 配置语音
export function NoticeSettingSet(data) {
  return request({
    url: `System/NoticeSetting/set`,
    method: "post",
    data,
  });
}
// 物流提醒
export function updateLogisticsReminder(data) {
  return request({
    url: `System/DeliverySetting/updateLogisticsReminder`,
    method: "post",
    data,
  });
}
// 消息推送列表页
export function getAllSystemPushMessage(data) {
  return request({
    url: `System/SystemPushMessage/getAll`,
    method: "post",
    data,
  });
}
// 消息推送启用/禁用
export function updateEnableStatus(data) {
  return request({
    url: `System/SystemPushMessage/updateEnableStatus`,
    method: "post",
    data,
  });
}
// 消息推送微信订阅消息/短信推送消息 启用/停用
export function updatePushEnableStatus(data) {
  return request({
    url: `System/SystemPushMessage/updatePushEnableStatus`,
    method: "post",
    data,
  });
}
// 获取推送消息设置详情
export function settingDetail(id, data) {
  return request({
    url: `System/SystemPushMessage/settingDetail/${id}`,
    method: "post",
    data,
  });
}
// 编辑推送消息设置
export function editSystemPushMessage(data) {
  return request({
    url: `System/SystemPushMessage/edit`,
    method: "post",
    data,
  });
}
//一键获取微信模板Id
export function autoCreateWeiXinTemplateId(id, data) {
  return request({
    url: `System/SystemPushMessage/autoCreateWeiXinTemplateId/${id}`,
    method: "post",
    data,
  });
}
