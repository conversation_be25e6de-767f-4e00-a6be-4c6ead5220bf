import request from "@/utils/request";

/**
 * 地区
 * */
// 获取省份
export function forApi() {
  return request({
    url: "/Common/Test/forApi",
    method: "get",
  });
}
// 获取省份
export function getAllProvince() {
  return request({
    url: "/Common/SysAreaChina/getAllProvince",
    method: "get",
  });
}
// 获取省下的市
export function getAllCityByProvinceCode(code) {
  return request({
    url: `/Common/SysAreaChina/getAllCityByProvinceCode/${code}`,
    method: "get",
  });
}
// 获取市下的区
export function getAllAreaByCityCode(code) {
  return request({
    url: `/Common/SysAreaChina/getAllAreaByCityCode/${code}`,
    method: "get",
  });
}
/**
 * 接收微信授权码
 * */
export function authorizationCode(data) {
  return request({
    url: `/common/WeiXinOpen/authorizationCode`,
    method: "post",
    data,
  });
}
/**
 * 上传证书文件
 * */
export function uploadFile(data) {
  console.log("data", data);
  return request({
    url: `/Common/Upload/uploadFile`,
    method: "post",
    data,
  });
}
/**
 * 生成七牛上传凭证
 * */
// 生成七牛上传凭证
export function uploadToken(data) {
  return request({
    url: "/Common/Upload/uploadToken",
    method: "post",
    data,
  });
}
// 七牛删除
export function UploadDel(data) {
  return request({
    url: "/Common/Upload/delete",
    method: "post",
    data,
  });
}
//  获取消息列表
export function getAllMessage(data) {
  return request({
    url: "/Message/Message/getAllMessage",
    method: "post",
    data,
  });
}
//  标记已读消息
export function receiveMessage(id, data) {
  return request({
    url: `/Message/Message/receiveMessage/${id}`,
    method: "get",
    data,
  });
}
/**
 * 增加单据打印次数
 * */
export function PrintNumPrintIncr(data) {
  return request({
    url: `/Common/PrintNum/printIncr`,
    method: "post",
    data,
  });
}

// 标签页打印
export function printLabel(data) {
  return request({
    url: `/Common/PrintNum/printLabel`,
    method: "post",
    data,
  });
}

// 单据小票统一打印
export function toPrint(data) {
  return request({
    url: `/Common/Receipt/toPrint`,
    method: "post",
    data,
  });
}
// 发送短信验证码
export function sendMobileCode(data) {
  return request({
    url: `/Common/SmsCode/sendMobileCode`,
    method: "post",
    data,
  });
}
// 刷新token
export function flushToken(data) {
  return request({
    url: `/Common/Common/flushToken`,
    method: "post",
    data,
  });
}
// 所有企业类型  Common/EnterpriseCategory/getAllEnterpriseCategory get
export function getAllEnterpriseCategory(data) {
  return request({
    url: `/Common/EnterpriseCategory/getAllEnterpriseCategory`,
    method: "get",
    data,
  });
}
// 价格暂存添加
export function addMoneyPauseSave(data) {
  return request({
    url: `/Common/PauseSave/addMoneyPauseSave`,
    method: "post",
    data,
  });
}
// 获取价格暂存
export function getMoneyPauseSave(data) {
  return request({
    url: `/Common/PauseSave/getMoneyPauseSave`,
    method: "post",
    data,
  });
}
//  暂存按钮  /Common/PauseSave/addPauseSave
export function addPauseSave(data) {
  return request({
    url: `/Common/PauseSave/addPauseSave`,
    method: "post",
    data,
  });
}
//  获取暂存信息
export function getPauseSave(data) {
  return request({
    url: `/Common/PauseSave/getPauseSave`,
    method: "post",
    data,
  });
}
//  暂存删除 Common/PauseSave/delPauseSave
export function delPauseSave(data) {
  return request({
    url: `/Common/PauseSave/delPauseSave`,
    method: "post",
    data,
  });
}
// 登录注册日志
export function getAllLog(data) {
  return request({
    url: `/Log/LoginLog/getAllLog`,
    method: "post",
    data,
  });
}
// 生成小程序二维码
export function createwxaqrcode(data) {
  return request({
    url: `/Common/Login/createwxaqrcode`,
    method: "post",
    data,
  });
}

// 生成二维码
export function createQrCode(data) {
  return request({
    url: `/Common/QrCode/getQRcode`,
    method: "post",
    responseType: "blob",
    data,
  });
}

// 绑定易联云W1打印机
export function bindYilianyunW1(data) {
  return request({
    url: `/Common/CloudPrint/bindYilianyunW1`,
    method: "post",
    data,
  });
}

// 解绑易联云W1打印机
export function unbindYilianyunW1(data) {
  return request({
    url: `/Common/CloudPrint/unbindYilianyunW1`,
    method: "post",
    data,
  });
}

// 获取易联云W1打印机绑定状态
export function getYilianyunW1BindStatus(data) {
  return request({
    url: `/Common/CloudPrint/getYilianyunW1BindStatus`,
    method: "get",
    data,
  });
}
