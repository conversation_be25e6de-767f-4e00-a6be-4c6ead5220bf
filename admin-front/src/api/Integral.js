import request from "@/utils/request";
/* --------------积分商城----------------- */
// 积分商品禁用
export function enableIntegralGoods(id, data) {
  return request({
    url: `/Integral/IntegralGoods/enableIntegralGoods/${id}`,
    method: "get",
    data,
  });
}
// 积分商品添加
export function addIntegralGoods(data) {
  return request({
    url: `/Integral/IntegralGoods/addIntegralGoods`,
    method: "post",
    data,
  });
}
// 积分商品详情
export function getIntegralGoodsInfo(id, data) {
  return request({
    url: `/Integral/IntegralGoods/getIntegralGoodsInfo/${id}`,
    method: "get",
    data,
  });
}
// 积分商品列表
export function getAllIntegralGoods(data) {
  return request({
    url: `/Integral/IntegralGoods/getAllIntegralGoods`,
    method: "post",
    data,
  });
}
// 积分商品修改
export function updateIntegralGoods(id, data) {
  return request({
    url: `/Integral/IntegralGoods/updateIntegralGoods/${id}`,
    method: "put",
    data,
  });
}
// 积分商品删除
export function deleteIntegralGoods(id, data) {
  return request({
    url: `/Integral/IntegralGoods/deleteIntegralGoods/${id}`,
    method: "delete",
    data,
  });
}
// 积分兑换列表
export function getAllIntegralGoodsExchange(data) {
  return request({
    url: `/Integral/IntegralGoods/getAllIntegralGoodsExchange`,
    method: "post",
    data,
  });
}
// 积分兑换状态修改
export function updateIntegralGoodsExchange(id, data) {
  return request({
    url: `/Integral/IntegralGoods/updateIntegralGoodsExchange/${id}`,
    method: "put",
    data,
  });
}
// 积分规则详情
export function getIntegralRuleInfo(id, data) {
  return request({
    url: `/Integral/IntegralGoods/getIntegralRuleInfo/${id}`,
    method: "post",
    data,
  });
}
// 积分规则列表
export function getAllIntegralRule(data) {
  return request({
    url: `/Integral/IntegralGoods/getAllIntegralRule`,
    method: "post",
    data,
  });
}

// 积分规则修改
export function updateIntegralRule(id, data) {
  return request({
    url: `/Integral/IntegralGoods/updateIntegralRule/${id}`,
    method: "put",
    data,
  });
}

// 积分规则禁用
export function enableIntegralRule(id, data) {
  return request({
    url: `/Integral/IntegralGoods/enableIntegralRule/${id}`,
    method: "get",
    data,
  });
}

// 积分规则删除
export function deleteIntegralRule(id, data) {
  return request({
    url: `/Integral/IntegralGoods/deleteIntegralRule/${id}`,
    method: "get",
    data,
  });
}

// 积分规则添加
export function addIntegralRule(data) {
  return request({
    url: `/Integral/IntegralGoods/addIntegralRule`,
    method: "post",
    data,
  });
}
