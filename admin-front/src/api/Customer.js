import request from "@/utils/request";

// 获取暂存 Customer/Customer/getTempData
export function getTempCustomerData(data) {
  return request({
    url: "/Customer/Customer/getTempData",
    method: "get",
    data,
  });
}
// 客户审核/待审核 Customer/Customer/updateCustomerCheckStatus
export function updateCustomerCheckStatus(data) {
  return request({
    url: "/Customer/Customer/updateCustomerCheckStatus",
    method: "post",
    data,
  });
}
/* --- 客户管理 --- */
//  新建客户 Customer/Customer/addCustomer post
export function addCustomer(data) {
  return request({
    url: "/Customer/Customer/addCustomer",
    method: "post",
    data,
  });
}
//  客户详情
export function getCustomerInfo(id, data) {
  return request({
    url: `/Customer/Customer/getCustomerInfo/${id}`,
    method: "get",
    data,
  });
}
//  编辑客户  Customer/Customer/editCustomer/3  post
export function editCustomer(id, data) {
  return request({
    url: `/Customer/Customer/editCustomer/${id}`,
    method: "post",
    data,
  });
}
//  首字母搜索客户
export function CustomerInitialsSearch(data) {
  return request({
    url: `/Customer/Customer/initialsSearch`,
    method: "post",
    data,
  });
}
//  客户列表  Customer/Customer/getAllCustomer post
export function getAllCustomer(data) {
  return request({
    url: `/Customer/Customer/getAllCustomer`,
    method: "post",
    data,
  });
}
// 客户列表导出
export function customerExport(data) {
  return request({
    url: `/Customer/Customer/export`,
    method: "post",
    responseType: "blob",
    data,
  });
}
//  删除客户 Customer/Customer/delCustomer/3 get
// export function delCustomer(id, data) {
//   return request({
//     url: `/Customer/Customer/delCustomer/${id}`,
//     method: "get",
//     data,
//   });
// }
//  客户停用禁用  Customer/Customer/updateCustomerStatus  post
export function updateCustomerStatus(data) {
  return request({
    url: `/Customer/Customer/updateCustomerStatus`,
    method: "post",
    data,
  });
}
// 添加客户标签  Customer/Customer/addCustomerTag  post
export function addCustomerTag(data) {
  return request({
    url: `/Customer/Customer/addCustomerTag`,
    method: "post",
    data,
  });
}
//  删除客户标签  Customer/Customer/delCustomerTag post
export function delCustomerTag(data) {
  return request({
    url: `/Customer/Customer/delCustomerTag`,
    method: "post",
    data,
  });
}
//  新增客户联系人  Customer/CustomerContact/addCustomerContact  post
export function addCustomerContact(data) {
  return request({
    url: `/Customer/CustomerContact/addCustomerContact`,
    method: "post",
    data,
  });
}
// 客户搜索 Customer/Customer/search post
export function searchCustomer(data) {
  return request({
    url: `/Customer/Customer/search`,
    method: "post",
    data,
  });
}
// 今日下单客户经纬度分部
export function getCustomerLocation(data) {
  return request({
    url: `/Customer/Customer/getCustomerLocation`,
    method: "post",
    data,
  });
}
// 后台批量分配部门及业务员
export function batchEditCustomer(data) {
  return request({
    url: `/Customer/Customer/batchEditCustomer`,
    method: "post",
    data,
  });
}
/* --------------客户收货地址-------------------- */
// 客户收货地址添加
export function addShippingAddress(data) {
  return request({
    url: `/Customer/ShippingAddress/addShippingAddress`,
    method: "post",
    data,
  });
}
// 客户收货地址删除
export function deleteShippingAddress(id, data) {
  return request({
    url: `/Customer/ShippingAddress/deleteShippingAddress/${id}`,
    method: "delete",
    data,
  });
}
// 客户收货地址修改
export function updateShippingAddress(data) {
  return request({
    url: `/Customer/ShippingAddress/updateShippingAddress`,
    method: "put",
    data,
  });
}
// 客户收货地址详情
export function getShippingAddressInfo(id, data) {
  return request({
    url: `/Customer/ShippingAddress/getShippingAddressInfo/${id}`,
    method: "get",
    data,
  });
}
// 客户收货地址列表
export function getAllShippingAddress(id, data) {
  return request({
    url: `/Customer/ShippingAddress/getAllShippingAddress/${id}`,
    method: "post",
    data,
  });
}
// 客户查询 Customer/Customer/query
export function query(data) {
  return request({
    url: `/Customer/Customer/query`,
    method: "post",
    data,
  });
}
// 获取跟进记录列表
export function getAllCustomerCommunication(data) {
  return request({
    url: `Customer/CustomerCommunication/getAllCustomerCommunication`,
    method: "post",
    data,
  });
}
// 添加跟进记录
export function addCustomerCommunication(data) {
  return request({
    url: `Customer/CustomerCommunication/addCustomerCommunication`,
    method: "post",
    data,
  });
}
// 跟进记录删除
export function delCustomerCommunication(id, data) {
  return request({
    url: `Customer/CustomerCommunication/delCustomerCommunication/${id}`,
    method: "delete",
    data,
  });
}
// 跟进记录修改
export function updateCustomerCommunication(id, data) {
  return request({
    url: `Customer/CustomerCommunication/updateCustomerCommunication/${id}`,
    method: "put",
    data,
  });
}
// 后台所有客户标签组
export function getAllCustomerTagLib(data) {
  return request({
    url: `Customer/CustomerTagLib/getAllCustomerTagLib`,
    method: "get",
    data,
  });
}
// 新增客户标签
export function addCustomerTagLib(data) {
  return request({
    url: `Customer/CustomerTagLib/addCustomerTagLib`,
    method: "post",
    data,
  });
}
// 删除客户标签
export function delCustomerTagLib(data) {
  return request({
    url: `Customer/CustomerTagLib/delCustomerTagLib`,
    method: "post",
    data,
  });
}
// 获取指定用户标签
export function getCustomerTagLibInfo(id, data) {
  return request({
    url: `Customer/CustomerTagLib/getCustomerTagLibInfo/${id}`,
    method: "post",
    data,
  });
}
// 编辑客户标签
export function editCustomerTagLib(data) {
  return request({
    url: `Customer/CustomerTagLib/editCustomerTagLib`,
    method: "post",
    data,
  });
}
// 需求提报
export function getAllCustomerdemand(data) {
  return request({
    url: `/Customer/CustomerDemand/getAllCustomerdemand`,
    method: "post",
    data,
  });
}
// 需求提报详情
export function getCustomerdemandInfo(id, data) {
  return request({
    url: `/Customer/CustomerDemand/getCustomerdemandInfo/${id}`,
    method: "get",
    data,
  });
}
// 修改客户指定标签
export function updateCustomerTagLibById(data) {
  return request({
    url: `Customer/CustomerTagLib/updateCustomerTagLibById`,
    method: "post",
    data,
  });
}
// 客户购买列表
export function searchCustomerBuyLog(data) {
  return request({
    url: `Customer/Customer/searchCustomerBuyLog`,
    method: "post",
    data,
  });
}
export function exportsearchCustomerBuyLog(data) {
  return request({
    url: `Customer/Customer/searchCustomerBuyLog`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 客户浏览列表
export function getAllCustomerVisitsLog(data) {
  return request({
    url: `Customer/CustomerVisitsLog/getAllCustomerVisitsLog`,
    method: "post",
    data,
  });
}
// 客户余额明细
export function getAllMemberBalanceDetail(data) {
  return request({
    url: "Customer/MemberBalanceDetail/getAllMemberBalanceDetail",
    method: "post",
    data,
  });
}
//客户积分明细
export function getAllCustomerIntegralDesc(data) {
  return request({
    url: "Customer/Customer/getAllCustomerIntegralDesc",
    method: "post",
    data,
  });
}
//修改积分
export function updateCustomerIntegral(data) {
  return request({
    url: "Customer/Customer/updateCustomerIntegral",
    method: "post",
    data,
  });
}
// 多长时间未下单客户统计
export function noOrderCustomer(data) {
  return request({
    url: "Customer/Customer/noOrderCustomer",
    method: "post",
    data,
  });
}
// 距离上一次上单多久未下单客户统计
export function intervalNoOrderCustomer(data) {
  return request({
    url: "Customer/Customer/intervalNoOrderCustomer",
    method: "post",
    data,
  });
}
// 后台充值
export function rechargeMemberBalance(data) {
  return request({
    url: "Customer/Customer/rechargeMemberBalance",
    method: "post",
    data,
  });
}
// 拉新统计
export function recommenderStatic(data) {
  return request({
    url: "Customer/Customer/recommenderStatic",
    method: "post",
    data,
  });
}
// 客户导入
export function customerImport(data) {
  return request({
    url: "Customer/Customer/customerImport",
    method: "post",
    data,
  });
}
// 获取司机信息
export function getAllDriver(data) {
  return request({
    url: `/System/Driver/getAllDriver`,
    method: "put",
    data,
  });
}
// 删除司机信息
export function delDriver(id, data) {
  return request({
    url: `System/Driver/delDriver/${id}`,
    method: "put",
    data,
  });
}
//添加司机信息
export function addDriver(data) {
  return request({
    url: `System/Driver/addDriver`,
    method: "put",
    data,
  });
}
//编辑司机信息
export function editDriver(data) {
  return request({
    url: `System/Driver/editDriver`,
    method: "put",
    data,
  });
}
//获取指定信息
export function getDriverInfo(id, data) {
  return request({
    url: `System/Driver/getDriverInfo/${id}`,
    method: "put",
    data,
  });
}

export function getAllOpenDriver(data) {
  return request({
    url: `System/Driver/getAllOpenDriver`,
    method: "post",
    data,
  });
}
// 拜访报表
export function getAllCustomerVisit(data) {
  return request({
    url: `Customer/CustomerCommunication/getAllCustomerVisit`,
    method: "post",
    data,
  });
}
export function getCustomerCallOnReportForm(data) {
  return request({
    url: `Customer/CustomerCommunication/getCustomerCallOnReportForm`,
    method: "post",
    data,
  });
}
export function getCustomerVisitInfo(data) {
  return request({
    url: `Customer/CustomerCommunication/getCustomerVisitInfo`,
    method: "post",
    data,
  });
}
export function getAllDepartment(data) {
  return request({
    url: `Department/Department/getAllDepartment`,
    method: "post",
    data,
  });
}
// 未拜访客户统计列表
export function getCustomerNoVisit(data) {
  return request({
    url: `Customer/CustomerCommunication/getCustomerNoVisit`,
    method: "post",
    data,
  });
}
//客户分布图
export function getCustomerDistributed(data) {
  return request({
    url: `Customer/Customer/getCustomerDistributed`,
    method: "post",
    data,
  });
}
//批量设置客户类型
export function setCustomerType(data) {
  return request({
    url: `Customer/Customer/setCustomerType`,
    method: "post",
    data,
  });
}
// 删除客户
export function delCustomer(data) {
  return request({
    url: `Customer/Customer/delCustomer`,
    method: "post",
    data,
  });
}
