import request from "@/utils/request";

/* ------------分销----------------- */
/* 等级设置 */
//  新增等级
export function addGrade(data) {
  return request({
    url: `/Commission/CommissionGrade/addGrade`,
    method: "post",
    data,
  });
}
//  升级条件
export function getAllGradeCondition(data) {
  return request({
    url: `/Commission/CommissionGrade/getAllGradeCondition`,
    method: "post",
    data,
  });
}
//  等级详情
export function getInfoGrade(id, data) {
  return request({
    url: `/Commission/CommissionGrade/getInfoGrade/${id}`,
    method: "get",
    data,
  });
}
//  等级列表
export function getAllGrade(data) {
  return request({
    url: `/Commission/CommissionGrade/getAllGrade`,
    method: "post",
    data,
  });
}
//  修改等级
export function updateGrade(id, data) {
  return request({
    url: `/Commission/CommissionGrade/updateGrade/${id}`,
    method: "put",
    data,
  });
}
//  删除等级
export function delGrade(id, data) {
  return request({
    url: `/Commission/CommissionGrade/delGrade/${id}`,
    method: "get",
    data,
  });
}
/* 分销设置 */
//  查询设置
export function getSetting(data) {
  return request({
    url: `/Commission/CommissionSetting/getSetting`,
    method: "post",
    data,
  });
}
// 保存设置
export function saveSetting(data) {
  return request({
    url: `/Commission/CommissionSetting/saveSetting`,
    method: "post",
    data,
  });
}
// 文本设置
export function getTxtSetting(data) {
  return request({
    url: `/Commission/CommissionSetting/getTxtSetting`,
    method: "post",
    data,
  });
}
export function saveTxtSetting(data) {
  return request({
    url: `/Commission/CommissionSetting/saveTxtSetting`,
    method: "post",
    data,
  });
}
/* 分销商 */
// 分销商审核
export function auditBusinessman(data) {
  return request({
    url: `/Commission/CommissionBusinessman/auditBusinessman`,
    method: "post",
    data,
  });
}
// 分销商列表
export function getAllBusinessman(data) {
  return request({
    url: `/Commission/CommissionBusinessman/getAllBusinessman`,
    method: "post",
    data,
  });
}
// 分销商列表
export function exportGetAllBusinessman(data) {
  return request({
    url: `/Commission/CommissionBusinessman/getAllBusinessman`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 下级列表
export function getAllSub(data) {
  return request({
    url: `/Commission/CommissionBusinessman/getAllSub`,
    method: "post",
    data,
  });
}
// 分销商详情
export function getInfoBusinessman(data) {
  return request({
    url: `/Commission/CommissionBusinessman/getInfoBusinessman`,
    method: "post",
    data,
  });
}
// 添加分销商
export function addBusinessman(data) {
  return request({
    url: `/Commission/CommissionBusinessman/addBusinessman`,
    method: "post",
    data,
  });
}
// 删除分销商
export function delBusinessman(data) {
  return request({
    url: `/Commission/CommissionBusinessman/delBusinessman`,
    method: "post",
    data,
  });
}
/* 分销商品 */
// 设置佣金
export function setCommission(data) {
  return request({
    url: `/Commission/CommissionGoods/setCommission`,
    method: "post",
    data,
  });
}
// 列表(可搜索)
export function CommissionGoodsGetAll(data) {
  return request({
    url: `/Commission/CommissionGoods/getAll`,
    method: "post",
    data,
  });
}
// 参与分销/不参与（可批量）
export function updateIsJoin(data) {
  return request({
    url: `/Commission/CommissionGoods/updateIsJoin`,
    method: "post",
    data,
  });
}
// 详情
export function CommissionGoodsGetInfo(id, data) {
  return request({
    url: `/Commission/CommissionGoods/getInfo/${id}`,
    method: "post",
    data,
  });
}

/* 分销订单 */
// 列表
export function CommissionOrderGetAll(data) {
  return request({
    url: `/Commission/CommissionOrder/getAll`,
    method: "post",
    data,
  });
}
/* 分销概览首页 */
// 首页统计(TOP)
export function statisticsCommissionThree(data) {
  return request({
    url: `/Commission/CommissionBusinessman/statisticsCommissionThree`,
    method: "get",
    data,
  });
}
// 首页统计
export function statisticsCommissionOne(data) {
  return request({
    url: `/Commission/CommissionBusinessman/statisticsCommissionOne`,
    method: "get",
    data,
  });
}
// 首页统计(新增分销商数)
export function statisticsCommissionTwo(data) {
  return request({
    url: `/Commission/CommissionBusinessman/statisticsCommissionTwo`,
    method: "get",
    data,
  });
}
/* 提现 */
// 获取提现申请列表
export function CommissionWithdrawalsGetAll(data) {
  return request({
    url: `/Commission/CommissionWithdrawals/getAll`,
    method: "post",
    data,
  });
}
// 修改提现审核状态
export function WithdrawalsUpdateAuditStatus(id, data) {
  return request({
    url: `/Commission/CommissionWithdrawals/updateAuditStatus/${id}`,
    method: "post",
    data,
  });
}
// 余额提现
export function getAllReflectDetail(data) {
  return request({
    url: `/Customer/Customer/getAllReflectDetail`,
    method: "post",
    data,
  });
}
// 审核
export function updateReflectDetail(data) {
  return request({
    url: `Customer/Customer/updateReflectDetail`,
    method: "post",
    data,
  });
}
// 业务员订单报表
export function salesManOrder(data) {
  return request({
    url: `Charts/Charts/salesManOrder`,
    method: "post",
    data,
  });
}
// 订单数据表
export function order(data) {
  return request({
    url: `Charts/Charts/order`,
    method: "post",
    data,
  });
}
// 客户订单报表
export function customerOrder(data) {
  return request({
    url: `Charts/Charts/customerOrder`,
    method: "post",
    data,
  });
}
//地区报表
export function areaOrder(data) {
  return request({
    url: `Charts/Charts/areaOrder`,
    method: "post",
    data,
  });
}
// 销售排行
export function salesManRank(data) {
  return request({
    url: `Customer/Customer/salesManRank`,
    method: "post",
    data,
  });
}
export function exportsalesManRank(data) {
  return request({
    url: `Customer/Customer/salesManRank`,
    method: "post",
    data,
  });
}
//修改分销商上级关系
export function editRelationship(data) {
  return request({
    url: `Commission/ApiCommissionBusinessman/editRelationship`,
    method: "post",
    data,
  });
}
