import request from "@/utils/request";
/* --- 价格管理 --- */
// 价格列表
export function getAllGoodsPrice(data) {
  return request({
    url: "/Price/Price/getAllGoodsPrice",
    method: "post",
    data,
  });
}
// 价格管理的搜索  Price/Price/search
export function searchPrice(data) {
  return request({
    url: "/Price/Price/search",
    method: "post",
    data,
  });
}
// 批量调价
export function batchPrice(data) {
  return request({
    url: "/Price/Price/batchPrice",
    method: "put",
    data,
  });
}

/* --- 商品调价 --- */
// 商品调价的搜索
export function getAllSearch(data) {
  return request({
    url: "/Price/PriceAdjustment/getAll",
    method: "post",
    data,
  });
}
// 增加调价单
export function PriceAdjustmentAdd(data) {
  return request({
    url: "/Price/PriceAdjustment/add",
    method: "post",
    data,
  });
}
// 获取调价单列表
export function PriceAdjustmentGetAll(data) {
  return request({
    url: "/Price/PriceAdjustment/getAll",
    method: "post",
    data,
  });
}
// 生效调价单
export function PriceAdjustmentEffective(id, data) {
  return request({
    url: `/Price/PriceAdjustment/effective/${id}`,
    method: "put",
    data,
  });
}
// 自动创建调价单且自动生效接口
export function addAndEffective(data) {
  return request({
    url: `/Price/PriceAdjustment/addAndEffective`,
    method: "post",
    data,
  });
}
// 获取指定商品的最后生效的客户调价单数据
export function getCustomerPriceByGoodsIds(data) {
  return request({
    url: `/Price/CustomerPriceAdjustment/getCustomerPriceByGoodsIds`,
    method: "post",
    data,
  });
}
// 获取指定商品的最后生效的客户类型调价数据
export function getCustomerTypePriceByGoodsIds(data) {
  return request({
    url: `/Price/CustomerTypePriceAdjustment/getCustomerTypePriceByGoodsIds`,
    method: "post",
    data,
  });
}
// 删除指定商品的最后生效的客户类型调价数据
export function delCustomerTypePrice(data) {
  return request({
    url: `/Price/CustomerTypePriceAdjustment/delCustomerTypePrice`,
    method: "post",
    data,
  });
}
// 删除指定商品的最后生效的客户调价单数据
export function delCustomerPrice(data) {
  return request({
    url: `/Price/CustomerPriceAdjustment/delCustomerPrice`,
    method: "post",
    data,
  });
}
// 客户类型调价单
export function addCustomerTypePriceAdjustment(data) {
  return request({
    url: `/Price/CustomerTypePriceAdjustment/add`,
    method: "post",
    data,
  });
}
// 客户类型调价单列表
export function getAllCustomerTypePriceAdjustment(data) {
  return request({
    url: `/Price/CustomerTypePriceAdjustment/getAll`,
    method: "post",
    data,
  });
}
// 导出客户类型调价单列表
export function exportgetAllCustomerTypePriceAdjustment(data) {
  return request({
    url: `/Price/CustomerTypePriceAdjustment/getAll`,
    method: "post",
    responseType: "blob",
    data,
  });
}
//商品掉价单导出
export function exportPriceAdjustment(data) {
  return request({
    url: `Price/PriceAdjustment/getAll`,
    method: "post",
    responseType: "blob",
    data,
  });
}
//客户调价单导出
export function exportCustomerPriceAdjustment(data) {
  return request({
    url: `Price/CustomerPriceAdjustment/getAll`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 生效客户类型调价单
export function effectiveCustomerTypePriceAdjustment(id, data) {
  return request({
    url: `/Price/CustomerTypePriceAdjustment/effective/${id}`,
    method: "post",
    data,
  });
}
/* --- 客户调价单 --- */
// 新增客户调价单
export function CustomerPriceAdjustmentAdd(data) {
  return request({
    url: `/Price/CustomerPriceAdjustment/add`,
    method: "post",
    data,
  });
}
// 获取客户调价单
export function CustomerPriceAdjustmentGetAll(data) {
  return request({
    url: `/Price/CustomerPriceAdjustment/getAll`,
    method: "post",
    data,
  });
}
// 生效客户调价单
export function CustomerPriceAdjustmentEffective(id, data) {
  return request({
    url: `/Price/CustomerPriceAdjustment/effective/${id}`,
    method: "put",
    data,
  });
}

// 保存供应商成本价
export function CostPriceSupplierSave(data) {
  return request({
    url: `/Price/CostPriceSupplier/save`,
    method: "post",
    data,
  });
}

// 获取供应商成本价列表
export function CostPriceSupplierGetAll(data) {
  return request({
    url: `/Price/CostPriceSupplier/getAll`,
    method: "post",
    data,
  });
}

// 获取供应商成本价详情
export function CostPriceSupplierGetByBasicGoodsId(id) {
  return request({
    url: `/Price/CostPriceSupplier/getByBasicGoodsId/${id}`,
    method: "post",
  });
}

// 设置供应商成本价是否启用
export function CostPriceSupplierEnableStatus(data) {
  return request({
    url: `/Price/CostPriceSupplier/enableStatus`,
    method: "post",
    data,
  });
}

// 商品是否存在供应商成本价
export function CostPriceSupplierIsExistGoods(basicGoodsId) {
  return request({
    url: `/Price/CostPriceSupplier/isExistGoods/${basicGoodsId}`,
    method: "get",
  });
}
