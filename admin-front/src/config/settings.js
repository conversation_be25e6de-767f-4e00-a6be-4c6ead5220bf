// api域名配置
const apiUrl = {
  INDEX_URL: "https://gxs.hui1688.cn", // 后端接口域名
  // INDEX_URL: "https://gxs.abc139.top", // 后端接口域名
  QINIU_UP: "https://up-z1.qiniup.com", // 七牛上传域名
  QINIU_URL: "https://img.hui1688.cn", // 七牛空间访问域名
  UPLOAD_URL: "https://xdb-nq.byshun.cn", // 项目上传域名
  QINIU_KEY: "hui1688-img", // 七牛空间名称
  SAASTAG: "xxx", // webSocket前缀
  wsServer: "ws://gxs.hui1688.cn:9501", // webSocket连结域名
};

const apiDevUrl = {
  // INDEX_URL: "http://localhost", // 后端接口域名
  INDEX_URL: "http://gxs.hui1688.local", // 后端接口域名
  // INDEX_URL: "https://gxs.abc139.top", // 后端接口域名
  QINIU_UP: "https://up-z1.qiniup.com", // 七牛上传域名
  QINIU_URL: "https://img.hui1688.cn", // 七牛空间访问域名
  UPLOAD_URL: "https://xdb-nq.byshun.cn", // 项目上传域名
  QINIU_KEY: "hui1688-img", // 七牛空间名称
  SAASTAG: "xxx", // webSocket前缀
  wsServer: "ws://127.0.0.1:9501", // webSocket连结域名
};

// 阿里云上传配置
const aliUpSetting = {
  region: "oss-cn-qingdao.aliyuncs.com",
  endpoint: "oss-cn-qingdao.aliyuncs.com",
  accessKeyId: "xxx",
  accessKeySecret: "XXXXXX",
  bucket: "xxxx",
};
/**
 * @description 全局变量配置
 */
// const Url = process.env.NODE_ENV === "development" ? apiUrl.dev : apiUrl.master;

const Url = process.env.NODE_ENV === "production" ? apiUrl : apiDevUrl;
// const Url = apiUrl;
module.exports = {
  // 是否是舜津科技标准系统
  isQN: true,
  //默认的接口地址 如果是开发环境和生产环境走dev
  apiUrl: Url,
  //默认的接口地址 如果是开发环境和生产环境走vab-mock-server，当然你也可以选择自己配置成需要的接口地址
  baseURL: Url.INDEX_URL,
  // apiUrl: apiUrl.dev,
  // 阿里云上传配置
  aliUpSetting: aliUpSetting,
  // 是否启用阿里云
  isAliYun: false,
  //开发以及部署时的URL，hash模式时在不确定二级目录名称的情况下建议使用""代表相对路径或者"/二级目录/"，history模式默认使用"/"或者"/二级目录/"
  publicPath: "",
  //生产环境构建文件的目录名
  outputDir: "dist",
  //放置生成的静态资源 (js、css、img、fonts) 的 (相对于 outputDir 的) 目录。
  assetsDir: "static",
  //开发环境每次保存时是否输出为eslint编译警告
  lintOnSave: false,
  //进行编译的依赖
  transpileDependencies: ["vue-echarts", "resize-detector"],
  //标题 （包括初次加载雪花屏的标题 页面的标题 浏览器的标题）
  title: "升辉ERP",
  //标题分隔符
  titleSeparator: " - ",
  //标题是否反转 如果为false:"page - title"，如果为ture:"title - page"
  titleReverse: false,
  //简写
  abbreviation: "升辉科技",
  //语言类型zh、en
  i18n: "zh",
  //开发环境端口号
  devPort: "9999",
  //版本号
  version: process.env.VUE_APP_VERSION,
  //公司名称
  companyName: "升辉信息科技（临沂）有限公司",
  // 备案号
  icCase: "鲁ICP备20008798号-6",
  // 公司地址
  companyAddress: "山东省临沂市兰山区双岭路临沂商城出口商品展示中心L3-033室",
  // 系统介绍
  systemDesc:
    "升辉ERP是专业的进销存订货商城小程序系统，可以为客户定制手机端下单商城小程序以及手机端进行进销存管理的小程序，方便厂家和批发零售企业管理自己的进销存订货业务。",
  //是否开启路由缓存
  onKeepAlive: false,
  // onKeepAlive: true,
  //缓存路由的最大数量
  keepAliveMaxNum: 20,
  //路由模式，可选值为 history 或 hash
  routerMode: "hash",
  //不经过token校验的路由
  routesWhiteList: [
    "/login",
    "/MenuList",
    "/register",
    "/callback",
    "/CashierLogin",
    "/MerchantsLogin",
    "/404",
    "/401",
  ],
  //加载时显示文字
  loadingText: "正在加载中...",
  //token名称
  tokenName: "Authorization",
  //token在localStorage、sessionStorage、cookie存储的key的名称
  tokenTableName: "qianNiaoToken",
  //token存储位置localStorage sessionStorage cookie
  storage: "sessionStorage",
  //token失效回退到登录页时是否记录本次的路由
  recordRoute: false,
  //是否显示logo，不显示时设置false，显示时请填写remixIcon图标名称，暂时只支持设置remixIcon
  logo: "vuejs-fill",
  //是否固定头部 固定fixed 不固定noFixed
  header: "fixed",
  //布局种类 horizontal vertical gallery comprehensive common
  layout: "gallery",
  //主题名称 default ocean green glory white
  themeName: "default",
  //是否显示顶部进度条
  showProgressBar: true,
  //是否显示多标签页
  showTagsBar: false,
  //是否显示语言选择组件
  showLanguage: false,
  //是否显示刷新组件
  showRefresh: true,
  //是否显示搜索组件
  showSearch: false,
  //是否显示主题组件
  showTheme: false,
  //是否显示通知组件
  showNotice: true,
  //是否显示全屏组件
  showFullScreen: true,
  //配后端数据的接收方式application/json;charset=UTF-8或者application/x-www-form-urlencoded;charset=UTF-8
  contentType: "application/json;charset=UTF-8",
  //消息框消失时间
  messageDuration: 3000,
  //最长请求时间
  requestTimeout: 60000,
  //操作正常code，支持String、Array、int多种类型
  successCode: [200, 0],
  //登录失效code
  invalidCode: 402,
  //无角色code
  noRoleCode: 401,
  //在哪些环境下显示高亮错误
  errorLog: ["development", "production"],
  //是否开启登录拦截
  loginInterception: true,
  //是否开启登录RSA加密
  loginRSA: false,
  //是否依据mock数据生成webstorm HTTP Request请求文件
  httpRequestFile: false,
  //intelligence（前端导出路由）和all（后端导出路由）两种方式
  authentication: "intelligence",
  //是否开启roles字段进行角色权限控制（如果是all模式后端完全处理角色并进行json组装，可设置false不处理路由中的roles字段）
  rolesControl: true,
  //vertical gallery comprehensive common布局时是否只保持一个子菜单的展开
  uniqueOpened: false,
  //vertical布局时默认展开的菜单path，使用逗号隔开建议只展开一个
  defaultOpeneds: ["/goods/manageG", "/goods/sale"],
  //需要加loading层的请求，防止重复提交
  debounce: ["newAddOrder", "newOrderEdit"],
  //需要自动注入并加载的模块
  providePlugin: { maptalks: "maptalks", "window.maptalks": "maptalks" },
  //npm run build时是否自动生成7z压缩包
  build7z: false,
  //代码生成机生成在view下的文件夹名称
  templateFolder: "project",
  //是否显示终端donation打印
  donation: false,
  //画廊布局和综合布局时，是否点击一级菜单默认开启第一个二级菜单
  openFirstMenu: true,
  // 显示多标签页时多标签页样式：卡片风格card、灵动风格smart、圆滑风格smooth
  tabsBarStyle: "smooth",
  // 是否多标签页图标
  showTabsBarRemixIcon: true,
};
