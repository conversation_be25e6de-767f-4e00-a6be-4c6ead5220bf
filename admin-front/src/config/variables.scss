/**

 * @description 全局主题变量配置
 */
//框架默认主题色
$base-color-default: #2153D4;
//默认层级
$base-z-index: 999;
//画廊最左侧菜单背景色
$base-gallery-first-menu-background: linear-gradient(to right, #282c34, #000);
//画廊时菜单背景色
$base-gallery-second-menu-background: #fff;
//画廊时菜单选中背景色
$base-gallery-second-menu-background-active: rgba(#1890ff, 0.1);
//横向、纵向时菜单背景色
$base-menu-background: #282c34;
//菜单文字颜色
$base-menu-color: hsla(0, 0%, 100%, 0.95);
//菜单选中文字颜色
$base-menu-color-active: hsla(0, 0%, 100%, 0.95);
//菜单选中背景色
$base-menu-background-active: $base-color-default;
//标题颜色
$base-title-color: #fff;
//字体大小配置
$base-font-size-small: 12px;
$base-font-size-default: 14px;
$base-font-size-big: 16px;
$base-font-size-bigger: 18px;
$base-font-size-max: 22px;
$base-font-color: #606266;
$base-color-blue: $base-color-default;
$base-color-white: #ffffff;
$base-color-black: #515a6e;
$base-color-green: #36B365;
$base-color-white: #fff;
$base-color-info: #CAD0D7;
$base-color-yellow: #F7BD1B;
$base-color-orange: #F5762C;
$base-color-red: #F54966;
$base-color-gray: rgba(0, 0, 0, 0.65);
$base-main-width: 1279px;
$base-border-radius: 2px;
$base-border-color: #dcdfe6;
//输入框高度
$base-input-height: 32px;
//默认paddiing
$base-padding: 20px;
//默认阴影
$base-box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
//横向时top-bar、logo、一级菜单的高度
$base-top-bar-height: 65px;
//纵向、画廊时logo的高度
$base-logo-height: 60px;
//顶部nav-bar的高度
$base-nav-bar-height: 60px;
//顶部多标签页tags-bar的高度
$base-tags-bar-height: 55px;
//顶部多标签页tags-bar中每一个item的高度
$base-tag-item-height: 34px;
//菜单li标签的高度
$base-menu-item-height: 50px;
//app-main的高度
$base-app-main-height: calc(100vh - #{$base-nav-bar-height} - #{$base-tags-bar-height} - #{$base-padding} * 2 - 56px);
//纵向时左侧导航未折叠时的宽度
$base-left-menu-width: 256px;
//纵向时左侧导航未折叠时右侧内容的宽度
$base-right-content-width: calc(100% - #{$base-left-menu-width});
//纵向时左侧导航已折叠时的宽度
$base-left-menu-width-min: 65px;
//纵向时左侧导航已折叠时右侧内容的宽度
$base-right-content-width-min: calc(100% - #{$base-left-menu-width-min});
//默认动画
$base-transition: all 0.2s;
//默认动画时长
$base-transition-time: 0.2s;

:export {
  //菜单文字颜色变量导出
  menu-color: $base-menu-color;
  //菜单选中文字颜色变量导出
  menu-color-active: $base-menu-color-active;
  //菜单背景色变量导出
  menu-background: $base-menu-background;
  //画廊菜单背景色变量导出
  gallery-second-menu-background: $base-gallery-second-menu-background;
}
