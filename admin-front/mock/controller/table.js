const { mock } = require("mockjs");
const { handleRandomImage } = require("../utils");
const List = [];
const count = 50;
for (let i = 0; i < count; i++) {
  List.push(
    mock({
      uuid: "@uuid",
      id: "@id",
      title: "@title(1, 2)",
      description: "@csentence",
      "status|1": ["published", "draft", "deleted"],
      author: "@cname",
      datetime: "@datetime",
      pageViews: "@integer(300, 5000)",
      img: handleRandomImage(228, 228),
      switch: "@boolean",
      percent: "@integer(80,99)",
      "rate|1": [1, 2, 3, 4, 5],
    })
  );
}

module.exports = [
  {
    url: "/table/getList",
    type: "get",
    response(config) {
      const { title, pageNo = 1, pageSize = 20 } = config.query;
      let mockList = List.filter((item) => {
        if (title && item.title.indexOf(title) < 0) return false;
        return true;
      });
      const pageList = mockList.filter(
        (item, index) =>
          index < pageSize * pageNo && index >= pageSize * (pageNo - 1)
      );
      return {
        code: 200,
        msg: "success",
        totalCount: mockList.length,
        data: pageList,
      };
    },
  },
  {
    url: "/table/doEdit",
    type: "post",
    response() {
      return {
        code: 200,
        msg: "模拟保存成功",
      };
    },
  },
  {
    url: "/table/doDelete",
    type: "post",
    response() {
      return {
        code: 200,
        msg: "模拟删除成功",
      };
    },
  },
];
