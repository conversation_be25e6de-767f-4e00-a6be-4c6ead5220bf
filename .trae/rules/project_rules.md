# Project Rules
## 前端项目规范
### 技术栈
- 前端框架：Vue2
- 状态管理：Vuex
- 路由管理：vue-router
- 样式管理：sass
- 构建工具：webpack
### 目录结构
目录入口： ./admin-front
- admin-front
  - src
    - access：权限管理
    - api：接口
    - assets：静态资源
    - component：组件
    - config：配置文件
    - extra：扩展
    - icons：图标
    - layouts：布局
    - mixin：混入
    - router：路由
    - store：状态管理
    - utils：工具类
    - vab: vab组件
    - views：页面
    - viewsMerch: 商家端
    - vxeTable: vxe-table组件
    - App.vue：入口文件
    - main.js：入口文件
    - package.json：包管理文件
    - README.md：项目说明文件
    - vue.config.js：webpack配置文件
  - public：公共资源
  - dist：打包后的文件
  - .gitignore：git忽略文件
  - .eslintrc.js：eslint配置文件
## 后端项目规范
### 技术栈
- 后端框架：php7.4
- 数据库：MySQL
- 缓存：Redis
### 目录结构
目录入口：./
- qianniao-admin
  - gxs.hui1688.cn
    - Cache: 缓存
    - Common: 公共文件
    - Config: 配置文件
    - Controller: 控制器
    - Dao: 数据访问层
    - Model: 模型
    - Util: 服务层
  - sql: sql文件
## 小程序商城项目规范
### 技术栈
- 小程序框架：Uniapp，vue2
- 状态管理：vuex
## 小程序后台管理项目规范
### 技术栈
- 小程序框架：Uniapp，vue2
- 状态管理：vuex
## wms-pda项目规范
### 技术栈
- 小程序框架：Uniapp，vue3
- 状态管理：pinia
- 样式管理：tailwindcss
- 构建工具：vite